<template>
  <div ref="volwindow" v-loading="loading" class="window-info">
    <div class="top">
      <el-image class="img" :src="fImgSrc" fit="cover" :preview-src-list="fSrcList">
        <div slot="placeholder" class="image-slot">
          <span class="dot">加载中...</span>
        </div>
      </el-image>
      <div class="content">
        <div class="title">{{ dataDetail.title }}</div>
        <p class="fulb">服务类别：{{ dataDetail.typeName }}</p>
        <p class="fzr">负责人：{{ dataDetail.userName }}</p>
        <p class="dh">电话：{{ dataDetail.phone }}</p>
        <p class="hdrq">活动日期：{{ dataDetail.startTime }} 至 {{ dataDetail.endTime }}</p>
        <p class="zmrs">招募人数：{{ dataDetail.volUserCount || 0 }}/{{ dataDetail.numOfRecruits }}</p>
      </div>
    </div>
    <!-- 详细内容 -->
    <div class="center">
      <div class="title">详细内容</div>
      <div class="content">
        <!-- <p>{{ dataDetail.content }}</p> -->
        <div v-html="dataDetail.content" />
      </div>
    </div>
    <!-- 精彩内容 -->
    <div class="bottom">
      <div class="title">精彩内容</div>
      <div class="scrollbar-box" :vertical="false">
        <el-scrollbar ref="scrollContainer" style="width: 100%;" @wheel.native.prevent="handleScroll">
          <div class="img-list">
            <el-image v-for="(url,idx) in sSrcList" :key="idx" class="img" :src="url" fit="cover" :preview-src-list="sSrcList">
              <div slot="placeholder" class="image-slot">
                <span class="dot">加载中...</span>
              </div>
            </el-image>
            <span v-if="!sSrcList.length" style="color: #888; padding-top: 10px;">暂无图片</span>
          </div>
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>

<script>
import { getTaskInfo } from '@/api/board/map/index'
import { getFiles } from '@/api/supervise/swit'

export default {
  data() {
    return {
      loading: false,
      dataDetail: {},
      windowInfo: null,
      fImgSrc: '',
      fSrcList: [],
      sSrcList: []
    }
  },
  computed: {
    scrollWrapper() {
      return this.$refs.scrollContainer.$refs.wrap
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.winResize)
  },
  methods: {
    handleScroll(e) {
      const eventDelta = e.wheelDelta || -e.deltaY * 40
      const $scrollWrapper = this.scrollWrapper
      $scrollWrapper.scrollLeft = $scrollWrapper.scrollLeft - eventDelta / 2
    },
    windowDataInit(id, windowInfo) {
      this.windowUpdate(windowInfo)
      this.loading = true
      Promise.all([
        getTaskInfo(id),
        getFiles({ businessId: id, tableName: 'vol_task' })
      ]).then(resAry => {
        let [dataDetail, files] = resAry
        this.dataDetail = dataDetail.data
        // 图片部分
        let fSrcList = [], sSrcList = []
        files.rows.forEach(item => {
          if (item.status == 1) {
            fSrcList.push('/zqzfj' + item.filePath)
          } else if (item.status == 2) {
            sSrcList.push('/zqzfj' + item.filePath)
          }
        })
        this.fSrcList = fSrcList
        this.fImgSrc = fSrcList[0]
        this.sSrcList = sSrcList
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    winResize() {
      setTimeout(() => {
        const winWidth = this.$refs.volwindow.clientWidth
        this.windowInfo.uT.style = `width: ${winWidth}px`
      }, 300)
    },
    windowUpdate(windowInfo) {
      if (!this.windowInfo) {
        this.windowInfo = windowInfo
        window.addEventListener('resize', this.winResize)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.window-info {
  display: none;
  width: pxtorem(1005);
  background: #fff;
  font-size: pxtorem(14);
  ::v-deep svg {
    position: static;
  }
  ::v-deep .image-slot {
    height: pxtorem(170);
    display: flex;
    justify-content: center;
    align-items: center;
  }
  p {
    padding: 0;
    margin: 0;
  }
  .top {
    display: flex;
    justify-content: space-between;
    .title {
      font-size: pxtorem(20);
      font-weight: 700;
    }
    .img {
      width: pxtorem(170);
      height: pxtorem(170);
      margin-right: 20px;
    }
    .content {
      flex: 1;
      p {
        padding-left: pxtorem(25);
        line-height: pxtorem(29);
      }
      .fulb {
        background: url(@/assets/images/windowInfo/fulb.png) no-repeat left center;
      }
      .fzr {
        background: url(@/assets/images/windowInfo/fzr.png) no-repeat left center;
      }
      .dh {
        background: url(@/assets/images/windowInfo/dh.png) no-repeat left center;
      }
      .hdrq {
        background: url(@/assets/images/windowInfo/hdrq.png) no-repeat left center;
      }
      .zmrs {
        background: url(@/assets/images/windowInfo/zmrs.png) no-repeat left center;
      }
    }
  }
  .center {
    margin-top: 20px;
    .title {
      background: #daf0fb;
      line-height: pxtorem(35);
      font-size: pxtorem(16);
      font-weight: 700;
      padding-left: 15px;
    }
    .content {
      background: #f2fafe;
      overflow: hidden;
      min-height: 100px;
      p {
        padding: 5px 15px;
      }
    }
  }
  .bottom {
    margin-top: 20px;
    .scrollbar-box {
      width: pxtorem(705);
    }
    .img-list {
      display: flex;
      flex-wrap: nowrap;
      height: pxtorem(170);
    }
    .img {
      width: pxtorem(170);
      height: pxtorem(170);
      margin-right: 10px;
      flex-shrink: 0;
    }
  }
}
</style>
