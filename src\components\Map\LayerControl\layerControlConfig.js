const AllLayerList = [
    {
      name: '城市运行管理服务系统',
      list: [
        {
          id: '1-1',
          name: '图层选择',
          nodeType: 'parent',
          children: [
            // {
            //   id: '1-1',
            //   name: '公共设施',
            //   type: 'poi',
            // },
            // {
            //   id: '1-2',
            //   name: '园林绿化',
            //   type: 'layer',
            // },
            {
              id: '1-3',
              name: '案件分布',
              type: 'poi',
            },
            {
              id: '1-4',
              name: '环境监测',
              name1: 'hjjc',
              type: 'poi',
            },
            {
              id: '1-5',
              name: '交通流量',
              name1: 'icon_road_2',
              type: 'layer',
            },
            {
              id: '1-6',
              name: '人口热力',
              name1: 'rkztHot0',
              type: 'layer',
            }
          ],
          number: 2,  
        },
      ],
    },
    {
      name: '综合评价一张图',
      list: [
        {
          id: '1-1',
          name: '图层选择',
          nodeType: 'parent',
          children: [
            {
              id: '1-1',
              name: '摄像头',
              type: 'poi',
            },
            {
              id: '1-2',
              name: '传感器',
              type: 'poi',
            },
          ],
          number: 2,  
        },
      ],
    },
    {
      name: '市容环卫一张图',
      list: [
        {
          id: '1-1',
          name: '图层选择',
          nodeType: 'parent',
          children: [
            {
              id: '1-1',
              name: '车辆',
              type: 'poi',
            },
            {
              id: '1-2',
              name: '垃圾桶',
              type: 'poi',
            },
            {
              id: '1-3',
              name: '垃圾焚烧场',
              type: 'poi',
            },
            {
              id: '1-4',
              name: '环境监测',
              type: 'layer',
            },
            {
              id: '1-5',
              name: '垃圾填埋厂',
              type: 'poi',
            },
            {
              id: '1-6',
              name: '公共卫生间',
              type: 'layer',
            }
          ],
          number: 6,  
        },
      ],
    },
    {
      name: '物联网设备一张图',
      list: [
        {
          id: '1-1',
          name: '图层选择',
          nodeType: 'parent',
          children: [
            {
              id: '1-1',
              name: '液位监测',
              name1: 'yyjc',
              type: 'poi',
            },
            // {
            //   id: '1-2',
            //   name: '液位、雨量监测',
            //   name1: 'yyyljc',
            //   type: 'poi',
            // },
            {
              id: '1-3',
              name: '流量监测',
              name1: 'lljc',
              type: 'poi',
            },
            {
              id: '1-4',
              name: '空气质量监测',
              name1: 'kqzljc',
              type: 'poi',
            },
            {
              id: '1-5',
              name: '水质监测',
              name1: 'szjc',
              type: 'poi',
            },
            {
              id: '1-6',
              name: '水肥一体化监测',
              name1: 'sfythjc',
              type: 'poi',
            },
            {
              id: '1-7',
              name: '气体监测',
              name1: 'qtjc',
              type: 'poi',
            }

          ],
          number: 7,  
        },
      ],
    },
    {
      name: '犬类一张图',
      list: [
        {
          id: '1-1',
          name: '图层选择',
          nodeType: 'parent',
          children: [
            {
              id: '1-1',
              name: '宠物医院',
              type: 'poi',
            },
            {
              id: '1-2',
              name: '动物诊所',
              type: 'poi',
            },
            {
              id: '1-3',
              name: '收容机构',
              type: 'poi',
            },
            {
              id: '1-4',
              name: '动物防疫站点',
              type: 'poi',
            },
          ],
          number: 4,  
        },
      ],
    },
    {
      name: '运行监测一张图',
      list: [
        {
          id: '1-1',
          name: '图层选择',
          nodeType: 'parent',
          children: [
            {
              id: '1-1',
              name: '监测报警事件',
              type: 'poi',
            },
            {
              id: '1-2',
              name: '环卫设施预警',
              type: 'poi',
            },
            {
              id: '1-3',
              name: '人员密集预警',
              type: 'poi',
            },
            {
              id: '1-4',
              name: '监测点',
              type: 'poi',
            },
          ],
          number: 4,  
        },
      ],
    },
  ]
  
  export default AllLayerList
  