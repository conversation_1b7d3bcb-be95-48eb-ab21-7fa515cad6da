import Vue from 'vue'
import elementUi from 'element-ui'
import App from './App.vue'
import store from './store'
import { initRouter } from './router'
import 'element-ui/lib/theme-chalk/index.css'
import '@/assets/fonts/iconfont.css'
import './index.less'
import './sigma.css'
import 'animate.css'
import vueSeamlessScroll from 'vue-seamless-scroll' // 循环滚动
import * as echarts from 'echarts'
import 'echarts-gl'
import '@/assets/idt/Idt'
import mixin from '@/minxins/setTimerMinxin'
import { numberFilter, getYearList, parseTime } from '@/utils/index'
import moment from 'moment'
import { getDicts } from '@/api/system/dict/data'
// import mainObj from '@/utils/PocMain.js'

// import jquery from 'jquery'
Vue.prototype.$moment = moment
Vue.use(vueSeamlessScroll)
const router = initRouter()
Vue.use(elementUi)
// Vue.use(Image)
Vue.mixin(mixin)
Vue.filter('numberFilter', numberFilter)

Vue.config.productionTip = false
Vue.prototype.$pageWidth = 3840
Vue.prototype.$pageHeight = 2160
Vue.prototype.$echarts = echarts
Vue.prototype.$getYearList = getYearList
Vue.prototype.$bus = new Vue()
Vue.prototype.$currentYear = moment(new Date()).format('YYYY')
Vue.prototype.parseTime = parseTime
// Vue.prototype.timeMixin = mixin;

// 全局方法挂载
Vue.prototype.getDicts = getDicts

// 全局挂载 POC 主对象
// Vue.prototype.$PocMain = mainObj;

router.beforeEach((to, from, next) => {
  /* 路由发生变化修改页面title */
  if (to.meta.title) {
    document.title = to.meta.title
  }
  next()
})

// 初始化城市选择状态
store.dispatch('initActiveCityName')

// 预加载 POC 模块
// mainObj.init().catch((error) => {
//   console.error('POC 预加载失败:', error)
// })

new Vue({
  store,
  router,
  render: (h) => h(App),
}).$mount('#app')
