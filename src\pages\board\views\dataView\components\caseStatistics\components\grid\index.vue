<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" :inline="true">
      <el-form-item label="年份">
        <el-date-picker
          v-model="yearDate"
          size="small"
          style="width: 240px;"
          type="year"
          placeholder="选择年份"
        />
      </el-form-item>
      <!-- <el-form-item label="网格小组">
        <el-select v-model="queryParams.deptIds" placeholder="请选择网格小组" clearable size="small">
          <el-option
            v-for="(deptItem, deptIndex) in deptOptions"
            :key="deptIndex"
            :label="deptItem.deptName"
            :value="deptItem.deptId"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="getList">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <right-toolbar :show-search.sync="showSearch" @queryTable="getList" /> -->
    </el-row>

    <el-table ref="table" v-loading="loading" :data="listData" height="calc(100vh - 435px)" show-summary>
      <el-table-column label="网格小组" align="center" prop="deptName" width="100" />
      <el-table-column v-for="item in tableTitle" :key="item.id" :label="item.titleName" align="center">
        <el-table-column
          v-for="value in item.children"
          :key="value.key"
          :label="value.lableName"
          align="center"
          :prop="value.count"
        >
          <template slot-scope="scope">
            <span style="cursor: pointer; color: #0094eb;" @click="openList(value.count,scope.row)">{{ scope.row[value.count] }}</span>
          </template>
        </el-table-column>
      </el-table-column>
    </el-table>

    <!-- 列表对话框 -->
    <el-dialog :close-on-click-modal="false" append-to-body :title="title" :visible.sync="open" width="80%" @close="closeDialog">
      <!-- 综合执法-日常巡查 -->
      <xcfxrcxc v-if="dialogRefKey == 'xcfxrcxc'" ref="xcfxrcxc" :dept-id="deptId" :date-range="queryParams.dateRange" :form-disabled="formDisabled" />
      <!-- 综合执法-店铺巡查 -->
      <patrol v-if="dialogRefKey == 'patrol'" ref="patrol" :dept-id="deptId" :date-range="queryParams.dateRange" :form-disabled="formDisabled" />
      <!-- 综合执法-简易处罚 -->
      <punishment v-if="dialogRefKey == 'punishment'" ref="punishment" :dept-id="deptId" :date-range="queryParams.dateRange" :form-disabled="formDisabled" />
      <!-- 综合执法-监控抓拍 -->
      <capture v-if="dialogRefKey == 'capture'" ref="capture" :dept-id="deptId" :date-range="queryParams.dateRange" :form-disabled="formDisabled" />
      <!-- 综合执法-智能抓拍 -->
      <aicapture v-if="dialogRefKey == 'aicapture'" ref="aicapture" :dept-id="deptId" :date-range="queryParams.dateRange" :form-disabled="formDisabled" />
      <!-- 运管执法-日常巡查 -->
      <dailyCheck v-if="dialogRefKey == 'dailyCheck'" ref="dailyCheck" :dept-id="deptId" :date-range="queryParams.dateRange" :form-disabled="formDisabled" />
      <!-- 运管执法-违规处置 -->
      <illegal v-if="dialogRefKey == 'illegal'" ref="illegal" :dept-id="deptId" :date-range="queryParams.dateRange" :form-disabled="formDisabled" />
      <!-- 交警执法-电子抓拍 -->
      <dzzp v-if="dialogRefKey == 'dzzp'" ref="dzzp" :dept-id="deptId" :date-range="queryParams.dateRange" :form-disabled="formDisabled" />
      <!-- 其他-柔性执法 -->
      <violation v-if="dialogRefKey == 'violation'" ref="violation" :dept-id="deptId" :date-range="queryParams.dateRange" :form-disabled="formDisabled" />
      <!-- 其他-督查考核 -->
      <check v-if="dialogRefKey == 'check'" ref="check" :dept-id="deptId" :date-range="queryParams.dateRange" :form-disabled="formDisabled" />
    </el-dialog>
  </div>
</template>

<script>
import { lisTallTeamReport } from '@/api/case/report/grid'
// import { userList as deptList} from '@/api/system/dict/type'
import xcfxrcxc from '@/pages/case/views/report/components/xcfxrcxc'
import patrol from '@/pages/case/views/report/components/patrol'
import punishment from '@/pages/case/views/report/components/punishment'
import capture from '@/pages/case/views/report/components/capture'
import aicapture from '@/pages/case/views/report/components/aicapture'
import dailyCheck from '@/pages/case/views/report/components/dailyCheck'
import illegal from '@/pages/case/views/report/components/illegal'
import dzzp from '@/pages/case/views/report/components/dzzp'
import violation from '@/pages/case/views/report/components/violation'
import check from '@/pages/case/views/report/components/check'

export default {
  name: 'Grip',
  components: {
    xcfxrcxc,
    patrol,
    punishment,
    capture,
    aicapture,
    dailyCheck,
    illegal,
    dzzp,
    violation,
    check
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 表头
      tableTitle: [
        {
          id: 0,
          titleName: '综合执法',
          children: [
            {
              key: 0,
              lableName: '日常巡查',
              count: 'inspectionCount'
            }, {
              key: 1,
              lableName: '店铺巡查',
              count: 'shopInspectionCount'
            }, {
              key: 2,
              lableName: '简易处罚',
              count: 'punishmentCount'
            }, {
              key: 3,
              lableName: '监控抓拍',
              count: 'captureCount'
            }, {
              key: 4,
              lableName: '智能抓拍',
              count: 'autoCaptureCount'
            }
          ]
        },
        {
          id: 1,
          titleName: '运管执法',
          children: [
            {
              key: 0,
              lableName: '日常巡查',
              count: 'transportCount'
            }, {
              key: 1,
              lableName: '违规处置',
              count: 'illegalTransportCount'
            }
          ]
        },
        {
          id: 2,
          titleName: '交警执法',
          children: [
            {
              key: 0,
              lableName: '电子抓拍',
              count: 'trafficCount'
            }
          ]
        }
        // {
        //   id: 3,
        //   titleName: '其他',
        //   children: [
        //     {
        //       key: 0,
        //       lableName: '柔性执法',
        //       count: 'violationCount'
        //     }, {
        //       key: 1,
        //       lableName: '督查考核',
        //       count: 'supervisionCount'
        //     }
        //   ]
        // }
      ],
      yearDate: new Date(),
      // 查询参数
      queryParams: {
        dateRange: [],
        deptIds: '',
        deptName: ''
      },
      // deptOptions: [],
      // typeList: 1,
      listData: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      formDisabled: false,
      // 当前弹窗Key
      dialogRefKey: '',
      // 部门id
      deptId: ''

    }
  },
  updated() {
    this.$nextTick(() => {
      this.$refs['table'].doLayout()
    })
  },
  created() {
    // this.getDeptNameOptions()
    // this.getList()
  },
  methods: {
    // 获取当月范围
    getDateRange() {
      this.queryParams.dateRange = [this.parseTime(this.yearDate, '{y}-01-01 {h}:{i}:{s}'), this.parseTime(this.yearDate, '{y}-12-31 {h}:{i}:{s}')]
    },
    // 关闭弹窗,重置弹窗中的筛选条件
    closeDialog() {
      this.open = false
      this.$refs[this.dialogRefKey].resetQueryParams()
    },
    // 打开弹窗
    openList(countName, row) {
      this.deptId = row.deptId
      const flagList = {
        'inspectionCount': { ref: 'xcfxrcxc', title: `日常巡查(${row.deptName})` },
        'shopInspectionCount': { ref: 'patrol', title: `店铺巡查(${row.deptName})` },
        'punishmentCount': { ref: 'punishment', title: `简易处罚(${row.deptName})` },
        'captureCount': { ref: 'capture', title: `监控抓拍(${row.deptName})` },
        'autoCaptureCount': { ref: 'aicapture', title: `智能抓拍(${row.deptName})` },
        'transportCount': { ref: 'dailyCheck', title: `日常巡查(${row.deptName})` },
        'illegalTransportCount': { ref: 'illegal', title: `违规处置(${row.deptName})` },
        'trafficCount': { ref: 'dzzp', title: `电子抓拍(${row.deptName})` },
        'violationCount': { ref: 'violation', title: `柔性执法(${row.deptName})` },
        'supervisionCount': { ref: 'check', title: `督查考核(${row.deptName})` }
      }

      this.open = true
      this.title = flagList[countName].title
      this.dialogRefKey = flagList[countName].ref
      this.$nextTick(() => {
        this.$refs[flagList[countName].ref].getList()
      })
    },

    // 网格小组选项
    // getDeptNameOptions() {
    //   deptList({ typeList: this.typeList }).then(res => {
    //     this.deptOptions = res.data.map(item => {
    //       return {deptId: item.id, deptName: item.label}
    //     })
    //   })
    // },
    /** 查询督查积分记录列表 */
    getList() {
      this.getDateRange()
      let { dateRange, deptIds } = this.queryParams
      let params = {type: 1, status: 9}
      if (dateRange && dateRange.length) params = { ...params, searchStartTime: dateRange[0], searchEndTime: dateRange[1] }
      if (deptIds) params.deptIds = deptIds
      this.loading = true

      lisTallTeamReport(params).then(response => {
        this.listData = response.data
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        dateRange: [],
        deptName: '',
        deptIds: ''
      }
      this.yearDate = new Date()
      this.getList()
    }
  }
}
</script>
