<template>
  <div class="wrap">
    <CommonTitle text="城市运行监测"></CommonTitle>
    <CommonTitle2 text="公共设施运行检测"></CommonTitle2>
    <div class="reportBtn flex-c" @click="openDialog">
      <img src="@/assets/yxjc/report.png" class="icon" />
      <div>综合运行态势报告</div>
    </div>

    <div class="item-wrap flex-c" v-if="!show">
      <div class="itemCon" v-for="(item, i) in itemList" :key="i" @click="showDetail(i)">
        <div class="title">{{ item.title }}</div>
        <div class="num">
          {{ item.name }}
          <span class="text-yellow">{{ item.num || '-' }}</span>
          {{ item.unit }}
        </div>
        <div class="flex-b">
          <div class="rate">
            同比
            <span :class="item.tb >= 0 ? 'text-red' : 'text-green'">
              <span class="rateValue">{{ item.tb > 0 ? '+' + item.tb : item.tb }}%</span>
              <img v-if="item.tb > 0" src="@/assets/yxjc/up.png" class="icon" />
              <img v-if="item.tb < 0" src="@/assets/yxjc/down.png" class="icon" />
            </span>
          </div>
          <div class="rate">
            环比
            <span :class="item.hb >= 0 ? 'text-red' : 'text-green'">
              <span class="rateValue">{{ item.hb > 0 ? '+' + item.hb : item.hb }}%</span>
              <img v-if="item.hb > 0" src="@/assets/yxjc/up.png" class="icon" />
              <img v-if="item.hb < 0" src="@/assets/yxjc/down.png" class="icon" />
            </span>
          </div>
        </div>
      </div>
    </div>
    <div v-else>
      <div class="backBtn flex-c" @click="back">
        <div class="icon"></div>
        <div>返回</div>
      </div>
      <div id="zbxq" style="width: 100%; height: 500px"></div>
    </div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import CommonTitle2 from '@/components/CommonTitle2'
import { getPublicList, getIndexData } from '@/api/yxjc/index.js'
export default {
  name: 'index',
  components: {
    CommonTitle,
    CommonTitle2,
  },
  data() {
    return {
      show: false, //展示图表
      itemList: [
        { title: '累计用水量', name: '总用水量', num: '', unit: '亿吨', tb: 0, hb: 0 },
        { title: '累计用电量', name: '总用电量', num: '', unit: '亿吨', tb: 0, hb: 0 },
        { title: '累计用气量', name: '总用气量', num: '', unit: '亿吨', tb: 0, hb: 0 },
        { title: '累计废弃排放量', name: '总废气排放量', num: '', unit: '亿吨', tb: 0, hb: 0 },
      ],
      lineChartData: [],
    }
  },
  computed: {},
  mounted() {
    this.init()
  },
  methods: {
    init() {
      getPublicList().then((res) => {
        this.itemList = res.data.map((item) => {
          return {
            title: item.name,
            name: '总' + item.name.substring(2),
            num: item.value,
            unit: item.unit,
            tb: item.tongRate,
            hb: item.huanRate,
          }
        })
        //调换用气与用电位置
        let a = this.itemList[1]
        let b = this.itemList[2]
        this.itemList[1] = b
        this.itemList[2] = a
      })
    },
    showDetail(i) {
      let type = ''
      let id = ''
      if (i == 0) {
        type = '/zbzx_yslzxt'
      } else if (i == 1) {
        type = '/zbzx_ydlzxt'
      } else if (i == 2) {
        type = '/zbzx_yqlzxt'
      } else {
        type = '/zbzx_rfqpqlzxt'
      }
      console.log(type)
      getIndexData({ indexid: type, id: id }).then((res) => {
        this.lineChartData = res.data.map((item) => {
          return {
            name: item.date_time1,
            value: item.current_value,
            unit: item.unit,
          }
        })
        this.show = true
        this.$nextTick(() => {
          this.initCharts(this.lineChartData)
        })
      })
    },
    back() {
      this.show = false
    },
    openDialog() {
      this.$emit('openDialog')
    },
    initCharts(data) {
      const chartDom = document.getElementById('zbxq')
      const myChart = this.$echarts.init(chartDom)

      const option = {
        color: ['#00ffff'],
        grid: {
          left: '7%',
          right: '7%',
          bottom: '5%',
          top: '20%',
          containLabel: true,
        },
        tooltip: {
          trigger: 'axis',
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: data.map((item) => item.name),
          axisLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.6)',
            },
          },
          axisLabel: {
            textStyle: {
              fontSize: 24,
              padding: 10,
            },
          },
        },
        yAxis: {
          name: data[0].unit,
          nameTextStyle: {
            color: 'rgba(255,255,255,0.6)',
            padding: [20, 10, 10, 10],
            fontSize: 24,
          },
          type: 'value',
          axisLabel: {
            textStyle: {
              fontSize: 24,
            },
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.6)',
            },
          },
          splitLine: {
            lineStyle: {
              color: '#4A6C89',
            },
          },
        },
        series: [
          {
            data: data.map((item) => item.value),
            type: 'line',
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(62, 215, 253, 0.30)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(62, 215, 253, 0)',
                  },
                ],
              },
            },
            lineStyle: {
              color: '#00ffff',
            },
            smooth: true,
            symbol: 'none',
          },
        ],
      }

      myChart.setOption(option)

      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
.wrap {
  display: relative;
  height: 710px;
  .reportBtn {
    position: absolute;
    top: 34px;
    right: 20px;
    // width: 284px;
    padding: 0 8px 0 14px;
    box-sizing: border-box;
    height: 60px;
    border-radius: 10px;
    background: linear-gradient(180deg, #17aee0 0%, #17aee0 0%, #0c8ac3 50%, #0166a6 100%);
    font-size: 28px;
    color: #ffffff;
    line-height: 32px;
    cursor: pointer;
    z-index: 10;
    .icon {
      width: 28px;
      height: 28px;
      margin-right: 10px;
    }
  }
  .backBtn {
    position: absolute;
    right: 20px;
    top: 120px;
    width: 170px;
    height: 60px;
    color: #ffffff;
    font-size: 32px;
    background-color: #2d3a5d;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    .icon {
      width: 36px;
      height: 33px;
      background: url('@/assets/yxjc/back.png') no-repeat;
      background-size: 240% 250%;
      background-position: -25px -15px;
      margin-right: 18px;
      // box-sizing: border-box;
    }
  }
}
.item-wrap {
  flex-wrap: wrap;
  align-items: flex-start;
  padding: 0 20px 0 40px;
  box-sizing: border-box;
  width: 100%;
  .itemCon {
    width: calc((100% - 30px) / 2);
    margin-right: 30px;
    margin-top: 30px;
    padding: 20px 30px 30px 30px;
    box-sizing: border-box;
    height: fit-content;
    background: url('@/assets/yxjc/itemBg.png') no-repeat;
    background-size: 100% 100%;
    cursor: pointer;
    &:nth-child(2n + 2) {
      margin-right: 0;
    }
    &:hover {
      background: url('@/assets/yxjc/itemBg_active.png') no-repeat;
      background-size: 100% 100%;
    }
    .title {
      font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
      font-weight: 400;
      font-size: 40px;
      line-height: 52px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      background: linear-gradient(180deg, #0ec5ec 0%, #effcfe 69%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .num {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 24px;
      line-height: 100px;
      color: #ffffff;
      text-align: center;
    }
    .text-yellow {
      font-family: YouSheBiaoTiHei;
      font-weight: 600;
      font-size: 40px;
      vertical-align: bottom;
      background: linear-gradient(90deg, #ffffff 32%, #f7b23b 100%);
      background: linear-gradient(0deg, #ffffff 32%, #f7b23b 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .rate {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 24px;
      color: #ffffff;
      .rateValue {
        margin: 0 8px;
      }
      .text-red {
        color: #fd364f;
      }
      .text-green {
        color: #22e097;
      }
    }
  }
}

.flex-c {
  display: flex;
  align-items: center;
}
.flex-b {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>