<template>
  <div class='pageContainer'>
    <animated-transition
      enter="fadeInLeft"
      leave="fadeOutLeft"
    >
      <left v-show="showPage"></left>
    </animated-transition>
    <animated-transition
      enter="fadeInRight"
      leave="fadeOutRight"
    >
      <right v-show="showPage"></right>
    </animated-transition>
    <animated-transition
      enter="fadeInUp"
      leave="fadeOutDown"
    >
      <bottom v-show="showPage"></bottom>
    </animated-transition>
  </div>
</template>

<script>
import left from '@/pages/ajhf/left'
import right from '@/pages/ajhf/right'
import bottom from '@/pages/ajhf/bottom'
import AnimatedTransition from '@/components/AnimatedTransition'
export default {
  name: 'index',
  components: {
    left,
    right,
    bottom,
    AnimatedTransition
  },
  data() {
    return {
      showPage: false
    }
  },
  computed: {},
  mounted() {
    this.$bus.$on("showPage", res => {
      this.showPage = res
    })
    setTimeout(() => {
      this.showPage = !this.showPage
    }, 100)
  },
  methods: {},
  watch: {}
}
</script>

<style scoped>

</style>