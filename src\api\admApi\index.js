import { request,requestAdm } from '@/utils/request'

//城市大脑一局一屏行政执法局检查掌上执法信息明细
export function getAdmActuator(data) {
  return requestAdm({
    url: `/mis/irs/actuator`,
    method: 'post',
    data
  })
}

//办理
export function CommandXzzfj(data) {
  return requestAdm({
    url: `/pub/provincial/CommandXzzfj/closeCase`,
    method: 'post',
    data
  })
}

//用户列表
export function getUserList(params) {
  return request({
    url: `/system/user/listJsc`,
    method: 'get',
    params
  })
}

//指令下达
export function xzzfjWorkNotification(data) {
  return request({
    url: `/pub/dingtalk/xzzfjWorkNotification`,
    method: 'post',
    data
  })
}
