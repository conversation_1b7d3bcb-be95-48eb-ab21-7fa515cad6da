<?xml version="1.0" encoding="UTF-8"?>
<svg width="100px" height="124px" viewBox="0 0 100 124" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 14</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#00FFFF" offset="0%"></stop>
            <stop stop-color="#00FFFF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#32455E" offset="0%"></stop>
            <stop stop-color="#203145" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="50%" x2="100%" y2="50%" id="linearGradient-3">
            <stop stop-color="#00FFFF" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#00FFFF" offset="51.9558566%"></stop>
            <stop stop-color="#00FFFF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <rect id="path-4" x="0" y="0" width="100" height="28" rx="14"></rect>
        <filter x="-4.0%" y="-14.3%" width="108.0%" height="128.6%" filterUnits="objectBoundingBox" id="filter-5">
            <feGaussianBlur stdDeviation="4" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 1   0 0 0 0 1  0 0 0 0.5 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="编组-13" transform="translate(0.000000, 48.000000)">
            <rect id="矩形" fill="url(#linearGradient-1)" x="49.5" y="30" width="1" height="41"></rect>
            <g id="编组-6">
                <polygon id="三角形" fill="#00FFFF" transform="translate(50.000000, 28.000000) scale(1, -1) translate(-50.000000, -28.000000) " points="50 24 56 32 44 32"></polygon>
                <g id="矩形">
                    <use fill="url(#linearGradient-2)" fill-rule="evenodd" xlink:href="#path-4"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-5)" xlink:href="#path-4"></use>
                    <rect stroke="url(#linearGradient-3)" stroke-width="1" stroke-linejoin="square" x="0.5" y="0.5" width="99" height="27" rx="13.5"></rect>
                </g>
                <circle id="椭圆形" fill="#00FFFF" opacity="0.200000003" cx="50" cy="72" r="4"></circle>
                <circle id="椭圆形" fill="#00FFFF" cx="50" cy="72" r="2"></circle>
            </g>
        </g>
    </g>
</svg>