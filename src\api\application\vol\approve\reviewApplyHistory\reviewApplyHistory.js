import {request} from '@/utils/request'

// 查询审批记录列表
export function listApprove(query) {
  return request({
    url: '/business/approve/list',
    method: 'get',
    params: query
  })
}

// 查询审批记录详细
export function getApprove(id) {
  return request({
    url: '/business/approve/' + id,
    method: 'get'
  })
}

// 新增审批记录
export function addApprove(data) {
  return request({
    url: '/business/approve/add',
    method: 'post',
    data: data
  })
}

// 修改审批记录
export function updateApprove(data) {
  return request({
    url: '/business/approve/edit',
    method: 'post',
    data: data
  })
}

// 删除审批记录
export function delApprove(id) {
  return request({
    url: '/business/approve/remove/' + id,
    method: 'post'
  })
}

// 导出审批记录
export function exportApprove(query) {
  return request({
    url: '/business/approve/export',
    method: 'get',
    params: query
  })
}
