import {request} from '@/utils/request'
// 获取所有经纬度信息
export function listAll(data) {
  return request({
    url: `/business/screen/listAll`,
    method: 'get',
    data
  })
}

// 获取商铺信息
export function getShopInfo(data) {
  return request({
    url: '/business/shop/info',
    method: 'post',
    data
  })
}

// 获取人员信息
export function getRecorderInfo(id) {
  return request({
    url: `/business/recorder/${id}`,
    method: 'get'
  })
}

// 获取人员GPS轨迹信息
export function getRecorderGps(params) {
  return request({
    url: '/business/recorder/gps/list',
    method: 'get',
    params
  })
}

// 获取志愿者信息
export function getTaskInfo(id) {
  return request({
    url: `/business/vol/task/${id}`,
    method: 'get'
  })
}

// 获取出租车信息
export function getTaxiInfo(data) {
  return request({
    url: '/business/car/taxi/getInfo',
    method: 'post',
    data
  })
}

// 获取出租车信息
export function getTaxiGps(params) {
  return request({
    url: '/business/carTaxi/gps/list',
    method: 'get',
    params
  })
}

// 获取执法车信息
export function getLawInfo(id) {
  return request({
    url: `/business/car/law/${id}`,
    method: 'get'
  })
}

// 获取执法车轨迹信息
export function getLawGps(params) {
  return request({
    url: '/business/carLaw/gps/list',
    method: 'get',
    params
  })
}

// 获取监控信息
export function getCameraInfo(id) {
  return request({
    url: `/business/camera/${id}`,
    method: 'get'
  })
}

// 案卷分布/热力图
export function listAllCase(params) {
  return request({
    url: `/business/screen/listAllCase`,
    method: 'get',
    params
  })
}

// 获取文件
export function getFiles(params) {
  return request({
    url: '/system/file/list',
    method: 'get',
    params
  })
}
