import {request} from '@/utils/request'

// 查询督查考核标准列表
export function listStandard(query) {
  return request({
    url: '/business/supervision/standard/list',
    method: 'get',
    params: query
  })
}

// 查询督查考核标准详细
export function getStandard(checkStandardId) {
  return request({
    url: '/business/supervision/standard/' + checkStandardId,
    method: 'get'
  })
}

// 新增督查考核标准
export function addStandard(data) {
  return request({
    url: '/business/supervision/standard/add',
    method: 'post',
    data: data
  })
}

// 修改督查考核标准
export function updateStandard(data) {
  return request({
    url: '/business/supervision/standard/edit',
    method: 'post',
    data: data
  })
}

// 删除督查考核标准
export function delStandard(checkStandardId) {
  return request({
    url: '/business/supervision/standard/remove/' + checkStandardId,
    method: 'post'
  })
}

// 导出督查考核标准
export function exportStandard(query) {
  return request({
    url: '/business/supervision/standard/export',
    method: 'get',
    params: query
  })
}

// 查询列表（排除节点）
export function listExcludeChild(checkStandardId) {
  return request({
    url: '/business/supervision/standard/exclude/' + checkStandardId,
    method: 'get'
  })
}
