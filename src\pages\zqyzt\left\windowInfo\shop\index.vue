<template>
  <div ref="volwindow" v-loading="loading" class="window-info">
    <div class="top">
      <el-image class="img" :src="fImgSrc" fit="cover" :preview-src-list="fSrcList">
        <div slot="placeholder" class="image-slot">
          <span class="dot">加载中...</span>
        </div>
      </el-image>
      <div class="content">
        <div class="w-title">{{ dataDetail.shopName }}</div>
        <p class="fulb">法人：{{ dataDetail.contactsName }}</p>
        <p class="dh">电话：{{ dataDetail.contactsTelephone }}</p>
        <p class="hdrq">地址：{{ dataDetail.address }}</p>
      </div>
    </div>
    <!-- 案件列表 -->
    <div class="center">
      <div class="content">
        <el-table :data="dataDetail.caseList || []" :height="300" header-cell-class-name="t-header-cell" style="width: 100%;">
          <el-table-column show-overflow-tooltip prop="inspectionName" label="名称" />
          <el-table-column show-overflow-tooltip prop="userName" label="当事人" width="80" />
          <el-table-column show-overflow-tooltip prop="happenDate" label="发生时间" />
          <el-table-column show-overflow-tooltip prop="policeType" label="案件类型" width="100" />
          <el-table-column show-overflow-tooltip prop="policeCategory" label="案件类别" />
          <el-table-column show-overflow-tooltip prop="policeContent" label="案件内容" />
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { getShopInfo } from '@/api/board/map/index'
import { getFiles } from '@/api/supervise/swit'

export default {
  data() {
    return {
      loading: false,
      dataDetail: {},
      windowInfo: null,
      fImgSrc: '',
      fSrcList: []
    }
  },
  computed: {
    scrollWrapper() {
      return this.$refs.scrollContainer.$refs.wrap
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.winResize)
  },
  methods: {
    handleScroll(e) {
      const eventDelta = e.wheelDelta || -e.deltaY * 40
      const $scrollWrapper = this.scrollWrapper
      $scrollWrapper.scrollLeft = $scrollWrapper.scrollLeft - eventDelta / 2
    },
    windowDataInit(id, windowInfo) {
      this.windowUpdate(windowInfo)
      this.loading = true
      getShopInfo({ shopId: id }).then(res => {
        this.dataDetail = res.data
        this.loading = false
        return getFiles({businessId: res.data.shopId, tableName: 'case_shop'})
      }).then(fileRes => {
        console.log(fileRes)
        if (Array.isArray(fileRes.rows)) {
          if (fileRes.rows[3]) {
            this.fImgSrc = `/zqzfj${fileRes.rows[3].filePath}`
          } else if (fileRes.rows[0]) {
            this.fImgSrc = `/zqzfj${fileRes.rows[0].filePath}`
          } else {
            this.fImgSrc = null
          }
          this.fSrcList = fileRes.rows.map(item => `/zqzfj${item.filePath}`)
        } else {
          this.fImgSrc = null
          this.fSrcList = []
        }
      }).catch(() => {
        this.loading = false
      })
    },
    winResize() {
      setTimeout(() => {
        const winWidth = this.$refs.volwindow.clientWidth
        this.windowInfo.uT.style = `width: ${winWidth}px`
      }, 300)
    },
    windowUpdate(windowInfo) {
      if (!this.windowInfo) {
        this.windowInfo = windowInfo
        window.addEventListener('resize', this.winResize)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.window-info {
  display: none;
  width: pxtorem(805);
  background: #fff;
  font-size: pxtorem(14);
  ::v-deep svg {
    position: static;
  }
  ::v-deep .image-slot {
    height: pxtorem(170);
    display: flex;
    justify-content: center;
    align-items: center;
  }
  p {
    padding: 0;
    margin: 0;
  }
  .top {
    display: flex;
    justify-content: space-between;
    .w-title {
      font-size: pxtorem(20);
      font-weight: 700;
    }
    .img {
      width: pxtorem(170);
      height: pxtorem(170);
      margin-right: 20px;
    }
    .content {
      flex: 1;
      p {
        padding-left: pxtorem(25);
        line-height: pxtorem(29);
      }
      .fulb {
        background: url(../../../../../../assets/images/windowInfo/fulb.png) no-repeat left center;
      }
      .fzr {
        background: url(../../../../../../assets/images/windowInfo/fzr.png) no-repeat left center;
      }
      .dh {
        background: url(../../../../../../assets/images/windowInfo/dh.png) no-repeat left center;
      }
      .hdrq {
        background: url(../../../../../../assets/images/windowInfo/hdrq.png) no-repeat left center;
      }
      .zmrs {
        background: url(../../../../../../assets/images/windowInfo/zmrs.png) no-repeat left center;
      }
    }
  }
  .center {
    margin-top: 20px;
    .title {
      background: #daf0fb;
      line-height: pxtorem(35);
      font-size: pxtorem(16);
      font-weight: 700;
      padding-left: 15px;
    }
    .content {
      ::v-deep .t-header-cell {
        background: #daf0fb;
        color: #333;
        font-size: pxtorem(14);
      }
      ::v-deep .gutter {
        background: #daf0fb;
      }
    }
  }
  .bottom {
    margin-top: 20px;
    .scrollbar-box {
      width: pxtorem(605);
    }
    .img-list {
      display: flex;
      flex-wrap: nowrap;
    }
    .img {
      width: pxtorem(170);
      height: pxtorem(170);
      margin-right: 10px;
      flex-shrink: 0;
    }
  }
}
</style>
