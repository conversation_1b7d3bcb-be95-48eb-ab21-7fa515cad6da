<template>
  <div>
    <el-form ref="form" v-loading="loading" :model="form" :disabled="form.status == 9" label-width="100px">
      <el-row>
        <!-- <el-col :span="8">
          <el-tooltip class="item" effect="dark" :content="form.title" placement="top">
            <el-form-item label="标题" prop="title">
              <el-input v-model="form.title" placeholder="请输入标题" />
            </el-form-item>
          </el-tooltip>
        </el-col> -->
        <h3 class="title" style="position: relative;">
          <span>基本信息</span>
          <attentionBtn :case-id="form.toutId || 0" case-type="tout" :case-content="form.content" />
        </h3>
        <el-col :span="12">
          <el-form-item label="违法人姓名" prop="lawbreakers">
            <el-input v-model="form.lawbreakers" placeholder="请输入违法人姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-tooltip :disabled="!form.address" class="item" effect="dark" :content="form.address" placement="top">
            <el-form-item label="案件地址" prop="address">
              <div>
                <el-input v-model="form.address" placeholder="请选择案件地址">
                  <svg-icon slot="suffix" icon-class="map" class="svg-icon" @click="openMap = true" />
                </el-input>
              </div>
            </el-form-item>
          </el-tooltip>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-tooltip :disabled="!form.happenDate" class="item" effect="dark" :content="form.happenDate" placement="top">
            <el-form-item label="发生时间" prop="happenDate">
              <div class="block">
                <el-date-picker
                  v-model="form.happenDate"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  type="datetime"
                  placeholder="选择日期时间"
                  default-time="12:00:00"
                />
              </div>
            </el-form-item>
          </el-tooltip>
        </el-col>
        <!-- <el-col :span="8">
          <el-tooltip :disabled="!form.handleDate" class="item" effect="dark" :content="form.handleDate" placement="top">
            <el-form-item label="处理时间" prop="handleDate">
              <div class="block">
                <el-date-picker
                  v-model="form.handleDate"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  type="datetime"
                  placeholder="选择日期时间"
                  default-time="12:00:00"
                />
              </div>
            </el-form-item>
          </el-tooltip>
        </el-col> -->
        <el-col :span="12">
          <el-tooltip :disabled="!form.completeDate" class="item" effect="dark" :content="form.completeDate" placement="top">
            <el-form-item label="完成时间" prop="completeDate">
              <div class="block">
                <el-date-picker
                  v-model="form.completeDate"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  type="datetime"
                  placeholder="选择日期时间"
                  default-time="12:00:00"
                />
              </div>
            </el-form-item>
          </el-tooltip>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="phone">
            <el-input v-model="form.phone" :maxlength="11" oninput="value=value.replace(/[^\d]/g,'')" placeholder="请输入联系电话" />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="12">
          <el-form-item label="身份证" prop="identityCard">
            <el-input v-model="form.identityCard" placeholder="请输入身份证" />
          </el-form-item>
        </el-col> -->
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="案件内容" prop="content">
            <el-input
              v-model="form.content"
              type="textarea"
              placeholder="请输入案件内容"
              maxlength="150"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="上传图片" prop="files">
            <el-upload
              ref="upload"
              multiple
              :limit="4"
              list-type="picture-card"
              class="upload-demo"
              accept=".jpg,.jpeg,.png,.JPG,.JPEG,.PNG"
              action="/zqzfj/system/file/upload"
              :headers="headers"
              :on-preview="handlePictureCardPreview"
              :on-remove="handleRemove"
              :on-error="handleError"
              :on-success="handleSuccess"
              :data="formData"
              :file-list="formFiles"
              :auto-upload="false"
              :before-remove="beforeRemove"
              name="files"
              :on-exceed="exceed"
            >
              <i class="el-icon-plus" />
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <div v-if="openMap" class="map">
        <div class="">
          <tdtMap ref="bindmap" :map-search="true" :styles="{width:'900px',height:'90vh'}" :dw="form" @onlnglat="onlnglat" />
        </div>
      </div> -->
      <tdtMap ref="bindmap" :visible.sync="openMap" append-to-body :map-search="true" :dw="form" @onlnglat="onlnglat" />
    </el-form>
    <!-- 图片预览 -->
    <el-dialog :close-on-click-modal="false" :visible.sync="dialogVisible" append-to-body>
      <img width="100%" :src="dialogImageUrl">
    </el-dialog>
    <div slot="footer" class="dialog-footer">
      <AddClassic :before-processing="form.content" after-processing="已处置" />
      <el-button type="success" :loading="!fromQd" :disabled="!clickQd" @click="primary(9)">办 结</el-button>
      <el-button type="primary" :loading="!fromQd" :disabled="!clickQd" @click="primary(2)">确 定</el-button>
      <el-button @click="cancel">取 消 </el-button>
    </div>
  </div>
</template>
<script>
import tdtMap from '@/components/tdtMap/tdtMap'
import { getToken } from '@/utils/auth'
import { getFiles, removeFiles} from '@/api/supervise/swit'
import toutCase from '@/api/case/tout/hncz'
import attentionBtn from '@/components/attentioneBtn/index.vue'
import AddClassic from '@/components/AddClassic/index.vue'

export default {
  name: 'Fromlist',
  components: {
    tdtMap,
    attentionBtn,
    AddClassic
  },
  props: {
    getForm: {
      type: Object,
      default() {
        return null
      }
    },
    detailId: {
      type: Number,
      default: 0
    },
    qd: {
      type: Boolean,
      default() {
        return true
      }
    }
  },
  data() {
    return {
      headers: {Authorization: 'Bearer ' + getToken()},
      loading: false,
      formData: {businessId: null, tableName: 'case_tout'},
      form: {files: []},
      fileList: [],
      openMap: false,
      disabled: false,
      formFiles: [], // 文件
      fromQd: this.qd,
      dialogImageUrl: '',
      dialogVisible: false,
      clickQd: true,
      // rules: {
      //   lawbreakers: [
      //     { required: true, message: '请输入违法人姓名', trigger: 'blur' }
      //   ],
      //   title: [
      //     { required: true, message: '请输入标题', trigger: 'blur' }
      //   ],
      //   content: [
      //     { required: true, message: '请输入处置内容', trigger: 'blur' }
      //   ],
      //   identityCard: [
      //     { required: true, message: '请输入身份证号', trigger: 'blur' },
      //     {pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '请输入正确的身份证号'}
      //   ],
      //   phone: [
      //     {required: true, message: '号码不能为空', trigger: 'blur'},
      //     {pattern: /^[1][3,4,5,7,8,9][0-9]{9}$/, message: '请输入正确的手机号'}
      //   ],
      //   happenDate: [
      //     {  required: true, message: '请选择发生时间', trigger: 'change' }
      //   ],
      //   handleDate: [
      //     {  required: true, message: '请选择处理时间', trigger: 'change' }
      //   ],
      //   completeDate: [
      //     {  required: true, message: '请选择完成时间', trigger: 'change' }
      //   ],
      //   address: [
      //     { required: true, message: '请选择地址', trigger: 'change' }
      //   ]
      // },
      open: ''
    }
  },
  created() {
    if (this.detailId) {
      this.loading = true
      toutCase.getDetail(this.detailId).then(res => {
        this.form = {...res.data}
        let {toutId, status} = this.form
        if (status == 9) this.clickQd = false
        let params = {businessId: toutId, tableName: 'case_tout'}
        this.getFile(params)
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    }
  },
  methods: {
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    getFile(params) {
      getFiles(params).then(res => {
        let filter = res.rows.reduce((arr, v) => {
          let url = {name: v.displayName, url: `/zqzfj${v.filePath}?id=${v.fileId}`, ...v}
          arr.push(url)
          return arr
        }, [])
        this.formFiles = filter
      })
    },
    onlnglat(lenlat) {      // 经纬度  详细地址
      let {lng: longitude, lat: latitude, address} = lenlat
      this.form = {...this.form, longitude, latitude, address}
      this.openMap = false
    },
    // 上传函数
    beforeRemove(file) {
      return new Promise((resolve, reject) => {
        this.$confirm('是否确认删除？ 删除后将无法恢复', '提示', { type: 'warning' }).then(() => {
          console.log(file)
          if (file.fileId) {
            removeFiles(file.fileId).then(resolve).catch(reject)
          } else {
            resolve()
          }
        }).catch(() => {
          reject()
        })
      })
    },
    handleRemove(files) {   // 删除图片
      console.log(files, this.formFiles)

    },
    exceed(files, filesList) {
      if (filesList.length >= 4) this.msgError('已超出最大上传数量')
    },
    handleSuccess(files) {    // 上传成功
      if (files.code == 200) {
        this.msgSuccess('上传成功')
      } else {
        this.msgSuccess('上传失败,请修改后重试')
      }
    },
    handleError(err, file, fileList) { // 上传失败
      console.log(err, file, fileList)
      this.msgError('上传失败,请修改后重试')
    },
    // 上传
    upload(data) {
      console.log(data)
      this.formData.businessId = data
      this.$refs.upload.submit()
    },
    // 确定
    primary(status) {
      this.form.status = status
      let params = {...this.form}
      if (status == 2) return this.$emit('onPrimary', params)
      // this.$refs['form'].validate(valid => {
      // if (valid) {
      // if (this.$refs.upload.uploadFiles.length == 0) return this.msgError('请上传图片')
      if (!this.fromQd) return
      this.$emit('onPrimary', params)
      this.openMap = false
      // } else {
      //   this.form.status = ''
      //   return false
      // }
      // })
    },
    // 取消
    cancel() {
      this.openMap = false
      this.form = {}
      this.$emit('oncancel')
    }
    // 表单验证
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  margin-top: 20px;
  text-align: right;
}
.el-upload__tip {
  display: inline-block;
  margin-left: 5%;
}
.mapTable {
  height: 300px;
}
::v-deep .el-upload--picture-card,
::v-deep .el-upload-list--picture-card .el-upload-list__item {
  width: 100px;
  height: 100px;
  line-height: 100px;
}
.map {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10000;
  padding: 5vh 18px 5vh 0;
  background-color: rgba(0, 0, 0, 0.6);
}
.title {
  display: block;
  padding: 10px 0 10px 30px;
  margin-bottom: 20px;
  border-bottom: 1px solid #e4e7ec;
  color: #000;
}
.svg-icon {
  font-size: 25px;
  margin-left: 2px;
}
</style>
