<template>
  <div class="bottom-menu-box">
    <div class="bottom-menu rMenu" :class="{ 'opne': open }">
      <ul v-if="open">
        <li :class="allCheck ? 'menu-1 checked': 'menu-1'" @click="handleAllCheck">全部</li>
        <li v-for="idx in left" :key="idx" clas :class="menuList[idx].checked ? `${menuList[idx].className} checked` : menuList[idx].className" @click="handleClick(menuList[idx])">{{ menuList[idx].name }}({{ overLayNum[menuList[idx].key] }})</li>
      </ul>
      <div class="title" @click="open = !open" @dblclick="isRight = !isRight">
        <p class="c-title">指挥调度</p>
        <p class="e-title">Scheduling command</p>
      </div>
      <ul v-if="open">
        <li v-for="idx in right" :key="idx" clas :class="menuList[idx].checked ? `${menuList[idx].className} checked` : menuList[idx].className" @click="handleClick(menuList[idx])">{{ menuList[idx].name }}({{ overLayNum[menuList[idx].key] }})</li>
      </ul>
    </div>
    <div class="bottom-event-menu rMenu" :class="{ 'opne': eventOpen }">
      <ul v-if="eventOpen">
        <li :class="allEventCheck ? 'menu-event-all checked': 'menu-event-all'" @click="handleAllEventCheck">全部</li>
        <li v-for="idx in eventLeft" :key="idx" clas :class="eventMenuList[idx].checked ? `${eventMenuList[idx].className} checked` : eventMenuList[idx].className" @click="handleClick(eventMenuList[idx])">{{ eventMenuList[idx].name }}({{ overLayNum[eventMenuList[idx].key] }})</li>
      </ul>
      <div class="title" @click="eventOpen = !eventOpen" @dblclick="isRight = !isRight">
        <p class="c-title">事件列表</p>
        <p class="e-title">event List</p>
      </div>
      <ul v-if="eventOpen">
        <li v-for="idx in eventRight" :key="idx" clas :class="eventMenuList[idx].checked ? `${eventMenuList[idx].className} checked` : eventMenuList[idx].className" @click="handleClick(eventMenuList[idx])">{{ eventMenuList[idx].name }}({{ overLayNum[eventMenuList[idx].key] }})</li>
      </ul>
    </div>
  </div>
</template>

<script>
import { changeOverlays } from '../../map/setMarker'
import { mapMutations } from 'vuex'

export default {
  data() {
    return {
      isRight: true,
      open: true,
      eventOpen: false,
      allCheck: true,
      left: [0, 1, 2],
      right: [3, 4, 5, 6],
      eventLeft: [0, 1, 2],
      eventRight: [3, 4, 5, 6],
      allEventCheck: true,
      menuList: [
        { key: 'monitor', className: 'menu-2', name: '监控', checked: true },
        { key: 'lawCar', className: 'menu-3', name: '执法车', checked: true },
        { key: 'taxi', className: 'menu-11', name: '出租车', checked: true },
        { key: 'recorder', className: 'menu-4', name: '执法人员', checked: true },
        { key: 'shop', className: 'menu-12', name: '店铺', checked: true },
        { key: 'vol', className: 'menu-10', name: '志愿者', checked: true },
        // { key: 'event', className: 'menu-6', name: '事件', checked: true },
        // { key: 'tout', className: 'menu-13', name: '黄牛热点', checked: true },
        { key: 'area', className: 'menu-8', name: '行政区划', checked: true }
      ],
      eventMenuList: [
        { key: 'four', className: 'menu-event-1', name: '四位一体', checked: true },
        { key: 'capture', className: 'menu-event-2', name: '监控抓拍', checked: true },
        { key: 'inspection', className: 'menu-event-3', name: '巡查发现', checked: true },
        { key: 'punish', className: 'menu-event-4', name: '简易案件', checked: true },
        { key: 'tout', className: 'menu-event-5', name: '黄牛处置', checked: true },
        { key: 'autoCapture', className: 'menu-event-6', name: '智能抓拍', checked: true },
        { key: 'transport', className: 'menu-event-7', name: '违规处置', checked: true }
      ]
    }
  },
  computed: {
    overLayNum() {
      return this.$store.state.board.overLayNum
    }
  },
  watch: {
    open(flag) {
      if (flag) this.eventOpen = false
    },
    eventOpen(flag) {
      if (flag) this.open = false
    }
  },
  methods: {
    ...mapMutations('board', ['SET_OVERLAY_STATUS']),
    setOverlayStatus() {
      let statusObj = {}
      this.menuList.forEach(item => {
        statusObj[item.key] = item.checked
      })
      this.SET_OVERLAY_STATUS(statusObj)
    },
    handleClick(menu) {
      menu.checked = !menu.checked
      const nMenu = this.menuList.filter(item => item.checked != menu.checked)
      if (nMenu.length) {
        this.allCheck = false
      } else {
        this.allCheck = menu.checked
      }
      this.setOverlayStatus()
      changeOverlays([menu.key], this.$store.getters.$map, menu.checked)
    },
    handleAllCheck() {
      let keys = []
      this.allCheck = !this.allCheck
      this.menuList = this.menuList.map(item => {
        if (item.checked != this.allCheck) {
          keys.push(item.key)
        }
        item.checked = this.allCheck
        return item
      })
      this.setOverlayStatus()
      changeOverlays(keys, this.$store.getters.$map, this.allCheck)
    },
    handleAllEventCheck() {
      let keys = []
      this.allEventCheck = !this.allEventCheck
      this.eventMenuList = this.eventMenuList.map(item => {
        if (item.checked != this.allEventCheck) {
          keys.push(item.key)
        }
        item.checked = this.allEventCheck
        return item
      })
      this.setOverlayStatus()
      changeOverlays(keys, this.$store.getters.$map, this.allEventCheck)
    }
  }
}
</script>

<style scoped lang="scss">
.bottom-menu-box {
  position: absolute;
  top: pxtorem(180);
  right: pxtorem(30);
  z-index: 1000;
}
.bottom-menu,
.bottom-event-menu {
  // width: pxtorem(210);
  height: pxtorem(108);
  background: #fff;
  border-radius: 10px;
  // position: absolute;
  // bottom: pxtorem(30);
  // left: 50%;
  // margin-left: pxtorem(-105);
  z-index: 500;
  display: flex;
  justify-content: space-around;
  padding: pxtorem(14) pxtorem(20);
  overflow: hidden;
  transition: all 0.3s;
  box-shadow: 0 4px 14px rgba(0, 0, 0, 0.2);
  &.opne {
    width: pxtorem(1200);
    margin-left: pxtorem(-600);
  }
  .title {
    width: pxtorem(170);
    height: pxtorem(80);
    flex-shrink: 0;
    background: url(@/assets/images/menu-btn.png) no-repeat center center / 100% 100%;
    color: #fff;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    p {
      margin: 0;
    }
    .c-title {
      font-size: pxtorem(24);
      margin-bottom: pxtorem(10);
    }
    .e-title {
      font-size: pxtorem(12);
    }
  }
  ul {
    width: pxtorem(450);
    display: flex;
    justify-content: space-between;
    margin: 0;
    padding: 0;
    li {
      list-style: none;
      width: pxtorem(90);
      height: pxtorem(80);
      display: flex;
      justify-content: center;
      align-items: flex-end;
      cursor: pointer;
      opacity: 0.3;
      font-size: pxtorem(14);
      &.menu-1 {
        background: url(@/assets/images/menu-1-s.png) no-repeat center pxtorem(5);
      }
      &.menu-2 {
        background: url(@/assets/images/menu-2-s.png) no-repeat center pxtorem(5);
      }
      &.menu-3 {
        background: url(@/assets/images/menu-3-s.png) no-repeat center pxtorem(5);
      }
      &.menu-4 {
        background: url(@/assets/images/menu-4-s.png) no-repeat center pxtorem(5);
      }
      &.menu-6 {
        background: url(@/assets/images/menu-6-s.png) no-repeat center pxtorem(5);
      }
      &.menu-8 {
        background: url(@/assets/images/menu-8-s.png) no-repeat center pxtorem(5);
      }
      &.menu-10 {
        background: url(@/assets/images/menu-10-s.png) no-repeat center pxtorem(5);
      }
      &.menu-11 {
        background: url(@/assets/images/menu-11-s.png) no-repeat center pxtorem(5);
      }
      &.menu-12 {
        background: url(@/assets/images/menu-12-s.png) no-repeat center pxtorem(5);
      }
      &.menu-13 {
        background: url(@/assets/images/menu-13-s.png) no-repeat center pxtorem(5);
      }
      &.menu-event-1 {
        background: url(@/assets/images/menu-event-1.png) no-repeat center pxtorem(5);
      }
      &.menu-event-all {
        background: url(@/assets/images/menu-event-all.png) no-repeat center pxtorem(5);
      }
      &.menu-event-2 {
        background: url(@/assets/images/menu-event-2.png) no-repeat center pxtorem(5);
      }
      &.menu-event-3 {
        background: url(@/assets/images/menu-event-3.png) no-repeat center pxtorem(5);
      }
      &.menu-event-4 {
        background: url(@/assets/images/menu-event-4.png) no-repeat center pxtorem(5);
      }
      &.menu-event-5 {
        background: url(@/assets/images/menu-event-5.png) no-repeat center pxtorem(5);
      }
      &.menu-event-6 {
        background: url(@/assets/images/menu-event-6.png) no-repeat center pxtorem(5);
      }
      &.menu-event-7 {
        background: url(@/assets/images/menu-event-7.png) no-repeat center pxtorem(5);
      }
      &.checked {
        opacity: 1;
      }

      background-size: pxtorem(40) pxtorem(40);
    }
  }
  &.rMenu {
    top: pxtorem(180);
    bottom: auto;
    right: pxtorem(30);
    left: auto;
    margin-left: auto;
    flex-wrap: wrap;
    &.opne {
      // width: pxtorem(1200);
      // margin-left: pxtorem(-600);
      width: pxtorem(210);
      height: pxtorem(480);
    }
    ul {
      width: pxtorem(85);
      order: 5;
      flex-direction: column;
    }
  }
}
.bottom-event-menu {
  margin-top: pxtorem(15);
  &.rMenu {
    top: pxtorem(300);
    bottom: auto;
    right: pxtorem(30);
    left: auto;
    margin-left: auto;
    flex-wrap: wrap;
    &.opne {
      // width: pxtorem(1200);
      // margin-left: pxtorem(-600);
      width: pxtorem(210);
      height: pxtorem(480);
    }
    ul {
      width: pxtorem(85);
      order: 5;
      flex-direction: column;
    }
  }
}
</style>
