import {request} from '@/utils/request'

// 查询代班申请记录列表
export function listRecord(query) {
  return request({
    url: '/business/substitute/record/list',
    method: 'get',
    params: query
  })
}

// 查询代班申请记录详细
export function getRecord(id) {
  return request({
    url: '/business/substitute/record/' + id,
    method: 'get'
  })
}

// 新增代班申请记录
export function addRecord(data) {
  return request({
    url: '/business/substitute/record/add',
    method: 'post',
    data: data
  })
}

// 修改代班申请记录
export function updateRecord(data) {
  return request({
    url: '/business/substitute/record/editBySupervision',
    method: 'post',
    data: data
  })
}

// 删除代班申请记录
export function delRecord(id) {
  return request({
    url: '/business/substitute/record/remove/' + id,
    method: 'post'
  })
}

// 导出代班申请记录
export function exportRecord(query) {
  return request({
    url: '/business/substitute/record/export',
    method: 'get',
    params: query
  })
}
