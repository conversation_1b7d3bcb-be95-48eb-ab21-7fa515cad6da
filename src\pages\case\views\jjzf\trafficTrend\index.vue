<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" :inline="true">
      <el-form-item label="日期范围">
        <el-date-picker
          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px;"
          value-format="yyyy-MM-dd hh:mm:ss"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="getList"
        />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <!-- 刷新工具 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="20">
        <h2 style="margin: 0;">交警执法数量趋势统计图</h2>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>
    <!-- 主体内容 -->
    <div v-loading="loading" class="container" :class="{ hideSearch: !showSearch }">
      <div ref="chart" style="width: 100%; height: 100%;" />
    </div>
  </div>
</template>

<script>
import {getTrafficTrend } from '@/api/case/jjzf/trafficTrend'

export default {
  data() {
    return {
      loading: false,
      showSearch: true,
      queryParams: {
        dateRange: [],
        type: ''
      },
      myChart: null,
      xAxisData: [],
      seriesData: []
    }
  },
  computed: {
    options() {
      return {
        tooltip: {
          trigger: 'axis',
          position: function(pt) {
            return [pt[0], '10%']
          }
        },
        grid: {
          show: false,
          top: 10,
          left: 40,
          bottom: 80,
          right: 40
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.xAxisData
        },
        yAxis: {
          type: 'value',
          boundaryGap: [0, '100%']
        },
        dataZoom: [{
          type: 'inside',
          start: 0,
          end: 100
        }, {
          start: 0,
          end: 100
        }],
        series: [
          {
            name: '发生数量',
            type: 'line',
            symbol: 'none',
            sampling: 'lttb',
            itemStyle: {
              color: 'rgb(255, 70, 131)'
            },
            areaStyle: {
              color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: 'rgb(255, 158, 68)'
              }, {
                offset: 1,
                color: 'rgb(255, 70, 131)'
              }])
            },
            data: this.seriesData
          }
        ]
      }
    }
  },
  watch: {
    /* 隐藏搜索框重置图表大小 */
    showSearch() {
      this.resizeChart()
    }
  },
  mounted() {
    this.initChart()
    this.getList()
    window.addEventListener('resize', this.resizeChart)
  },
  destroyed() {
    window.removeEventListener('resize', this.resizeChart)
  },
  methods: {
    /* 获取数据 */
    getList() {
      this.loading = true
      const { dateRange, type } = this.queryParams
      let params = {}
      if (dateRange && dateRange.length) params = { ...params, startTime: dateRange[0], endTime: dateRange[1] }
      if (type) params = { ...params, type }
      getTrafficTrend(params).then(res => {
        this.xAxisData = res.data.legendData
        this.seriesData = res.data.seriesData
        this.myChart.setOption(this.options)
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    /* 重置数据 */
    resetQuery() {
      this.queryParams = {
        dateRange: [],
        type: ''
      }
      this.getList()
    },
    /* 初始化空数据图表 */
    initChart() {
      this.myChart = this.$echarts.init(this.$refs.chart)
      this.myChart.setOption(this.options)
    },
    /* 页面大小变更，图标自适应 */
    resizeChart() {
      setTimeout(() => {
        this.myChart.resize()
      }, 500)
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  .container {
    width: 100%;
    height: calc(100vh - 225px);
    &.hideSearch {
      height: calc(100vh - 170px);
    }
  }
}
</style>
