const { defineConfig } = require('@vue/cli-service')
const path = require('path')
const resolve = (dir) => {
  return path.join(__dirname, dir)
}
const CompressionWebpackPlugin = require('compression-webpack-plugin')
const isProd = process.env.NODE_ENV === 'production'

module.exports = defineConfig({
  transpileDependencies: true,
  outputDir: process.env.NODE_ENV === 'production' ? 'dist' : 'stage',
  productionSourceMap: false, // 生产环境禁用 source map
  chainWebpack: (config) => {
    config.resolve.alias.set('@$', resolve('src'))
  },
  configureWebpack: {
    resolve: {
      alias: {
        '@': resolve('src'),
      },
    },
    optimization: {
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          elementUI: {
            name: 'chunk-elementUI',
            test: /[\\/]node_modules[\\/]_?element-ui(.*)/,
            priority: 30,
          },
          echarts: {
            name: 'chunk-echarts',
            test: /[\\/]node_modules[\\/]_?echarts(?!-gl)(.*)/,
            priority: 25,
          },
          echartsGL: {
            name: 'chunk-echarts-gl',
            test: /[\\/]node_modules[\\/]_?echarts-gl(.*)/,
            priority: 20,
          },
          lodash: {
            name: 'chunk-lodash',
            test: /[\\/]node_modules[\\/]_?lodash(.*)/,
            priority: 15,
          },
          moment: {
            name: 'chunk-moment',
            test: /[\\/]node_modules[\\/]_?moment(.*)/,
            priority: 10,
          },
          libs: {
            name: 'chunk-libs',
            test: /[\\/]node_modules[\\/]/,
            priority: 5,
            chunks: 'initial',
          },
        },
      },
    },
    plugins: isProd
      ? [
          new CompressionWebpackPlugin({
            test: /\.js$|\.html$|\.css$/u,
            threshold: 4096, // 超过 4kb 压缩
          }),
        ]
      : [],
  },
  pages: {
    index: {
      // page 的入口
      entry: 'src/main.js',
      // 模板来源
      template: 'public/index.html',
      // 在 dist/index.html 的输出
      filename: 'index.html',
      // 当使用 title 选项时，
      // template 中的 title 标签需要是 <title><%= htmlWebpackPlugin.options.title %></title>
      title: '金华市行政执法指挥中心',
      // 在这个页面中包含的块，默认情况下会包含
      // 提取出来的通用 chunk 和 vendor chunk。
      chunks: [
        'chunk-vendors',
        'chunk-elementUI',
        'chunk-echarts',
        'chunk-echarts-gl',
        'chunk-lodash',
        'chunk-moment',
        'chunk-libs',
        'index',
      ],
    },
  },
  publicPath: './',

  devServer: {
    client: {
      overlay: {
        warnings: false,
        errors: false,
      },
    },
    // headers: {
    //   'Cross-Origin-Opener-Policy': 'same-origin',
    //   'Cross-Origin-Embedder-Policy': 'require-corp',
    // },
    host: '0.0.0.0',
    port: '8080',
    proxy: {
      '/prod-api': {
        changeOrigin: true,
        secure: false,
        target: 'http://************',
        // target: "http://************:9000",
        // target: "http://*************:9000",
        // target: "http://*************:9000",
        // target: "http://************:9000",
        pathRewrite: {
          '^/prod-api': '/prod-api',
        },
        onProxyRes(proxyRes, req, res) {
          const realUrl = req.url || '' // 真实请求网址
          console.log(realUrl) // 在终端显示
          proxyRes.headers['A-Real-Url'] = realUrl // 添加响应标头(A-Real-Url为自定义命名)，在浏览器中显示
        },
      },
      '/adm-api': {
        changeOrigin: true,
        secure: false,
        target: 'https://csdn.dsjj.jinhua.gov.cn:8300',
        pathRewrite: {
          '^/adm-api': '/adm-api',
        },
        onProxyRes(proxyRes, req, res) {
          const realUrl = req.url || '' // 真实请求网址
          console.log(realUrl) // 在终端显示
          proxyRes.headers['A-Real-Url'] = realUrl // 添加响应标头(A-Real-Url为自定义命名)，在浏览器中显示
        },
      },
    },
  },
})
