import {request} from '@/utils/request'

// 查询站前动态列表
export function listNews(query) {
  return request({
    url: '/business/vol/news/list',
    method: 'get',
    params: query
  })
}

// 查询站前动态详细
export function getNews(id) {
  return request({
    url: '/business/vol/news/' + id,
    method: 'get'
  })
}

// 新增站前动态
export function addNews(data) {
  return request({
    url: '/business/vol/news/add',
    method: 'post',
    data: data
  })
}

// 修改站前动态
export function updateNews(data) {
  return request({
    url: '/business/vol/news/edit',
    method: 'post',
    data: data
  })
}

// 删除站前动态
export function delNews(id) {
  return request({
    url: '/business/vol/news/remove/' + id,
    method: 'post'
  })
}

export function editTopNews(data) {
  return request({
    url: '/business/vol/news/editTop',
    method: 'post',
    data
  })
}
