<template>
  <div>
    <CommonTitle text="服务热榜"></CommonTitle>
    <div class="wrap-container" id="barChart2"></div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import { serviceFriendly } from '@/api/gzfw/index.js'
export default {
  name: 'index',
  components: {
    CommonTitle,
  },
  data() {
    return {
      chartsData: [],
    }
  },
  computed: {},
  mounted() {
    serviceFriendly().then((res) => {
      this.chartsData = res.data.map((item) => {
        return {
          name: item.key,
          value: item.value,
        }
      })
      this.initCharts()
    })
  },
  methods: {
    initCharts() {
      let max = this.chartsData[0].value
      this.chartsData.forEach((item) => {
        if (item.value > max) {
          max = item.value
        }
      })
      let myChart = this.$echarts.init(document.getElementById('barChart2'))
      let option = {
        tooltip: {
          trigger: 'axis',
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          formatter: function (params) {
            return (
              params.find((item) => item.seriesName != '背景').axisValue +
              ': ' +
              params.find((item) => item.seriesName != '背景').value
            )
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '32',
          },
        },
        grid: {
          top: '8%',
          left: '5%',
          right: '5%',
          bottom: '-10%',
          containLabel: true,
        },
        xAxis: {
          type: 'value',
          show: false,
        },
        yAxis: {
          name: '',
          type: 'category',
          triggerEvent: false,
          inverse: true,
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
            length: 20,
          },
          axisLabel: {
            show: true,
            inside: true, // 将标签放置在坐标轴内部
            verticalAlign: 'bottom', // 垂直对齐方式
            padding: [0, 0, 20, 0], // 上右下左的内边距，调整标签位置
            textStyle: {
              color: '#FFFFFF',
              fontSize: 32,
            },
            formatter: function (value, index) {
              let num = index <= 2 ? 'Top' + (index + 1) : index + 1
              return `{a|${num}}${value}`
            },
            rich: {
              a: {
                width: 100,
                color: '#FFFFFF',
                fontSize: 32,
              },
            },
          },
          data: this.chartsData.map((item) => item.name),
        },
        series: [
          {
            type: 'bar',
            name: '',
            showBackground: true,
            backgroundStyle: {
              color: 'transparent',
            },
            itemStyle: {
              normal: {
                barBorderRadius: 30,
                color: new this.$echarts.graphic.LinearGradient(0, 0, 1, 1, [
                  {
                    offset: 0,
                    color: '#3FD9FF00',
                  },
                  {
                    offset: 1,
                    color: '#3FD9FF',
                  },
                ]),
              },
            },
            label: {
              show: true,
              position: [840, -46],
              color: '#fff',
              fontSize: 32,
              textStyle: {
                color: '#FFFFFF',
                fontWeight: 'normal',
                fontFamily: 'Source Han Sans CN',
              },
            },
            barWidth: 20,
            color: '#539FF7',
            data: this.chartsData.map((item) => item.value),
          },
          {
            name: '背景',
            type: 'bar',
            barWidth: 20,
            barGap: '-100%',
            data: this.chartsData.map((item) => max),
            itemStyle: {
              normal: {
                barBorderRadius: 30,
                color: '#026EF133',
              },
            },
            z: 0,
          },
        ],
      }
      myChart.setOption(option)
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
.wrap-container {
  width: 100%;
  height: 540px;
}
</style>