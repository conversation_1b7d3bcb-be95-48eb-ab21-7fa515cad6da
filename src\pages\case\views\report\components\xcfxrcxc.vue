<template>
  <div>
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      :inline="true"
    >
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px;"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="daterange"
          unlink-panels
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>

      <el-form-item label="关键词检索">
        <el-input v-model="queryParams.searchValue" size="small" clearable placeholder="请输入内容" />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="listData"
      :cell-style="cellStyle"
    >
      <el-table-column label="巡查人" prop="userName" align="center" :show-overflow-tooltip="true" width="100" />
      <el-table-column label="辅助人员" prop="userNames" align="center" :show-overflow-tooltip="true" />
      <el-table-column label="网格小组" prop="deptName" align="center" :show-overflow-tooltip="true" />
      <el-table-column label="巡查时间" align="center" prop="happenDate">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.happenDate) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="店铺名称" prop="businessName" :show-overflow-tooltip="true" />
      <el-table-column label="地址" prop="address" :show-overflow-tooltip="true" />
      <el-table-column label="内容" prop="inspectionContent" :show-overflow-tooltip="true" />
      <el-table-column label="状态" align="center" prop="statusName" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button v-if="scope.row.status == 9 || queryParams.data==1" size="mini" type="text" icon="el-icon-edit" style="color: #e6a23c;" @click="handleUpdate(scope.row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改角色配置对话框 -->
    <el-dialog :close-on-click-modal="false" append-to-body :title="title" :visible.sync="open" width="1000px">
      <fromlist v-if="open" ref="fromlist" :detail-id="detailId" :form-disabled="formDisabled" :user-options="userOptions" :qd="qd" @onPrimary="primary" @oncancel="cancel" />
    </el-dialog>
  </div>
</template>

<script>
import {inspectionList, editInspection} from '@/api/case/synthetical/patrol'
import {userList} from '@/api/supervise/swit'
import fromlist from '@/pages/case/views/synthetical/xcfxrcxc/components/fromlist'

export default {
  name: 'XcfxrcxcList',
  components: {
    fromlist
  },
  props: {
    // 用户id
    searchId: {
      type: [String, Number]
    },
    // 日期范围
    dateRange: {
      type: Array,
      default: () => []
    },
    // 部门id
    deptId: {
      type: [String, Number]
    }
  },
  data() {
    return {
      $map: null,
      value: [],
      detailId: '',
      userOptions: [],
      formData: [{id: 0, value: '我的'}, {id: 1, value: '全部'}],
      caseData: [ {id: 1, value: '待处理', statusColor: '#409EFF'}, {id: 2, value: '处理中', statusColor: '#E6A23C'}, {id: 9, value: '已办结', statusColor: '#bdc3bf'}],
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [],
      // 弹出层标题
      title: '新增案件',
      // 是否显示弹出层
      open: false,
      // 状态数据字典
      statusOptions: [],
      // 菜单列表
      menuOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        typeName: '',
        type: 0,
        statusName: '',
        status: undefined,
        // 日期范围
        dateRange: [],
        data: 0,
        dataName: '我的',
        deptId: '',
        deptName: '',
        searchValue: ''
      },
      // 表单参数
      form: {},
      qd: true,
      formDisabled: false,
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      listData: [],
      deptOptions: [],
      typeList: 1
    }
  },
  created() {
    if (this.$route.query.data) {
      let data = JSON.parse(this.$route.query.data)
      this.open = true
      this.detailId = data.id
    }

    // this.getList()
    this.getuserList()
    this.getDicts('sys_normal_disable').then(response => {
      this.statusOptions = response.data
    })
  },
  methods: {
    /** 详情按钮操作 */
    handleUpdate(row) {
      this.open = true
      this.formDisabled = false
      if (this.queryParams.data == 1 || row.status == 9) this.formDisabled = true
      this.detailId = row.inspectionId
      this.title = '详情信息'
    },
    // 选择部门
    handleConfirmDept({id, name}) {
      this.queryParams = { ...this.queryParams, deptId: id, deptName: name }
    },
    handlePrint(id) {
      let routeUrl = this.$router.resolve({ path: '/case/print/patrol', query: { id } })
      window.open(routeUrl.href, '_blank')
    },
    // 类型选择
    handleCommand(command) {
      if (command.type == 'user') {
        this.queryParams = {...this.queryParams, data: command.id, dataName: command.value, status: '', statusName: ''}
      } else {
        this.queryParams = {...this.queryParams, status: command.id, statusName: command.value}
      }
      this.handleQuery()
    },
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.queryParams.pageNum = 1
      this.getList()
    },
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.getList()
    },
    // 状态颜色
    cellStyle(row) {
      if (row.column.label == '状态') return `color: ${row.row.statusColor}`
    },
    // 已下为模板
    /** 查询列表 */
    getList(showSearch) {
      this.loading = true
      if (!showSearch) {
        this.queryParams.dateRange = this.dateRange
        this.queryParams.deptId = this.deptId
      }
      let {searchValue, dateRange, pageNum, pageSize, type, deptId } = this.queryParams
      let params = { pageNum, pageSize, status: 9, type, searchId: this.searchId }
      if (searchValue) params.searchValue = searchValue
      if (deptId) params.deptId = deptId
      if (Array.isArray(dateRange) && !dateRange.length == 0) { params = {...params, searchStartTime: dateRange[0], searchEndTime: dateRange[1] } }
      console.log(params)
      inspectionList(params).then(res => {
        res.rows.forEach((v, i) => {
          this.caseData.forEach(tv => {
            if (v.status == tv.id) {
              res.rows[i].statusName = tv.value
              res.rows[i].statusColor = tv.statusColor
            }
          })
        })
        this.listData = res.rows
        this.total = res.total
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    getuserList() {
      userList().then(res => {
        this.userOptions = res.data
      })
    },
    // 确定按钮
    primary(params) {
      console.log(params)
      this.qd = false
      editInspection(params).then(() => {
        this.$refs.fromlist.upload({id: params.inspectionId, status: params.status})
        this.qd = true
        this.msgSuccess('修改成功')
        this.timer = setTimeout(() => {
          this.open = false
          this.getList()
        }, 1000)
      }).catch(err => { console.log(err); this.qd = true })
    },
    // 取消
    cancel() {
      this.open = false
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList(true)
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        searchValue: '',
        pageNum: 1,
        pageSize: 10,
        typeName: '',
        type: 0,
        statusName: '',
        status: undefined,
        // 日期范围
        dateRange: this.dateRange,
        data: 0,
        dataName: '我的',
        deptId: this.deptId,
        deptName: ''
      }
      this.handleQuery()
    },
    // 重置弹窗中的筛选条件
    resetQueryParams() {
      this.queryParams = {
        searchValue: '',
        pageNum: 1,
        pageSize: 10,
        typeName: '',
        type: 0,
        statusName: '',
        status: undefined,
        // 日期范围
        dateRange: [],
        data: 0,
        dataName: '我的',
        deptId: '',
        deptName: ''
      }
    }
  }
}
</script>

