<template>
  <div>
    <CommonTitle text="设备详览"></CommonTitle>
    <div class="info flex-c">
      <div class="item flex-c">
        <div class="label">设备名称：</div>
        <div class="value">{{ deviceInfo.deviceName }}</div>
      </div>
      <div class="item flex-c">
        <div class="label">设备ID：</div>
        <el-tooltip effect="dark" placement="top" :content="deviceInfo.deviceCode">
          <div class="value">{{ deviceInfo.deviceCode }}</div>
        </el-tooltip>
      </div>
      <div class="item flex-c">
        <div class="label">状态：</div>
        <div class="value">{{ deviceInfo.status == 1 ? '在线' : '离线' }}</div>
      </div>
      <div class="item flex-c">
        <div class="label">型号：</div>
        <div class="value">{{ deviceInfo.typeName }}</div>
      </div>
      <div class="item flex-c">
        <div class="label">最新数据：</div>
        <div class="value">{{ deviceInfo.lastConnectionTime }}</div>
      </div>
      <div class="item flex-c">
        <div class="label">组自名称：</div>
        <div class="value">{{ deviceInfo.groupName }}</div>
      </div>
      <div class="item flex-c">
        <div class="label">设备地址：</div>
        <el-tooltip effect="dark" placement="top" :content="deviceInfo.address">
          <div class="value">{{ deviceInfo.address }}</div>
        </el-tooltip>
      </div>
      <div class="item flex-c">
        <div class="label">所属单位：</div>
        <div class="value">{{ deviceInfo.deptName }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import { getDeviceDetail } from '@/api/wlwsb/index.js'
export default {
  name: 'index',
  components: {
    CommonTitle,
  },
  data() {
    return {
      deviceInfo: {},
    }
  },
  computed: {},
  mounted() {
    this.getData()
  },
  methods: {
    getData() {
      getDeviceDetail({ deviceCode: '330782100015010003300361180000000000' }).then((res) => {
        if (res.data) {
          this.deviceInfo = res.data
          this.deviceInfo.address = this.deviceInfo.administrativeDivisionName + this.deviceInfo.townshipDivisionName
        }
      })
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
.info {
  padding: 20px 20px 20px 20px;
  box-sizing: border-box;
  flex-wrap: wrap;
  height: 420px;
  justify-content: space-around;
  .item {
    margin-bottom: 30px;
    margin-right: 20px;
    &:nth-child(2n + 2) {
      margin-right: 0;
    }
    .label {
      width: 130px;
      text-align: right;
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 28px;
      color: #dcefff;
      white-space: nowrap;
      margin-right: 20px;
    }
    .value {
      width: 280px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 28px;
      color: #dcefff;
    }
  }
}
.flex-c {
  display: flex;
  align-items: center;
}
</style>