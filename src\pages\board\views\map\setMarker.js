import recorderLine from '@/assets/images/recorder-l.png'
import recorderOff from '@/assets/images/recorder-off.png'
import taxiLine from '@/assets/images/taxi-l.png'
import lawCarLine from '@/assets/images/lawCar-l.png'
import shopLine from '@/assets/images/shop-l.png'
import monitorLine from '@/assets/images/monitor-l.png'
import volLine from '@/assets/images/vol-l.png'

import fourIcon from '@/assets/images/four-l.png'
import captureIcon from '@/assets/images/capture-l.png'
import inspectionIcon from '@/assets/images/inspection-l.png'
import punishIcon from '@/assets/images/punish-l.png'
import toutIcon from '@/assets/images/tout-l.png'
import trafficCaptureIcon from '@/assets/images/trafficCapture-l.png'
import transportIcon from '@/assets/images/transport-l.png'

import fourIconOff from '@/assets/images/four-l-off.png'
import captureIconOff from '@/assets/images/capture-l-off.png'
import inspectionIconOff from '@/assets/images/inspection-l-off.png'
import punishIconOff from '@/assets/images/punish-l-off.png'
import toutIconOff from '@/assets/images/tout-l-off.png'
import trafficCaptureIconOff from '@/assets/images/trafficCapture-l-off.png'
import transportIconOff from '@/assets/images/transport-l-off.png'

import store from '@/store/index'

const markerIcon = {
  'recorder': recorderLine,
  'recorderOff': recorderOff,
  'taxi': taxiLine,
  'lawCar': lawCarLine,
  'shop': shopLine,
  'monitor': monitorLine,
  'vol': volLine,

  'four': fourIcon,
  'capture': captureIcon,
  'inspection': inspectionIcon,
  'punish': punishIcon,
  'tout': toutIcon,
  'autoCapture': trafficCaptureIcon,
  'trafficCapture': trafficCaptureIcon,
  'transport': transportIcon,

  'fourOff': fourIconOff,
  'captureOff': captureIconOff,
  'inspectionOff': inspectionIconOff,
  'punishOff': punishIconOff,
  'toutOff': toutIconOff,
  'autoCaptureOff': trafficCaptureIconOff,
  'trafficCaptureOff': trafficCaptureIconOff,
  'transportOff': transportIconOff
}

let markerMap = {
  'recorder': [],
  'taxi': [],
  'lawCar': [],
  'shop': [],
  'monitor': [],
  'vol': [],
  'area': [],

  'four': [],
  'capture': [],
  'inspection': [],
  'punish': [],
  'tout': [],
  'autoCapture': [],
  'transport': []
}

let markerLabel = []
let markerIndex = 1000 // 点击标注点的时候把层级调高

function setMarkerNum() {
  let markerNum = {}
  Object.keys(markerMap).forEach(key => {
    markerNum[key] = markerMap[key].length
  })
  store.commit('board/SET_OVERLAY_NUM', markerNum)
}

/*
  第一次创建点调用方法
  @type     覆盖点类型，不同类型取用不同图标
  @lng      覆盖点经度
  @lat      覆盖点纬度
  @$map     地图实例
  @dataSet  后台返回的覆盖点数据
  @event    点击事件回调
 */
function createMarker(type, lng, lat, $map, dataSet = {}, event, isShow = true) {
  const img = markerIcon[type]
  const recorderOff = markerIcon['recorderOff']
  let iconUrl = img
  if (dataSet.status == '0' && type == 'recorder') {
    iconUrl = recorderOff
  } else if (dataSet.status == '9' && dataSet.type == 'event') {
    iconUrl = markerIcon[`${dataSet.caseType}Off`]
  } else if (dataSet.type == 'event') {
    iconUrl = markerIcon[dataSet.caseType]
  }
  // 创建marker
  const icon = new window.T.Icon({
    iconUrl: iconUrl,
    iconSize: new window.T.Point(30, 30),
    iconAnchor: new window.T.Point(15, 15)
  })
  // if (dataSet.terminalNo == '1103') {
  //   lng = '119.633087'
  //   lat = '29.113527'
  // }
  const marker = new window.T.Marker(new window.T.LngLat(lng, lat), { icon })
  marker.dataSet = dataSet
  if (event) marker.addEventListener('click', event)
  if (isShow && dataSet.caseType != 'trafficCapture') $map.addOverLay(marker)
  return marker
}

function createPolygon(points, color, $map) {
  const polygon = new window.T.Polygon(points, {
    color: color,
    weight: 8,
    fillColor: color,
    fillOpacity: 0.3
  })
  $map.addOverLay(polygon)
  return polygon
}

export function createAllOverLay($map, data, event) {
  let rData = { ...data }
  if (rData.recorder && rData.recorder.length) {
    rData.recorder = rData.recorder.filter(item => item.status == '1')
  }
  markerMap = { ...markerMap, ...rData }
  Object.keys(markerMap).forEach(key => {
    markerMap[key] = markerMap[key].map(item => {
      if (key !== 'area') {
        if (key === 'recorder') {
          // 给执法记录仪添加文本标记
          const label = new window.T.Label({
            text: `<span ${item.status == '1' ? 'class="online"' : ''}>${item.name || item.terminalNo}</span>`,
            position: new window.T.LngLat(item.longitude, item.latitude),
            offset: new window.T.Point(-11, -17)
          })
          label.dataSet = item
          if (event) label.addEventListener('click', event)
          markerLabel.push(label)
          $map.addOverLay(label)
        }
        return createMarker(key, item.longitude, item.latitude, $map, item, event)
      } else {
        const points = item.area.split(';').map(lnglatStr => {
          const lnglat = lnglatStr.split(',')
          return new window.T.LngLat(lnglat[0], lnglat[1])
        })
        return createPolygon(points, '#04a9e7', $map)
      }
    })
  })
  // 设置数量
  setMarkerNum()
}

/*
  根据传递过来的参数，决定删除或者增加坐标点
  @types  类型
  @$map   地图实例
*/
export function changeOverlays(types = [], $map, isAdd) {
  let changeOverlayAry = []
  types.forEach(key => {
    changeOverlayAry = [...changeOverlayAry, ...markerMap[key]]
  })
  if (types.includes('recorder')) {
    changeOverlayAry = [ ...changeOverlayAry, ...markerLabel ]
  }
  changeOverlayAry.forEach(overLay => {
    if (isAdd) {
      $map.addOverLay(overLay)
    } else {
      $map.removeOverLay(overLay)
    }
  })
}

/*
  替换数据源
  @type  需要替换的数据源类型
  @$map  地图实例
  @isShow  当前坐标点在地图上是否是显示的
*/

export function changeData(type, data, $map, isShow, event) {
  if (!Array.isArray(data) || !markerMap[type]) return
  // 如果是执法记录仪，需要删除原本存在的标签数据
  let rData = [ ...data ]
  if (type === 'recorder') {
    rData = data.filter(item => item.status == '1')
    if (isShow) markerLabel.forEach(labelObj => $map.removeOverLay(labelObj))
    markerLabel = []
  }
  // 如果坐标本身显示，那么先删除再显示
  if (isShow) markerMap[type].forEach(item => $map.removeOverLay(item))

  markerMap[type] = rData.map(item => {
    if (type === 'recorder') {
      // 给执法记录仪添加文本标记
      const label = new window.T.Label({
        text: `<span ${item.status == '1' ? 'class="online"' : ''}>${item.name || item.terminalNo}</span>`,
        position: new window.T.LngLat(item.longitude, item.latitude),
        offset: new window.T.Point(-11, -17)
      })
      label.dataSet = item
      if (event) label.addEventListener('click', event)
      markerLabel.push(label)
      if (isShow) $map.addOverLay(label)
    }

    return createMarker(type, item.longitude, item.latitude, $map, item, event, isShow)
  })

  // 设置数量
  setMarkerNum()
}

export function eventChangeData(type, data, $map, isShow, event) {
  if (typeof data == 'object' && (type == 'event' || type == 'taxi')) {
    const mType = type == 'taxi' ? 'taxi' : data.caseType
    if (data.sendType == 'add') {
      const marker = createMarker(type, data.longitude, data.latitude, $map, data, event, isShow)
      markerMap[mType].push(marker)
    } else if (data.sendType == 'upd' || data.sendType == 'end') {
      if (!markerMap[mType].length) {
        // 刚打开页面可能是upd先进来，此时无法比较数据层，直接添加
        const marker = createMarker(type, data.longitude, data.latitude, $map, data, event, isShow)
        markerMap[mType].push(marker)
        return
      }
      for (let ui = 0; ui < markerMap[mType].length; ui++) {
        const tItem =  markerMap[mType][ui]
        const eventFlag = tItem.dataSet.id == data.id && type == 'event'
        const taxiFlag = tItem.dataSet.vehicleIndexCode == data.vehicleIndexCode && type == 'taxi'
        if (eventFlag || taxiFlag) {
          // 普通更新数据
          if (isShow && markerMap[mType][ui]) $map.removeOverLay(markerMap[mType][ui])
          const marker = createMarker(type, data.longitude, data.latitude, $map, data, event, isShow)
          if (markerMap[mType][ui]) markerMap[mType].splice(ui, 1, marker)
          break
        } else if (markerMap[mType].length - 1 == ui) {
          // 此时数据层已有数据，但是upd的数据可能不存在数据层中，此时也是添加数据
          const marker = createMarker(type, data.longitude, data.latitude, $map, data, event, isShow)
          markerMap[mType].push(marker)
        }
      }
    } else if (data.sendType == 'del') {
      for (let di = 0; di < markerMap[mType].length; di++) {
        const tItem =  markerMap[mType][di]
        const eventFlag = tItem.dataSet.id == data.id && type == 'event'
        const taxiFlag = tItem.dataSet.vehicleIndexCode == data.vehicleIndexCode && type == 'taxi'
        if (eventFlag || taxiFlag) {
          // 符合条件的删除数据
          if (isShow && markerMap[mType][di]) $map.removeOverLay(markerMap[mType][di])
          if (markerMap[mType][di]) markerMap[mType].splice(di, 1)
          break
        }
      }
    }
  }

  // 设置数量
  setMarkerNum()
}

export function showUserMarker($map, id) {
  let index = null
  const marker = markerMap['recorder'].filter((item, idx) => {
    const flag = item.dataSet.id == id
    if (flag) index = idx
    return flag
  })
  markerIndex++
  markerLabel[index].setZindex(markerIndex)
  marker[0].setZIndexOffset(markerIndex)
  $map.panTo(new window.T.LngLat(marker[0].dataSet.longitude, marker[0].dataSet.latitude), 18)
}
