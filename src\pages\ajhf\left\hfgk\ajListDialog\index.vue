<template>
  <ygfDialog :visible='visible' width='1415px'>
    <div id="ndjhs" class="rwgz-tc">
      <div class="rw-title flex-between">
        <div class="fs-44 text-mid-yellow dialogTitle" id="rwTitle" style="margin-left: 20px;">{{ dialogTitle }}</div>
        <div class="close cursor" @click="close" style="margin-right: 20px;"></div>
      </div>
      <div class="content">
        <div class="table">
          <div class="table-line title-line">
            <div class="table-column table-title" style="flex: 1;margin-left: 30px">序号</div>
            <div class="table-column table-title" style="flex: 3">案件编号</div>
            <div class="table-column table-title" style="flex: 2.5">国标行政区划名称</div>
            <div class="table-column table-title" style="flex: 2">决定送达日期</div>
            <div class="table-column table-title" style="flex: 2">回访类型</div>
            <div class="table-column table-title" style="flex: 2">回访状态</div>
          </div>
          <div class="table-container">
            <div class="table-line" v-for="(item,i) in tableData" :key="i" :class="{activeTableLine:i % 2 == 0}">
              <div class="table-column" style="flex: 1;margin-left: 30px">{{i + 1}}</div>
              <div class="table-column" style="flex: 3" :title="item.caseNo" @click="showDetail(item)">{{item.caseNo}}</div>
              <div class="table-column" style="flex: 2.5">{{item.nationRegionName}}</div>
              <div class="table-column" style="flex: 2">{{item.deciDeliveryDate}}</div>
              <div class="table-column" style="flex: 2">{{ labelGet(followTypeList, item.followType) }}</div>
              <div class="table-column" style="flex: 2">{{ labelGet(followStatusList, item.followStatus) }}</div>
            </div>
          </div>
          <div class="el_page_sjzx">
            <el-pagination
              background
              v-show="total > 0"
              :total="total"
              :current-page.sync="queryParams.pageNum"
              :page-size.sync="queryParams.pageSize"
              @current-change="getList"
              layout="prev, pager, next, jumper">
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
  </ygfDialog>
</template>

<script>
import ygfDialog from '@/components/ygfDialog'
import { getAjList } from '@/api/ajhf'
export default {
  name: 'index',
  props: ['dialogTitle','type','nationRegionName', 'followStatus', 'visible'],
  components: {
    ygfDialog
  },
  data() {
    return {
      tableData: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        nationRegionName: null, //行政区划
        followStatus: null, //回访状态
      },
      // 回访类型列表
      followTypeList: [
        {
          label: '未选择',
          value: '0',
        },
        {
          label: '无需回访',
          value: '1',
        },
        {
          label: '自行回访',
          value: '2',
        },
        {
          label: '短信回访',
          value: '3',
        },
      ],
      // 回访状态列表
      followStatusList: [
        {
          label: '无需回访',
          value: '0',
        },
        {
          label: '未回访',
          value: '1',
        },
        {
          label: '回访中',
          value: '3',
        },
        {
          label: '已回访',
          value: '2',
        }
      ],
    }
  },
  computed: {
    city() {
      return localStorage.getItem("city")
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      this.queryParams.nationRegionName = this.nationRegionName;
      this.queryParams.followStatus = this.followStatus;
      getAjList(this.queryParams).then(res => {
        console.log(res);
        this.tableData = res.rows?res.rows:[];
        this.total = res.total;
      })
    },
    close() {
      this.$emit('close')
    },
    // 字典翻译
    labelGet(list, value) {
      if (!Array.isArray(list)) {
        console.error("Expected an array for the 'list' parameter.")
        return ''
      }

      const foundItem = list.find((item) => item.value == value)

      // 确保找到对应的item后再返回其label属性，否则返回空字符串
      return value === null || value === undefined || value === '' || !foundItem
        ? ''
        : foundItem.label
    },
    showDetail(item) {
      //案件总数内点击案件名称
      if (this.type == '1') {
        this.$emit('showAjDetail',item.caseNo)
      } else {
        //已回访案件内点击案件名称
        this.$emit('showAjDetailTable',item.caseNo)
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.getList()
      }
    }
  }
}
</script>

<style scoped lang='less'>

.search-box {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

::-webkit-scrollbar {
  display: none;
}


ul,
ul li {
  list-style: none;
}

.rwgz-tc {
  width: 1515px;
  height: 1186px;
  background: url("@/assets/zhdd/dialogBg2.png") no-repeat;
  background-size: 100% 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-direction: column;
}

.rw-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 46px 3% 0;
  width: 95%;
  height: 60px;
  line-height: 60px;
}

.close {
  background: url("@/assets/zhdd/close.png") no-repeat;
  width: 34px;
  height: 34px;
}

.ql-indent-1 img {
  width: 100%;
}

img {
  width: 100%;
}

.content {
  height: 910px;
  margin-top: 10px;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  flex-direction: column;
}

::-webkit-scrollbar {
  width: 0;
  height: 0;
}

.scrollTab {
  width:928px;
  height:70px;
}

.table-container {
  height: 880px;
  overflow-y: scroll;
}

.tableContainer2 {
  height: 400px;
  overflow-y: scroll;
}

.table-line {
  width: 1429px;
  height: 80px;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  background: rgba(50,134,248,0.15);
}

.title-line {
  background: transparent !important;
}

.table-title {
  font-size: 32px;
  font-family: Source Han Sans CN;
  font-weight: bold;
  color: #FFFFFF;
  text-align: left;
}

.table-column {
  font-size: 32px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
}
.activeTableLine {
  background: rgba(50,134,248,0.25);
}

/deep/ .el-input {
  /*position: absolute;*/
  /*top: 229px;*/
  /*left: 1630px;*/
  width: 537px;
  height: 72px;
}

/deep/ .el-input__icon {
  height: 100%;
  width: 25px;
  text-align: center;
  -webkit-transition: all .3s;
  transition: all .3s;
  line-height: 75px;
}

/deep/ .el-scrollbar {
  overflow: hidden;
  /*position: relative;*/
  height: 500px;
  background: #020b28;
  border-radius: 10px;
}

/deep/ .el-autocomplete-suggestion__wrap {
  max-height: unset !important;
  padding: 10px 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

/deep/ .el-input--suffix .el-input__inner {
  border: 1px solid #359CF8;
  border-radius: 8px;
  padding-right: 30px;
  height: 70px;
  font-family: MicrosoftYaHei;
  font-size: 28px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 1px;
  color: #bbe5fd !important;
  background: #020b28;
}

/deep/ .el-input.is-active .el-input__inner, .el-input__inner:focus {
  border: 1px solid #bbe5fd;
  outline: 0;
}

/deep/ .el-input__suffix-inner {
  pointer-events: all;
  font-size: 28px;
  margin: 15px 20px 0 0;
  color: #bbe5fd !important;
}

/deep/ .el-autocomplete-suggestion li {
  padding: 0 20px;
  line-height: 34px;
  cursor: pointer;
  color: #bbe5fd;
  font-size: 28px;
  list-style: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin: 10px 0 25px 0;
}

/deep/ .el-autocomplete-suggestion li:hover {
  background: unset !important;
}

.search {
  width: 100px;
  height: 65px;
  line-height: 65px;
  text-align: center;
  background: #0A619E;
  border: 1px solid #359CF8;
  border-radius: 8px;
  font-size: 28px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FEFEFE;
  margin-left: 10px;
  cursor: pointer;
}

.tab-con-active {
  background: url('@/assets/zfts/tab-active.png') no-repeat;
  background-size: 110% 100%;
}

.toolBar {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tab-con {
  width: 351px;
  height: 70px;
  line-height: 70px;
  color: #fff;
  font-size: 32px;
}

.s-flex {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-evenly;
}

.tab-item {
  font-size: 36px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  font-style: italic;
  color: rgba(171, 206, 239, 0.7);
  line-height: 59px;
}

.tabConActive {
  background: url('@/assets/zfts/tab-active.png') no-repeat;
  background-size: 110% 100%;
  font-family: Source Han Sans CN;
  font-weight: bold;
  font-size: 36px;
  color: #ffffff;
}

/* 分页 */
/deep/ .el-pagination {
  padding: 2px 1px !important;
}

/deep/ .el_page_sjzx>.el-pagination button {
  height: 52px;
  width: 40px;
  background: transparent !important;
}

/deep/ .el_page_sjzx .el-pagination button .el-icon {
  font-size: 24px !important;
  color: #c1e2fa;
  font-weight: 400;
}

/deep/ .el_page_sjzx .el-pagination button .el-icon:hover {
  color: #20aeff;
}

/deep/ .el_page_sjzx .el-pagination__jump {
  font-size: 22px !important;
  font-weight: 400;
  color: #c1e2fa;
  line-height: 45px;
  margin-left: 0 !important;
}

/deep/ .el_page_sjzx .el-pagination__jump.el-input.el-pagination__editor {
  width: 140px;
}

/deep/ .el_page_sjzx .el-pagination__jump.el-input.el-pagination__editor input {
  overflow: hidden;
  width: 96px;
  overflow: auto;
  height: 56px !important;
  color: #c1e2fa;
  font-size: 24px;
  border: 2px solid #a7a889;
  border-radius: 4px;
  background: transparent !important;
}

/deep/ .el_page_sjzx ul {
  margin-top: 2px !important;
}

/deep/ .el_page_sjzx ul li {
  border: 2px solid transparent;
  margin-left: 0px !important;
  height: 37px;
  padding: 0 3px !important;
  font-size: 20px !important;
  color: #c1e2fa !important;
  background: transparent !important;
  font-weight: 500;
  line-height: 36px !important;
  border-radius: 4px;
}

/deep/ .el_page_sjzx li.active {
  margin: 0;
  padding: 0;
  color: #fff !important;
  /* border: 2px solid #035b86; */
  background-color: #00c0ff !important;
}

/deep/ .el_page_sjzx {
  margin: 0 auto;
  text-align: center;
  margin-top: -10px;
}

/deep/ .el-pagination button, .el-pagination span:not([class*='suffix']) {
  height: 42px !important;
  line-height: 42px !important;
}

/deep/ .el-input {
  font-size: 26px !important;
}

/deep/ .el-pagination__editor.el-input {
  width: 77px;
  margin: 0 10px;
}

/deep/ .el-pagination__editor.el-input .el-input__inner {
  color: #fff;
  height: 33px;
  font-size: 24px;
  background: #132c4e;
  border: 1px solid;
  border-image: linear-gradient(-32deg, #359cf8, #afdcfb) 1 1;
}
</style>