<template>
  <div class="clock-on">
    <vueSeamlessScroll :data="listData" :class-option="defaultOption">
      <div v-for="item in listData" :key="item.deptId" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.8)" class="clock-on-list">
        <div class="name">{{ item.deptName }}</div>
        <div class="item">
          <span class="num">{{ item.total }}</span>
          <span>总人数</span>
        </div>
        <div class="item">
          <span class="num">{{ item.notOnDuty }}</span>
          <span>未到岗人数</span>
        </div>
        <div class="item">
          <span class="num">{{ item.late }}</span>
          <span>迟到人数</span>
        </div>
        <div class="item">
          <span class="num">{{ item.leave }}</span>
          <span>请假人数</span>
        </div>
        <div class="item">
          <span class="num">{{ item.ratio }}%</span>
          <span>出勤率</span>
        </div>
      </div>
      <div v-if="!listData.length" class="clock-on-list">
        <span class="noData">无考勤数据</span>
      </div>
    </vueSeamlessScroll>
  </div>
</template>

<script>
import { getInCount } from '@/api/board/dataView/index'
import vueSeamlessScroll from 'vue-seamless-scroll'

export default {
  components: {
    vueSeamlessScroll
  },
  data() {
    return {
      listData: [],
      loading: false
    }
  },
  computed: {
    defaultOption() {
      return {
        step: 0.2, // 数值越大速度滚动越快
        limitMoveNum: 4, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 1000 // 单步运动停止的时间(默认值1000ms)
      }
    }
  },
  mounted() {
    this.fetchData()
  },
  methods: {
    fetchData() {
      this.loading = true
      const punchTime = this.parseTime(new Date())
      getInCount({ punchTime }).then(res => {
        this.listData = res.data
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style scoped lang="scss">
.clock-on {
  height: 100%;
  font-size: 12px;
  padding: 0 pxtorem(20);
  overflow: hidden;
  .noData {
    color: #00f7ff;
    display: block;
    padding: 15px;
    text-align: center;
  }
  &-list {
    overflow: hidden;
    padding-bottom: 5px;
    border-bottom: 1px solid rgba($color: #fff, $alpha: 0.2);
    .name {
      color: #00f7ff;
      padding: 5px 0;
    }
    .item {
      width: 20%;
      text-align: center;
      float: left;
      span {
        display: block;
      }
      .num {
        font-size: pxtorem(19);
        text-shadow: 0 1px 6px #00f7ff;
        padding: 5px 0;
      }
    }
  }
}
</style>
