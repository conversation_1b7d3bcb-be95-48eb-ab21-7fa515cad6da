import {request} from '@/utils/request'

// 查询督查考核记录列表
export function listRecord(query) {
  return request({
    url: '/business/supervision/record/list',
    method: 'get',
    params: query
  })
}

// 查询督查考核记录详细
export function getRecord(checkRecordId) {
  return request({
    url: '/business/supervision/record/' + checkRecordId,
    method: 'get'
  })
}

// 新增督查考核记录
export function addRecord(data) {
  return request({
    url: '/business/supervision/record/add',
    method: 'post',
    data: data
  })
}

// 修改督查考核记录
export function updateRecord(data) {
  return request({
    url: '/business/supervision/record/edit',
    method: 'post',
    data: data
  })
}

// 删除督查考核记录
export function delRecord(checkRecordId) {
  return request({
    url: '/business/supervision/record/remove/' + checkRecordId,
    method: 'post'
  })
}

// 导出督查考核记录
export function exportRecord(query) {
  return request({
    url: '/business/supervision/record/export',
    method: 'get',
    params: query
  })
}

// 获取申诉列表
export function getAppealList(query) {
  return request({
    url: '/business/appeal/record/list',
    method: 'get',
    params: query
  })
}
