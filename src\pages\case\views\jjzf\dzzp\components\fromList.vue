<template>
  <div>
    <el-dialog class="m-dialog" :close-on-click-modal="false" append-to-body v-bind="$attrs" width="900px" :title="title" v-on="$listeners" @close="onClose">
      <el-scrollbar v-loading="formLoading" style="height: 100%;" :element-loading-text="formLoadingText">
        <div style="margin-right: 10px;">
          <!--:gutter="15"-->
          <el-form ref="form" :model="form" :rules="rules" :disabled="formDisabled" size="medium" label-width="120px">
            <el-row>
              <!-- <el-col :span="8">
                <el-form-item label="案件名称" prop="title">
                  <el-input v-model="form.title" placeholder="请输入案件名称" clearable />
                </el-form-item>
              </el-col> -->
              <!-- <el-col :span="8">
                <el-form-item label="操作人" prop="userName">
                  <el-tooltip :disabled="!form.userName" class="item" effect="dark" :content="form.userName" placement="top">
                    <el-input v-model="form.userName" placeholder="请输入抄告单号" clearable />
                  </el-tooltip>
                </el-form-item>
              </el-col> -->

              <h3 class="title">
                <span>违法基本信息</span>
                <attentionBtn :case-id="form.trafficCaptureId || 0" case-type="trafficCapture" :case-content="form.content" />
              </h3>
              <!-- <el-col :span="8">
                <el-form-item label="抄告单号" prop="noticeNo">
                  <el-tooltip :disabled="!form.noticeNo" class="item" effect="dark" :content="form.noticeNo" placement="top">
                    <el-input v-model="form.noticeNo" placeholder="请输入抄告单号" clearable />
                  </el-tooltip>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="执勤民警" prop="policeman">
                  <el-tooltip :disabled="!form.policeman" class="item" effect="dark" :content="form.policeman" placement="top">
                    <el-input v-model="form.policeman" placeholder="请输入执勤民警名称" clearable />
                  </el-tooltip>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="数据来源" prop="type">
                  <el-select v-model="form.type" placeholder="请选择">
                    <el-option
                      v-for="dict in typeOptions"
                      :key="dict.dictValue"
                      :label="dict.dictLabel"
                      :value="dict.dictValue"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="号牌种类" prop="carType">
                  <el-select v-model="form.carType" clearable @change="handleIsTypeChange" @clear="form.carTypeName = ''">
                    <el-option v-for="item in carTypeData" :key="item.id" :label="item.dictLabel" :value="item.dictValue" />
                  </el-select>
                </el-form-item>
              </el-col> -->
              <el-col :span="24">
                <el-form-item label="上报人" prop="userName">
                  <el-tooltip :disabled="!form.userName" class="item" effect="dark" :content="form.userName" placement="top">
                    <el-input v-model="form.userName" readonly placeholder="上报人" clearable />
                  </el-tooltip>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="车牌号码" prop="carNo">
                  <el-tooltip :disabled="!form.carNo" class="item" effect="dark" :content="form.carNo" placement="top">
                    <el-input v-model="form.carNo" placeholder="请输入抄告单号" clearable />
                  </el-tooltip>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="违规时间" prop="happenTime">
                  <el-tooltip :disabled="!form.happenTime" class="item" effect="dark" :content="form.happenTime" placement="top">
                    <el-date-picker v-model="form.happenTime" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="请选择违规时间" clearable />
                  </el-tooltip>
                </el-form-item>
              </el-col>
              <!-- <el-col :span="8">
                <el-form-item label="道路编码" prop="roadCode">
                  <el-tooltip :disabled="!form.roadCode" class="item" effect="dark" :content="form.roadCode" placement="top">
                    <el-input v-model="form.roadCode" placeholder="请输入道路编码" clearable />
                  </el-tooltip>
                </el-form-item>
              </el-col>
              <el-col :span="16">
                <el-form-item label="路口" prop="intersection">
                  <el-tooltip :disabled="!form.intersection" class="item" effect="dark" :content="form.intersection" placement="top">
                    <el-input v-model="form.intersection" placeholder="请输入路口" clearable />
                  </el-tooltip>
                </el-form-item>
              </el-col> -->
              <el-col :span="24">
                <el-form-item label="地址描述" prop="caseAddress">
                  <el-tooltip :disabled="!form.caseAddress" class="item" effect="dark" :content="form.caseAddress" placement="top">
                    <el-input v-model="form.caseAddress" placeholder="请输入地址描述" clearable>
                      <el-button slot="append" type="primary" @click="openMap = true">选择</el-button>
                    </el-input>
                  </el-tooltip>
                  <TdtMap :visible.sync="openMap" append-to-body :map-search="true" :dw="form" @onlnglat="onlnglat" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="违法行为" prop="behavior">
                  <el-tooltip :disabled="!form.behavior" class="item" effect="dark" :content="form.behavior" placement="top">
                    <el-input v-model="form.behavior" type="textarea" placeholder="请输入违法行为" clearable />
                  </el-tooltip>
                </el-form-item>
              </el-col>
              <!-- <el-col :span="24">
                <el-form-item label="案件内容" prop="content">
                  <el-input v-model="form.content" type="textarea" placeholder="请输入案件内容" :maxlength="150" show-word-limit :autosize="{minRows: 2, maxRows: 4}" />
                </el-form-item>
              </el-col> -->
              <el-col :span="24">
                <el-form-item label="上传图片" prop="files">
                  <el-upload
                    ref="upload"
                    multiple
                    :limit="4"
                    list-type="picture-card"
                    class="upload-demo"
                    accept=".jpg,.jpeg,.png,.JPG,.JPEG,.PNG"
                    action="/zqzfj/system/file/upload"
                    :auto-upload="false"
                    :headers="headers"
                    :file-list="formFiles"
                    :on-preview="handlePictureCardPreview"
                    :before-remove="beforeRemove"
                    :on-success="handleSuccess"
                    :on-error="handleError"
                    :data="formData"
                    name="files"
                    :on-exceed="exceed"
                  >
                    <i class="el-icon-plus" />
                  </el-upload>
                  <el-image v-show="false" ref="imgList" :src="dialogImageUrl" :preview-src-list="srcList" />
                </el-form-item>
              </el-col>
            </el-row>
            <!-- <h3 class="title">机动车基本信息</h3>
            <el-row>
              <el-col :span="8">
                <el-form-item label="车身颜色" prop="carColor">
                  <el-input v-model="form.carColor" placeholder="请输入车身颜色" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="车辆品牌" prop="carBrand">
                  <el-input v-model="form.carBrand" placeholder="请输入车辆品牌" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="所有人" prop="carOwnner">
                  <el-input v-model="form.carOwnner" placeholder="请输入所有人" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="住所地址" prop="ownnerAddress">
                  <el-tooltip :disabled="!form.ownnerAddress" class="item" effect="dark" :content="form.ownnerAddress" placement="top">
                    <el-input v-model="form.ownnerAddress" placeholder="请输入住所地址" clearable>
                      <el-button slot="append" type="primary" @click="openOwnnerMap = true">选择</el-button>
                    </el-input>
                  </el-tooltip>
                  <TdtMap :visible.sync="openOwnnerMap" append-to-body :map-search="true" @onlnglat="onOwnnerlnglat" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="车辆车型" prop="carModel">
                  <el-input v-model="form.carModel" placeholder="请输入车辆车型" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="使用性质" prop="useProperties">
                  <el-select v-model="form.useProperties" placeholder="请输入使用性质" clearable :loading="typeLoading" no-data-text="无数据" :style="{ width: '100%' }" @change="handleUsePropertiesChange" @clear="clearType('useProperties','propertiesData')">
                    <el-option v-for="(item, index) in propertiesData" :key="index" :label="item.dictLabel" :value="item.dictLabel" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="邮政编码" prop="postalcode">
                  <el-input v-model="form.postalcode" placeholder="请输入邮政编码" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="联系电话" prop="phone">
                  <el-input v-model="form.phone" placeholder="请输入联系电话" clearable />
                </el-form-item>
              </el-col>
            </el-row> -->
          </el-form>
        </div>
      </el-scrollbar>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="!isBoard" type="success" :disabled="formDisabled" @click="handelOver">办 结</el-button>
        <el-button v-if="!isBoard" type="primary" :disabled="formDisabled" @click="primary(2)">暂 存</el-button>
        <el-button @click="close">取 消 </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'
import { getFiles, removeFiles} from '@/api/supervise/swit'
import dzzp from '@/api/case/dzzp'
import {listData} from '@/api/system/dict/type'
import attentionBtn from '@/components/attentioneBtn/index.vue'
import TdtMap from '@/components/tdtMap/tdtMap.vue'

export default {
  name: 'FromList',
  components: {
    attentionBtn,
    TdtMap
  },
  props: {
    title: String,
    detailId: Number,
    formDisabled: Boolean,
    typeData: Array,
    isBoard: Boolean
  },
  data() {
    return {
      headers: {Authorization: 'Bearer ' + getToken()},
      formData: {businessId: null, tableName: 'case_traffic', status: 1},
      formLoadingText: '数据上传中',
      typeLoading: false,
      formLoading: false,
      userOpen: false,
      propertiesData: [],
      typeOptions: [],
      carTypeData: [],
      circleColor: {
        1: '#25c548',
        2: '#25c548',
        9: '#bdc3bf'
      },
      form: {},
      formFiles: [],
      fileList: [],
      rules: {
        // userName: [{ required: true, message: '请输入负责人名称', trigger: 'blur' }],
        // noticeNo: [{ required: true, message: '请输入抄告单号', trigger: 'blur' }],
        // policeman: [{ required: true, message: '请输入执勤民警', trigger: 'blur' }],
        // carType: [{ required: true, message: '请选择号牌种类', trigger: 'change' }],
        carNo: [{ required: true, message: '请输入号牌号码', trigger: 'blur' }],
        // roadCode: [{ required: true, message: '请输入道路编码', trigger: 'blur' }],
        // intersection: [{ required: true, message: '请输入路口', trigger: 'blur' }],
        happenTime: [{ required: true, message: '请选择违法时间', trigger: 'blur' }],
        caseAddress: [{ required: true, message: '请输入地址描述', trigger: 'change' }]
        // behavior: [{ required: true, message: '请输入违法行为', trigger: 'blur' }],
        // carColor: [{ required: true, message: '请输入车身颜色', trigger: 'blur' }],
        // carBrand: [{ required: true, message: '请输入车辆品牌', trigger: 'blur' }],
        // carOwnner: [{ required: true, message: '请输入所有人', trigger: 'blur' }],
        // ownnerAddress: [{ required: true, message: '请输入住所地址', trigger: 'change' }],
        // carModel: [{ required: true, message: '请输入车辆车型', trigger: 'blur' }],
        // useProperties: [{ required: true, message: '请输入使用性质', trigger: 'blur' }],
        // postalcode: [{ required: true, message: '请输入邮政编码', trigger: 'blur' }],
        // phone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
        // status: [{ required: true, message: '请输入状态', trigger: 'blur' }],
        // type: [{ required: true, message: '请输入类型 1 = 电子抓拍', trigger: 'blur' }],
        // boardingType: [{ required: true, message: '请输入类型 1=出租车 2=大客车', trigger: 'blur' }]
      },
      openMap: false,
      openOwnnerMap: false,
      dialogImageUrl: ''
    }
  },
  computed: {
    srcList() {
      return this.formFiles.map(item => item.url)
    }
  },
  watch: {
    '$attrs.visible'(nVal) {
      if (nVal && this.detailId) {
        this.getData()
      } else if (nVal) {
        this.form = { happenTime: this.parseTime(new Date()), userName: this.$store.getters.nickName, userId: this.$store.getters.uid }
      }
    }
  },
  async mounted() {
    await Promise.all([listData({dictType: 'car_type'}), listData({dictType: 'use_properties'})]).then(res => {
      this.carTypeData = res[0].rows
      this.propertiesData = res[1].rows
    })
    // this.getData()
  },
  created() {
    this.getDicts('case_capture_type').then(response => {
      this.typeOptions = response.data
    })
  },
  methods: {
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.$nextTick(() => {
        this.$refs.imgList.clickHandler()
      })
    },
    onlnglat(e) {
      this.form = { ...this.form, caseAddress: e.address, longitude: e.lng, latitude: e.lat }
      this.openMap = false
    },
    onOwnnerlnglat(e) {
      this.form = { ...this.form, ownnerAddress: e.address, longitude: e.lng, latitude: e.lat }
      this.openOwnnerMap = false
    },
    handleIsTypeChange(val) {
      let data = this.carTypeData.find(v => v.dictValue == val)
      if (data) this.form.carTypeName = data.dictLabel
    },
    handleUsePropertiesChange(val) {
      let data = this.propertiesData.find(v => v.dictValue == val)
      if (data) this.form.useProperties = data.dictLabel
    },
    clearType(data, option) {
      this.$nextTick(() => {
        this.form = {...this.form, [data]: '', [option]: ''}
      })
    },
    getData() {
      this.formLoading = true
      this.formLoadingText = '加载中'
      Promise.all([
        dzzp.oneList(this.detailId),
        getFiles({businessId: this.detailId, tableName: 'case_traffic'})
      ]).then(res => {
        this.form = res[0].data
        this.$refs.form.clearValidate()
        // if (this.form.type) this.launchDept(this.form.type)
        // 文件部分
        res[1].rows.map(item => {
          const obj = { name: item.displayName, url: `/zqzfj${item.filePath}`, ...item }
          this.formFiles.push(obj)
        })

        this.formLoading = false
      }).catch(err => { console.log(err), this.formLoading = false, this.msgError('加载失败') })
    },
    userConfirm(e) {    //	执行部门名称
      this.form = {...this.form, userName: e.name, userId: e.id}
    },
    handelOver() {
      this.$confirm('提示', '是否确认办结？', { type: 'warning' }).then(() => {
        this.$refs['form'].validate(valid => {
          if (this.$refs.upload.uploadFiles.length == 0) return this.msgError('请上传图片')
          if (valid) {
            this.primary(9)
          } else {
            return false
          }
        })
      }).catch(() => {})
    },
    primary(status) {
      // this.handleIsTypeChange(this.form.carType)
      let params = {...this.form, status}
      this.formLoading = true
      this.formLoadingText = '数据上传中'
      let api = params.trafficCaptureId ? dzzp.edit(params) : dzzp.add(params)
      api.then(res => {
        this.upload({id: this.form.trafficCaptureId ? this.form.trafficCaptureId : res.data.trafficCaptureId})
        console.log(this.$refs.upload)
        if (0 == this.$refs.upload.uploadFiles.length  - this.formFiles.length) {
          this.$emit('reLoad')
          this.close()
        } else {
          this.msgSuccess('上传成功')
          this.formLoading = false
        }
      }).catch(err => { console.log(err); this.formLoading = false, this.msgError('上传失败') })

    },
    // 上传函数
    beforeRemove(file) {
      return new Promise((resolve, reject) => {
        this.$confirm('是否确认删除？ 删除后将无法恢复', '提示', { type: 'warning' }).then(() => {
          if (file.fileId) {
            removeFiles(file.fileId).then(resolve).catch(reject)
          } else {
            resolve()
          }
        }).catch(() => {
          reject()
        })
      })
    },
    handleChange(file, fileList) {   // 删除图片
      this.fileList = fileList
    },
    exceed(files, filesList) {
      if (filesList.length >= 4) this.msgError('已超出最大上传数量')
    },
    handleSuccess(files) {    // 上传成功
      this.$emit('reLoad')
      this.close()
      this.formLoading = false
      if (files.code == 200) {
        this.msgSuccess('上传成功')
      } else {
        this.msgError('上传失败,请修改后重试')
      }
    },
    handleError() { // 上传失败
      this.msgError('上传失败,请修改后重试')
      this.formLoading = false
    },
    // 上传
    upload(data) {
      this.formLoading = true
      this.formLoadingText = '图片上传中'
      this.formData.businessId = data.id
      this.$refs.upload.submit()
    },
    reset() {
      this.form = {}
      this.resetForm('form')
    },
    onClose() {
      this.reset()
      this.formFiles = []
      this.formLoadingText = '数据上传中'
    },
    close() {
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style lang="scss" scoped>
.suffix {
  padding: 10px 0 10px 30px;
  margin-bottom: 20px;
  margin-right: 5%;
  border: 1px solid #e4e7ec;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}
.suffix .list {
  width: 100%;
  overflow: hidden;
  margin-bottom: 20px;
}
.list .labels {
  margin-right: 4%;
  width: 20%;
  text-align: right;
  display: inline-block;
}
.satae {
  position: absolute;
  top: 10px;
  right: 10px;
}
.satae .cir {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #fff;
  margin-right: 10px;
}
</style>
