<template>
  <div>
    <CommonTitle text='基层治理协同'></CommonTitle>
    <div class="jczlXt-container">
      <div v-show="year == $currentYear" class='yearChange'>
        <el-date-picker
          v-model="value2"
          type="monthrange"
          @change="initApi"
          range-separator="-"
          start-placeholder="开始月份"
          value-format="yyyy-MM"
          end-placeholder="结束月份"
          :append-to-body='false'
        >
        </el-date-picker>
      </div>
      <div class="zlxt-item-center"></div>
      <div class="zlxt-items">
        <div class="zlxt-item" v-for="(item,i) in zlxtData" :key="i" :class="{zlxtItemLeft:i % 2 == 0,zlxtItemRight:i % 2 != 0}" @click="showZlxtDialog(item)">
          <div class="zlxt-item-name" :class="{ml:i % 2 != 0}">{{item.name}}:</div>
          <div class="zlxt-item-value" :class="{ml2:i % 2 == 0}">{{item.value}}</div>
        </div>
      </div>
    </div>

    <jczlxtDialog :name='name' :time='time' :visible='dialogShow' @close='dialogShow = false'></jczlxtDialog>
    <jczlxtDialog2 :name='name' :time='time' :visible='dialogShow2' @close='dialogShow2 = false'></jczlxtDialog2>
    <jczlxtDialog3 :name='name' :time='time' :visible='dialogShow3' @close='dialogShow3 = false'></jczlxtDialog3>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import { indexApi } from '@/api/indexApi'
import moment from 'moment'
import jczlxtDialog from './jczlxtDialog'
import jczlxtDialog2 from './jczlxtDialog2'
import jczlxtDialog3 from './jczlxtDialog3'
export default {
  name: 'index',
  components: {
    CommonTitle,
    jczlxtDialog,
    jczlxtDialog2,
    jczlxtDialog3
  },
  data() {
    return {
      value2: [
        new Date().getFullYear() + "-01",
        moment(new Date()).format("YYYY-MM"),
      ],
      year: localStorage.getItem("year"),
      city: localStorage.getItem("city"),
      //治理协同数据
      zlxtData:[
        {
          name:"网格上报数",
          value:"0"
        },
        {
          name:"超期办结",
          value:"0"
        },
        {
          name:"协同数",
          value:"0"
        },
        {
          name:"已协同事项数",
          value:"0"
        },
        {
          name:"办结数",
          value:"0"
        },
        {
          name:"参与处置部门数",
          value:"0"
        }
      ],

      name:"",
      dialogShow: false,
      dialogShow2: false,
      dialogShow3: false,
      time:""
    }
  },
  computed: {},
  mounted() {
    this.$bus.$on('cityChange', (city) => {
      this.city = city;
      this.initApi(city,localStorage.getItem("year"));
    })
    this.$bus.$on('yearChange', (year) => {
      this.year = year;
      this.value2 = this.$getYearList(year)
      this.initApi(localStorage.getItem("city"),year);
    })
    this.initApi(localStorage.getItem("city"),localStorage.getItem("year"));
  },
  methods: {
    initApi(city,year) {
      this.zlxtData.forEach((item,i) => {
        item.value = 0
      })
      indexApi("csdn_yjyp_jczlxt",{area_name:city,sj1:this.value2[0],sj2:this.value2[1]}).then(res => {
        res.data.forEach((item,i) => {
          switch (item.zbmc) {
            case "网格上报数":
              this.zlxtData[0].value = item.tjz
              break;
            case "超期办结数":
              this.zlxtData[1].value = item.tjz
              break;
            case "协同数":
              this.zlxtData[2].value = item.tjz
              break;
            case "已协同":
              this.zlxtData[3].value = item.tjz
              break;
            case "办结数":
              this.zlxtData[4].value = item.tjz
              break;
            case "处置部门数":
              this.zlxtData[5].value = item.tjz
              break;
          }
        })
      })
    },
    showZlxtDialog(item) {
      this.name = item.name
      this.time = this.value2
      if (item.name == "参与处置部门数" || item.name == "已协同事项数") {
        this.dialogShow = true
      } else if (item.name == "网格上报数" || item.name == "协同数" || item.name == "办结数") {
        this.dialogShow2 = true
      } else {
        this.dialogShow3 = true
      }
    }
  },
  watch: {}
}
</script>

<style scoped lang='less'>
.jczlXt-container {
  height: 380px;
  position: relative;
  /deep/ .yearChange {
    position: absolute;
    top: -75px;
    left: 680px;
    z-index: 2;
      .el-input__inner {
        height: 48px !important;
        background-color: #132c4e !important;
        border: 2px solid #afdcfb !important;
        color: #fff !important;
        border-radius: 15px !important;
      }
      .el-picker-panel {
        top: 160px !important;
        left: -320px !important;
      }
  }
}

.zlxt-items {
  height: 239px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  flex-wrap: wrap;
  position: absolute;
  top: 58px;
}

.zlxt-item {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  width:298px;
  height:62px;
  background-size: cover;
  cursor: pointer;
}

.zlxt-item-center {
  width: 335px;
  height: 353px;
  background: url("@/assets/zfts/jczlxtbg.png") no-repeat;
  background-size: cover;
  position: absolute;
  top: -15px;
  left: 347px;
}

.zlxtItemLeft {
  justify-content: flex-end;
  background: url("@/assets/zfts/jczlxtlbg.png") no-repeat;
}

.zlxtItemRight {
  justify-content: flex-start;
  margin-left: 200px;
  background: url("@/assets/zfts/jczlxtrbg.png") no-repeat;
}

.zlxt-item-name {
  font-size: 28px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #F6F9FE;
  white-space: nowrap;
}

.zlxt-item-value {
  font-size: 28px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #3CFDFF;
  margin-left: 10px;
}
</style>