<template>

    <div class="person-info-container">
      <!-- 右侧地图区域 -->
      <div class="right-panel">
          <!-- 顶部工具栏 -->
          <div class="top-toolbar">
              <div class="date-picker-wrapper">
                <el-date-picker class="my-date-picker" v-model="dateRange" value-format="yyyy-MM-dd 00:00:00" unlink-panels style="width: 400px;" size="large" type="daterange" range-separator="—" start-placeholder="开始日期" end-placeholder="结束日期" @change="handleChange" :append-to-body='false'/>
              </div>
              <div class="close-btn" @click="handleClose">×</div>
          </div>
          
          <!-- 地图容器 -->
          <div class="map-container" id="mapContainer"></div>
          <div class="bottom-toolbar"></div>
      </div>
    </div>
</template>

<script>
import MapService from '@/components/Map/index.js';
import Map from "@arcgis/core/Map";
import SceneView from "@arcgis/core/views/SceneView";
import { getIRSLayer } from "@/components/Map/MapUtils/basemap.js";
import FeatureLayer from "@arcgis/core/layers/FeatureLayer";
import Point from "@arcgis/core/geometry/Point";
import Graphic from "@arcgis/core/Graphic";
import GraphicsLayer from "@arcgis/core/layers/GraphicsLayer";
import {listAllCase } from '@/api/zqyzt';

export default {
  name: 'zfcInfo',
  components: {
  },
  data() {
    return {
      defaultAvatar: require('@/assets/zqyzt/windowInfo/fulb.png'),
      dateRange: [],
      map: null,
      view: null,
      currentFeatureLayer: null
    }
  },
  mounted() {
    this.initMap()
  },
  methods: {
    initMap() {
      this.map = new Map({
        basemap: getIRSLayer(),
        ground: {
          opacity: 1,
          surfaceColor: "#08294a",
        },
      });

      this.view = new SceneView({
        container: "mapContainer",
        map: this.map,
        camera: {
          position: {
            spatialReference: {
            wkid: 4490,
            },
            x: 119.63010238256342,
            y: 29.110005649408036,
            z: 2226.879426258436,
        },
        heading: 0.26874578434742386,
        tilt: 0.49999999999694683,
      },
      qualityProfile: "low",
      });

      // 等待视图加载完成后创建热力图
      this.view.when(() => {
        this.setDefaultDate()
        this.handleChange()
      });
    },
    handleClose() {
      this.$emit('close')
      if (this.view) {
        this.view.destroy()
        this.view = null
      }
      if (this.map) {
        this.map = null
      }
    },
    setDefaultDate() {
      const nowDate = new Date()
      const ym = this.parseTime(nowDate, '{y}-{m}')
      const endofMon = new Date(nowDate.getFullYear(), nowDate.getMonth() + 1, 0).getDate()
      this.dateRange = [`${ym}-01 00:00:00`, `${ym}-${endofMon} 00:00:00`]
      
      // 设置默认日期后立即获取数据
      this.handleChange();
    },
    handleChange() {
      let params = {}
      if (this.dateRange[0]) params.searchStartTime = this.dateRange[0]
      if (this.dateRange[1]) params.searchEndTime = this.dateRange[1]
      
      listAllCase(params).then(res => {
        const points = res.data.event.map(item => ({
          longitude: item.longitude,
          latitude: item.latitude,
          intensity: 150 // 可以根据实际数据设置强度
        }));
        this.updateHeatmap(points);
      }).catch(error => {
        console.error('获取数据失败:', error);
      });
    },
    updateHeatmap(points) {
      // 如果存在旧图层，先移除
      if (this.currentFeatureLayer) {
        this.map.remove(this.currentFeatureLayer);
      }

      // 创建新的图形图层
      const graphicsLayer = new GraphicsLayer();
      
      // 添加点数据
      points.forEach((point, index) => {
        const graphic = new Graphic({
          geometry: new Point({
            longitude: point.longitude,
            latitude: point.latitude
          }),
          attributes: {
            ObjectID: index,
            intensity: point.intensity
          }
        });
        graphicsLayer.add(graphic);
      });

      // 创建新的要素图层
      const featureLayer = new FeatureLayer({
        source: graphicsLayer.graphics,
        objectIdField: "ObjectID",
        fields: [
          {
            name: "ObjectID",
            alias: "ObjectID",
            type: "oid"
          },
          {
            name: "intensity",
            alias: "Intensity",
            type: "double"
          }
        ],
        renderer: {
          type: "heatmap",
          field: "intensity",
          blurRadius: 10,
          maxPixelIntensity: 100,
          minPixelIntensity: 0,
          colorStops: [
            { ratio: 0, color: "rgba(0, 0, 255, 0)" },
            { ratio: 0.2, color: "rgba(0, 0, 255, 0.7)" },
            { ratio: 0.5, color: "rgba(0, 255, 255, 0.7)" },
            { ratio: 0.8, color: "rgba(255, 255, 0, 0.7)" },
            { ratio: 1, color: "rgba(255, 0, 0, 0.7)" }
          ]
        }
      });

      // 添加新图层到地图
      this.map.add(featureLayer);
      this.currentFeatureLayer = featureLayer;

      // 如果有数据点，缩放到数据范围
      // if (graphicsLayer.graphics.length > 0) {
      //   this.view.goTo({
      //     target: graphicsLayer.graphics,
      //     zoom: 11
      //   });
      // }
    },
  }
}
</script>

<style scoped>
.person-info-container {
  display: flex;
  width: 1800px;
  height: 1400px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(16, 28, 63, 0.98);
  position: absolute;
  top: 200px;
  left: 1000px;
  border: 2px solid rgba(18, 142, 232, 0.5) !important;
}

.left-panel {
  width: 400px;
  padding: 30px;
  background: rgba(16, 28, 63, 0.98) ;
  display: flex;
  flex-direction: column;
}

.avatar-section {
  text-align: center;
  margin-bottom: 60px;
  margin-top: 30px;
}

.avatar-img {
  width: 200px;
  height: 200px;
  border-radius: 100px;
  object-fit: cover;
}

.info-section {
  flex: 1;
}

.info-title {
  font-size: 32px;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 30px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
  font-size: 28px;
  color: #ffffff;
}

.info-icon {
  width: 28px;
  height: 28px;
  margin-right: 15px;
  background-size: contain;
}

.info-label {
  width: 140px;
}

.info-value {
  color: #ffffff;
}

.button-section {
  margin-top: 30px;
}

.comm-btn {
  width: 100%;
  height: 60px;
  margin-bottom: 20px;
  border: none;
  border-radius: 8px;
  color: #fff;
  font-size: 28px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-btn {
  background: #409EFF;
}

.voice-btn {
  background: #67C23A;
}

.btn-icon {
  width: 28px;
  height: 28px;
  margin-right: 10px;
  background-size: contain;
}

.right-panel {
flex: 1;
display: flex;
flex-direction: column;
position: relative;
padding: 0px 40px 40px 40px; /* 增加右边和下边的内边距 */
background-color: rgba(16, 28, 63, 0.98);
}

.top-toolbar {
height: 110px;
padding: 0 40px;
display: flex;
justify-content: space-between;
align-items: center;
background: rgba(16, 28, 63, 0.98);
}

.date-picker-wrapper {
  flex: 1;
  max-width: 800px;
}

.date-picker-wrapper :deep(.el-date-editor.el-input) {
  width: 500px !important;
}

.date-picker-wrapper :deep(.el-input__inner) {
  width: 500px !important;
  height: 50px;
  line-height: 50px;
  font-size: 32px;
  color: #00d4ff !important;
  font-weight: bold;
  background-color: rgba(16, 28, 63, 0.98);
  border: 1px solid rgba(0, 212, 255, 0.3);
}

.date-picker-wrapper :deep(.el-range-separator) {
  font-size: 32px;
  line-height: 50px;
  color: #00d4ff !important;
  font-weight: bold;
}

.date-picker-wrapper :deep(.el-range-input) {
  font-size: 32px;
  color: #00d4ff !important;
  font-weight: bold;
  background-color: transparent;
}

/* 日历面板样式 */
.date-picker-wrapper :deep(.el-picker-panel) {
  min-width: 700px !important;
  padding: 20px 28px 18px 28px !important;
  border-radius: 12px !important;
  font-size: 20px !important;
}

.date-picker-wrapper :deep(.el-picker-panel .el-date-range-picker__header) {
  font-size: 28px !important;
  margin-bottom: 12px !important;
  letter-spacing: 2px;
}

.date-picker-wrapper :deep(.el-picker-panel .el-date-table th),
.date-picker-wrapper :deep(.el-picker-panel .el-date-table td) {
  font-size: 18px !important;
  height: 44px !important;
  min-width: 44px !important;
  padding: 0 !important;
}

.date-picker-wrapper :deep(.el-picker-panel .el-date-table td span) {
  width: 38px !important;
  height: 38px !important;
  line-height: 38px !important;
  display: inline-block !important;
  text-align: center !important;
  border-radius: 50% !important;
  font-size: 18px !important;
  margin: 0 auto !important;
}

.date-picker-wrapper :deep(.el-picker-panel .el-date-table td) {
  padding: 2px 0 !important;
}

.date-picker-wrapper :deep(.el-picker-panel .el-date-table td.in-range div) {
  background: rgba(0, 212, 255, 0.13) !important;
  border-radius: 18px !important;
}

.date-picker-wrapper :deep(.el-picker-panel .el-date-table td.start-date div),
.date-picker-wrapper :deep(.el-picker-panel .el-date-table td.end-date div) {
  background: #00d4ff !important;
  color: #000 !important;
  border-radius: 50% !important;
}

.date-picker-wrapper :deep(.el-picker-panel .el-date-table) {
  border-spacing: 0 4px !important;
}

.date-picker-wrapper :deep(.el-picker-panel .el-date-table th) {
  padding-bottom: 6px !important;
  font-size: 18px !important;
  letter-spacing: 1px;
}

.close-btn {
width: 60px;
height: 60px;
line-height: 50px;
text-align: center;
background: rgba(0, 0, 0, 0.2);
color: #fff;
font-size: 50px;
border-radius: 25px;
cursor: pointer;
margin-right: -45px;
}

.map-container {
flex: 1;
width: 100%;
border-radius: 8px; /* 可选：增加圆角 */
overflow: hidden; /* 确保圆角生效 */
box-shadow: 0 0 10px rgba(16, 28, 63, 0.98);
border: 2px solid rgba(18, 142, 232, 0.5) !important;
}  
/* 图标样式 */
.user-icon { background-image: url('@/assets/zqyzt/windowInfo/fulb.png'); }
.gender-icon { background-image: url('@/assets/zqyzt/windowInfo/fulb.png'); }
.dept-icon { background-image: url('@/assets/zqyzt/windowInfo/fulb.png'); }
.phone-icon { background-image: url('@/assets/zqyzt/windowInfo/fulb.png'); }
.video-call-icon { background-image: url('@/assets/zqyzt/windowInfo/fulb.png'); }
.voice-call-icon { background-image: url('@/assets/zqyzt/windowInfo/fulb.png'); }

</style>