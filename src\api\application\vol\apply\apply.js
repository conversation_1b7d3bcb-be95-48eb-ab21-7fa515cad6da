import {request} from '@/utils/request'

// 查询志愿者申请列表
export function listApply(query) {
  return request({
    url: '/business/vol/apply/list',
    method: 'get',
    params: query
  })
}

// 查询志愿者申请详细
export function getApply(id) {
  return request({
    url: '/business/vol/apply/' + id,
    method: 'get'
  })
}

// 新增志愿者申请
export function addApply(data) {
  return request({
    url: '/business/vol/apply/add',
    method: 'post',
    data: data
  })
}

// 修改志愿者申请
export function updateApply(data) {
  return request({
    url: '/business/vol/apply/edit',
    method: 'post',
    data: data
  })
}

// 修改志愿者申请
export function approvalApply(data) {
  return request({
    url: '/business/vol/apply/approval',
    method: 'post',
    data: data
  })
}

// 删除志愿者申请
export function delApply(id) {
  return request({
    url: '/business/vol/apply/remove/' + id,
    method: 'post'
  })
}

// 导出志愿者申请
export function exportApply(query) {
  return request({
    url: '/business/vol/apply/export',
    method: 'get',
    params: query
  })
}
