<template>
  <el-dialog :close-on-click-modal="false" v-bind="$attrs" title="选择店铺" width="1000px" v-on="$listeners" @close="onClose">
    <el-row :gutter="20">
      <el-form ref="queryForm" :model="queryParams" :inline="true">
        <el-col :span="1.5">
          <el-form-item label="项目名称">
            <el-input v-model="queryParams.searchValue" size="small" />
          </el-form-item>
        </el-col>
        <el-col :span="1.5">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="getList">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
    <el-table
      ref="shopTable"
      v-loading="loading"
      class="m-table"
      :data="shopList"
      height="55vh"
      @selection-change="handleSelection"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="店铺名称" align="center" prop="shopName" width="200" show-overflow-tooltip />
      <el-table-column label="联系人" align="center" prop="contactsName" width="100" />
      <el-table-column label="联系电话" align="center" prop="contactsTelephone" width="150" show-overflow-tooltip />
      <el-table-column label="详细地址" align="center" prop="address" show-overflow-tooltip />
    </el-table>
    <!-- 确认按钮 -->
    <div slot="footer" class="dialog-footer">
      <el-button :loading="loading" @click="close">取 消 </el-button>
      <el-button type="primary" :loading="loading" @click="confirm">保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
import shop from '@/api/case/synthetical/shop'

export default {
  name: 'ShopList',
  props: {
    defaultSelection: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      shopList: [],
      queryParams: {},
      listData: [],
      checkRows: []
    }
  },
  watch: {
    '$attrs.visible'(nVal) {
      if (nVal) {
        this.getList()
      }
    }
  },
  methods: {
    onClose() {
      this.queryParams = {}
      this.checkRow = {}
    },
    getList() {
      this.loading = true
      shop.list(this.queryParams).then(response => {
        this.shopList = response.rows
        this.loading = false
        this.$nextTick(() => {
          if (this.defaultSelection) {
            const ids = this.defaultSelection.split(',')
            response.rows.forEach(item => {
              if (ids.includes(`${item.shopId}`)) {
                console.log(item)
                this.$refs.shopTable.toggleRowSelection(item, true)
              }
            })
          }
        })
      }).catch(() => {
        this.loading = false
      })
    },
    handleSelection(selection) {
      this.checkRows = selection
    },
    resetQuery() {
      this.queryParams = {}
      this.resetForm('queryForm')
      this.getList()
    },
    close() {
      this.$emit('update:visible', false)
    },
    confirm() {
      this.$emit('confirm', this.checkRows)
      this.close()
    }
  }
}
</script>

<style scoped lang="scss">
.m-table {
  ::v-deep {
    .tb-cell {
      // width: 150;
      display: inline-flex;
      &-text {
        margin-left: 10px;
        width: 160px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}

</style>
