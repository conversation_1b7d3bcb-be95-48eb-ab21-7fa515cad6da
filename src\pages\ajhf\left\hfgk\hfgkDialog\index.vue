<template>
  <ygfDialog :visible='visible' width='1415px'>
    <div id="ndjhs" class="rwgz-tc">
      <div class="rw-title flex-between">
        <div class="fs-44 text-mid-yellow dialogTitle" id="rwTitle" style="margin-left: 20px;">{{ dialogTitle }}</div>
        <div class="close cursor" @click="close" style="margin-right: 20px;"></div>
      </div>
      <div class="content">
        <div class="table" v-show="dialogTitle == '案件总数'">
          <div class="table-line title-line">
            <div class="table-column table-title" style="flex: 1;margin-left: 30px">序号</div>
            <div class="table-column table-title" style="flex: 2">区县</div>
            <div class="table-column table-title" style="flex: 3">案件数</div>
            <div class="table-column table-title" style="flex: 3">已回访案件数</div>
            <div class="table-column table-title" style="flex: 3">回访中案件数</div>
            <div class="table-column table-title" style="flex: 3">未回访案件数</div>
            <div class="table-column table-title" style="flex: 3">无需回访案件数</div>
          </div>
          <div class="table-container">
            <div class="table-line">
              <div class="table-column" style="flex: 1"></div>
              <div class="table-column" style="flex: 2;margin-left: 30px">合计</div>
              <div class="table-column" style="flex: 3" @click="showAnjianList(city,'案件数','')">{{ajzs.ajshj}}</div>
              <div class="table-column" style="flex: 3" @click="showAnjianList(city,'已回访案件','2')">{{ajzs.yhfajshj}}</div>
              <div class="table-column" style="flex: 3" @click="showAnjianList(city,'回访中案件','3')">{{ajzs.hfzajshj}}</div>
              <div class="table-column" style="flex: 3" @click="showAnjianList(city,'未回访案件','1')">{{ajzs.whfajshj}}</div>
              <div class="table-column" style="flex: 3" @click="showAnjianList(city,'无需回访案件','0')">{{ajzs.wxhfajshj}}</div>
            </div>
            <div class="table-line" v-for="(item,i) in tableData" :key="i" :class="{activeTableLine:i % 2 == 0}">
              <div class="table-column" style="flex: 1;margin-left: 30px">{{i + 1}}</div>
              <div class="table-column" style="flex: 2" :title="item.region">{{item.region}}</div>
              <div class="table-column" style="flex: 3" @click="showAnjianList(item.region,'案件数','')">{{item.ajTotal}}</div>
              <div class="table-column" style="flex: 3" @click="showAnjianList(item.region,'已回访案件','2')">{{item.hfTotal}}</div>
              <div class="table-column" style="flex: 3" @click="showAnjianList(item.region,'回访中案件','3')">{{item.hfz}}</div>
              <div class="table-column" style="flex: 3" @click="showAnjianList(item.region,'未回访案件','1')">{{item.wfh}}</div>
              <div class="table-column" style="flex: 3" @click="showAnjianList(item.region,'无需回访案件','0')">{{item.wxhfs}}</div>
            </div>
          </div>
        </div>

        <div class="table" v-show="dialogTitle == '回访总数'">
          <div class="table-line title-line">
            <div class="table-column table-title" style="flex: 1;margin-left: 30px">序号</div>
            <div class="table-column table-title" style="flex: 3">区县</div>
            <div class="table-column table-title" style="flex: 2">回访数</div>
          </div>
          <div class="table-container">
            <div class="table-line">
              <div class="table-column" style="flex: 1;margin-left: 30px"></div>
              <div class="table-column" style="flex: 3">合计</div>
              <div class="table-column" style="flex: 2" @click="showAnjianList(city,'已回访案件','2')">{{hfzs.hfshj}}</div>
            </div>
            <div class="table-line" v-for="(item,i) in tableData" :key="i" :class="{activeTableLine:i % 2 == 0}">
              <div class="table-column" style="flex: 1;margin-left: 30px">{{i + 1}}</div>
              <div class="table-column" style="flex: 3" :title="item.region">{{item.region}}</div>
              <div class="table-column" style="flex: 2" @click="showAnjianList(item.region,'已回访案件','2')">{{item.hfTotal}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ygfDialog>
</template>

<script>
import ygfDialog from '@/components/ygfDialog'
import { gethfgkList } from '@/api/ajhf'
export default {
  name: 'index',
  props: ['dialogTitle','visible'],
  components: {
    ygfDialog
  },
  data() {
    return {
      tableData: [],
      ajzs: {
        ajshj: 0,
        yhfajshj: 0,
        hfzajshj: 0,
        whfajshj: 0,
      },
      hfzs: {
        hfshj: 0,
      }
    }
  },
  computed: {
    city() {
      return localStorage.getItem("city")
    }
  },
  mounted() {
    // 监听城市变化
    this.$bus.$on('cityChange', (city) => {
      this.getDetail();
    });
    // 监听年份变化
    this.$bus.$on('yearChange', (year) => {
      this.getDetail();
    });
    this.getDetail()
  },
  methods: {
    //获取数据
    getDetail() {
      gethfgkList({region:localStorage.getItem("city")}).then(res => {
        this.tableData = res.data?res.data:[];
        this.ajzs = {
          ajshj: 0,
          yhfajshj: 0,
          hfzajshj: 0,
          whfajshj: 0,
          wxhfajshj: 0
        };
        this.hfzs = {
          hfshj: 0,
        };
        this.tableData.forEach((item,i) => {
          this.ajzs.ajshj += Number(item.ajTotal)
          this.ajzs.yhfajshj += Number(item.hfTotal)
          this.ajzs.hfzajshj += Number(item.hfz)
          this.ajzs.whfajshj += Number(item.wfh)
          this.ajzs.wxhfajshj += Number(item.wxhfs)

          this.hfzs.hfshj += Number(item.hfTotal)
        });
      })
    },
    //打开案件列表弹窗
    showAnjianList(region,typeName,typeValue) {
      this.$emit('openAjListDialog',{
        nationRegionName: region === "金华市"?"":region,
        followStatus: typeValue,
        dialogTitle: typeName,
        type: this.dialogTitle == "案件总数"?"1":"2"
      })
    },
    close() {
      this.$emit('close')
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.getDetail();
      }
    }
  }
}
</script>

<style scoped lang='less'>

.search-box {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

::-webkit-scrollbar {
  display: none;
}


ul,
ul li {
  list-style: none;
}

.rwgz-tc {
  width: 1515px;
  height: 1186px;
  background: url("@/assets/zhdd/dialogBg2.png") no-repeat;
  background-size: 100% 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-direction: column;
}

.rw-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 46px 3% 0;
  width: 95%;
  height: 60px;
  line-height: 60px;
}

.close {
  background: url("@/assets/zhdd/close.png") no-repeat;
  width: 34px;
  height: 34px;
}

.ql-indent-1 img {
  width: 100%;
}

img {
  width: 100%;
}

.content {
  height: 910px;
  margin-top: 10px;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  flex-direction: column;
}

::-webkit-scrollbar {
  width: 0;
  height: 0;
}

.scrollTab {
  width:928px;
  height:70px;
}

.table-container {
  height: 960px;
  overflow-y: scroll;
}

.tableContainer2 {
  height: 400px;
  overflow-y: scroll;
}

.table-line {
  width: 1429px;
  height: 80px;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  background: rgba(50,134,248,0.15);
}

.title-line {
  background: transparent !important;
}

.table-title {
  font-size: 32px;
  font-family: Source Han Sans CN;
  font-weight: bold;
  color: #FFFFFF;
  text-align: left;
}

.table-column {
  font-size: 32px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  overflow: hidden;
  text-overflow: ellipsis;
}
.activeTableLine {
  background: rgba(50,134,248,0.25);
}

/deep/ .el-input {
  /*position: absolute;*/
  /*top: 229px;*/
  /*left: 1630px;*/
  width: 537px;
  height: 72px;
}

/deep/ .el-input__icon {
  height: 100%;
  width: 25px;
  text-align: center;
  -webkit-transition: all .3s;
  transition: all .3s;
  line-height: 75px;
}

/deep/ .el-scrollbar {
  overflow: hidden;
  /*position: relative;*/
  height: 500px;
  background: #020b28;
  border-radius: 10px;
}

/deep/ .el-autocomplete-suggestion__wrap {
  max-height: unset !important;
  padding: 10px 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

/deep/ .el-input--suffix .el-input__inner {
  border: 1px solid #359CF8;
  border-radius: 8px;
  padding-right: 30px;
  height: 70px;
  font-family: MicrosoftYaHei;
  font-size: 28px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 1px;
  color: #bbe5fd !important;
  background: #020b28;
}

/deep/ .el-input.is-active .el-input__inner,
/deep/ .el-input__inner:focus {
  border: 1px solid #bbe5fd;
  outline: 0;
}

/deep/ .el-input__suffix-inner {
  pointer-events: all;
  font-size: 28px;
  margin: 15px 20px 0 0;
  color: #bbe5fd !important;
}

/deep/ .el-autocomplete-suggestion li {
  padding: 0 20px;
  line-height: 34px;
  cursor: pointer;
  color: #bbe5fd;
  font-size: 28px;
  list-style: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin: 10px 0 25px 0;
}

/deep/ .el-autocomplete-suggestion li:hover {
  background: unset !important;
}

.search {
  width: 100px;
  height: 65px;
  line-height: 65px;
  text-align: center;
  background: #0A619E;
  border: 1px solid #359CF8;
  border-radius: 8px;
  font-size: 28px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FEFEFE;
  margin-left: 10px;
  cursor: pointer;
}

.tab-con-active {
  background: url('@/assets/zfts/tab-active.png') no-repeat;
  background-size: 110% 100%;
}

.toolBar {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tab-con {
  width: 351px;
  height: 70px;
  line-height: 70px;
  color: #fff;
  font-size: 32px;
}

.s-flex {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-evenly;
}

.tab-item {
  font-size: 36px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  font-style: italic;
  color: rgba(171, 206, 239, 0.7);
  line-height: 59px;
}

.tabConActive {
  background: url('@/assets/zfts/tab-active.png') no-repeat;
  background-size: 110% 100%;
  font-family: Source Han Sans CN;
  font-weight: bold;
  font-size: 36px;
  color: #ffffff;
}
</style>