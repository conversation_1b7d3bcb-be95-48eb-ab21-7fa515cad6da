<template>
  <div class='right'>
    <zfba></zfba>
    <zfxt></zfxt>
<!--    <jczlxt></jczlxt>-->
    <ajhf></ajhf>
  </div>
</template>

<script>
import zfba from './zfba'
import zfxt from './zfxt'
import jczlxt from './jczlxt'
import ajhf from './ajhf'
export default {
  name: 'index',
  components: {
    zfba,
    zfxt,
    jczlxt,
    ajhf
  },
  data() {
    return {}
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped>

</style>