import {request} from '@/utils/request'

// 查询智慧城管人行道违停列表
export function listSidewalk(query) {
  return request({
    url: '/business/sidewalk/list',
    method: 'get',
    params: query
  })
}

// 查询智慧城管人行道违停详细
export function getSidewalk(id) {
  return request({
    url: '/business/sidewalk/' + id,
    method: 'get'
  })
}

// 新增智慧城管人行道违停
export function addSidewalk(data) {
  return request({
    url: '/business/sidewalk/add',
    method: 'post',
    data: data
  })
}

// 修改智慧城管人行道违停
export function updateSidewalk(data) {
  return request({
    url: '/business/sidewalk/edit',
    method: 'post',
    data: data
  })
}

// 删除智慧城管人行道违停
export function delSidewalk(id) {
  return request({
    url: '/business/sidewalk/remove/' + id,
    method: 'post'
  })
}

// 导出智慧城管人行道违停
export function exportSidewalk(query) {
  return request({
    url: '/business/sidewalk/export',
    method: 'get',
    params: query
  })
}

// 导出智慧城管人行道违停
export function asyncListByCode() {
  return request({
    url: '/business/sidewalk/listByCode',
    method: 'get'
  })
}

