import {request} from '@/utils/request'

// 查询违规记录列表
export function listRecord(query) {
  return request({
    url: '/business/violation/record/list',
    method: 'get',
    params: query
  })
}

// 查询违规记录详细
export function getRecord(violationId) {
  return request({
    url: '/business/violation/record/' + violationId,
    method: 'get'
  })
}

// 新增违规记录
export function addRecord(data) {
  return request({
    url: '/business/violation/violation/record/add',
    method: 'post',
    data: data
  })
}

// 修改违规记录
export function updateRecord(data) {
  return request({
    url: '/business/violation/record/edit',
    method: 'post',
    data: data
  })
}

// 删除违规记录
export function delRecord(violationId) {
  return request({
    url: '/business/violation/record/remove/' + violationId,
    method: 'post'
  })
}

// 导出违规记录
export function exportRecord(query) {
  return request({
    url: '/business/violation/record/export',
    method: 'get',
    params: query
  })
}

// 查询试卷详细
export function getExamAnswer(params) {
  return request({
    url: 'business/violation/record/infoByCase',
    method: 'get',
    params
  })
}

