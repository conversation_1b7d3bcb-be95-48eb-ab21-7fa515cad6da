import {request} from '@/utils/request'

// 查询积分记录列表
export function listRecord(query) {
  return request({
    url: '/business/oa/score/record/list',
    method: 'get',
    params: query
  })
}

// 查询时常记录
export function listLearnRecord(query) {
  return request({
    url: '/business/learn/record/list',
    method: 'get',
    params: query
  })
}

// 查询积分记录详细
export function getRecord(id) {
  return request({
    url: '/business/oa/score/record/' + id,
    method: 'get'
  })
}

// 新增积分记录
export function addRecord(data) {
  return request({
    url: '/business/oa/score/record/add',
    method: 'post',
    data: data
  })
}

// 修改积分记录
export function updateRecord(data) {
  return request({
    url: '/business/oa/score/record/edit',
    method: 'post',
    data: data
  })
}

// 删除积分记录
export function delRecord(id) {
  return request({
    url: '/business/oa/score/record/remove/' + id,
    method: 'post'
  })
}

// 导出积分记录
export function exportRecord(query) {
  return request({
    url: '/business/oa/score/record/export',
    method: 'get',
    params: query
  })
}

// 积分学习时长统计
export function countRecord(query) {
  return request({
    url: '/business/oa/score/record/count',
    method: 'get',
    params: query
  })
}
