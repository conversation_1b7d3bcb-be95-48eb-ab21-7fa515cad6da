<template>
  <div>
    <CommonTitle text='涉企行政检查'></CommonTitle>
    <div class="ptzy_box">
      <div class="ptzy_item" v-for="(item,index) in sqxzjcData">
        <div class='ptzy_item_img' :style="{background: 'url(' + item.icon + ') no-repeat'}" />
        <div style="margin-top: 20px;margin-left: 30px;">
          <div class="item_name">{{item.label}}</div>
          <div class="item_bottom">
              <span
                style='font-size: 52px'
                class="xt_font s-yellow"
              >{{item.number}}</span>
            <span
              style='margin-left: 10px;'
              class="s-font-25 s-yellow"
            >{{item.unit}}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import countTo from 'vue-count-to'
import CommonTitle from '@/components/CommonTitle'
export default {
  name: 'index',
  components: {
    CommonTitle,
    countTo
  },
  data() {
    return {
      sqxzjcData: [
        {
          label:"涉企检查户次",
          number:"3965",
          unit: "次",
          icon: require("@/assets/zfts/涉企检查户次.png")
        },
        {
          label:"减少企业干扰户",
          number:"640",
          unit: "次",
          icon: require("@/assets/zfts/减少企业干扰户.png")
        },
        {
          label:"涉企“综合查一次”户次",
          number:"2526",
          unit: "次",
          icon: require("@/assets/zfts/涉企“综合查一次”户次.png")
        },
        {
          label:"涉企“综合查一次”实施率",
          number:"63.7",
          unit: "%",
          icon: require("@/assets/zfts/涉企“综合查一次”实施率.png")
        }
      ]
    }
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang='less'>
.ptzy_box {
  width: 1000px;
  height: 447px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-evenly;
  align-items: center;
}
.ptzy_item {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.ptzy_item_img {
  width: 129px;
  height: 115px;
  background-size: 100% 100%;
}
.item_name {
  width: 200px;
  height: auto;
  font-size: 32px;
  color: #d1d6df;
  margin-top: -10px;
}
.xt_font {
  font-family: DINCondensed;
  //font-style: italic;
}
.s-yellow {
  background: linear-gradient(to bottom, #ffeccb, #ffffff, #ffc460, #ffe2b0, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
}
.s-blue {
  background: linear-gradient(to bottom, #cbf2ff, #ffffff, #00c0ff, #80e0ff, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
}

</style>