<template>
  <ygfDialog :visible='visible' width='1250px'>
    <div id="zlxt" class="rwgz-tc">
      <div class="rw-title flex-between">
        <div class="fs-44 text-mid-yellow dialogTitle" id="rwTitle" style="margin-left: 20px;">{{name}}</div>
        <div class="close cursor" @click="close" style="margin-right: 20px;"></div>
      </div>
      <div class="content">
        <div class="toolBar">
          <div class="search-box">
            <el-autocomplete class="inline-input" suffix-icon="el-icon-search" v-model="searchInput"
                             :fetch-suggestions="querySearch" placeholder="请输入关键词" value-key="bm" :trigger-on-focus="false"
                             @select="handleSelect" @change="handleChange"></el-autocomplete>
            <div class="search" @click="handleChange">搜索</div>
          </div>
        </div>
        <div class="table">
          <div class="table-line title-line">
            <div class="table-column table-title" style="flex: 1;margin-left: 30px">序号</div>
            <div class="table-column table-title" style="flex: 3;margin-left: 30px">区域</div>
            <div class="table-column table-title" style="flex: 3">部门</div>
            <div class="table-column table-title" style="flex: 2">事件内容</div>
          </div>
          <div class="table-container">
            <div class="table-line" v-for="(item,i) in TableData" :key="i" :class="{activeTableLine:i % 2 == 0}">
              <div class="table-column" style="flex: 1;margin-left: 30px">{{i + 1}}</div>
              <div class="table-column" style="flex: 3" :title="item.area">{{item.area}}</div>
              <div class="table-column" style="flex: 3">{{item.bm}}</div>
              <div class="table-column" style="flex: 2" :title="item.sjmc">{{item.sjmc}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ygfDialog>
</template>

<script>
import ygfDialog from '@/components/ygfDialog'
import { indexApi } from '@/api/indexApi'
export default {
  name: 'index',
  props: ['name','time','visible'],
  components: {
    ygfDialog
  },
  data() {
    return {
      searchInput: "",
      TableData: []
    }
  },
  computed: {
    city() {
      return localStorage.getItem("city")
    }
  },
  mounted() {
    // 监听城市变化
    this.$bus.$on('cityChange', (city) => {
      this.city = city;
      this.getDetail(this.time)
    });
    // 监听年份变化
    this.$bus.$on('yearChange', (year) => {
      this.year = year;
      this.getDetail(this.time)
    });
    this.getDetail(this.time)
  },
  methods: {
    //获取数据
    getDetail(time) {
      indexApi("csdn_yjyp_cqbjs1",{area_name:this.city,cybm:this.searchInput,sj1:time[0],sj2:time[1]}).then(res => {
        console.log(res,"csdn_yjyp_cqbjs1");
        this.TableData = res.data.map(item => {return {
          area:item.qxwd,
          bm:item.cybm,
          sjmc:item.nr
        }})
      })
    },
    querySearch(queryString, cb) {
      var restaurants = this.TableData
      var results = queryString
        ? restaurants.filter(this.createFilter(queryString.replace(/\s*/g, '')))
        : restaurants
      // 调用 callback 返回建议列表的数据
      cb(results)
    },
    createFilter(queryString) {
      return (restaurant) => {
        return restaurant.bm.toLowerCase().indexOf(queryString.toLowerCase()) !== -1
      }
    },
    handleSelect() {
      this.getDetail(this.time)
    },
    handleChange() {
      this.getDetail(this.time)
    },
    close() {
      this.$emit('close')
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.getDetail(this.name,this.time)
      }
    }
  }
}
</script>

<style scoped lang='less'>

::-webkit-scrollbar {
  display: none;
}


ul,
ul li {
  list-style: none;
}

.rwgz-tc {
  width: 1387px;
  height: 860px;
  background: url("@/assets/zhdd/dialogBg.png") no-repeat;
  background-size: 100% 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-direction: column;
}

.rw-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 46px 3% 0;
  width: 95%;
  height: 60px;
  line-height: 60px;
}

.close {
  background: url("@/assets/zhdd/close.png") no-repeat;
  width: 34px;
  height: 34px;
}

.ql-indent-1 img {
  width: 100%;
}

img {
  width: 100%;
}

.content {
  height: 850px;
  margin-top: 10px;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  flex-direction: column;
}

::-webkit-scrollbar {
  width: 0;
  height: 0;
}

.scrollTab {
  width:928px;
  height:70px;
}

.table-container {
  height: 562px;
  overflow-y: scroll;
}

.tableContainer2 {
  height: 400px;
  overflow-y: scroll;
}

.table-line {
  width: 1296px;
  height: 80px;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  background: rgba(50,134,248,0.15);
}

.title-line {
  background: transparent !important;
}

.table-title {
  font-size: 32px;
  font-family: Source Han Sans CN;
  font-weight: bold;
  color: #FFFFFF;
  text-align: left;
}

.table-column {
  font-size: 32px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.activeTableLine {
  background: rgba(50,134,248,0.25);
}

/deep/ .el-input {
  /*position: absolute;*/
  /*top: 229px;*/
  /*left: 1630px;*/
  width: 537px;
  height: 72px;
}

/deep/ .el-input__icon {
  height: 100%;
  width: 25px;
  text-align: center;
  -webkit-transition: all .3s;
  transition: all .3s;
  line-height: 75px;
}

/deep/ .el-scrollbar {
  overflow: hidden;
  /*position: relative;*/
  height: 500px;
  background: #020b28;
  border-radius: 10px;
}

/deep/ .el-autocomplete-suggestion__wrap {
  max-height: unset !important;
  padding: 10px 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

/deep/ .el-input--suffix .el-input__inner {
  border: 1px solid #359CF8;
  border-radius: 8px;
  padding-right: 30px;
  height: 70px;
  font-family: MicrosoftYaHei;
  font-size: 28px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 1px;
  color: #bbe5fd !important;
  background: #020b28;
}

/deep/ .el-input.is-active .el-input__inner,
/deep/ .el-input__inner:focus {
  border: 1px solid #bbe5fd;
  outline: 0;
}

/deep/ .el-input__suffix-inner {
  pointer-events: all;
  font-size: 28px;
  margin: 15px 20px 0 0;
  color: #bbe5fd !important;
}

/deep/ .el-autocomplete-suggestion li {
  padding: 0 20px;
  line-height: 34px;
  cursor: pointer;
  color: #bbe5fd;
  font-size: 28px;
  list-style: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin: 10px 0 25px 0;
}

/deep/ .el-autocomplete-suggestion li:hover {
  background: unset !important;
}

.search-box {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.search {
  width: 100px;
  height: 65px;
  line-height: 65px;
  text-align: center;
  background: #0A619E;
  border: 1px solid #359CF8;
  border-radius: 8px;
  font-size: 28px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FEFEFE;
  margin-left: 10px;
  cursor: pointer;
}

.toolBar {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
</style>