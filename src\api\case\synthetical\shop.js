import {request} from '@/utils/request'
let shop = {
  list(params) {
    return request({
      url: '/business/shop/list',
      method: 'get',
      params
    })
  },
  oneList(params) {
    return request({
      url: '/business/shop/' + params,
      method: 'get'
    })
  },
  add(data) {
    return request({
      url: '/business/shop/add',
      method: 'post',
      data
    })
  },
  edit(data) {
    return request({
      url: '/business/shop/edit',
      method: 'post',
      data
    })
  },
  remove(data) {
    return request({
      url: '/business/shop/remove/' + data,
      method: 'post'
    })
  },
  xukeList(params) {
    return request({
      url: '/business/licensing/list',
      method: 'get',
      params
    })
  },
  xukeAdd(data) {
    return request({
      url: '/business/licensing/add',
      method: 'post',
      data
    })
  }
}

export default shop
