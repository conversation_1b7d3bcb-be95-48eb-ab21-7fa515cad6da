<template>
  <div>
    <CommonTitle text='综合查一次'></CommonTitle>
    <div class="zhcyc">
      <div
        class="quan"
        :class="`quan${index==0?2:index==2?0:index}`"
        v-for="(item,index) in zhcycData"
        :key="index"
        @click="showjcdxDialog(item)"
      >
        <li>{{item.name}}</li>
        <li :class="`txt${index}`">{{item.value}}{{item.unit}}</li>
      </div>
    </div>

    <zhcycDialog :name='name' :visible='dialogShow' @close='dialogShow=false'></zhcycDialog>
  </div>
</template>

<script>
import zhcycDialog from './zhcycDialog'
import CommonTitle from '@/components/CommonTitle'
import {indexApi} from '@/api/indexApi'
export default {
  name: 'index',
  components: {
    CommonTitle,
    zhcycDialog
  },
  data() {
    return {
      year: localStorage.getItem("year"),
      city: localStorage.getItem("city"),
      //综合查一次
      zhcycData: [],
      name:"",
      dialogShow: false
    }
  },
  computed: {},
  mounted() {
    this.$bus.$on('cityChange', (city) => {
      this.city = city;
      this.initApi(city,localStorage.getItem("year"));
    })
    this.$bus.$on('yearChange', (year) => {
      this.year = year;
      this.initApi(localStorage.getItem("city"),year);
    })
    this.initApi(localStorage.getItem("city"),localStorage.getItem("year"));
  },
  methods: {
    initApi(city,year) {
      //综合查一次
      indexApi("/csdn_yjyp1", { area_code: city,sjwd2: year }).then((res) => {
        this.zhcycData = res.data.map((a,i) => ({
          name: a.label.split("-")[1],
          value: a.num,
          unit: a.unit,
        }))
      });
    },
    showjcdxDialog(item) {
      this.name = item.name;
      this.dialogShow = true
    }
  },
  watch: {}
}
</script>

<style scoped lang='less'>
/* 综合查一次 */
.zhcyc {
  width: 100%;
  height: 460px;
  padding: 20px 60px;
  box-sizing: border-box;
  position: relative;
  background: url("@/assets/zfts/zhcyc-bg.png") no-repeat;
  background-size: 100% 100%;
}
.quan {
  cursor: pointer;
}

.quan0 {
  position: absolute;
  left: 339px;
  top: -10px;
  width: 308px;
  height: 260px;
  background: url("@/assets/zfts/zrw.png") no-repeat;
  background-size: 100% 100%;
  font-size: 36px;
  color: #fff;
  text-align: center;
  line-height: 60px;
  padding-top: 50px;
  box-sizing: border-box;
}

.quan1 {white-space: nowrap;
  position: absolute;
  left: 100px;
  top: 30px;
  width: 155px;
  height: 155px;
  background: url("@/assets/zfts/rwzb.png") no-repeat;
  background-size: 100% 100%;
  font-size: 28px;
  color: #fff;
  text-align: center;
  line-height: 40px;
  padding-top: 40px;
  box-sizing: border-box;
}

.quan2 {white-space: nowrap;
  position: absolute;
  right: 170px;
  top: 30px;
  width: 155px;
  height: 155px;
  background: url("@/assets/zfts/cybm.png") no-repeat;
  background-size: 100% 100%;
  font-size: 28px;
  color: #fff;
  text-align: center;
  line-height: 40px;
  padding-top: 40px;
  box-sizing: border-box;
}

.quan3 {
  position: absolute;
  left: 150px;
  top: 200px;
  width: 186px;
  height: 186px;
  background: url("@/assets/zfts/jchc.png") no-repeat;
  background-size: 100% 100%;
  font-size: 28px;
  color: #fff;
  text-align: center;
  line-height: 40px;
  padding-top: 50px;
  box-sizing: border-box;
}

.quan4 {
  position: absolute;
  right: 210px;
  top: 195px;
  width: 179px;
  height: 179px;
  background: url("@/assets/zfts/jsqygr.png") no-repeat;
  background-size: 100% 100%;
  font-size: 28px;
  color: #fff;
  text-align: center;
  line-height: 40px;padding: 50px 0px;
  box-sizing: border-box;
}

.quan5 {
  position: absolute;
  left: 400px;
  top: 254px;
  width: 197px;
  height: 197px;
  background: url("@/assets/zfts/rwaswc.png") no-repeat;
  background-size: 100% 100%;
  font-size: 28px;
  color: #fff;
  text-align: center;
  line-height: 40px;
  padding: 40px;
  box-sizing: border-box;
}

.txt0 {
  color: #eed252;
  font-size: 40px;
  font-weight: bold;
}

.txt1 {
  color: #08a0f5;
  font-size: 30px;
  font-weight: bold;
}

.txt2 {
  color: #d958de;
  font-size: 30px;
  font-weight: bold;
}

.txt3 {
  color: #00fffc;
  font-size: 30px;
  font-weight: bold;
}

.txt4 {
  color: #45f95e;
  font-size: 30px;
  font-weight: bold;
}

.txt5 {
  color: #ffb436;
  font-size: 30px;
  font-weight: bold;
}

.quan0 {
  animation: move infinite 5s;
}
.quan1 {
  animation: move infinite 5s 0.5s;
}
.quan2 {
  animation: move infinite 5s 1.2s;
}
.quan3 {
  animation: move infinite 5s 1s;
}
.quan4 {
  animation: move infinite 5s 0.5s;
}
@keyframes move {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(20px);
  }
  100% {
    transform: translateY(0px);
  }
}
</style>