<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-03-10 14:47:13
 * @LastEditors: wjb
 * @LastEditTime: 2025-03-11 08:42:20
-->
<template>
  <div class="left-Map">
    <sjtr></sjtr>
    <qsycfx></qsycfx>
  </div>
</template>

<script>
import sjtr from './sjtr'
import qsycfx from './qsycfx'
export default {
  name: 'index',
  components: { sjtr, qsycfx },
  data() {
    return {}
  },
  computed: {},
  mounted() {},
  methods: {},
  watch: {},
}
</script>

<style scoped>
</style>