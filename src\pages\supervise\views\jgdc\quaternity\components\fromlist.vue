<template>
  <div>
    <el-dialog :append-to-body= "true" class="m-dialog" :close-on-click-modal="false" v-bind="$attrs" width="900px" :title="title" v-on="$listeners" @close="onClose">
      <el-scrollbar v-loading="loading" style="height: 100%;" :element-loading-text="formLoadingText">
        <div style="margin-right: 10px;">
          <el-form ref="form" :model="form" :rules="rules" :disabled="formDisabled" label-width="100px">
            <el-row>
              <!-- <el-col :span="8">
                <el-form-item label="标题" prop="title">
                  <el-input v-model="form.title" placeholder="请输入标题" />
                </el-form-item>
              </el-col> -->
              <h3 class="title">
                <span>基本信息</span>
                <attentionBtn :case-id="form.fourId || 0" case-type="four" :case-content="form.content" />
              </h3>
              <el-col :span="12">
                <el-form-item label="上报人名称" prop="userName">
                  <el-input v-model="form.userName" placeholder="请选择上报人名称" readonly>
                    <!-- <el-button slot="append" type="primary" @click="userOpen = true">选择</el-button> -->
                  </el-input>
                  <!-- <userSelect id="userName" v-model="userOpen" :multiple="false" :select-user-keys="[parseInt(form.userId)]" :default-expanded-keys="[form.userId+'']" @confirm="userConfirm" /> -->
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系电话" prop="phone">
                  <el-input v-model="form.phone" :maxlength="11" oninput="value=value.replace(/[^\d]/g,'')" placeholder="请输入联系电话" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="案件类型" prop="type">
                  <el-select v-model="form.typeName" style="width: 100%;" placeholder="请选择案件类型" @change="selectOne">
                    <el-option
                      v-for="item in typeData"
                      :key="item.id"
                      :label="item.value"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="发生时间" prop="happenDate">
                  <div class="block">
                    <el-date-picker
                      v-model="form.happenDate"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      type="datetime"
                      placeholder="选择日期时间"
                      default-time="12:00:00"
                    />
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8" />
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="案件地址" prop="address">
                  <div>
                    <el-input v-model="form.address" placeholder="请选择案件地址">
                      <svg-icon slot="suffix" icon-class="map" class="svg-icon" @click="openMap = true" />
                    </el-input>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="案件内容" prop="content">
                  <el-input
                    v-model="form.content"
                    type="textarea"
                    placeholder="请输入案件内容"
                    maxlength="150"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="上传图片" prop="files">
                  <el-upload
                    ref="upload"
                    multiple
                    :limit="4"
                    list-type="picture-card"
                    class="upload-demo"
                    accept=".jpg,.jpeg,.png,.JPG,.JPEG,.PNG"
                    action="/zqzfj/system/file/upload"
                    :headers="headers"
                    :on-remove="handleRemove"
                    :on-error="handleError"
                    :on-preview="handlePictureCardPreview"
                    :on-success="handleSuccess"
                    :before-remove="beforeRemove"
                    :data="formData"
                    :file-list="formFiles"
                    :auto-upload="false"
                    name="files"
                    :on-exceed="exceed"
                    :on-change="onChange"
                  >
                    <i class="el-icon-plus" />
                  </el-upload>
                </el-form-item>
              </el-col>
            </el-row>
            <h3 class="title">处理情况</h3>
            <el-form-item label="处理人名称" prop="handleUserName">
              <el-input v-model="form.handleUserName" placeholder="请选择处理人名称" readonly>
                <el-button slot="append" type="primary" @click="userOpen1 = true">选择</el-button>
              </el-input>
              <userSelect id="handleUserName" v-model="userOpen1" :multiple="false" :select-user-keys="[form.handleUserId]" :default-expanded-keys="form.handleUserId?[form.handleUserId]:['100']" @confirm="confirm" />
            </el-form-item>
            <el-form-item v-if="form.handleDate" label="处理时间" prop="handleDate">
              <div class="block">
                <el-date-picker
                  v-model="form.handleDate"
                  type="datetime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="选择日期时间"
                  default-time="12:00:00"
                />
              </div>
            </el-form-item>
            <el-form-item v-if="form.handleContent" label="处理结果" prop="handleContent">
              <el-input
                v-model="form.handleContent"
                placeholder="请输入处理结果"
              />
            </el-form-item>
            <el-row v-if="formFiles2.length">
              <el-col :span="24">
                <el-form-item label="上传图片" prop="files">
                  <el-upload
                    ref="upload2"
                    multiple
                    :limit="4"
                    list-type="picture-card"
                    class="upload-demo"
                    accept=".jpg,.jpeg,.png,.JPG,.JPEG,.PNG"
                    action="/zqzfj/system/file/upload"
                    :headers="headers"
                    :on-remove="handleRemove"
                    :on-error="handleError"
                    :on-success="handleSuccess"
                    :on-preview="handlePictureCardPreview"
                    :data="formData2"
                    :file-list="formFiles2"
                    :before-remove="beforeRemove"
                    :auto-upload="false"
                    name="files"
                    :on-exceed="exceed"
                  >
                    <i class="el-icon-plus" />
                  </el-upload>
                </el-form-item>
              </el-col>
            </el-row>
            <tdtMap ref="bindmap" :visible.sync="openMap" append-to-body :map-search="true" :dw="form" @onlnglat="onlnglat" />
          </el-form>
          <el-form label-width="100px">
            <el-form-item v-if="handDisabled || form.completeDate" label="完成时间" prop="completeDate">
              <div class="block">
                <el-date-picker
                  v-model="form.completeDate"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  type="datetime"
                  placeholder="选择日期时间"
                  default-time="12:00:00"
                />
              </div>
            </el-form-item>
            <el-form-item v-if="handDisabled || form.rejectContent" label="驳回理由" prop="rejectContent">
              <el-input
                v-model="form.rejectContent"
                type="textarea"
                placeholder="请输入驳回理由"
              />
            </el-form-item>
          </el-form>
        </div>
      </el-scrollbar>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="handDisabled" type="danger" @click="handlePrimary">驳 回</el-button>
        <el-button v-if="handDisabled" type="success" @click="successPrimary">完 成</el-button>
        <el-button v-if="!formDisabled" type="primary" @click="primary(1)">确 定</el-button>
        <el-button @click="close">取 消 </el-button>
      </div>
    </el-dialog>
    <el-dialog :close-on-click-modal="false" :visible.sync="dialogVisible" append-to-body>
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
  </div>
</template>
<script>
import tdtMap from '@/components/tdtMap/tdtMap'
import {mapGetters} from 'vuex'
import userSelect from '@/components/userselect/index'
import { getFiles, removeFiles, editPolice, addPolice, revokePolice} from '@/api/supervise/swit'
import { getToken } from '@/utils/auth'
import { policeOne} from '@/api/supervise/swit'
import attentionBtn from '@/components/attentioneBtn/index.vue'

export default {
  name: 'Fromlist',
  components: {
    tdtMap,
    userSelect,
    attentionBtn
  },
  props: {
    userOptions: {
      type: Array,
      default() {
        return []
      }
    },
    typeData: {
      type: Array
    },
    title: String,
    detailId: Number,
    formDisabled: Boolean,
    handDisabled: Boolean
  },
  data() {
    return {
      headers: {Authorization: 'Bearer ' + getToken()},
      loading: true,
      formLoadingText: '数据上传中',
      formData: {businessId: null, status: 1, tableName: 'case_four_in_one'},
      formData2: {businessId: null, status: 2, tableName: 'case_four_in_one'},
      form: {handleUserId: '', handleUserName: '', userId: '', userName: '', files: []},
      // fileList: [],
      mapAdderssData: [],
      // 表单校验
      qb: true,
      userOpen: false,
      userOpen1: false,
      openMap: false,
      dialogImageUrl: '',
      dialogVisible: false,
      disabled: false,
      formFileAll: [],
      fileall: [],
      formFiles: [], // 文件
      formFiles2: [], // 文件
      fromQd: true,
      rules: {
        name: [
          { required: true, message: '请输入活动名称', trigger: 'blur' },
          { min: 3, max: 5, message: '长度在 3 到 5 个字符', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入案件内容', trigger: 'blur' }
        ],
        userName: [
          { required: true, message: '请选择上报人名称', trigger: 'change' }
        ],
        handleUserName: [
          { required: true, message: '请选择处理人名称', trigger: 'change' }
        ],
        phone: [
          {required: true, message: '号码不能为空', trigger: 'blur'}
          // {pattern: /^[1][3,4,5,7,8,9][0-9]{9}$/, message: '请输入正确的手机号'}
        ],
        happenDate: [
          {  required: true, message: '请选择发生时间', trigger: 'change' }
        ],
        type: [
          {  required: true, message: '请选择案件类型', trigger: 'change' }
        ],
        address: [
          { required: true, message: '请选择地址', trigger: 'change' }
        ]
      },
      open: ''
    }
  },
  computed: {
    ...mapGetters(['uid', 'name'])
  },
  watch: {
    '$attrs.visible'(nVal) {
      if (nVal && this.detailId) {
        this.getData()
      } else if (nVal) {
        this.form = { userId: this.$store.getters.uid, userName: this.$store.getters.nickName, happenDate: this.parseTime(new Date()) }
      }
    }
  },
  created() {
    // this.getData()
  },
  methods: {
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    getData() {
      this.loading = true
      this.formLoadingText = '数据加载中'
      let params = {businessId: this.detailId, tableName: 'case_four_in_one'}
      Promise.all([policeOne(this.detailId), this.getFile(params)]).then(res => {
        this.form = {...this.form, ...res[0].data}
        this.typeData.forEach(v => {
          if (v.id == this.form.type) this.form.typeName = v.value
        })
        this.loading = false
      })
    },
    getFile(params) {
      getFiles(params).then(res => {
        let filter = res.rows.reduce((obj, v) => {
          let url = {name: v.displayName, url: `/zqzfj${v.filePath}?id=${v.fileId}`, ...v}
          if (v.status == 1) {
            obj.formFiles.push(url)
          } else {
            console.log(v.status)
            obj.formFiles2.push(url)
          }
          return obj
        }, {formFiles: [], formFiles2: []})
        let {formFiles, formFiles2} = filter
        this.formFileAll = [...formFiles, ...formFiles2]
        this.formFiles = formFiles
        this.formFiles2 = formFiles2
      })
    },
    onlnglat(lenlat) {      // 经纬度  详细地址
      let {lng: longitude, lat: latitude, address} = lenlat
      this.form = {...this.form, longitude, latitude, address}
      this.openMap = false
    },
    binddrawMarker() {
      // this.$refs.bindmap.drawMarker()
    },
    inputs(e) {
      this.userOpen = e
      this.userOpen1 = e
    },
    userConfirm(e) {    // 上报人名称
      this.form = {...this.form, userName: e.name, userId: e.id}
    },
    confirm(e) {    // 处理人名称
      this.form = {...this.form, handleUserName: e.name, handleUserId: e.id}
    },
    selectOne(e) {    // 选择案件类型
      let data = this.typeData.filter(item => { return item.id == e })
      this.form.type = data[0].id
      this.form.typeName = data[0].value
    },
    // 上传函数
    beforeRemove(file) {
      return new Promise((resolve, reject) => {
        this.$confirm('是否确认删除？ 删除后将无法恢复', '提示', { type: 'warning' }).then(() => {
          if (file.fileId) {
            removeFiles(file.fileId).then(resolve).catch(reject)
          } else {
            resolve()
          }
        }).catch(() => {
          reject()
        })
      })
    },
    handleRemove() {   // 删除图片
    },
    exceed(files, filesList) {
      if (filesList.length >= 4) this.msgError('已超出最大上传数量')
    },
    handleSuccess(files) {    // 上传成功
      if (files.code == 200) {
        this.msgSuccess('上传成功')
        this.$emit('reLoad')
        this.close()
        this.loading = false
      } else {
        this.loading = false
        this.msgError('上传失败,请修改后重试')
      }
    },
    handleError() { // 上传失败
      this.loading = false
      this.msgError('上传失败,请修改后重试')
    },
    onChange(file, fileList) {
      console.log(fileList)
      this.fileall = fileList
    },
    // 上传
    uploads(data) {
      this.formLoadingText = '图片上传中'
      this.formData.businessId = data
      this.formData2.businessId = data
      this.$refs.upload.submit()
      if (this.formFiles2.length) this.$refs.upload2.submit()
    },
    // 确定
    primary(status) {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.form.status = status
          let params = {...this.form}
          if (!this.form.completeDate) params.completeDate = this.form.completeDate
          if (!this.$refs.upload.uploadFiles.length) return this.msgError('请上传违规图片')
          this.loading = true
          this.formLoadingText = '数据上传中'
          let api = params.fourId ? editPolice(params) : addPolice(params)
          api.then(res => {
            this.uploads(params.fourId ? params.fourId : res.data.fourId)
            this.qd = true
            this.open = false
            this.msgSuccess('操作成功')
            if (0 == this.$refs.upload.uploadFiles.length  - this.formFiles.length) {
              this.loading = false
              this.$emit('reLoad')
              this.close()
            }
          }).catch(() => {
            this.loading = false
            this.qd = true
          })
          this.openMap = false
        } else {
          return false
        }
      })
    },
    successPrimary() {
      this.$confirm('是否确认完成？ ', '提示', { type: 'warning' }).then(() => {
        if (!this.form.completeDate) return this.msgError('请输入完成时间')
        this.primary(3)
      }).catch(() => {
      })
    },
    handlePrimary() {
      this.$confirm('是否确认驳回？ ', '提示', { type: 'warning' }).then(() => {
        if (!this.form.rejectContent) return this.msgError('请输入驳回理由')
        const params = { fourId: this.form.fourId, rejectContent: this.form.rejectContent, status: 0 }
        this.formLoadingText = '数据上传中'
        this.loading = true
        revokePolice(params).then(() => {
          this.loading = false
          this.$emit('reLoad')
          this.close()
        }).catch(() => {
          this.loading = false
        })
      }).catch(() => {

      })

    },
    reset() {
      this.form = {}
      this.resetForm('form')
    },
    onClose() {
      this.reset()
      this.formFiles = []
      this.formLoadingText = '数据上传中'
    },
    close() {
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  margin-top: 20px;
  text-align: right;
}
.el-upload__tip {
  display: inline-block;
  margin-left: 5%;
}
.mapTable {
  height: 300px;
}
::v-deep .el-upload--picture-card,
::v-deep .el-upload-list--picture-card .el-upload-list__item {
  width: 100px;
  height: 100px;
  line-height: 100px;
}
.map {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10000;
  padding: 5vh 18px 5vh 0;
  background-color: rgba(0, 0, 0, 0.6);
}
.title {
  display: block;
  padding: 10px 0 10px 30px;
  margin-bottom: 20px;
  border-bottom: 1px solid #e4e7ec;
  color: #000;
}
.svg-icon {
  font-size: 25px;
  margin-left: 2px;
}
</style>
