<template>
  <div>
    <CommonTitle text="物联总览"></CommonTitle>
    <div class="zlList flex-a">
      <div class="zlItem" v-for="(item, i) in zlList" :key="i">
        <div class="num">{{ item.num }}</div>
        <div class="name">{{ item.name }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import { getSummary } from '@/api/wlwsb/index.js'
export default {
  name: 'index',
  components: {
    CommonTitle,
  },
  data() {
    return {
      zlList: [
        { name: '总设备数', num: 0 },
        { name: '在线数', num: 0 },
        { name: '离线数', num: 0 },
        { name: '故障数', num: 0 },
      ],
    }
  },
  computed: {},
  mounted() {
    this.getData()
  },
  methods: {
    getData() {
      getSummary().then((res) => {
        if (res.data) {
          this.zlList[0].num = res.data.totalDevices
          this.zlList[1].num = res.data.onlineDevices
          this.zlList[2].num = res.data.offlineDevices
          this.zlList[3].num = res.data.faultDevices
        }
      })
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
.zlList {
  margin: 100px 0 120px 0;
  .zlItem {
    width: 186px;
    height: 136px;
    background: url('@/assets/wlwsb/item-bg.png') no-repeat;
    background-size: 100% 100%;
    .num {
      font-family: MiSans, MiSans;
      font-weight: 500;
      font-size: 36px;
      color: #ffffff;
      text-align: center;
      margin-top: -26px;
      margin-bottom: 130px;
    }
    .name {
      font-family: Alimama ShuHeiTi, Alimama ShuHeiTi;
      font-weight: 700;
      font-size: 28px;
      color: #ffffff;
      text-align: center;
    }
  }
}
.flex-a {
  display: flex;
  justify-content: space-around;
}
</style>