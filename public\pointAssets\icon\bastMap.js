/*
 * @Description: file content
 * @Author: xufeng
 * @Date: 2022-04-26 10:40:01
 * @LastEditTime: 2022-05-25 14:37:26
 * @FilePath: \EGS(v1.0.0)\js\bastMap.js
 */

class BastMap  {
    // 弹窗
    constructor(egs,map){
        this.egs = egs;      // 地图类
        this.map = map;      // 地图对象
        this.popups = null;  // 弹窗
        this.addSouceLayers = {}; // 所有数据图层集合
        this.pointLayers = {};  // 带弹窗的点位集合
        this.heatMap = null; // 热力图层
        this.columnarThermodynamicDiagram = null; // 柱状热力图
        this.videoPointAggregationMap = null; // 聚合图
        this.circleLayerId = []; // 圆形layerid
        this.polygonLayerId = [];


        // 线缓冲数据查询对象
        this.lineStringBuffObj = null;
        // 3D文字对象
        this.text3DObj = {};
    }
    /**
     * @description: 初始化天空图层
     */ 
     addSkyLayer(){
        this.map.addLayer({
            id: "sky",
            type: "sky",
            paint: {
                "sky-type": "atmosphere",
                "sky-atmosphere-sun": [0.0, 0.0],
                "sky-atmosphere-sun-intensity": 15,
            },
        });
    }
    /**
     * @description: 基础图层切换，显示于影藏
     * @param {*} data:配置中的bastLayers:[], showID:初始地图显示的ID:string,
     */    
     creatBastLayer(data, showID){
        for (let i = 0; i < data.length; i++) {

            let layerConfig = {
                id: data[i].url,
                type:'raster',
                source: {
                    type: 'raster',
                    tiles: [ data[i].url ],  // 4326
                    tileSize: 256,
                    // zoomOffset: -1,
                    scheme: "xyz"
                },
            }
            if(data[i].id == showID){
                layerConfig.layout =  {
                    'visibility': 'visible'
                }
            }else {
                layerConfig.layout =  {
                    'visibility': 'none'
                }
            }
            this.map.addLayer(layerConfig);
        }
        // map.addLayer(
        //     {
        //         id: 'ArcGIS_WMTS_Map',
        //         type:'raster',
        //         source: {
        //             type: 'raster',
        //             tiles: ["http://222.73.139.7:8081/tileMap/services/MapServer/jh_test/tile/otherF/{z}/{y}/{x}"],  // 3857
        //             tileSize: 256,
        //             //zoomOffset: -1,
        //             scheme: "xyz"
        //         },
        //     },
        // );
    }

    /************************************************ 线缓冲查询 ********************************************************/    

    getRoadVideo(distance = '50',lngLats ='119.64600294828413,29.10509534934764;119.6462121605873,29.10573747284333'){
        let _this = this;
        var formData = new FormData();
        formData.append('distance',distance)
        formData.append('lngLats',lngLats)

        //return http.post(`http://10.24.161.200:8180/data_use/road/videos`,formData);
        // 显示图片视屏点位置
        // 添加点位置
        //const searchData = {"data":[{"channel_name":"GZ550011aGF1婺城浙师大东门_DH201912HS雪亮X","score":0.0,"gid":"e020352d4c9b4f3fb7226ce63c35b882","channel_code":"33070255001321084314","geom":"POINT (119.646872 29.138178)"},{"channel_name":"GZ550025aGM1浙师大东门_DH201912YD雪亮X","score":0.0,"gid":"4860e558b86244d4af870567916334c9","channel_code":"33070255001321084758","geom":"POINT (119.647074 29.138123)"},{"channel_name":"GJ583267aQM1迎宾大道高速出口球机_DH201912DX雪亮G","score":0.0,"gid":"4e3f766eb6fd4b92883beb72c1ab9dd8","channel_code":"33070299001321043766","geom":"POINT (119.647273 29.139431)"}],"total":3,"pages":1,"pageNum":1,"pageSize":500}
        axios({
			method: "post",
			url: "http://10.24.161.200:8180/data_use/road/videos",
			data: formData,
		}).then(searchData => {
			_this.secrchPoint(searchData)
		})    
        
    }
    secrchPoint(searchData){
        let _this = this;
        if(searchData.data && searchData.data.length){
            // 循环生成点位
            let geojsonData = {"type":"FeatureCollection", "features":[]}
            for (let i = 0; i < searchData.data.length; i++) {
                const element = searchData.data[i];
                let lonLat =  element.geom.slice(element.geom.indexOf("(")+1,element.geom.indexOf(")")) 
                let lonLatArr = lonLat.split(" ");
                let feauther = turf.point(lonLatArr, element); 
                geojsonData.features.push(feauther);
            }
            // 判断图标是否加载
            const imgUrl = "../image/spritesImage/videoCon1.png";
            // if(!this.map.getImage("searchImg")){
            this.map.loadImage(imgUrl, (error, image) => {
                if (error) throw error;
                this.map.addImage("searchImg", image);
                // 数据处理
                addSearchLayer(geojsonData)
                
            })
            // }
            function addSearchLayer(geojsonData) {
                // 创建UUid
                let uuid = _this.creatUuid(8,16);
                let searcgLayer = {
                    "id": uuid,
                    "type": "symbol",          
                    "source": {
                        'type': 'geojson',
                        'data': geojsonData,
                    },
                    "layout": {
                        'icon-image': 'searchImg', // reference the image
                        'icon-size': 1,
                        "icon-offset": [0, 0], 
                        "icon-allow-overlap": true,
                        //"text-allow-overlap": true
                    },
                }
                _this.map.addLayer(searcgLayer);
                _this.map.on("click", uuid, (e)=>{
                    searchLayerClick(e);
                }) 
                _this.map.on('mouseenter', uuid, ()=>{
                    _this.layerMouseenter();
                });
                _this.map.on('mouseleave', uuid, ()=>{
                    _this.layerMouseleave()
                });
                // 定位位置
                let jzData = turf.bbox(geojsonData);
                _this.map.fitBounds(jzData, {padding: 100});
            }
            function searchLayerClick(e){
                searchData
                e.features[0].properties.gid
            }
        }  
    }
    /**
     * @description: 添加3D文字
     * @return {*}
     */    
    text3D(textData){
        if(textData && textData.length){
            let geojsonData = {"type":"FeatureCollection", "features":[]}
            for (let i = 0; i < textData.length; i++) {
                const element = textData[i];
                let feauther = turf.point([element.pos[0], element.pos[1]],{text:element.text}) 
                geojsonData.features.push(feauther);
            }
            let uuid = this.creatUuid(8,16);
            let symbolLayer = {
                "id": uuid,
                "type": "symbol",          
                "source": {
                    'type': 'geojson',
                    'data': geojsonData
                },
                "layout": {
                    "text-field": "{text}", 
                    "text-font": [ 
                        "Open Sans Semibold,Arial Unicode MS Bold"
                    ], 
                    "text-size": 60,
                    "text-offset": [0, 0], 
                    "text-anchor": "top",
                    "text-allow-overlap": true,
                    
                },
                "paint": { 
                    "text-color": "#00f",
                    "text-halo-color": "#fff", 
                    "text-halo-width": 2,
                    "text-opacity": 1 
                }, 
            }
            this.text3DObj[uuid] = symbolLayer;
            this.map.addLayer(symbolLayer);
        }
    }
    /**
     * @description: 清除3D文字
     * @return {*}
     */    
    rm3DText(){
        if(this.text3DObj){
            for (const key in this.text3DObj) {
                if(this.map.getLayer(key)){
                    this.map.removeLayer(key)
                    this.map.removeSource(key)
                    delete this.pointLayers[key];
                }  
            }
        }
    }
    

    

    /**
     * @description: 图层切换
     * @param {*} data,{funcName:string, layerUrl:string}
     */    
     toggleBastLayer (data){
         let url = data.layerUrl
         if (data.layerUrl.indexOf('?startLevel=1') != -1) {
            url = url.Split('?')[0]
         }
        // 影藏图层
        for (let i = 0; i < mapConfig.bastLayers.length; i++) {
            this.map.setLayoutProperty(mapConfig.bastLayers[i].url, 'visibility', "none");
        }
        // 显示该图层 
        this.map.setLayoutProperty(url, 'visibility', "visible");
    }
    /**
     * @description: 创建UUid
     * @param {Number} len 位数
     * @param {Number} radix 进制
     * @return {*} uuid
     */
    creatUuid (len, radix) {
        var chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
        var uuid = [], i;
        radix = radix || chars.length;
    
        if (len) {
        // Compact form
        for (i = 0; i < len; i++) uuid[i] = chars[0 | Math.random()*radix];
        } else {
        // rfc4122, version 4 form
        var r;
    
        // rfc4122 requires these characters
        uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-';
        uuid[14] = '4';
    
        // Fill in random data.  At i==19 set the high bits of clock sequence as
        // per rfc4122, sec. 4.1.5
        for (i = 0; i < 36; i++) {
            if (!uuid[i]) {
            r = 0 | Math.random()*16;
            uuid[i] = chars[(i == 19) ? (r & 0x3) | 0x8 : r];
            }
        }
        }
    
        return uuid.join('');
    }
    /**
     * @description: 点位图层添加
     * @param {*} data: {funcName:string,pointType:点位类型图标 string,pointId:string,height:高度：number, pointData}
     */    
     addPointLayer (data){
        // 图片添加 
        let _this = this;
        let imgUrl = "./image/spritesImage/" + data.pointType +'.png';
        this.map.loadImage(imgUrl, (error, image) => {
            if (error) throw error;
            this.map.addImage("pointLoad", image);
            let uuid = this.creatUuid(8,16);
            data.pointId = data.pointId ? data.pointId : uuid;
            // 数据处理
            _this.addSource.call(_this,data)
            
        })
    }
    /**
     * @description: 添加数据
     */    
     addSource(data){
        let geojsonData = {"type":"FeatureCollection", "features":[]}
        let pointData = data.pointData;
        let pointId = data.pointId;
        for (let i = 0; i < pointData.length; i++) {
            const msg = pointData[i].data;
            const pointArr = pointData[i].point.split(",");
            let feature = turf.point(pointArr, { msg: msg });  
            geojsonData.features.push(feature);
        }
        // 添加点位图层
        if(!data.imageConfig){
            data.imageConfig = {}
        }
        let layer = {
            "id": pointId,
            "type": "symbol",          
            "source": {
                'type': 'geojson',
                'data': geojsonData,
                //"cluster": true,
                //"clusterRadius": 35 // 聚合半径
            },
            //"filter": ["==", "$type", "Point"],
            //"filter": ["in", "id", ""],
            "layout": {
                'icon-image': 'pointLoad', // reference the image
                'icon-size': data.imageConfig.iconSize ? data.imageConfig.iconSize: 1,
                "icon-offset": data.imageConfig.iconOffset ? data.imageConfig.iconOffset : [0,-30], 
                "icon-allow-overlap": data.imageConfig.iconAllowOverlap ? data.imageConfig.iconAllowOverlap :false,
                //"text-allow-overlap": true
            },
        }
        this.map.addLayer(layer);
        // this.map.off('click', pointId, this.destinationPoint)
        this.map.on("click", pointId, (e)=>{
            this.layerClick(e,data);
        }) 
        this.map.on('mouseenter', pointId, ()=>{
            this.layerMouseenter();
        });
        this.map.on('mouseleave', pointId, ()=>{
            this.layerMouseleave()
        });
        this.pointLayers[pointId] = layer;
        // 居中定位
        let jzData = turf.bbox(geojsonData);
        this.map.fitBounds(jzData, {padding: 100});
        // this.map.parent.postMessage(geojsonData, "*");
    }
    /**
     * @description: 图层点击
     */   
     layerClick (e,data){
        // 弹窗效果
        let coordinates = e.features[0].geometry.coordinates; // 当前点位数据
        let featureData = JSON.parse(e.features[0].properties.msg);
        // featureData = {"key":["名称","地址","电话"],"value":["王二","金华","1388888888"]}

        let keyArr = featureData.key;
        let valueArr = featureData.value;

        if(this.popups){
            this.popups.remove();
        }

        let htmlStr = "<div class='map-popup-point-click'>"
        for (let i = 0; i < keyArr.length; i++) {
            // 创建HTML节点
            htmlStr += "<li><span class='key'>"+keyArr[i]+":</span><span class='key'>"+valueArr[i]+"</span></li>"
        }
        htmlStr += "</div>"
        
        // 循环key value
        if(!data.popup){
            data.popup = {};
        }
        this.popups = new egs.eli.Popup(
            {
                content: htmlStr,
                isTooltip: data.popup.isTooltip ? data.popup.isTooltip: true,
                closeButton: data.popup.closeButton ? data.popup.closeButton: true,
                height: data.popup.height ? data.popup.height : 180,
                width: data.popup.width ? data.popup.width : 200,
                offset: data.popup.offset ? data.popup.offset : [0, -20],
                center: coordinates,
            },
            this.map
        )
    }
    /**
     * @description: 移除弹窗
     */
     removePopup(){
        if(this.popups){
            this.popups.remove();
        }
    }
    /**
     * @description: 点位图层删除
     */
     removePointLayer(data){
        // 弹窗删除
        if(this.popups){
            this.popups.remove();
        }
        // 图层删除
        if(data.pointId){
            if(this.map.getLayer(data.pointId)){
                // 事件删除
                this.map.off('click', data.pointId);
                this.map.off('mouseenter', data.pointId);
                this.map.off('mouseleave', data.pointId)
                // 图层删除
                this.map.removeLayer(data.pointId)
                this.map.removeSource(data.pointId)
                delete this.pointLayers[data.pointId];
            }
        }else{
            // 全删除
            for (const key in this.pointLayers) {
                if(this.map.getLayer(key)){
                    // 事件删除
                    this.map.off('click', key);
                    this.map.off('mouseenter', key);
                    this.map.off('mouseleave', key)
                    // 图层删除
                    this.map.removeLayer(key)
                    this.map.removeSource(key)
                    delete this.pointLayers[key];
                }  
            }
        }
    }
    /**
     * @description: 添加文字标注
     * @param {*}
     * @return {*}
     */    
    /**
     * @description: 图层移入
     */    
    layerMouseenter (){
        this.map.getCanvas().style.cursor = 'pointer';
    }
    /**
     * @description: 图层移出
     */ 
    layerMouseleave (){
        this.map.getCanvas().style.cursor = '';
    } 
    // 消息安全性检测
    checkMessage(){
        return true
    }
    /**
     * @description: 通过点定位
     * @param {*}
     * @return {*}
     */    

    flyToPoint(data){
        this.map.flyTo({
            center: [data.flyData.postion.x, data.flyData.postion.y],
            zoom: data.flyData.zoom? data.flyData.zoom: 16
        });
    }

    /**
     * @description: 加载热力图
     * @param {*}
     * @return {*}
     */
    getHeatMap (pointArray) {
        this.removeHeatMap();
        const data = {
            "type": "FeatureCollection",
            "crs": { "type": "name", "properties": { "name": "urn:ogc:def:crs:OGC:1.3:CRS84" } },
            "features": []
        }
        const length = pointArray.length;
        for (let i = 0; i < length; i++) {
            data.features.push(turf.point(pointArray[i],{ "id": i, "mag": 0.2}));
        }
        this.heatMap = this.map.createHeatmp(
            'heatMapDemo',
            data,
            { type: 'light',opacity: 0.8 }
        )
    }
    /**
     * @description: 删除热力图
     * @param {*}
     * @return {*}
     */
    removeHeatMap () {
        if (this.heatMap) {
            this.heatMap.remove();
            this.heatMap = null;
        }
    }

    /**
     * @description: 加载柱状图
     * @param {*}
     * @return {*}
     */
    getHistogram (array) {
        const regionalLocation = [
            {
                name: '浦江县',
                latLon: [119.94315399169922,29.5630503845215]
            },
            {
                name: '兰溪市',
                latLon: [119.46214447021484,29.28517738342285]
            },
            {
                name: '婺城区',
                latLon: [119.5569204711914,29.00677101135254]
            },
            {
                name: '金义新区',
                latLon: [119.8483056640625,29.188559951782227]
            },
            {
                name: '义乌市',
                latLon: [120.08206787109375,29.322123641967773]
            },
            {
                name: '武义县',
                latLon: [119.7269204711914,28.79677101135254]
            },
            {
                name: '永康市',
                latLon: [120.1469204711914,28.97677101135254]
            },
            {
                name: '东阳市',
                latLon: [120.4169204711914,29.24677101135254]
            },
            {
                name: '磐安县',
                latLon: [120.6299204711914,29.06677101135254]
            },
        ];
        const sum = array.reduce((prev,cur) => prev+cur.num ,0);
        const features = [];
        const heightArray = [];
        const length = array.length;
        for (let i = 0; i < length; i++) {
            const latLon = regionalLocation.filter(item => item.name == array[i].name)[0].latLon;
            features.push(turf.circle(latLon, 5,{steps: 500, units: 'kilometers', properties: {id: i+1}}));
            heightArray.push(Math.ceil((array[i].num * 100000) /sum))
        }
        this.map.addLayer({
            id: 'histogramLayer',
            source: {
                type: 'geojson',
                data: {
                    "type": "FeatureCollection",
                    "features": features
                }
            },
            type: "fill-extrusion",
            'paint': {
                'fill-extrusion-color': '#409EFF',
                'fill-extrusion-height': [
                    'at',
                    ["-", ["get", "id"], 1],
                    ["literal",heightArray]
                ],
                'fill-extrusion-base': 1,
                'fill-extrusion-opacity': 0.8
            }
        });
    }
    /**
     * @description: 移除柱状图
     * @param {*}
     * @return {*}
     */
    removeHistogram () {
        if (this.map.getLayer('histogramLayer')) {
            this.map.removeLayer('histogramLayer')
        }
    }
    /**
     * @description: 加载版块高亮
     * @param {*}
     * @return {*}
     */
    getSectionHighlight (regionalSectionItem) {
        const regionalSectionFeatureCollection = JSON.parse(JSON.stringify(regionalSection));
        regionalSectionFeatureCollection.features = regionalSectionFeatureCollection.features.filter(item => regionalSectionItem.some(items => item.properties.NAME == items.name));
        const fillExtrusionColor = ["match",["get","NAME"]];
        const length = regionalSectionItem.length
        for (let i = 0; i < length; i++ ) {
            const color = `rgba(${regionalSectionItem[i].color[0]},${regionalSectionItem[i].color[1]},${regionalSectionItem[i].color[2]},${regionalSectionItem[i].color[3]})`;
            fillExtrusionColor.push(regionalSectionItem[i].name)
            fillExtrusionColor.push(color)
        }
        fillExtrusionColor.push('rgba(64,158,255,0.8)')
        let featuress = []
        
        regionalSectionFeatureCollection.features.map(function(feature){
           let ss =  polygonToLineString(feature.geometry.coordinates,feature.properties);
           featuress.push(ss[0]);
        })
        let geojson = {
            type: "FeatureCollection",
            features: featuress
        }

        function polygonToLineString(coordinates, properties) {
            return coordinates.map(function(coordinates) {
                return turf.lineString(coordinates, properties);
            });
        }
        debugger
        //console.log(JSON.stringify(geojson))
        new Blob([JSON.stringify(geojson)], {
            type: "application/json;charset=utf-8",
          }),
          "data.json"
        if (this.map.getLayer('SectionHighlight')) {
            this.map.setPaintProperty('SectionHighlight', 'fill-extrusion-color', fillExtrusionColor);
        } else{
            this.map.addLayer({
                id: 'SectionHighlight',
                source: {
                    type: 'geojson',
                    data: regionalSection
                },
                type: "fill-extrusion",
                'paint': {
                    'fill-extrusion-color': fillExtrusionColor,
                    'fill-extrusion-height': 1000,
                    'fill-extrusion-base': 0,
                    'fill-extrusion-opacity': 1
                }
            });
            this.map.addLayer({
                id: 'SectionHighlight1',
                source: {
                    type: 'geojson',
                    data: regionalSection1
                },
                type: "fill-extrusion",
                'paint': {
                    'fill-extrusion-color': fillExtrusionColor,
                    'fill-extrusion-height': 1010,
                    'fill-extrusion-base': 1000,
                    'fill-extrusion-opacity': 1
                }
            });
        }
    }
    /**
     * @description: 移除版块高亮
     * @param {*}
     * @return {*}
     */
     removeSectionHighlight () {
         if (this.map.getLayer('SectionHighlight')) {
            this.map.setPaintProperty('SectionHighlight', 'fill-extrusion-color', 'rgba(64,158,255,0.8)');
         }
     }
    /**
     * @description: 柱状热力图
     * @param {*}
     * @return {*}
     */
    getColumnarThermodynamicDiagram (array) {
        const data = {
            "type": "FeatureCollection",
            "crs": { "type": "name", "properties": { "name": "urn:ogc:def:crs:OGC:1.3:CRS84" } },
            "features": []
        }
        const length = array.length;
        for (let i = 0; i < length; i++) {
            data.features.push(turf.point([array[i].lng,array[i].lat],{ "id": i, "mag": 1}));
        }
        this.columnarThermodynamicDiagram = this.map.createHeatmp(
            'heatMapDemo',
            data,
            {
                type: "columnar",
                cellSizePixels: 300,
                elevationScale: 100,
            }
        )
    }
    /**
     * @description: 移除柱状热力图
     * @param {*}
     * @return {*}
     */
     removeColumnarThermodynamicDiagram () {
        if (this.columnarThermodynamicDiagram) {
            this.columnarThermodynamicDiagram.remove();
            this.columnarThermodynamicDiagram = null;
        }
     }
    /**
     * @description: 移除所有
     * @param {*}
     * @return {*}
     */
    removeAll () {
        this.layerMouseleave();
        this.removeHeatMap();
        this.removeHistogram();
        this.removeSectionHighlight();
        this.removeColumnarThermodynamicDiagram();
        this.removeLineSegments();
        this.removeMeasuringSurface();
        const circleLength = this.circleLayerId.length;
        for(let i = 0; i < circleLength; i++) {
            this.removeCircle(this.circleLayerId[i]);
        }
        const polygonLength = this.polygonLayerId.length;
        for(let i = 0; i < polygonLength; i++) {
            this.removeCircle(this.polygonLayerId[i]);
        }
        if (this.map.getLayer('SectionHighlight')) {
            this.map.removeLayer('SectionHighlight')
        }
        this.removePointLayer({})
    }
    /**
     * @description: 视频点位聚合
     * @param {*}
     * @return {*}
     */
    videoPointAggregation (videoPointArray) {
        console.log(videoPointArray)
        const gojesion = {
            'type': 'FeatureCollection',
            'crs': {
                'type': 'name',
                'properties': {
                    'name': 'urn:ogc:def:crs:OGC:1.3:CRS84'
                }
            },
            'features': []
        }
        const length = videoPointArray.length;
        for (let i = 0; i < length; i++) {
            gojesion.features.push(turf.point([videoPointArray[i].lon,videoPointArray[i].lat],{
                "code": videoPointArray[i].code,
                "mag": 1,
                "name": videoPointArray[i].name,
                "first": videoPointArray[i].first,
                "second": videoPointArray[i].second,
                "third": videoPointArray[i].third
            }))
        }

        this.map.addSource("earthquakes", {
            type: "geojson",
            data: gojesion,
            cluster: true,
            clusterMaxZoom: 14, //最大缩放到群集点
            clusterRadius: 50 // 每一组点的半径（=50）
          });

            // 外围有数字的圆圈，加晕染
            this.map.addLayer({
                id: "clusters",
                type: "circle",
                source: "earthquakes",
                filter: ["has", "point_count"],
                paint: {
                  //*蓝色，当点数小于100时为20px圆
                  //*点计数在100到750之间时为黄色，21px圆
                  //*点计数大于或等于750时为22像素的粉红色圆圈
                  "circle-color": [
                    "step",
                    ["get", "point_count"],
                    "rgba(81, 187, 214, 0.8)",
                    100,
                    "rgba(241, 240, 117, 0.8)",
                    750,
                    "rgba(242, 140, 177, 0.8)"
                  ],
                  "circle-radius": [
                    "step",
                    ["get", "point_count"],
                    20, //蓝色，当点数小于100时为20px圆
                    100, //对应上面circle-color 数字，意思为100以内
                    21, //点计数在100到750之间时为黄色，21px圆
                    750, //对应上面circle-color 数字，意思为750以内
                    22 //点计数大于或等于750时为22像素的粉红色圆圈
                  ],
                  // 这个是外边框的颜色 circle-stroke-color这个对应了上面circle-color
                  "circle-stroke-color": [
                    "step",
                    ["get", "point_count"],
                    "rgba(81, 187, 214, 0.2)",
                    100,
                    "rgba(241, 240, 117, 0.2)",
                    750,
                    "rgba(242, 140, 177, 0.2)"
                  ],
                  // 这个是外边框晕染的范围
                  "circle-stroke-width": [
                    "step",
                    ["get", "point_count"],
                    5, //蓝色晕染长度，当点数小于100时为5px晕染
                    100, //对应上面circle-color 数字，意思为100以内
                    6, //点计数在100到750之间时为黄色，6px晕染
                    750, //对应上面circle-color 数字，意思为750以内
                    7 //点计数大于或等于750时为7px像素的粉红色晕染
                  ]
                }
              });
              //聚合图圆圈中的数字
              this.map.addLayer({
                id: "cluster-count",
                type: "symbol",
                source: "earthquakes",
                filter: ["has", "point_count"],
                layout: {
                  "text-field": "{point_count_abbreviated}",
                  "text-font": ["Open Sans Bold,Arial Unicode MS Bold"],
                  "text-size": 12
                },
                // 添加这个就可以改变圆圈内字样式，这里我改变了他的颜色
                paint: {
                  "text-color": "#fff",
                  "text-opacity": 1
                }
              });
              // 聚合图中没有数字的显示小圆点
              this.map.addLayer({
                id: "unclustered-point",
                type: "circle",
                source: "earthquakes",
                filter: ["!", ["has", "point_count"]],
                paint: {
                  "circle-color": "#11b4da",
                  "circle-radius": 10,
                  "circle-stroke-width": 1,
                  "circle-stroke-color": "#fff"
                }
              });
   
              // 单击时检查群集
              this.map.on("click", "unclustered-point", (e) => {
                let obj = this.map.queryRenderedFeatures(e.point, {
                    layers: ["unclustered-point"]
                })[0].properties
                console.log(obj)
              });
    }
    /**
     * @description: 绘制线段
     * @param {*}
     * @return {*}
     */
    drawLineSegments (lineObj) {
        if (this.map.getLayer('drawLineSegments')) {
            this.map.removeLayer('drawLineSegments')
        }
        const lineArray = [];
        const length = lineObj.coords.length / 3;
        const color = `rgba(${lineObj.color[0]},${lineObj.color[1]},${lineObj.color[2]},${lineObj.color[3]})`
        for (let i = 1; i <= length; i++) {
            const index = i*3;
            lineArray.push([lineObj.coords[index-3],lineObj.coords[index-2]])
        }
        const geojson = turf.lineString(lineArray)
        this.map.addLayer({
            "id": "drawLineSegments",
            "type": "line",
            source: {
                "type": "geojson",
                data: geojson,
            },
            "layout": {
                "line-join": "round",
                "line-cap": "round"
            },
            "paint": {
                "line-color": color,
                "line-width": lineObj.width
            }
        });
        const bbox = this.egs.turf.bbox({
            "type": "geojson",
            data: geojson,
        });
        this.map.fitBounds(bbox, {
          padding: { top: 40, bottom: 40, left: 20, right: 20 },
        });
    }
    /**
     * @description: 清除线段
     * @param {*}
     * @return {*}
     */
     removeLineSegments () {
        if (this.map.getLayer('drawLineSegments')) {
            this.map.removeLayer('drawLineSegments')
        }
        if (this.map.getLayer('rangingLineLayer')) {
            this.map.removeLayer('rangingLineLayer');
            this.map.removeSource('rangingLineSource');
            this.map.removeLayer('rangingPointLayer');
            this.map.removeSource('rangingPointSource');
            this.map.removeImage('linePointIcon')
        }
     }
    /**
     * @description: 绘制圆
     * @param {*}
     * @return {*}
     */
     drawCircle (circleObj) {
         const circleArray = [circleObj.coords[0],circleObj.coords[1]];
        const geojson = turf.circle(circleArray, circleObj.r, {steps: 100})
        const color = `rgba(${circleObj.color[0]},${circleObj.color[1]},${circleObj.color[2]},${circleObj.color[3]})`
        this.map.addLayer({
            "id": circleObj.name,
            "type": "fill",
            source: {
                "type": "geojson",
                data: geojson,
            },
            'paint': {
                'fill-color': color,
            }
        })
        this.circleLayerId.push(circleObj.name);
        const bbox = this.egs.turf.bbox({
            "type": "geojson",
            data: geojson,
        });
        this.map.fitBounds(bbox, {
          padding: { top: 40, bottom: 40, left: 20, right: 20 },
        });
     }
    /**
     * @description: 清除圆
     * @param {*}
     * @return {*}
     */
     removeCircle (circleName) {
        if (this.map.getLayer(circleName)) {
            this.map.removeLayer(circleName);
            this.map.removerSource(circleName)
            this.circleLayerId = this.circleLayerId.filter(item => item != circleName);
         }
     }
    /**
     * @description: 绘制多边形
     * @param {*}
     * @return {*}
     */
    drawPolygon (polygonObj) {
        const length = polygonObj.coords.length /2;
        const PolygonArray = [];
        const color = `rgba(${polygonObj.color[0]},${polygonObj.color[1]},${polygonObj.color[2]},${polygonObj.color[3]})`
        for (let i = 1; i <= length; i++) {
            const index = i*2
            PolygonArray.push([polygonObj.coords[index-2],polygonObj.coords[index-1]])
        }
        const geojson = turf.polygon([PolygonArray]);
        this.map.addLayer({
            "id": polygonObj.name,
            "type": "fill",
            source: {
                "type": "geojson",
                "data": geojson,
            },
            'paint': {
                'fill-color': color,
            }
        })
        this.polygonLayerId.push(polygonObj.name)
        const bbox = this.egs.turf.bbox({
            "type": "geojson",
            data: geojson,
        });
        this.map.fitBounds(bbox, {
          padding: { top: 40, bottom: 40, left: 20, right: 20 },
        });
    }
    /**
     * @description: 清除多边形
     * @param {*}
     * @return {*}
     */
     clearPolygon (polygonName) {
        if (this.map.getLayer(polygonName)) {
            this.map.removeLayer(polygonName);
            this.map.removerSource(polygonName)
            this.polygonLayerId = this.polygonLayerId.filter(item => item != polygonName);
         }
     }
    /**
     * @description: 两点测距
     * @param {*}
     * @return {*}
     */
     ranging () {
        let fristClick = true; // 第一次点击
        const clickFeatures = [];
        const linePostion = [];
        // 判断是否已经加入图标
        if (!this.map.hasImage('linePointIcon')) {
            this.map.loadImage('./image/linePointIcon.png', (error,image) => {
                if (error) { throw error; }
                this.map.addImage('linePointIcon', image);
            })
        }
        this.map.addSource('rangingPointSource',{
            'type': 'geojson',
            'data': {
              'type': 'FeatureCollection',
              'features': []
            }
        })
        this.map.addSource('rangingLineSource',{
            'type': 'geojson',
            'data': {
              'type': 'FeatureCollection',
              'features': {}
            }
        })
        this.map.addLayer({
            'id': 'rangingLineLayer',
            'type': 'line',
            'source': 'rangingLineSource',
            "layout": {
                "line-join": "round",
                "line-cap": "round"
            },
            "paint": {
                "line-color": "#fff",
                "line-width": 5
            }
        })
        this.map.addLayer({
            'id': 'rangingPointLayer',
            'type': 'symbol',
            'source': 'rangingPointSource',
            'layout': {
              'icon-image': 'linePointIcon', // reference the image
              'icon-size': .5,
              'icon-offset': [0,-90]
            }
        })
        const mapClick = (e) => {
            clickFeatures.push(turf.point([e.lngLat.lng, e.lngLat.lat]))
            this.map.getSource('rangingPointSource').setData({
                "type": "FeatureCollection",
                "features": clickFeatures
            });
            if (fristClick) {
                fristClick = false;
                linePostion.push([e.lngLat.lng, e.lngLat.lat])
            } else {
                this.map.off('click',mapClick);
                this.map.off('mousemove',mapMousemove);
                window.parent.postMessage({
                    "type": "line",
                    "linenData": linePostion,
                    "getDistance": turf.distance(linePostion[0], linePostion[1], {units: 'kilometers'})*1000
                },'*');
            }
        }
        const mapMousemove = (e) => {
            if (!fristClick) {
                if (linePostion.length > 1) {
                    linePostion.pop();
                }
                linePostion.push([e.lngLat.lng, e.lngLat.lat]);
                const lineFeatures = turf.lineString(linePostion);
                this.map.getSource('rangingLineSource').setData(lineFeatures);
            }
        }
        this.map.on('click',mapClick);
        this.map.on('mousemove',mapMousemove);
     }
    /**
     * @description: 测面
     * @param {*}
     * @return {*}
     */
     measuringSurface () {
        const clickFeatures = [];
        const noodlesPostion = [[]]
        // 判断是否已经加入图标
        if (!this.map.hasImage('noodlesPointIcon')) {
            this.map.loadImage('./image/linePointIcon.png', (error,image) => {
                if (error) { throw error; }
                this.map.addImage('noodlesPointIcon', image);
            })
        }
        // 点source
        this.map.addSource('measuringSurfacePointSource',{
            'type': 'geojson',
            'data': {
              'type': 'FeatureCollection',
              'features': []
            }
        })
        // 面source
        this.map.addSource('measuringSurfaceSource',{
            'type': 'geojson',
            'data': {
              'type': 'FeatureCollection',
              'features': {}
            }
        })
        // 线source
        this.map.addSource('measuringSurfaceLineSource',{
            'type': 'geojson',
            'data': {
              'type': 'FeatureCollection',
              'features': {}
            }
        })
        // 面layer
        this.map.addLayer({
            'id': 'measuringSurfaceLayer',
            'type': 'fill',
            'source': 'measuringSurfaceSource',
            'paint': {
                'fill-color': '#088',
                'fill-opacity': 0.8
            }
        })
        // 点layer
        this.map.addLayer({
            'id': 'measuringSurfacePointLayer',
            'type': 'symbol',
            'source': 'measuringSurfacePointSource',
            'layout': {
              'icon-image': 'noodlesPointIcon', // reference the image
              'icon-size': .5,
              'icon-offset': [0,-90]
            }
        })
        // 线layer
        this.map.addLayer({
            'id': 'measuringSurfaceLineLayer',
            'type': 'line',
            'source': 'measuringSurfaceLineSource',
            "layout": {
                "line-join": "round",
                "line-cap": "round"
            },
            "paint": {
                "line-color": "#088",
                "line-width": 5
            }
        })
        const mapClick = (e) => {
            clickFeatures.push(turf.point([e.lngLat.lng, e.lngLat.lat]))
            this.map.getSource('measuringSurfacePointSource').setData({
                "type": "FeatureCollection",
                "features": clickFeatures
            });
            noodlesPostion[0].push([e.lngLat.lng, e.lngLat.lat]);
        }
        const mapMousemove = (e) => {
            const clickFeaturesLength = clickFeatures.length;
            switch (clickFeaturesLength) {
                case 0:
                    break;
                case 1:
                    const lineFeatures = turf.lineString([noodlesPostion[0][0],[e.lngLat.lng, e.lngLat.lat]])
                    this.map.getSource('measuringSurfaceLineSource').setData(lineFeatures);
                    break;
                default:
                    if (this.map.getLayer('measuringSurfaceLineLayer')) {
                        this.map.removeLayer('measuringSurfaceLineLayer')
                        this.map.removeSource('measuringSurfaceLineSource')
                    }
                    noodlesPostion[0].push([e.lngLat.lng, e.lngLat.lat]);
                    noodlesPostion[0].push(noodlesPostion[0][0]);
                    const noodlesFeatures = turf.polygon(noodlesPostion);
                    this.map.getSource('measuringSurfaceSource').setData(noodlesFeatures);
                    noodlesPostion[0].pop();
                    noodlesPostion[0].pop();
                    break;
            }
        }
        const mapContextmenu = (e) => {
            if (noodlesPostion[0].length > 2) {
                noodlesPostion[0].push(noodlesPostion[0][0]);
                const noodlesFeatures = turf.polygon(noodlesPostion);
                this.map.getSource('measuringSurfaceSource').setData(noodlesFeatures);
                this.map.off('click',mapClick);
                this.map.off('mousemove',mapMousemove);
                this.map.off('ontextmenu',mapContextmenu);
                const polygonFuteace = turf.polygon(noodlesPostion)
                window.parent.postMessage({
                    "type": "Polygon",
                    "linenData": noodlesPostion,
                    "getDistance": turf.area(polygonFuteace)
                },'*');
            }
        }
        this.map.on('click',mapClick);
        this.map.on('mousemove',mapMousemove);
        this.map.on('contextmenu',mapContextmenu);
     }
    /**
     * @description: 重置多边形
     * @param {*}
     * @return {*}
     */
     removeMeasuringSurface () {
        if (this.map.getLayer('measuringSurfaceLayer')) {
            this.map.removeLayer('measuringSurfaceLayer');
            this.map.removeSource('measuringSurfaceSource');
            this.map.removeLayer('measuringSurfacePointLayer');
            this.map.removeSource('measuringSurfacePointSource');
            this.map.removeImage('noodlesPointIcon')
        }
     }
    /**
     * @description: 飞线
     * @param {*}
     * @return {*}
     */
     flyingLine (data) {
        // 创建贝塞尔曲线
        const bezierArray = [];
        const length = data.flyData.length
        for (let i = 0; i < length; i++) {
            console.log()
            const sPoint = data.flyData[i].sPoint.split(",");
            const ePoint = data.flyData[i].ePoint.split(",");
            const line = turf.lineString([sPoint,ePoint])
            bezierArray.push(turf.bezierSpline(line));
        }
        // 创建分割线
        const arcs = []
        const step = data.bezierRate
        
        const split = (lines) => {
          for (let line of lines.features) {
            const arc = []
            const bezier = turf.bezierSpline(line)
            const len = turf.length(bezier)
        
            for (let i = 0; i < len; i += len / step) {
              const p = turf.along(bezier, i)
              arc.push(p.geometry.coordinates)
            }
        
            arcs.push(arc)
          }
        }
        split({
            'type': 'FeatureCollection',
            'features': bezierArray
            });

        this.map.addSource('segment', {
            type: 'geojson',
            lineMetrics: true,
            data: null
        })
      
          this.map.addLayer({
            id: 'segment',
            source: 'segment',
            type: 'line',
            paint: {
              'line-color': 'red',
              'line-width': 2,
              'line-gradient': [
                'interpolate',
                ['linear'],
                ['line-progress'],
                0,
                'rgba(255, 255, 255, 0.1)',
                0.8,
                'rgba(255, 0, 0, 0.6)',
                1,
                'red'
              ]
            }
          })
      
        const source = map.getSource('segment')

        const segments = {
            type: 'FeatureCollection',
            features: []
          }
          
          let counter = 0
          const animate = () => {
            if (counter === step) {
              counter = 0
              segments.features.forEach(f => f.geometry.coordinates.length = 0)
            }
          
            arcs.forEach((arc, i) => {
              if (!segments.features[i]) {
                segments.features[i] = {
                  type: 'Feature',
                  geometry: {
                    type: 'LineString',
                    coordinates: []
                  }
                }
              }
          
              segments.features[i].geometry.coordinates.push(arc[counter])
            })
          
            source.setData(segments)
            requestAnimationFrame(animate)
            counter++
          }
          animate()
     }
    /**
     * @description: postMessage 消息分类
     * @param {*} data {funcName: '消息类型' ...}
     */    
    classiFication(data){
        console.log(data.funcName)
        // postMessage 消息安全性检测
        // this.checkMessage()
        switch (data.funcName) {
            // 图层切换
            case "layer":
                this.toggleBastLayer(data); 
                break;
            // 3D文字添加    
            case "3Dtext": 
                this.text3D(data.textData);  
                break;  
            // 3D文字删除
            case "rm3DText": 
                this.rm3DText();  
                break;  
            // 点位添加
            case "pointLoad":
                this.addPointLayer(data); 
                break;
            // 弹窗移除
            case "rmPop":
                this.removePopup(data); 
                break;
            // 删除弹窗
            case "removePoint":
                this.removePointLayer(data); 
                break;
            // 飞行定位
            case "flyto": 
                this.flyToPoint(data); 
                break;
            // 添加热力图
            case "hotPowerMap":
                this.getHeatMap(data.hotPowerMapData)
                break;
            // 删除热力图
            case "rmhotPowerMap":
                this.removeHeatMap()
                break;
                // 加载柱状图
            case "Histogram":
                console.log(123)
                this.getHistogram(data.HistogramData)
                break;
                // 移除柱状图
            case "rmHistogram":
                this.removeHistogram()
                break;
                // 加载版块高亮
            case "renderBankuai":
                this.getSectionHighlight(data.renderBankuaiData)
                break;
                // 移除版块高亮
            case "rmRedbankuai":
                this.removeSectionHighlight()
                break;
                // 柱状热力图
            case "HistogramMap":
                this.getColumnarThermodynamicDiagram(data.HistogramMapName)
                break;
                // 移除柱状热力图
            case "rmHistogramMap":
                this.removeColumnarThermodynamicDiagram()
                break;
                // 绘制线段
            case "createLine":
                this.drawLineSegments(data)
                break;
                // 绘制圆
            case "drawCircle":
                this.drawCircle(data.circleData)
                break;
            // 清除圆
            case "clearCircle":
                this.removeCircle(data.circleName)
                break;
            // 绘制多边形
            case "shape":
                this.drawPolygon(data)
                break;
            // 清除多边形
            case "rmShape":
                this.clearPolygon(data.shapeName)
                break;
                // 两点测距
            case "getDistance":
                this.ranging()
                break;
            // 测面
            case "startDrawPolygon":
                this.measuringSurface()
                break;
            // 重置多边形
            case "rmDrawPolygon":
                this.removeMeasuringSurface()
                break;
            // 飞线
            case "newFlyLine":
                this.flyingLine(data)
                break;
            case "rmAll":
                this.removeAll()
                break;
            case "textData": 
                //this.flyToPoint(data);
            default:
                break;
        }
    }
}