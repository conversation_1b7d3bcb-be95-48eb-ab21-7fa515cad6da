<template>
  <div class='pageContainer-Map'>
    <left></left>
    <center></center>
    <right></right>
    <!-- <top></top> -->
  </div>
</template>

<script>
import left from './left'
import right from './right'
import center from './center'
// import top from './top'
export default {
  name: 'index',
  components: {
    left,
    right,
    center,
    // top
  },
  data() {
    return {}
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped>

</style>