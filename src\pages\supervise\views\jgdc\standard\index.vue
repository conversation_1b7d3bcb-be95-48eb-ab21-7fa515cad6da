<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" :inline="true">
      <el-form-item label="项目名称" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入项目名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="考核类型" prop="type">
        <el-select v-model="queryParams.type" placeholder="考核类型" clearable size="small">
          <el-option
            v-for="item in typeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:standard:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >
          新增
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="standardList"
      row-key="checkStandardId"
      default-expand-all
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
    >
      <el-table-column prop="title" label="考核项目" width="350" show-overflow-tooltip />
      <el-table-column prop="content" label="考核标准" width="350" show-overflow-tooltip />
      <!-- <el-table-column prop="score" label="分值" /> -->
      <el-table-column prop="type" label="考核类型">
        <template slot-scope="scope">
          <span>{{ scope.row.type | typeName }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="code" label="小类代码" />
      <el-table-column prop="sort" label="排序" />
      <!-- <el-table-column prop="status" label="状态" :formatter="statusFormat" /> -->
      <el-table-column label="创建时间" align="center" prop="createTime" width="200">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['system:standard:edit']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >
            修改
          </el-button>
          <el-button
            v-hasPermi="['system:standard:add']"
            size="mini"
            type="text"
            icon="el-icon-plus"
            @click="handleAdd(scope.row)"
          >
            新增
          </el-button>
          <el-button
            v-if="scope.row.parentId != 0"
            v-hasPermi="['system:standard:remove']"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col v-if="form.parentId !== 0" :span="24">
            <el-form-item label="上级" prop="parentId">
              <treeselect v-model="form.parentId" :options="standardOptions" :normalizer="normalizer" placeholder="选择上级" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="考核项目" prop="title">
              <el-input v-model="form.title" placeholder="请输入名称" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="考核标准" prop="content">
              <el-input v-model="form.content" placeholder="请输入考核标准" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="类别" prop="status">
              <el-select v-model="form.status" placeholder="请选择类别" :style="{ width: '100%' }" clearable>
                <el-option label="目录" value="0" />
                <el-option label="标准" value="1" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="小类代码" prop="code">
              <el-input-number v-model="form.code" controls-position="right" :min="0" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="积分类型" prop="type">
              <el-select v-model="form.type" placeholder="请选择考核类型" :style="{ width: '100%' }" clearable>
                <el-option label="加分" value="0" />
                <el-option label="扣分" value="1" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序" prop="sort">
              <el-input-number v-model="form.sort" controls-position="right" :min="0" />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="状态">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in statusOptions"
                  :key="dict.dictValue"
                  :label="dict.dictValue"
                >
                  {{ dict.dictLabel }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col> -->
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" placeholder="请输入名称" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <TMapDraw :visible.sync="mapVisible" :area="form.area" @confirm="handleConfirm" />
  </div>
</template>

<script>
import { listStandard, getStandard, delStandard, addStandard, updateStandard, listExcludeChild } from '@/api/supervise/standard'
import TMapDraw from '@/components/tMapDraw'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'

export default {
  name: 'Standard',
  components: { Treeselect, TMapDraw },
  filters: {
    typeName(type) {
      const name = { 0: '加分', 1: '扣分' }
      return name[type] || '无'
    }
  },
  data() {
    return {
      typeOptions: [{
        value: '0',
        label: '加分'
      }, {
        value: '1',
        label: '扣分'
      }],
      // 区域选择
      mapVisible: false,
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 督查考核标准表格数据
      standardList: [],
      // 督查考核标准树选项
      standardOptions: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 状态数据字典
      statusOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: null,
        parentId: null,
        ancestors: null,
        content: null,
        score: null,
        sort: null,
        code: null,
        type: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        parentId: [
          { required: true, message: '上级不能为空', trigger: 'blur' }
        ],
        title: [
          { required: true, message: '名称不能为空', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '排序不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  async created() {
    await this.getDicts('sys_normal_disable').then(response => {
      this.statusOptions = response.data
    })
    this.getList()
  },
  methods: {
    /* 打开地图 */
    handleOpenMap() {
      this.mapVisible = true
    },
    /* 确认选择地图 */
    handleConfirm(area) {
      this.form = { ...this.form, area }
    },
    /** 查询部门列表 */
    getList() {
      this.loading = true
      listStandard(this.queryParams).then(response => {
        this.standardList = this.handleTree(response.data, 'checkStandardId')
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.checkStandardId,
        label: node.title,
        children: node.children
      }
    },
    // 字典状态字典翻译
    statusFormat(row) {
      return this.selectDictLabel(this.statusOptions, row.status)
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        checkStandardId: undefined,
        parentId: undefined,
        title: undefined,
        sort: undefined,
        leader: undefined,
        email: undefined,
        status: '1'
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset()
      if (row != undefined) {
        this.form.parentId = row.checkStandardId
      }
      this.open = true
      this.title = '添加考核标准'
      listStandard().then(response => {
        this.standardOptions = this.handleTree(response.data, 'checkStandardId')
      })
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      getStandard(row.checkStandardId).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改考核标准'
      })
      listExcludeChild(row.checkStandardId).then(response => {
        this.standardOptions = this.handleTree(response.data, 'checkStandardId')
      })
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.checkStandardId != undefined) {
            updateStandard(this.form).then(() => {
              this.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addStandard(this.form).then(() => {
              this.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$confirm('是否确认删除名称为"' + row.title + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return delStandard(row.checkStandardId)
      }).then(() => {
        this.getList()
        this.msgSuccess('删除成功')
      })
    }
  }
}
</script>
