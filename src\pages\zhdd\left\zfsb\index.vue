<template>
  <div style='margin-bottom: 40px;'>
    <CommonTitle text='执法设备'>
      <div class='zhddTitleBar'>
        <img
          v-if="manageUrl != ''"
          src="@/assets/common/edit.png"
          alt=""
          style="margin-left: 10px; cursor: pointer"
          @click="openManage()"
        />
      </div>
    </CommonTitle>
    <div
      class="s-flex s-c-grey-light s-m-b-20 s-m-t-20 s-m-l-10"
      style="width: 100%; position: relative"
    >
      <div class="s-flex s-flex-1">
        <div style="line-height: 24px; margin-top: -50px">
          <img src="@/assets/zhdd/left_car.png" alt="" />
          <p class="font-b s-font-60 ">
            <span class="yellow">{{jdcData.numjd}}</span>/{{fjdcData.numfjd}}
          </p>
        </div>
        <div style="width: 320px; margin-left: 30px;cursor: pointer">
          <p class="tit_data s-font-35" @click="showZfsbDialog(jdcData)">{{jdcData.tit}}<img src="@/assets/common/sbDetail.png" alt="" style="margin-left: 10px"></p>
          <p class="s-flex s-row-between s-font-30 s-m-t-15">
              <span v-for="item in jdcData.son" @click="showZfsbDialog(item)">
                {{item.tit}}<br />
                <span class="green s-font-60 font-b">{{item.num}}</span>
              </span>
          </p>
        </div>
      </div>
      <i class="line" style="position: absolute; left: 50%"></i>
      <div style="width: 440px;cursor: pointer">
        <p class="tit_data s-font-35" @click="showZfsbDialog(fjdcData)">{{fjdcData.tit}}<img src="@/assets/common/sbDetail.png" alt="" style="margin-left: 10px"></p>
        <p class="s-flex s-row-between s-font-30 s-p-r-55 s-m-t-15">
            <span v-for="item in fjdcData.son" @click="showZfsbDialog(item)">
              {{item.tit}}<br />
              <span class="green s-font-60 font-b">{{item.num}}</span>
            </span>
        </p>
      </div>
    </div>
    <div class="s-flex s-flex-wrap s-m-l-10 s-row-between">
      <div class="s-flex" v-for="(item,index) in zfsbList" @click="showZfsbDialog(item)">
        <img :src="item.img" alt="" />
        <div class="s-c-grey-light s-m-l-30" style="width: 300px;cursor: pointer">
          <p
            class="tit_data s-font-35"
            :style="{cursor: item.tit=='无人机'?'pointer':'no-repeat'}"
          >
            {{item.tit}}
            <img src="@/assets/common/sbDetail.png" alt="">
          </p>

          <p class="font-b s-font-60 ">
            <span class="yellow">{{item.num1}}</span>/{{item.num}}
          </p>
        </div>
      </div>
    </div>

    <zfsbDialog :visible='visible' :name='name' @close='visible = false'></zfsbDialog>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import { indexApi } from '@/api/indexApi'
import zfsbDialog from './zfsbDialog'
export default {
  name: 'index',
  components: {
    CommonTitle,
    zfsbDialog
  },
  data() {
    return {
      year: localStorage.getItem("year"),
      city: localStorage.getItem("city"),
      manageUrl: "https://csdn.dsjj.jinhua.gov.cn:8303/lawEquip",
      jdcData: {
        tit: "机动车辆",
        numjd: "",
        son: [
          { tit: "汽车", num: 40 },
          { tit: "摩托车", num: 60 },
        ],
      },
      fjdcData: {
        tit: "非机动车辆",
        numfjd: "",
        son: [
          { tit: "四轮电瓶车", num: 40 },
          { tit: "两轮电瓶车", num: 60 },
        ],
      },
      zfsbList: [
        { tit: "执法记录仪", num1: 156, num: 200, img:require('@/assets/zhdd/zfsb1.png') },
        { tit: "对讲机", num1: 113, num: 122, img:require('@/assets/zhdd/zfsb2.png') },
        { tit: "PDA", num1: 98, num: 122, img:require('@/assets/zhdd/zfsb3.png') },
        { tit: "无人机", num1: 30, num: 30, img:require('@/assets/zhdd/zfsb4.png') },
      ],

      name:"",
      visible: false
    }
  },
  computed: {},
  mounted() {
    this.$bus.$on('cityChange', (city) => {
      this.city = city;
      this.initApi(city,localStorage.getItem("year"));
    })
    this.$bus.$on('yearChange', (year) => {
      this.year = year;
      this.initApi(localStorage.getItem("city"),year);
    })
    this.initApi(localStorage.getItem("city"),localStorage.getItem("year"));
  },
  methods: {
    openManage() {
      window.open(this.manageUrl)
    },
    initApi(city) {
      indexApi("/csdn_yjyp12_new", { area_name: city }).then((res) => {
        this.jdcData.numjd = this.fjdcData.numfjd = res.data[0].gg;
        this.jdcData.son[0].num = res.data[0].qc;
        this.jdcData.son[1].num = res.data[0].mtc;
        this.fjdcData.son[0].num = res.data[0].sldpc;
        this.fjdcData.son[1].num = res.data[0].lldpc;
        this.zfsbList[0].num1 = res.data[0].jly;
        this.zfsbList[0].num = res.data[0].jly_xjr;
        this.zfsbList[1].num1 = this.zfsbList[1].num = res.data[0].djj;
        this.zfsbList[2].num1 = this.zfsbList[2].num = res.data[0].pad;
        this.zfsbList[3].num1 = this.zfsbList[3].num = res.data[0].wrj;
      });
    },
    showZfsbDialog(item) {
      this.name = item.tit;
      this.visible = true
    },
  },
  watch: {}
}
</script>

<style scoped lang='less'>
.zhddTitleBar {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.font-b {
  font-family: DINCondensed;
}

.yellow {
  background: linear-gradient(to bottom, #ffeccb, #ffffff, #ffc460, #ffe2b0, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
}

.green {
  background: linear-gradient(to bottom, #cbf2ff, #ffffff, #00c0ff, #80e0ff, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
}

.line {
  margin: 0 30px;
  width: 3px;
  height: 120px;
  background: linear-gradient(to bottom, #ffffff00, #fff, #ffffff00);
}

.tit_data {
  background: url('@/assets/zhdd/tit_bg.png') no-repeat;
  /* background-size: 100%; */
}
</style>