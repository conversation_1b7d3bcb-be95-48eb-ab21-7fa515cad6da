import {request} from '@/utils/request'
// 获取出租车列表
export function getTaxiList(params) {
  return request({
    url: '/business/taxi/inOut/list',
    method: 'get',
    params
  })
}

// 获取今日考勤
export function getInCount(data) {
  return request({
    url: '/business/punchIn/count',
    method: 'POST',
    data
  })
}

// 获取案件类型
export function getCaseType(data = {}) {
  return request({
    url: '/business/statistics/caseType',
    method: 'POST',
    data
  })
}

// 获取案件趋势
export function getCaseTrend(data = {}) {
  return request({
    url: '/business/statistics/caseTrend',
    method: 'POST',
    data
  })
}

// 获取装备统计
export function getListDevice() {
  return request({
    url: '/business/statistics/listDevice',
    method: 'get'
  })
}

// 获取实时警情
export function getListRealTimeCase(params) {
  return request({
    url: '/business/statistics/listRealTimeCase',
    method: 'get',
    params
  })
}

// 获取区域动态列表
/* export function getDynamicList(params) {
  return request({
    url: '/business/case/dynamic/list',
    method: 'get',
    params
  })
} */
export function getDynamicList(params) {
  return request({
    url: '/business/inspection/list',
    method: 'get',
    params
  })
}

// 获取现场执勤人员列表
export function getLocaleUsersList(params) {
  return request({
    url: '/business/statistics/localeUsers',
    method: 'get',
    params
  })
}

// 获取事件列表
export function getEventList(params) {
  return request({
    url: '/business/statistics/eventList',
    method: 'get',
    params
  })
}

// 获取案件趋势（新）
export function getCaseTrending(params) {
  return request({
    url: '/business/statistics/caseType',
    method: 'get',
    params
  })
}

// 获取志愿者统计
export function getCountVol() {
  return request({
    url: '/business/statistics/countVol',
    method: 'get'
  })
}

export function caseStatistics() {
  return request({
    url: '/business/statistics/caseStatistics',
    method: 'get'
  })
}
// 获取志愿者统计数字
export function countVolUser() {
  return request({
    url: '/business/statistics/countVolUser',
    method: 'get'
  })
}

// 获取服务动态列表
export function getNewsList(params) {
  return request({
    url: '/business/vol/news/list',
    method: 'get',
    params
  })
}

// 获取弹窗接口详情
export function listVolUser(params) {
  return request({
    url: '/business/statistics/listVolUser',
    method: 'get',
    params
  })
}
