import {request} from '@/utils/request'
/** 简易处罚 */
// 列表
export function generalList(params) {
  return request({
    url: '/business/case/general/list',
    method: 'get',
    params
  })
}
// 列表
export function generalOne(params) {
  return request({
    url: '/business/case/general/' + params,
    method: 'get'
  })
}
export function mygeneralList(params) {
  return request({
    url: '/business/case/general/myCases',
    method: 'get',
    params
  })
}
// 修改
export function editgeneral(data) {
  return request({
    url: '/business/case/general/edit',
    method: 'post',
    data
  })
}
// 删除
export function removegeneral(data) {
  return request({
    url: '/business/case/general/remove/' + data,
    method: 'post'
  })
}
