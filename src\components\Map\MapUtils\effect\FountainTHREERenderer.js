import SpatialReference from "@arcgis/core/geometry/SpatialReference.js";
import * as externalRenderers from "@arcgis/core/views/3d/externalRenderers.js";
const THREE = window.THREE_r123;
export default class FountainTHREERenderer {
  constructor(view, { center, particleCount, color, sizeLevel = 1 }) {
    this.view = view;
    this.renderer = null; // three js 渲染器
    this.camera = null; //three js 相机
    this.scene = null; //three js 场景
    this.pointCloud = null;
    this.particleCount = particleCount || 3000;
    this.center = center;
    this.initY = null;
    this.map = null;
    this.color = color;
    this.trans = null;
    this.sizeLevel = sizeLevel;
  }
  setup(context) {
    let that = this;
    this.renderer = new THREE.WebGLRenderer({
      context: context.gl, // 可用于将渲染器附加到已有的渲染环境(RenderingContext)中
      premultipliedAlpha: false, // renderer是否假设颜色有 premultiplied alpha. 默认为true
    });
    this.renderer.setPixelRatio(window.devicePixelRatio); // 设置设备像素比。通常用于避免HiDPI设备上绘图模糊
    this.renderer.setViewport(0, 0, this.view.width, this.view.height); // 视口大小设置

    this.renderer.autoClear = false;
    this.renderer.autoClearDepth = false;
    this.renderer.autoClearColor = false;
    // this.renderer.autoClearStencil = false;

    let originalSetRenderTarget = this.renderer.setRenderTarget.bind(
      this.renderer
    );
    this.renderer.setRenderTarget = function (target) {
      originalSetRenderTarget(target);
      if (target == null) {
        context.bindRenderTarget();
      }
    };

    const trans = new Array(16).fill(0);
    externalRenderers.renderCoordinateTransformAt(
      this.view,
      [this.center[0], this.center[1], 0],
      this.view.spatialReference,
      trans
    );
    this.trans = new THREE.Matrix4().fromArray(trans);
    this.scene = new THREE.Scene();
    // setup the camera
    let cam = context.camera;
    this.camera = new THREE.PerspectiveCamera(
      cam.fovY,
      cam.aspect,
      cam.near,
      cam.far
    );

    // 添加坐标轴辅助工具
    const axesHelper = new THREE.AxesHelper(1);
    axesHelper.position.copy(1000000, 100000, 100000);
    this.scene.add(axesHelper);

    function spawnBehavior(index) {
      var v = new THREE.Vector3(0, 0, 0);

      var dX, dY, dZ;

      dZ = Math.random() * 20 + 10;
      dX = Math.random() * 4 - 2;
      dY = Math.random() * 4 - 2;
      v.velocity = new THREE.Vector3(dX, dY, dZ);

      return v;
    }

    var points = new THREE.Geometry();
    // _.times(this.particleCount, function (index) {
    //   points.vertices.push(spawnBehavior(index));
    // })
    let index = 0;
    while (index++ < this.particleCount) {
      points.vertices.push(spawnBehavior(index));
    }
    let material = new THREE.PointCloudMaterial({
      size: 1,
      color: this.color,
    });

    this.pointCloud = new THREE.PointCloud(points, material);
    this.scene.add(this.pointCloud);

    var ambient = new THREE.AmbientLight(0xffffff, 1);
    ambient.position.set(0, 100, 0);
    this.scene.add(ambient);
    context.resetWebGLState();
  }
  render(context) {
    let that = this;
    const cam = context.camera;
    this.camera.projectionMatrix.multiplyMatrices(
      { elements: cam.viewProjectionMatrix },
      this.trans
    );

    function frameBehavior(particle) {
      particle.x += particle.velocity.x;
      particle.y += particle.velocity.y;
      particle.z += particle.velocity.z;

      particle.velocity.z -= .25;

      if (particle.z < that.center[2]) {
        particle.x = particle.y = 0;
        particle.z = that.center[2];
        var dX, dY, dZ;
        dZ = Math.random() * 4 + 10;
        dX = Math.random() * 4 - 2;
        dY = Math.random() * 4 - 2;
        particle.velocity = new THREE.Vector3(
          dX * that.sizeLevel,
          dY * that.sizeLevel,
          dZ * that.sizeLevel
        ); //乘以倍数 调整喷泉大小
      }
    }

    this.pointCloud.geometry.vertices.forEach(frameBehavior);
    this.pointCloud.geometry.verticesNeedUpdate = true;
    this.pointCloud.geometry.colorsNeedUpdate = true;

    // 绘制场景
    this.renderer.state.reset();
    this.renderer.render(this.scene, this.camera);
    // 请求重绘视图。
    externalRenderers.requestRender(this.view);
    // cleanup
    context.resetWebGLState();
  }
}
