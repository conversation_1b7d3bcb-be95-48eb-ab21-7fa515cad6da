import {request} from '@/utils/request'
// 获取分页查询
export function transportList(params) {
  return request({
    url: '/business/transport/list',
    method: 'get',
    params
  })
}

// 新增
export function addTransport(data) {
  return request({
    url: '/business/transport/add',
    method: 'post',
    data
  })
}

// 查看
export function getTransport(id) {
  return request({
    url: `/business/transport/${id}`,
    method: 'get'
  })
}

// 修改
export function editTransport(data) {
  return request({
    url: '/business/transport/edit',
    method: 'post',
    data
  })
}

// 新增
export function removeTransport(id) {
  return request({
    url: `/business/transport/remove/${id}`,
    method: 'post'
  })
}
