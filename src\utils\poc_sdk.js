//======================================动态同步加载JS,请勿修改=======================================
loadjs = (function () {
  var h = function () {},
    c = {},
    u = {},
    f = {};
  function o(e, n) {
    if (e) {
      var r = f[e];
      if (((u[e] = n), r)) for (; r.length; ) r[0](e, n), r.splice(0, 1);
    }
  }
  function l(e, n) {
    e.call && (e = { success: e }),
      n.length ? (e.error || h)(n) : (e.success || h)(e);
  }
  function d(r, t, s, i) {
    var c,
      o,
      e = document,
      n = s.async,
      u = (s.numRetries || 0) + 1,
      f = s.before || h,
      l = r.replace(/[\?|#].*$/, ""),
      a = r.replace(/^(css|img)!/, "");
    (i = i || 0),
      /(^css!|\.css$)/.test(l)
        ? (((o = e.createElement("link")).rel = "stylesheet"),
          (o.href = a),
          (c = "hideFocus" in o) &&
            o.relList &&
            ((c = 0), (o.rel = "preload"), (o.as = "style")))
        : /(^img!|\.(png|gif|jpg|svg|webp)$)/.test(l)
        ? ((o = e.createElement("img")).src = a)
        : (((o = e.createElement("script")).src = r),
          (o.async = void 0 === n || n)),
      !(o.onload =
        o.onerror =
        o.onbeforeload =
          function (e) {
            var n = e.type[0];
            if (c)
              try {
                o.sheet.cssText.length || (n = "e");
              } catch (e) {
                18 != e.code && (n = "e");
              }
            if ("e" == n) {
              if ((i += 1) < u) return d(r, t, s, i);
            } else if ("preload" == o.rel && "style" == o.as)
              return (o.rel = "stylesheet");
            t(r, n, e.defaultPrevented);
          }) !== f(r, o) && e.head.appendChild(o);
  }
  function r(e, n, r) {
    var t, s;
    if ((n && n.trim && (t = n), (s = (t ? r : n) || {}), t)) {
      if (t in c) throw "LoadJS";
      c[t] = !0;
    }
    function i(n, r) {
      !(function (e, t, n) {
        var r,
          s,
          i = (e = e.push ? e : [e]).length,
          c = i,
          o = [];
        for (
          r = function (e, n, r) {
            if (("e" == n && o.push(e), "b" == n)) {
              if (!r) return;
              o.push(e);
            }
            --i || t(o);
          },
            s = 0;
          s < c;
          s++
        )
          d(e[s], r, n);
      })(
        e,
        function (e) {
          l(s, e), n && l({ success: n, error: r }, e), o(t, e);
        },
        s
      );
    }
    if (s.returnPromise) return new Promise(i);
    i();
  }
  return (
    (r.ready = function (e, n) {
      return (
        (function (e, r) {
          e = e.push ? e : [e];
          var n,
            t,
            s,
            i = [],
            c = e.length,
            o = c;
          for (
            n = function (e, n) {
              n.length && i.push(e), --o || r(i);
            };
            c--;

          )
            (t = e[c]), (s = u[t]) ? n(t, s) : (f[t] = f[t] || []).push(n);
        })(e, function (e) {
          l(n, e);
        }),
        r
      );
    }),
    (r.done = function (e) {
      o(e, []);
    }),
    (r.reset = function () {
      (c = {}), (u = {}), (f = {});
    }),
    (r.isDefined = function (e) {
      return e in c;
    }),
    r
  );
})();
//======================================动态同步加载JS,请勿修改=======================================

// 确保 poc 对象在全局范围内可用
window.poc = window.poc || new (function () {
  this.data = {}; //数据接口相关函数/回调
  this.ptt = {}; //通信接口相关函数/回调
  this.video = {}; //视频接口相关函数/回调
})();

var poc = window.poc;

poc.JS_VERSION = "20200701 12:00:00";

// 创建一个初始化函数，返回 Promise
poc.initialize = function() {
  return new Promise((resolve, reject) => {
    // 加载基础依赖
    loadjs(
      [
        "/engine/ptt/webrtc-adapter.js",
        "/engine/ptt/fingerprint2.min.js",
        "/engine/data/aes.js",
        "/engine/data/pad-zeropadding.js",
        "/engine/data/axios.min.js",
        "/engine/data/dataAction.js",
      ],
      {
        success: function() {
          console.log('[POC] 基础依赖加载成功');
          
          // 检查 poc.data.auth 是否已定义
          if (typeof poc.data.auth !== 'function') {
            console.warn('[POC] poc.data.auth 未定义，添加本地实现');
            
            // 添加本地版本的 auth 函数
            poc.data.auth = function(objParameters) {
              if(objParameters && objParameters.constructor == Object){
                objParameters={...objParameters};
                var PostMsgUrl = objParameters.Url;
                var Body = JSON.stringify(objParameters);
                
                // 使用 axios 发送请求
                axios.post(PostMsgUrl, Body, {
                  headers: {
                    'Content-Type': 'application/json'
                  }
                })
                .then(function(response) {
                  if (objParameters.Callback) {
                    objParameters.Callback(response.data);
                  }
                })
                .catch(function(error) {
                  console.error('Auth request failed:', error);
                  if (objParameters.Callback) {
                    objParameters.Callback({isAuth: false, error: error.message});
                  }
                });
              }
              else {
                var requestFailed = {isAuth: false, error: "Invalid parameters"};
                if(objParameters && objParameters.Callback){
                  objParameters.Callback(requestFailed);
                } else {
                  return requestFailed;
                }
              }
            };
          }
          
          // 检测 WebRTC 和 WebAssembly 支持
          try {
            poc.isWebRTCSupported = (function () {
              try {
                if (
                  RTCPeerConnection !== undefined &&
                  RTCPeerConnection !== null &&
                  navigator.mediaDevices.getUserMedia !== undefined &&
                  navigator.mediaDevices.getUserMedia !== null
                ) {
                  return true;
                }
              } catch (e) {}
              return false;
            })();

            poc.isWebAssemblySupported = (function () {
              try {
                if (
                  typeof WebAssembly === "object" &&
                  typeof WebAssembly.instantiate === "function"
                ) {
                  var module = new WebAssembly.Module(
                    Uint8Array.of(0x0, 0x61, 0x73, 0x6d, 0x01, 0x00, 0x00, 0x00)
                  );
                  if (module instanceof WebAssembly.Module)
                    return new WebAssembly.Instance(module) instanceof WebAssembly.Instance;
                }
              } catch (e) {}
              return false;
            })();

            resolve(poc);
          } catch (error) {
            console.error('[POC] 初始化失败:', error);
            reject(error);
          }
        },
        error: function(pathsNotFound) {
          console.error('[POC] 基础依赖加载失败:', pathsNotFound);
          reject(new Error('基础依赖加载失败: ' + pathsNotFound.join(', ')));
        }
      }
    );
  });
};

// 修改 ptt.init 函数，使用 Promise
var Module = typeof Module !== "undefined" ? Module : {};
poc.ptt.init = function () {
  return new Promise((resolve, reject) => {
    if (!poc.isWebRTCSupported || !poc.isWebAssemblySupported) {
      reject(new Error("WebRTC or WebAssembly unsupported!"));
      return;
    }

    Module.onRuntimeInitialized = function () {
      console.log("[POC] Info: Module.onRuntimeInitialized!");

      // 加载 janus 视频相关
      loadjs(
        [
          "/engine/video/ctl_mdsvideo.js",
          "/engine/video/janus.js",
          "/engine/video/mdsvideoclass.js",
        ],
        {
          success: function() {
            console.log('[POC] 视频模块加载成功');
            
            // 初始化 ptt 内部模块
            if (typeof poc.ptt.init_internal === 'function') {
              poc.ptt.init_internal(poc)
                .then(function (x) {
                  console.log('[POC] PTT 内部初始化成功');
                  resolve();
                })
                .catch(function (err) {
                  console.error('[POC] PTT 内部初始化失败:', err);
                  reject(err);
                });
            } else {
              console.warn('[POC] poc.ptt.init_internal 未定义，跳过初始化');
              resolve();
            }
          },
          error: function(pathsNotFound) {
            console.error('[POC] 视频模块加载失败:', pathsNotFound);
            reject(new Error('视频模块加载失败: ' + pathsNotFound.join(', ')));
          }
        }
      );
    };

    // 加载 ptt 对讲相关
    loadjs(
      [
        "/engine/ptt/BenzAMRRecorder.min.js",
        "/engine/ptt/web_engine.js",
      ],
      {
        success: function() {
          console.log('[POC] PTT 对讲模块加载成功');
          // web_engine.js 会触发 Module.onRuntimeInitialized
        },
        error: function(pathsNotFound) {
          console.error('[POC] PTT 对讲模块加载失败:', pathsNotFound);
          reject(new Error('PTT 对讲模块加载失败: ' + pathsNotFound.join(', ')));
        }
      }
    );
  });
};

// 导出 poc 对象
module.exports = poc;
