<template>
  <div v-loading="loading" element-loading-text="地图数据加载中..." element-loading-background="rgba(0, 0, 0, 0.8)" class="map-box">
    <m-header />
    <div id="map" ref="map" />
    <div class="link">
      <div class="link-item" style="margin-right: 10px;" @click="handleOpenPage('dataView')">
        <svg-icon icon-class="server" style="color: #fff; font-size: 16px;" />
      </div>
      <div class="link-item" @click="handleOpenPage('monitor')">
        <svg-icon icon-class="boardMonitor" style="color: #fff; font-size: 16px;" />
      </div>
    </div>
    <!-- 右边菜单选择模块 -->
    <m-menu />
    <!-- <m-b-menu /> -->
    <!-- 志愿者弹窗 -->
    <vol-window-info ref="volWindowInfo" />
    <!-- 商铺弹窗 -->
    <shop-window-info ref="shopWindowInfo" />
    <!-- 人员弹窗 -->
    <recorder-window-info ref="recorderWindowInfo" :visible.sync="recorderVisible" :detail-id="recorderId" />
    <!-- 出租车弹窗 -->
    <taxiWindowInfo ref="taxiWindowInfo" :visible.sync="taxiVisible" :detail-id="taxiId" />
    <!-- 执法车辆弹窗 -->
    <lawCarWindowInfo ref="lawCarWindowInfo" :visible.sync="lawCarVisible" :detail-id="lawCarId" />
    <!-- 视频 -->
    <video-box :visible.sync="$store.getters.videoVisible" :terminal-no="terminalNo" :modal="false" :close-on-click-modal="false" />
    <!-- 海康视频控件 -->
    <windowVideo v-model="hkVideoVisible" :video-name="winVideoName" :video-id="hkVideoId" />
    <!-- 四位一体详情 -->
    <fourWindow :visible.sync="fourWindowVisible" title="四位一体详情" :hand-disabled="false" :type-data="typeData" :detail-id="fourWindowId" :form-disabled="true" />
    <!-- 监控抓拍详情 -->
    <captureWindow :visible.sync="captureWindowVisible" :form-disabled="true" title="监控抓拍详情" :detail-id="captureWindowId" />
    <!-- 巡查发现详情 -->
    <el-dialog :close-on-click-modal="false" title="巡查发现详情" :visible.sync="inspectionWindowVisible" width="1000px">
      <inspectionWindow v-if="inspectionWindowVisible" :detail-id="inspectionWindowId" :form-disabled="true" :qd="false" @oncancel="inspectionWindowVisible = false" />
    </el-dialog>
    <!-- 简易案件 -->
    <el-dialog :close-on-click-modal="false" title="简易案件详情" :visible.sync="punishWindowVisible" width="1000px">
      <punishWindow v-if="punishWindowVisible" :detail-id="punishWindowId" :form-disabled="true" :qd="false" @oncancel="punishWindowVisible = false" />
    </el-dialog>
    <!-- 黄牛处置 -->
    <el-dialog :close-on-click-modal="false" title="黄牛处置详情" :visible.sync="toutWindowVisible" width="1000px">
      <toutWindow v-if="toutWindowVisible" :detail-id="toutWindowId" :qd="true" @oncancel="toutWindowVisible = false" />
    </el-dialog>
    <!-- 电子抓拍 -->
    <trafficCaptureWindow :visible.sync="trafficCaptureWindowVisible" title="智能抓拍详情" :detail-id="trafficCaptureWindowId" :form-disabled="true" />
    <!-- 违规处置 -->
    <transportWindow :visible.sync="transportWindowVisible" title="违规处置详情" :detail-id="transportWindowVisibleId" :form-disabled="true" :car-type-options="transportWindowTypeData" />
    <!-- 热力图 -->
    <div class="left-btn-box">
      <div class="heatmapBox" @click="handleOpenHot">
        <div class="bg">
          <p>案卷热力图</p>
          <p class="e-title">Thermodynamic diagram</p>
        </div>
      </div>
      <div class="userListBtn" @click="visibleUserList = !visibleUserList">
        <div class="bg">
          <p>人员列表</p>
          <p class="e-title">Personnel list</p>
        </div>
      </div>
      <!-- 人员列表弹窗 -->
      <UserList :visible.sync="visibleUserList" :userlist-data="userlistData" @setTerminalNo="no => terminalNo = no" @changeCenter="changeCenter" @openDetail="handleOpenUserDetail" />
    </div>
    <!-- 热力图弹窗 -->
    <HeatmapOverlay :visible.sync="heatmapVisible" />
    <!-- 消息提示音 -->
    <audio ref="audio" controls="controls" hidden src="@/assets/dd.mp3" />
  </div>
</template>

<script>
import { fn_start, fn_Exit } from '@/assets/idt/Idt'
import mHeader from '../components/header/index.vue'
import mMenu from '../components/menu/index.vue'
// import mBMenu from '../components/b-menu/index.vue'
import { mapMutations } from 'vuex'
import { createAllOverLay, changeData, eventChangeData, showUserMarker } from './setMarker'
import { listAll } from '@/api/board/map/index'
import { createSocket } from '@/utils/websocket'
import volWindowInfo from '../components/windowInfo/vol/index.vue'
import shopWindowInfo from '../components/windowInfo/shop/index.vue'
import recorderWindowInfo from '../components/windowInfo/recorder/index.vue'
import taxiWindowInfo from '../components/windowInfo/taxi/index.vue'
import lawCarWindowInfo from '../components/windowInfo/lawCar/index.vue'
import videoBox from '../components/windowInfo/videoBox/index.vue'
import windowVideo from '../components/windowVideo/index.vue'
import fourWindow from '@/pages/supervise/views/jgdc/quaternity/components/fromlist.vue'
import captureWindow from '@/pages/case/views/synthetical/capture/components/fromlist.vue'
import inspectionWindow from '@/pages/case/views/synthetical/patrol/components/fromlist.vue'
import punishWindow from '@/pages/case/views/synthetical/punishment/components/fromlist.vue'
import toutWindow from '@/pages/case/views/tout/hncz/components/fromlist.vue'
import trafficCaptureWindow from '@/pages/case/views/synthetical/aicapture/components/fromlist.vue'
import transportWindow from '@/pages/case/views/pipe/illegal/components/fromlist.vue'
import HeatmapOverlay from '../components/windowInfo/HeatmapOverlay/index.vue'
import UserList from '../components/userList/index.vue'
import notifyMsg from '../components/notifyMsg/index.vue'

export default {
  components: {
    mHeader,
    mMenu,
    volWindowInfo,
    shopWindowInfo,
    recorderWindowInfo,
    taxiWindowInfo,
    lawCarWindowInfo,
    videoBox,
    windowVideo,
    fourWindow,
    captureWindow,
    inspectionWindow,
    punishWindow,
    toutWindow,
    trafficCaptureWindow,
    transportWindow,
    HeatmapOverlay,
    UserList
    // mBMenu
  },
  data() {
    return {
      loading: false,
      $map: null,
      markerData: [],
      recorderVisible: false,
      recorderId: undefined,
      taxiVisible: false,
      taxiId: undefined,
      lawCarVisible: false,
      lawCarId: undefined,
      terminalNo: '',
      hkVideoVisible: false,
      winVideoName: '',
      hkVideoId: '',
      typeData: [{id: 1, value: '环卫保洁'}, {id: 2, value: '园林'}, {id: 3, value: '绿化'}, {id: 4, value: '市政'} ],
      fourWindowVisible: false,
      fourWindowId: 0,
      captureWindowVisible: false,
      captureWindowId: 0,
      inspectionWindowVisible: false,
      inspectionWindowId: 0,
      punishWindowVisible: false,
      punishWindowId: 0,
      toutWindowVisible: false,
      toutWindowId: 0,
      trafficCaptureWindowVisible: false,
      trafficCaptureWindowId: 0,
      transportWindowVisible: false,
      transportWindowVisibleId: 0,
      transportWindowTypeData: [],
      heatmapVisible: false,
      visibleUserList: false,
      userlistData: [],
      notifyData: {}
    }
  },
  computed: {
    overLayStatus() {
      return this.$store.state.board.overLayStatus
    }
  },
  watch: {
    '$store.getters.callType'(nVal) {
      if (nVal == 'callIn') {
        // 当有执法记录仪呼入时
        this.$notify.info({
          title: `${this.$store.state.board.callArgs.name}呼入`,
          showClose: false,
          position: 'bottom-right',
          duration: 0,
          dangerouslyUseHTMLString: true,
          message: <notifyMsg />
        })
      }
    }
  },
  mounted() {
    this.getDicts('transport_car_type').then(res => {
      this.transportWindowTypeData = res.data
    })
    this.mapInit()
    fn_start()
  },
  destroyed() {
    window.removeEventListener('onmessageWS', this.getsocketData)
    fn_Exit()
  },
  methods: {
    ...mapMutations('board', ['SET_MAP', 'SET_STATUS']),
    changeCenter(id) {
      showUserMarker(this.$map, id)
    },
    handleOpenUserDetail({ id, terminalNo }) {
      this.recorderVisible = true
      this.recorderId = id
      this.terminalNo = terminalNo
    },
    handleOpenPage(url) {
      this.$router.push({ path: url })
    },
    handleOpenHot() {
      this.heatmapVisible = !this.heatmapVisible
    },
    mapInit() {
      this.$map = new window.T.Map(this.$refs.map)
      this.$map.centerAndZoom(new window.T.LngLat(119.63139, 29.1136), 17)
      this.$map.setMaxZoom(22)
      // this.$map.setStyle('indigo')
      // 初次创建坐标点
      this.overLayInit()
      // 添加地图实例至vuex
      this.SET_MAP(this.$map)
    },
    overLayInit() {
      this.loading = true
      listAll().then(res => {
        this.loading = false
        // 创建覆盖物
        let resData = res.data
        if (res.data.event && res.data.event.length) {
          res.data.event.forEach(item => {
            const type = item.caseType
            if (Array.isArray(resData[type])) {
              resData[type].push(item)
            } else {
              resData[type] = [item]
            }
          })
        }
        if (resData.event) delete resData.event
        if (resData.recorder) {
          this.userlistData = resData.recorder
          // resData.recorder = [resData.recorder[9], resData.recorder[10], resData.recorder[11]]
          // resData.recorder.splice(14, 1)
        }
        createAllOverLay(this.$map, resData, this.overLayEvent)
        // 覆盖物渲染完毕，链接websocket
        createSocket({ screen: this.$store.getters.uid })
        window.addEventListener('onmessageWS', this.getsocketData)
      }).catch(() => {
        this.loading = false
      })
    },
    overLayEvent(e) {
      const marker = e.target
      if (marker.dataSet.type == 'vol') {
        this.openWindowInfo(marker, 'volWindowInfo')
      } else if (marker.dataSet.type == 'shop') {
        this.$refs.shopWindowInfo.fImgSrc = null
        this.$refs.shopWindowInfo.fSrcList = []
        this.openWindowInfo(marker, 'shopWindowInfo')
      } else if (marker.dataSet.type == 'recorder') {
        this.recorderVisible = true
        this.recorderId = marker.dataSet.id
        this.terminalNo = marker.dataSet.terminalNo
      } else if (marker.dataSet.type == 'taxi') {
        this.taxiVisible = true
        this.taxiId = marker.dataSet.vehicleIndexCode
      } else if (marker.dataSet.type == 'lawCar') {
        this.lawCarVisible = true
        this.lawCarId = marker.dataSet.id
      } else if (marker.dataSet.type == 'monitor') {
        this.hkVideoVisible = true
        this.winVideoName = marker.dataSet.name
        // this.hkVideoId = '33070289581314000020'
        this.hkVideoId = marker.dataSet.code
      } else if (marker.dataSet.type == 'event') {
        this.openEventWindow(marker.dataSet.caseType, marker.dataSet.id)
      }
    },
    openEventWindow(type, id) {
      if (type == 'four') {
        this.fourWindowVisible = true
        this.fourWindowId = id
      } else if (type == 'capture') {
        this.captureWindowVisible = true
        this.captureWindowId = id
      } else if (type == 'inspection') {
        this.inspectionWindowVisible = true
        this.inspectionWindowId = id
      } else if (type == 'punish') {
        this.punishWindowVisible = true
        this.punishWindowId = id
      } else if (type == 'autoCapture') {
        this.trafficCaptureWindowVisible = true
        this.trafficCaptureWindowId = id
      } else if (type == 'transport') {
        this.transportWindowVisible = true
        this.transportWindowVisibleId = id
      } else if (type == 'tout') {
        this.toutWindowVisible = true
        this.toutWindowId = id
      } else {
        this.$message.error('信息错误，请联系管理员')
      }
    },
    openWindowInfo(marker, windowName) {
      this.$refs[windowName].$el.style = 'display: block;'
      const windowInfo = new window.T.InfoWindow(this.$refs[windowName].$el, {
        autoPan: true,
        autoPanPadding: [50, 160],
        closeOnClick: true
      })
      windowInfo.setMaxWidth(1000)
      marker.openInfoWindow(windowInfo)
      this.$refs[windowName].windowDataInit(marker.dataSet.id, windowInfo)
    },
    // 接收websocket返回的数据
    getsocketData(e) {
      const data = e && e.detail.data
      try {
        const msgData = JSON.parse(data)
        if (Array.isArray(msgData)) {
          const type = msgData[0].type
          if (type == 'recorder') this.userlistData = msgData
          changeData(type, msgData, this.$map, this.overLayStatus[type], this.overLayEvent)
        } else {
          const type = msgData.type
          eventChangeData(type, msgData, this.$map, this.overLayStatus[type], this.overLayEvent)
          if (type == 'event' && msgData.caseType == 'autoCapture') this.eventNotify(msgData)
        }
      } catch (error) {
        // this.$message.error('监听数据格式错误，请联系管理员')
        console.error('监听数据格式错误，请联系管理员', error)
      }
    },
    // 监控抓拍警情弹窗
    eventNotify(msgData) {
      this.notifyData = msgData
      this.$refs.audio.currentTime = 0 // 从头开始播放提示音
      this.$refs.audio.play() // 播放
      this.$notify({
        title: '消息提醒',
        message: '抓拍到一件新的事件，请点击查看',
        type: 'warning',
        position: 'bottom-right',
        onClick: () => {
          this.trafficCaptureWindowVisible = true
          this.trafficCaptureWindowId = msgData.id
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.map-box {
  width: 100%;
  height: 100%;
  min-width: 1280px;
  min-height: 720px;
  position: absolute;
  top: 0;
  left: 0;
  .left-btn-box {
    width: pxtorem(180);
    position: absolute;
    left: pxtorem(30);
    top: pxtorem(100);
    z-index: 999;
    .heatmapBox,
    .userListBtn {
      background: #fff;
      border-radius: 10px;
      box-shadow: 0 4px 14px rgba(0, 0, 0, 0.2);
      cursor: pointer;
      margin-bottom: pxtorem(10);
      overflow: hidden;
      .bg {
        color: #fff;
        font-size: pxtorem(16);
        background: url(@/assets/images/menu-btn.png) no-repeat center center / 100% 100%;
        padding: 10px;
        margin: 10px;
        p {
          margin: 0;
        }
        .e-title {
          font-size: 12px;
        }
      }
    }
  }
  .link {
    width: pxtorem(210);
    padding: pxtorem(14) pxtorem(20);
    position: absolute;
    top: pxtorem(100);
    right: pxtorem(30);
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 14px rgba(0, 0, 0, 0.2);
    z-index: 999;
    display: flex;
    justify-content: space-between;
    .link-item {
      flex: 1;
      color: #fff;
      height: pxtorem(40);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: pxtorem(14);
      background: url(@/assets/images/menu-btn.png) no-repeat center center / 100% 100%;
      cursor: pointer;
    }
  }
  #map {
    width: 100%;
    height: 100%;
    ::v-deep .tdt-label {
      line-height: 14px;
      background: transparent;
      color: #fff;
      box-shadow: none;
      border: none;
      padding: 0;
      span {
        background: #bbb;
        border-radius: 5px;
        text-align: center;
        border: 2px solid #fff;
        padding: 2px 5px;
        position: relative;
        left: -50%;
        &.online {
          background: #ffc600;
        }
      }
    }
  }
}
</style>
