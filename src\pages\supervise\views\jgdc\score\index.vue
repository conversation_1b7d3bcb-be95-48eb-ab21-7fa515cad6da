<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" :inline="true">
      <el-form-item label="日期">
        <el-date-picker
          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px;"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="getList">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="recordList" height="calc(100vh - 225px)">
      <el-table-column label="日期" align="center" prop="date" width="180" />
      <el-table-column v-for="(item, idx) in tableTitle" :key="idx" :label="item.deptName" align="center">
        <el-table-column :label="item.leader" align="center" :prop="`num${idx}`" />
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { listRecord } from '@/api/supervise/score'

export default {
  name: 'Record',
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 督查积分记录表格数据
      recordList: [],
      tableTitle: [],
      // 查询参数
      queryParams: {
        dateRange: []
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询督查积分记录列表 */
    getList() {
      let { dateRange } = this.queryParams
      let params = {}
      if (dateRange && dateRange.length) params = { startTime: dateRange[0], endTime: dateRange[1] }
      this.loading = true
      console.log(params)
      listRecord(params).then(response => {
        console.log(response)
        this.tableTitle = response.data.title
        this.recordList = Object.keys(response.data.data).map(date => {
          const valAry = response.data.data[date]
          let item = { date: date }
          valAry.forEach((num, idx) => {
            item[`num${idx}`] = num
          })
          return item
        })
        // this.recordList = response.rows
        // this.total = response.total
        this.loading = false
      })
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.dateRange = []
      this.getList()
    }
  }
}
</script>
