<template>
  <div>
    <CommonTitle text="告警记录"></CommonTitle>
    <div class="table-container">
      <TableComponent :thConfig="thConfig" :tableData="tableData" :tableHeight="460"></TableComponent>
    </div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import TableComponent from '@/components/TableComponent'
import { getAlarmRecords } from '@/api/wlwsb/index.js'
export default {
  name: 'index',
  components: {
    CommonTitle,
    TableComponent,
  },
  data() {
    return {
      //表格
      tableData: [],
      thConfig: [
        {
          th: '时间',
          field: 'gatherTime',
          width: '20%',
          hover: false,
        },
        {
          th: '类型',
          field: 'info',
          width: '15%',
          hover: true,
        },
        {
          th: '内容',
          field: 'gatherValue',
          width: '45%',
          hover: true,
        },
        {
          th: '持续时长',
          field: 'warningDuration',
          width: '20%',
          hover: false,
        },
      ],
    }
  },
  computed: {},
  mounted() {
    this.getData()
  },
  methods: {
    getData() {
      getAlarmRecords({ deviceCode: '330783002010100002000299050000000001' }).then((res) => {
        if (res.data) {
          this.tableData = res.data
        }
      })
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
.table-container {
  width: 100%;
  // // height:600px;
  padding: 20px 20px 30px 20px;
  box-sizing: border-box;
}
</style>