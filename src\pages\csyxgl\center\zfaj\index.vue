<template>
  <div>
    <CommonTitle text="执法案件">
      <TabSwitch :tabList="list" :activeIndex="index" @tab-change="handleTabChange" />
    </CommonTitle>
    <div class="chart_box">
      <div class="chart_box_item">
        <div class="wrap-container" ref="chart"></div>
      </div>
      <div class="chart_box_item1">
        <div class="search_box">
          <el-select v-model="value1" placeholder="请选择" style="width: 220px; margin-left: 20px" @change="getData1">
            <el-option v-for="item in options1" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
          <el-select v-model="value" placeholder="请选择" style="width: 220px; margin-left: 20px" @change="getData1">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </div>
        <CommonTable :height="'340px'" :tableData="tableData" style="margin-top: 16px" :showIndex="true"></CommonTable>
      </div>
    </div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import TabSwitch from '@/components/TabSwitch'
import CommonTable from '@/components/CommonTable'
import { getEventTypeJscStatistics, getEventJscStatistics, getDictType } from '@/api/csyxgl/index.js'
export default {
  name: 'index',
  components: {
    CommonTitle,
    TabSwitch,
    CommonTable,
  },
  data() {
    return {
      index: 0,
      list: [
        { name: '案件类型', value: '1' },
        { name: '行政区划', value: '2' },
      ],
      chatData: [],
      value1: '',
      options1: [],
      value: '',
      options: [],
      tableData: {
        thead: [
          { label: '案件类型', property: 'type', width: 140, align: 'left' },
          { label: '行政区划', property: 'deptname', width: 140, align: 'left' },
          { label: '案件数量', property: 'acount', width: 100, align: 'left', sortable: true },
        ],
        tbody: [],
      },
    }
  },
  computed: {},
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.getDict()
      this.getData()
      this.getData1()
    },
    getDict() {
      getDictType('county').then((res) => {
        this.options = res.data.map((item) => {
          return {
            label: item.dictLabel,
            value: item.dictValue,
          }
        })
        this.options.unshift({
          label: '全部',
          value: '',
        })
      })
      getDictType('zhcg_event').then((res) => {
        this.options1 = res.data.map((item) => {
          return {
            label: item.dictLabel,
            value: item.dictValue,
          }
        })
        this.options1.unshift({
          label: '全部',
          value: '',
        })
      })
    },
    getData() {
      getEventTypeJscStatistics({ type: this.list[this.index].value }).then((res) => {
        this.chatData = res.data.map((item) => {
          return {
            name: item.name,
            value: item.acount,
          }
        })
        this.initChart()
      })
    },
    getData1() {
      let params = {}
      if (this.value1) {
        params.type2id = this.value1
      }
      if (this.value) {
        params.areaid = this.value
      }
      getEventJscStatistics(params).then((res) => {
        this.tableData.tbody = res.data
      })
    },
    handleTabChange(i) {
      this.index = i
      this.getData()
    },
    initChart() {
      this.chart = this.$echarts.init(this.$refs.chart)
      var total = 0
      this.chatData.forEach((item) => {
        total += item.value
      })
      const option = {
        backgroundColor: 'transparent',
        graphic: {
          type: 'image',
          style: {
            image: require('@/assets/dog/chartBg.png'),
            width: 360,
            height: 360,
          },
          left: '3', // 调整背景图位置，与环形图对齐
          top: 'center',
        },
        title: [
          {
            text: total,
            left: '18.5%',
            top: '48%',
            textStyle: {
              color: '#E3F4FF',
              fontSize: 52,
              fontWeight: 'normal',
              fontFamily: 'DIN',
              lineHeight: 72,
            },
            z: 10, // 确保文字在背景图之上
          },
          {
            text: '公用设施',
            left: '19%',
            top: '38%',
            textStyle: {
              color: '#E3F4FF',
              fontSize: 28,
              fontWeight: 'normal',
              lineHeight: 28,
            },
            z: 10, // 确保文字在背景图之上
          },
        ],
        legend: {
          orient: 'vertical',
          left: '52%',
          y: 'center',
          itemWidth: 16,
          itemHeight: 16,
          itemGap: 28,
          icon: 'circle',
          formatter: (name) => {
            var data = option.series[0].data //获取series中的data
            let tarValue = 0
            for (var i = 0, l = data.length; i < l; i++) {
              if (data[i].name == name) {
                tarValue = data[i].value
                return `{name|${name}}{value|${tarValue}}`
              }
            }
          },
          textStyle: {
            rich: {
              name: {
                fontSize: 26,
                color: '#fff',
                padding: [0, 30, 0, 0],
              },
              value: {
                fontSize: 26,
                color: '#fff',
                padding: [0, 0, 0, 0],
              },
            },
          },
        },
        series: [
          {
            type: 'pie',
            radius: ['55%', '65%'],
            center: ['26%', '50%'],
            startAngle: 90,
            itemStyle: {
              borderRadius: 0,
              borderColor: 'rgba(2,47,115,0.5)',
              borderWidth: 2,
            },
            data: this.chatData,
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
            emphasis: {
              scale: false,
            },
          },
        ],
      }

      this.chart.setOption(option)
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
.chart_box {
  display: flex;
  .chart_box_item {
    width: 700px;
    .wrap-container {
      width: 100%;
      height: 460px;
      position: relative;
    }
  }
  .chart_box_item1 {
    flex: 1;
    margin: 0 40px;
    .search_box {
      display: flex;
      align-content: center;
      align-items: center;
      /deep/.el-input__inner {
        height: 48px !important;
        background-color: #132c4e !important;
        border: 2px solid #afdcfb !important;
        color: #fff !important;
        border-radius: 15px !important;
        font-size: 24px;
      }
    }
  }
}
</style>