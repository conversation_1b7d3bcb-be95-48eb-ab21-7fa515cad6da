<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--部门数据-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-input
            v-model="deptName"
            placeholder="请输入案由名称"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px;"
          />
        </div>
        <div class="head-container">
          <el-tree
            ref="tree"
            :data="summaryOptions"
            :props="defaultProps"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            default-expand-all
            @node-click="handleNodeClick"
          />
        </div>
      </el-col>
      <!--数据-->
      <el-col :span="20" :xs="24">
        <el-form v-show="showSearch" ref="queryForm" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="案由名称" prop="title">
            <el-input
              v-model="queryParams.searchValue"
              placeholder="请输入案由名称"
              clearable
              size="small"
              style="width: 240px;"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="案由内容" prop="content">
            <el-input
              v-model="queryParams.searchValue"
              placeholder="请输入案由内容"
              clearable
              size="small"
              style="width: 240px;"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['system:user:add']"
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
            >
              新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['system:user:edit']"
              type="success"
              plain
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
            >
              修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['system:user:remove']"
              type="danger"
              plain
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
            >
              删除
            </el-button>
          </el-col>

          <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
        </el-row>

        <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column label="案由名称" align="center" prop="title" />
          <el-table-column label="案由内容" align="center" prop="content" />
          <!-- <el-table-column  label="案由编号" align="center" prop="code"  /> -->
          <el-table-column label="违则" align="center" prop="violate" />
          <!-- <el-table-column  label="违则内容" align="center" prop="violateContent"  /> -->
          <!-- <el-table-column  label="罚则" align="center" prop="punish"  /> -->
          <!-- <el-table-column  label="罚则内容" align="center" prop="punishContent"  /> -->
          <el-table-column label="排序" align="center" prop="sort" width="80" />
          <el-table-column
            label="操作"
            align="center"
            width="160"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                v-hasPermi="['system:user:edit']"
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
              >
                修改
              </el-button>
              <el-button
                v-if="scope.row.summaryId !== 1"
                v-hasPermi="['system:user:remove']"
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="案由名称" prop="title">
              <el-input v-model="form.title" placeholder="请输入案由名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="归属案由" prop="parentId">
              <treeselect v-model="form.parentId" :options="summaryOptions" :show-count="true" placeholder="请选择归属案由" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="案由内容" prop="content">
              <el-input v-model="form.content" placeholder="请输入案由内容" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="案由编号" prop="code">
              <el-input v-model="form.code" placeholder="请输入案由编号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="类型" prop="menuType">
              <el-select v-model="form.menuType" placeholder="请选择案由类型" clearable size="small">
                <el-option label="菜单" value="1" />
                <el-option label="内容" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序" prop="sort">
              <el-input v-model="form.sort" placeholder="请输入案由排序" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="是否显示" prop="summaryType">
              <el-select v-model="form.summaryType" :style="{width: '100%'}">
                <el-option label="显示" value="1" />
                <el-option label="隐藏" value="0" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="违则" prop="violate">
              <el-input v-model="form.violate" type="textarea" placeholder="请输入违则" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="违则内容" prop="violateContent">
              <el-input v-model="form.violateContent" type="textarea" placeholder="请输入违则内容" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="罚则" prop="punish">
              <el-input v-model="form.punish" type="textarea" placeholder="罚则" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="罚则内容" prop="punishContent">
              <el-input v-model="form.punishContent" type="textarea" placeholder="罚则内容" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { treeselect, listSummary, getSummary, delSummary, addSummary, updateSummary} from '@/api/case/ay/summary'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'

export default {
  name: 'User',
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 案由表格数据
      dataList: null,
      // 弹出层案由名称
      title: '',
      // 部门树选项
      summaryOptions: undefined,
      // 是否显示弹出层
      open: false,
      // 部门名称
      deptName: undefined,
      // 默认密码
      initPassword: undefined,
      // 日期范围
      dateRange: [],
      // 状态数据字典
      statusOptions: [],
      // 性别状态字典
      sexOptions: [],
      // 岗位选项
      postOptions: [],
      // 角色选项
      roleOptions: [],
      // 表单参数
      form: {},
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        searchValue: undefined,
        parentId: 1
      },
      // 表单校验
      rules: {
        title: [
          { required: true, message: '案由名称不能为空', trigger: 'blur' }
        ],
        parentId: [
          { required: true, message: '案由归属不能为空', trigger: 'change' }
        ],
        menuType: [
          { required: true, message: '案由类型不能为空', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.getList()
    this.getTreeselect()
    this.getDicts('sys_normal_disable').then(response => {
      this.statusOptions = response.data
    })
  },
  methods: {
    /** 查询案由列表 */
    getList() {
      this.loading = true
      listSummary(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.dataList = response.rows
        this.total = response.total
        this.loading = false
      }
      )
    },
    /* 岗位下拉选择添加keys */
    handlePostKeys(list) {
      const postKeys = list.map(postId => {
        const postInfo = this.postOptions.find(item => item.postId == postId)
        return postInfo.postCode
      })
      this.form = { ...this.form, postKeys: postKeys.join(',') }
    },
    /** 查询部门下拉树结构 */
    getTreeselect() {
      treeselect({menuType: 1}).then(response => {
        this.summaryOptions = response.data
      })
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.parentId = data.id
      this.getList()
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        summaryId: undefined,
        title: undefined,
        content: undefined,
        code: undefined,
        violate: undefined,
        violateContent: undefined,
        punish: undefined,
        punishContent: undefined,
        remark: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.summaryId)
      this.single = selection.length != 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.getTreeselect()
      this.open = true
      this.title = '添加案由'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.getTreeselect()
      const summaryId = row.summaryId
      getSummary(summaryId).then(response => {
        if (response.data.summaryType != 1) response.data.summaryType = '0'
        this.form = response.data
        this.open = true
        this.title = '修改案由'
        this.form = { ...this.form }
      })
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.summaryId != undefined) {
            updateSummary(this.form).then(() => {
              this.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addSummary(this.form).then(() => {
              this.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const summaryIds = row.summaryId || this.ids
      this.$confirm('是否确认删除案由编号为"' + summaryIds + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return delSummary(summaryIds)
      }).then(() => {
        this.getList()
        this.msgSuccess('删除成功')
      })
    }
  }
}
</script>
