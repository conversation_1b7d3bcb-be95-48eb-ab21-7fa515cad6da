import {request} from '@/utils/request'

// 查询流动书吧书籍列表
export function listBook(query) {
  return request({
    url: '/business/vol/book/list',
    method: 'get',
    params: query
  })
}

// 查询流动书吧书籍分类列表
export function listCategory(query) {
  return request({
    url: '/business/vol/book/category/list',
    method: 'get',
    params: query
  })
}

// 查询流动书吧书籍详情
export function getBook(id) {
  return request({
    url: '/business/vol/book/' + id,
    method: 'get'
  })
}

// 新增流动书吧书籍
export function addBook(data) {
  return request({
    url: '/business/vol/book/add',
    method: 'post',
    data: data
  })
}

// 修改流动书吧书籍
export function updateBook(data) {
  return request({
    url: '/business/vol/book/edit',
    method: 'post',
    data: data
  })
}

// 删除流动书吧书籍
export function delBook(id) {
  return request({
    url: '/business/vol/book/remove/' + id,
    method: 'post'
  })
}

// 导出流动书吧书籍
export function exportBook(query) {
  return request({
    url: '/business/vol/book/export',
    method: 'get',
    params: query
  })
}
