<template>
  <div>
    <CommonTitle text="风险预警"></CommonTitle>
    <CommonTitle2 text="事件统计"></CommonTitle2>
    <div id="sjtj" style="width: 100%; height: 430px"></div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import CommonTitle2 from '@/components/CommonTitle2'
import { getwarningCount } from '@/api/yxjc/index.js'
export default {
  name: 'index',
  components: {
    CommonTitle,
    CommonTitle2,
  },
  data() {
    return {
      year: '',
      pieChartData: [],
    }
  },
  computed: {},
  mounted() {
    this.year = localStorage.getItem('year')
    this.init()
    this.$bus.$on('yearChange', (res) => {
      if (res !== this.year) {
        this.year = res
        this.init()
      }
    })
  },
  methods: {
    init() {
      getwarningCount({ year: this.year }).then((res) => {
        this.pieChartData = res.data.map((item) => {
          return {
            name: item.key,
            value: item.value,
          }
        })
        this.initChart(this.pieChartData)
      })
    },
    initChart(data) {
      let total = 0
      data.forEach((item) => {
        total += item.value
      })
      this.chart = this.$echarts.init(document.getElementById('sjtj'))

      const option = {
        backgroundColor: 'transparent',
        color: ['#22E197', '#98DC3E', '#00EAFF', '#FF6A00'],
        graphic: {
          type: 'image',
          style: {
            image: require('@/assets/dog/chartBg.png'),
            width: 360,
            height: 360,
          },
          left: '8.4%', // 调整背景图位置，与环形图对齐
          top: 'center',
        },
        title: [
          {
            text: total,
            left: '20%',
            top: '48%',
            textStyle: {
              color: '#E3F4FF',
              fontSize: 52,
              fontWeight: 'normal',
              fontFamily: 'DIN',
              lineHeight: 72,
              textAlign: 'center',
            },
            z: 10, // 确保文字在背景图之上
          },
          {
            text: '事件总数',
            left: '19.5%',
            top: '38%',
            textStyle: {
              color: '#E3F4FF',
              fontSize: 28,
              fontWeight: 'normal',
              lineHeight: 28,
              textAlign: 'center',
            },
            z: 10, // 确保文字在背景图之上
          },
        ],
        legend: {
          orient: 'vertical',
          left: '46%',
          y: 'center',
          itemWidth: 20,
          itemHeight: 16,
          itemGap: 32,
          icon: 'circle',
          formatter: (name) => {
            var data = option.series[0].data //获取series中的data
            let tarValue = 0
            for (var i = 0, l = data.length; i < l; i++) {
              if (data[i].name == name) {
                tarValue = data[i].value
                return `{name|${name}}{value|${tarValue}}`
              }
            }
          },
          textStyle: {
            rich: {
              name: {
                fontSize: 28,
                color: '#fff',
                padding: [0, 30, 0, 0],
                width: 380,
              },
              value: {
                fontSize: 28,
                color: '#fff',
                padding: [0, 0, 0, 0],
              },
            },
          },
        },
        series: [
          {
            type: 'pie',
            radius: ['55%', '65%'],
            center: ['26%', '50%'],
            startAngle: 90,
            itemStyle: {
              borderRadius: 0,
              borderColor: 'rgba(2,47,115,0.5)',
              borderWidth: 2,
            },
            data: data,
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
            emphasis: {
              scale: false,
            },
          },
        ],
      }

      this.chart.setOption(option)
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
</style>