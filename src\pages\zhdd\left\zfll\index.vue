<template>
  <div>
    <CommonTitle text='执法力量'></CommonTitle>
    <div class="zfll-box">
      <div v-for="(item,i) in zfll" :key="i" class="zfll-box-item"
           :class="{mleft:i % 2 != 0,mbottom: i == 0 || i == 1}"
           @click="item.name != '其他'?showDialog(item.name):false">
<!--        <el-tooltip class="item" effect="dark" :content="item.info" placement="top-start" popper-class="tooltip">-->
          <div class="zfll-box-item-num" :title='item.info'>{{item.num}} <div class="unit">{{item.unit}}</div> </div>
<!--        </el-tooltip>-->
        <div class="zfll-box-item-name">{{item.name}}</div>
      </div>
      <div class="zfll-box-center"></div>
    </div>

    <zfllDialog :visible='visible' :type='type' @close='visible = false'></zfllDialog>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import { indexApi } from '@/api/indexApi'
import zfllDialog from './zfllDialog'
export default {
  name: 'index',
  components: {
    CommonTitle,
    zfllDialog
  },
  data() {
    return {
      year: localStorage.getItem("year"),
      city: localStorage.getItem("city"),
      zfll:[
        {
          name:"综合执法",
          num:0,
          unit:"人",
          info:""
        },
        {
          name:"专业领域",
          num:0,
          unit:"人",
          info:""
        },
        {
          name:"乡镇执法",
          num:0,
          unit:"人",
          info:""
        },
        {
          name:"其他",
          num:0,
          unit:"人",
          info:""
        }
      ],

      type:"",
      visible:false
    }
  },
  computed: {},
  mounted() {
    this.$bus.$on('cityChange', (city) => {
      this.city = city;
      this.initApi(city,localStorage.getItem("year"));
    })
    this.$bus.$on('yearChange', (year) => {
      this.year = year;
      this.initApi(localStorage.getItem("city"),year);
    })
    this.initApi(localStorage.getItem("city"),localStorage.getItem("year"));
  },
  methods: {
    initApi(city,year) {
      indexApi("/csdn_yjyp24",{qxwd:city}).then((res) => {
        this.zfll[0].num = this.getNumber(res.data[2].tjz); //综合
        this.zfll[1].num = this.getNumber(res.data[0].tjz); //专业
        this.zfll[2].num = this.getNumber(res.data[1].tjz); //乡镇
        this.zfll[3].num = res.data[3].tjz; //其它

        this.zfll[0].info = res.data[2].tjz
        this.zfll[1].info = res.data[0].tjz
        this.zfll[2].info = res.data[1].tjz
        this.zfll[3].info = res.data[3].tjz
      });
    },
    getNumber(str) {
      return str.split(",").length > 1 ? Number(str.split(",")[0].split(":")[1]) + Number(str.split(",")[1].split(":")[1])  :   Number(str.split(",")[0].split(":")[1])
    },
    showDialog(type) {
      this.type = type
      this.visible = true
    }
  },
  watch: {}
}
</script>

<style scoped lang='less'>
.zfll-box {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  height: 474px;
  margin-top: 20px;
  background: url("@/assets/zhdd/zfllBases.png");
  background-size: 100% 100%;
}

.mleft {
  margin-left: 459px;
}

.mbottom {
  position: relative;
  bottom: 30px;
}

.zfll-box-item {
  width: 209px;
  height: 216px;
  cursor: pointer;
}

.zfll-box-center {
  position: absolute;
  top: 1520px;
  width: 407px;
  height: 338px;
  text-align: center;
}

.zfll-box-item-num {
  text-align: center;
  font-size: 52px;
  font-family: DINCond-Bold;
  font-weight: 600;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(to bottom, #ffeccb, #ffffff, #ffc460, #ffe2b0, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
}

.zfll-box-item-name {
  text-align: center;
  font-size: 36px;
  font-family: Source Han Sans CN;
  font-weight: 600;
  background: linear-gradient(to bottom, #ffffff, #52C3F7);
  -webkit-background-clip: text;
  color: transparent;
}
.unit {
  font-size: 26px;
  margin: 14px 0 0 2px;
}

.tooltip {
  font-size: 30px;
}
</style>