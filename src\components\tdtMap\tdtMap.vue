<template>
  <el-dialog :close-on-click-modal="false" v-bind="$attrs" title="选择位置" v-on="$listeners" @open="onOpen" @close="onClose">
    <div class="mapCont">
      <div ref="tMap" class="divTdtMap" @click="drawMarker" />
      <div v-if="mapSearch" class="maps">
        <el-input v-model="keyWord" placeholder="请输入内容" class="input-with-select" clearable @input="searchMap">
          <el-button slot="append" icon="el-icon-search" @click="searchMap" />
        </el-input>
        <div v-if="tableData.length" class="mtable">
          <el-table size="mini" :data="tableData" style="width: 100%;" @row-click="rowClick">
            <el-table-column prop="name" label="姓名" width="180" />
            <el-table-column prop="address" label="地址" />
          </el-table>
        </div>
      </div>
    </div>
    <div slot="footer">
      <el-button @click="$emit('update:visible', false)">取 消</el-button>
      <el-button type="primary" @click="mapPrimary">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import axios from 'axios'
var control  = true
export default {
  name: 'TdtMap',
  inheritAttrs: false,
  props: {
    zoom: {
      type: Number,
      default() {
        return 15
      }
    },
    dw: {
      type: Object,
      default() {
        return {}
      }
    },
    mapSearch: {
      type: Boolean,
      default() {
        return false
      }
    },
    name: String
  },
  data() {
    return {
      tdtMapDivID: 'tdtMapDivID_' + this._uid,
      tdtMap: null,
      keyWord: '',
      tableData: [],
      onlnglat: {}
    }
  },
  watch: {},
  created() {},
  mounted() {

  },
  methods: {
    onOpen() {
      // 初始化天地图
      this.$nextTick(() => {
        this.initTdtMap()
      })
    },
    onClose() {
      this.keyWord = ''
      this.tableData = []
      if (this.tdtMap) this.tdtMap.clearOverLays()
    },
    // 初始化天地图
    initTdtMap() {
      if (!this.tdtMap) {
        this.tdtMap = new window.T.Map(this.$refs.tMap)
        this.tdtMap.centerAndZoom(new window.T.LngLat(this.dw.longitude || 119.63094, this.dw.latitude || 29.11286), this.zoom)
        document.getElementsByClassName('tdt-control-copyright tdt-control')[0].style.display = 'none'
      }
      if (this.dw.longitude) this.marker(this.dw.longitude, this.dw.latitude)
    },

    // 绘制标注(点)
    drawMarker() {
      var markerTool = new window.T.MarkTool(this.tdtMap, {follow: true})
      markerTool.open()
      // 绑定mouseup事件 在用户每完成一次标注时触发事件。
      markerTool.addEventListener('mouseup', this.getPoints)
    },
    // 绘制标记
    async getPoints(e) {
      // 清除标记
      this.tdtMap.clearOverLays()
      let {lng, lat }  = e.currentLnglat
      console.log(lng, lat)
      let address =  await this.getSite(lng, lat)
      // this.$emit('onlnglat', {lng, lat, address})
      this.onlnglat = {lng, lat, address}
      console.log(this.onlnglat)
      this.marker(lng, lat)
    },
    // 标注点的坐标
    marker(lng, lat) {
      var marker = new window.T.Marker(new window.T.LngLat(lng, lat))
      this.tdtMap.panTo(new window.T.LngLat(lng, lat), 18)
      this.tdtMap.addOverLay(marker)
    },
    // 经纬度获取具体地址
    async getSite(lon, lat) {
      return axios.get('https://api.tianditu.gov.cn/geocoder', {params: { tk: '3b3dad51fc70dd595f8e208e8751d10f', type: 'geocode', postStr: '{\'lon\':' + lon + ',\'lat\':' + lat + ',\'ver\':1}'}}).then(response => {
        console.log(response.data)
        return response.data.result.formatted_address
      })
    },
    // 地图搜索框
    searchMap() {
      if (this.keyWord.trim() == '') {
        this.tableData = []
      }
      //              输入内容             范围                         查询级别                   普通搜索        开始页数     返回条数
      // searchMap: { keyWord: '金华学校', mapBound: '-180,-90,180,90', level: '10', show: '1', queryType: '1', start: '0', count: '50'},
      let data = {keyWord: '金华市' + this.keyWord, mapBound: '-180,-90,180,90',  level: '1', show: '1', queryType: '1', start: '0', count: '50', layers: 'cva'}
      let params = JSON.stringify(data)
      if (!control) return
      control = false
      setTimeout(() => { control = true }, 1000)
      axios.get(`http://api.tianditu.gov.cn/v2/search?postStr=${params} &type=query&tk=3b3dad51fc70dd595f8e208e8751d10f`).then(res => {
        if (res.data.pois) {
          this.tableData = res.data.pois
        } else {
          this.tableData = []
        }
      })
    },
    rowClick(row) {
      let {address, lonlat} = row
      let lon = Number(lonlat.split(',')[0])
      let lat = Number(lonlat.split(',')[1])
      this.getPoints({currentLnglat: {lng: lon, lat, address}})
    },
    mapPrimary() {
      this.$emit('onlnglat', this.onlnglat, this.name)
    }
  }
}
</script>
<style scoped>
.divTdtMap {
  padding: 0;
  width: 100%;
  height: 65vh;
  z-index: 0;
  margin: 0 auto;
}
.maps {
  position: absolute;
  top: 10px;
  left: 30px;
  z-index: 100000;
}
.mapCont {
  /* width: 100%;
  height: 100%; */
  position: relative;
  margin: 0 auto;
}
.mtable {
  height: 500px;
  overflow: auto;
}

</style>
