import {request} from '@/utils/request'
let toutCase = {
  // 获取黄牛处置查询
  list(params) {
    return request({
      url: '/business/case/tout/list',
      method: 'get',
      params
    })
  },
  // 新增黄牛处置
  add(data) {
    return request({
      url: '/business/case/tout/add',
      method: 'post',
      data
    })
  },
  // 修改黄牛处置
  edit(data) {
    return request({
      url: '/business/case/tout/edit',
      method: 'post',
      data
    })
  },
  // 删除黄牛处置
  remove(data) {
    return request({
      url: '/business/case/tout/remove/' + data,
      method: 'post'
    })
  },
  // 获取详情
  getDetail(id) {
    return request({
      url: '/business/case/tout/' + id,
      method: 'get'
    })
  }
}
export default toutCase
