<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" :inline="true" label-width="100px">
      <!-- <el-form-item label="队员id" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入队员id"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="上报人">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入上报人"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属部门">
        <el-input
          v-model="queryParams.deptName"
          placeholder="请输入所属部门"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="上报时间">
        <el-date-picker
          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px;"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="getList"
        />
        <!-- <el-date-picker
          v-model="queryParams.happenTime"
          clearable
          size="small"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择上报时间"
        /> -->
      </el-form-item>
      <!-- <el-form-item label="经度" prop="longitude">
        <el-input
          v-model="queryParams.longitude"
          placeholder="请输入经度"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="纬度" prop="latitude">
        <el-input
          v-model="queryParams.latitude"
          placeholder="请输入纬度"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态 0-正常 1-停用" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态 0-正常 1-停用" clearable size="small">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="部门id" prop="deptId">
        <el-input
          v-model="queryParams.deptId"
          placeholder="请输入部门id"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:dynamic:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >
          新增
        </el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:dynamic:edit']"
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >
          修改
        </el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:dynamic:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="dynamicList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="自增id" align="center" prop="id" /> -->
      <!-- <el-table-column label="队员id" align="center" prop="userId" /> -->
      <el-table-column label="上报人" align="center" prop="userName" />
      <el-table-column label="所属部门" align="center" prop="deptName" />
      <!-- <el-table-column label="类型" align="center" prop="type" /> -->
      <el-table-column label="上报时间" align="center" prop="happenTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.happenTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="上报地址" align="center" prop="address" />
      <el-table-column label="上报内容" align="center" prop="content" />

      <!-- <el-table-column label="经度" align="center" prop="longitude" />
      <el-table-column label="纬度" align="center" prop="latitude" /> -->
      <!-- <el-table-column label="状态 0-正常 1-停用" align="center" prop="status" /> -->
      <!-- <el-table-column label="部门id" align="center" prop="deptId" /> -->
      <!-- <el-table-column label="所属部门名称" align="center" prop="deptName" /> -->
      <!-- <el-table-column label="备注" align="center" prop="remark" /> -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['business:dynamic:edit']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >
            修改
          </el-button>
          <el-button
            v-hasPermi="['business:dynamic:remove']"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改队员工作动态对话框 -->
    <div>
      <el-dialog class="m-dialog" :title="title" :visible.sync="open">
        <el-scrollbar style="height: 100%;">
          <el-row :gutter="15" style="margin-right: 10px;">
            <el-form ref="form" v-loading="formLoading" :model="form" :rules="rules" label-width="100px">
              <el-col :span="12">
                <el-form-item label="上报人" prop="userName">
                  <el-input v-model="form.userName" disabled placeholder="请输入上报人" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="所属部门" prop="deptName">
                  <el-input v-model="form.deptName" disabled placeholder="请输入所属部门" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="上报时间" prop="happenTime">
                  <el-date-picker
                    v-model="form.happenTime" clearable
                    size="small"
                    type="datetime"
                    value-format="yyyy-MM-dd hh:mm:ss"
                    placeholder="选择上报时间"
                  />
                  <!-- <el-date-picker v-model="form.happenTime" clearable
                                  size="small"
                                  type="date"
                                  value-format="yyyy-MM-dd"
                                  placeholder="选择上报时间"
                  /> -->
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="上报地址" prop="address">
                  <!-- <el-input v-model="form.address" type="textarea" placeholder="请输入内容" /> -->
                  <div>
                    <el-input v-model="form.address" placeholder="请选择上报地址">
                      <svg-icon slot="suffix" icon-class="map" class="svg-icon" @click="openMap = true" />
                    </el-input>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="工作内容" prop="content">
                  <!-- <editor v-model="form.content" :min-height="192" /> -->
                  <el-input v-model="form.content" type="textarea" rows="10" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="动态图片" prop="files">
                  <MFileUpload ref="mainFile" :limit="4" :ex-data="exData" :file-list="mainFileList" not-upload-msg="请上传动态图片" @uploadSucces="handleFileSuccess" @error="fileError" />
                </el-form-item>
              </el-col>
            </el-form>
          </el-row>
        </el-scrollbar>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" :loading="formLoading" @click="submitForm(9)">提 交</el-button>
          <el-button :loading="formLoading" @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
    </div>
    <!-- 地图选择 -->
    <tdtMap ref="bindmap" :visible.sync="openMap" append-to-body :map-search="true" :dw="form" @onlnglat="onlnglat" />
  </div>
</template>

<script>
import { listDynamic, getDynamic, delDynamic, addDynamic, updateDynamic } from '@/api/supervise/dynamic'
// import Editor from '@/components/Editor'
import MFileUpload from '@/components/MFileUpload/index.vue'
import { getFiles } from '@/api/supervise/swit'
import tdtMap from '@/components/tdtMap/tdtMap'

export default {
  name: 'Dynamic',
  components: {
    MFileUpload,
    tdtMap
    // Editor
  },
  data() {
    return {
      openMap: false,
      formLoading: false,
      // 多选选中上报人id
      userIds: [],
      exData: {
        tableName: 'case_work_dynamic',
        status: '1'
      },
      mainFileList: [],

      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 队员工作动态表格数据
      dynamicList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dateRange: [],
        content: null,
        userId: null,
        userName: null,
        type: null,
        happenTime: null,
        address: null,
        longitude: null,
        latitude: null,
        status: null,
        deptId: null,
        deptName: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        userName: [
          { required: true, message: '上报人不能为空', trigger: 'change' }
        ],
        deptName: [
          { required: true, message: '所属部门不能为空', trigger: 'change' }
        ],
        happenTime: [
          { required: true, message: '上报时间不能为空', trigger: 'change' }
        ],
        address: [
          { required: true, message: '上报地址不能为空', trigger: 'change' }
        ],
        content: [
          { required: true, message: '工作内容不能为空', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    onlnglat(lenlat) {      // 经纬度  详细地址
      let {lng: longitude, lat: latitude, address} = lenlat
      this.form = {...this.form, longitude, latitude, address}
      this.openMap = false
    },
    /** 查询队员工作动态列表 */
    getList() {
      this.loading = true
      const { dateRange,  pageNum, pageSize, deptName, userName} = this.queryParams
      let params = { pageNum, pageSize }
      if (dateRange && dateRange.length) params = { ...params, searchStartTime: dateRange[0], searchEndTime: dateRange[1] }
      if (userName) params = { ...params, userName }
      if (deptName) params = { ...params, deptName }

      // if (this.queryParams.dateRange && this.queryParams.dateRange.length) this.queryParams = { ...this.queryParams, searchStartTime: this.queryParams.dateRange[0], searchEndTime: this.queryParams.dateRange[1] }
      // if (type) params = { ...params, type }
      // if (searchValue) params = { ...params, searchValue }
      // listDynamic(params).then(response => {
      //   this.recordList = response.rows
      //   this.total = response.total
      //   this.loading = false
      // })
      listDynamic(params).then(response => {
        this.dynamicList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.mainFileList = []
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        // id: null,
        content: null,
        userId: this.$store.getters.uid,
        userName: this.$store.getters.nickName,
        type: 1,
        happenTime: null,
        address: null,
        longitude: null,
        latitude: null,
        status: '0',
        // delFlag: null,
        deptId: this.$store.getters.deptId,
        deptName: this.$store.getters.deptName
        // createBy: null,
        // createTime: null,
        // updateTime: null,
        // remark: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10
      }
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.userIds = selection.map(item => item.userId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加队员工作动态'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      this.open = true
      this.formLoading = true
      this.title = '修改队员工作动态'
      Promise.all([
        getDynamic(id),
        getFiles({ businessId: id, tableName: 'case_work_dynamic' })
      ]).then(response => {
        const [formData, fileData] = response
        this.form = formData.data
        this.formLoading = false

        // 获取文件
        this.mainFileList = fileData.rows.map(item => {
          return { name: item.displayName, url: `/zqzfj${item.filePath}`, ...item }
        })

      }).catch(() => this.formLoading = false)
    },
    /** 提交按钮 */
    submitForm(status) {
      if (this.form.userId != this.$store.getters.uid) return this.msgError('您不是上报人，没有操作权限！')

      this.form.status = status
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.formLoading = true
          if (this.form.id != null) {
            updateDynamic(this.form).then(() => {
              this.exData.businessId = this.form.id
              this.$refs.mainFile.submitFile()
              // this.formLoading = false
              // this.open = false
              // this.mainFileList = []
              // this.reset()
              // this.getList()
              // this.msgSuccess('修改成功')
            }).catch(() => {
              this.formLoading = false
            })
          } else {
            addDynamic(this.form).then(res => {
              this.exData.businessId = res.data.id
              this.$refs.mainFile.submitFile()
              // this.mainFileList = []

              // this.msgSuccess('新增成功')
              // this.open = false
              // this.getList()
            }).catch(() => {
              this.formLoading = false
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      if (this.userIds.length != 0) {
        if (!this.userIds.every(item => item == this.$store.getters.uid)) return this.msgError('您不是上报人，没有操作权限！')
      } else {
        if (row.userId != this.$store.getters.uid) return this.msgError('您不是上报人，没有操作权限！')
      }
      const ids = row.id || this.ids
      this.$confirm('是否确认删除队员工作动态编号为"' + ids + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return delDynamic(ids)
      }).then(() => {
        this.getList()
        this.msgSuccess('删除成功')
      })
    },
    // 图片上传成功
    handleFileSuccess() {
      this.msgSuccess('操作成功')
      this.formLoading = false
      this.open = false
      this.reset()
      this.getList()
      this.mainFileList = []
    },
    // 图片上传失败
    fileError() {
      this.formLoading = false
      this.form.id = this.exData.businessId
    }
  }
}
</script>
