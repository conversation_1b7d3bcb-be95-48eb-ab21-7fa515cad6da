<template>
  <div class="wrap">
    <CommonTitle2 text="环境检测"></CommonTitle2>
    <!-- <div class="yearChange">
      <el-date-picker
        v-model="datas"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="queryData"
        :append-to-body="false"
      ></el-date-picker>
    </div> -->
    <div id="hjjc" style="width: 100%; height: 480px"></div>
  </div>
</template>

<script>
import CommonTitle2 from '@/components/CommonTitle2'
import moment from 'moment'
import { getIndexData } from '@/api/yxjc/index.js'

export default {
  name: 'index',
  components: {
    CommonTitle2,
  },
  data() {
    return {
      datas: [new Date().getFullYear() + '-01-01', moment(new Date()).format('YYYY-MM-DD')],
      lineChartData: [],
    }
  },
  computed: {},
  mounted() {
    this.queryData()
  },
  methods: {
    queryData() {
      getIndexData({ indexid: '/zbzx_kqzlzs_ygf' }).then((res) => {
        this.lineChartData = res.data.map((item) => {
          return {
            name: item.date_time1,
            value: item.current_value,
          }
        })
        this.initCharts(this.lineChartData)
      })
    },
    initCharts(data) {
      const chartDom = document.getElementById('hjjc')
      const myChart = this.$echarts.init(chartDom)

      const option = {
        color: ['#3ED7FD', '#22E197'],
        grid: {
          left: '7%',
          right: '7%',
          bottom: '5%',
          top: '20%',
          containLabel: true,
        },
        tooltip: {
          trigger: 'axis',
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        legend: {
          icon: 'circle',
          right: '60',
          top: '20',
          itemGap: 40,
          textStyle: {
            color: '#D6E7F9',
            fontSize: 28,
          },
        },
        xAxis: {
          type: 'category',
          data: data.map((item) => item.name),
          axisLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.6)',
            },
          },
          axisLabel: {
            textStyle: {
              fontSize: 24,
            },
          },
        },
        yAxis: {
          name: '空气质量指数(AQI)',
          nameTextStyle: {
            color: 'rgba(255,255,255,0.6)',
            padding: [20, 10, 10, 10],
            fontSize: 24,
          },
          type: 'value',
          axisLabel: {
            textStyle: {
              fontSize: 24,
            },
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.6)',
            },
          },
          splitLine: {
            lineStyle: {
              color: '#4A6C89',
            },
          },
        },
        series: [
          {
            data: data.map((item) => item.value),
            type: 'line',
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(62, 215, 253, 0.30)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(62, 215, 253, 0)',
                  },
                ],
              },
            },
            lineStyle: {
              color: '#3ED7FD',
            },
            smooth: true,
            symbol: 'none',
          },
        ],
      }

      myChart.setOption(option)

      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
.wrap {
  position: relative;
  margin-bottom: 40px;
  /deep/ .yearChange {
    position: absolute;
    right: 10px;
    top: 0px;
    .el-input__inner {
      height: 48px !important;
      background-color: #132c4e !important;
      border: 2px solid #afdcfb !important;
      color: #fff !important;
      border-radius: 15px !important;
      font-size: 24px;
    }
  }
}

.flex-b {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>