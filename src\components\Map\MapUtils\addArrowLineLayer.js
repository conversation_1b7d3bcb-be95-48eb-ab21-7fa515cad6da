import { layerCreate } from "./core.js";
import Polyline from "@arcgis/core/geometry/Polyline.js";
import Graphic from "@arcgis/core/Graphic.js";

function _getFields(objectId, attributes) {
  const fields = [{ name: objectId, alias: "OBJECTID", type: "oid" }];
  for (let key in attributes) {
    if (key.toUpperCase() !== objectId) {
      if (typeof attributes[key] === "string") {
        fields.push({
          name: key,
          alias: key,
          type: "string",
        });
      } else if (typeof attributes[key] === "number") {
        if (attributes[key] % 1 == 0) {
          fields.push({
            name: key,
            alias: key,
            type: "integer",
          });
        } else {
          fields.push({
            name: key,
            alias: key,
            type: "double",
          });
        }
      }
      // 日期格式设置为Date报错？
      else if (attributes[key] instanceof Date) {
        fields.push({
          name: key,
          alias: key,
          type: "string",
        });
      }
    }
  }

  return fields;
}


/**
 *
 * @param {*} view
 * @param {*} data {paths:[],attributes:{}}
 */
async function addArrowLineLayer({ view = window.view, data, width = 1, color = "red", wkid = '4490' }) {
  if (!data) {
    throw new Error("data为必传参数！");
  }
  const graphics = [];
  for (let i = 0; i < data.length; i++) {
    const { paths, attributes } = data[i];

    const polyline = new Polyline({
      paths,
      spatialReference: { wkid: wkid },
    });
    const graphic = new Graphic({
      geometry: polyline,
      attributes: {
        OBJECTID: `Polyline_${i}`,
        ...attributes,
      },
    });
    graphics.push(graphic);
  }

  let renderer = {
    type: "simple", // autocasts as new SimpleRenderer()
    symbol: {
      type: "line-3d", // autocasts as new LineSymbol3D()

      // Add two symbolizations on top of each other
      symbolLayers: [
        {
          // 2. Dashed line with arrows as markers
          type: "line", // autocasts as new LineSymbol3DLayer()
          material: {
            color: color
          },
          size: "3px",
          pattern: {
            type: "style", // autocasts as new LineStylePattern3D()
            style: "long-dash"
          },
          marker: {
            type: "style", // autocasts as new LineStyleMarker3D()
            style: "arrow",
            placement: "end"
          }
        }
      ]
    }
  };

  const fields = _getFields("OBJECTID", data[0]?.attributes || {});
  const layer = layerCreate({
    type: "feature",
    objectIdField: "OBJECTID",
    outFields: ["*"],
    fields: fields,
    source: graphics, // 使用自定义的数据源
    renderer: renderer,
    elevationInfo: {
      mode: "relative-to-ground",
    },
    customLayerType: "custom_line",
  });
  view.map.add(layer);
  return layer;
}

export default addArrowLineLayer;
