<template>
  <div>
    <div class="table">
      <div class="table-header">
        <div class="th" v-for="item of thConfig" :key="item.field" :style="{ width: item.width }">{{ item.th }}</div>
      </div>
      <div
        class="table-body"
        ref="tableBody"
        @mouseenter="handleMouseEnter"
        @mouseleave="handleMouseLeave"
      >
        <div
          class="tr"
          v-for="(item, index) of tableData"
          :class="{ 'tr-bg': index % 2 === 1 }"
          :key="item.id"
          @click="infoClick(item)"
        >
          <div
            v-for="(config, idx) in thConfig"
            :key="idx"
            class="td"
            :class="{ cursor: idx === 3 }"
            :style="{
              minWidth: config.width,
              color: idx === 3 ? item.color : undefined
            }"
            v-html="item[config.field]"
            :title="config.hover ? item[config.field] : ''"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    thConfig: {
      type: Array,
      default: () => [],
      required: true
    },
    tableData: {
      type: Array,
      default: () => [],
      required: true
    },
    tableHeight: {
      type: [String, Number],
      default: 375
    },
    autoScroll: {
      type: Boolean,
      default: false
    },
    scrollInterval: {
      type: Number,
      default: 1500
    },
    scrollStep: {
      type: Number,
      default: 92
    }
  },
  data() {
    return {
      scrollTimer: null
    }
  },
  mounted() {
    if (this.autoScroll) {
      this.startScroll();
    }
  },
  beforeDestroy() {
    this.clearScrollTimer();
  },
  watch: {
    autoScroll(newVal) {
      if (newVal) {
        this.startScroll();
      } else {
        this.clearScrollTimer();
      }
    },
    tableData() {
      // 当数据变化时，重置滚动位置
      if (this.$refs.tableBody) {
        this.$refs.tableBody.scrollTop = 0;
      }
      // 如果启用了自动滚动，重新启动滚动
      if (this.autoScroll) {
        this.clearScrollTimer();
        this.startScroll();
      }
    }
  },
  methods: {
    infoClick(item) {
      this.$emit('infoClick', item)
    },
    startScroll() {
      this.clearScrollTimer();
      this.scrollTimer = setInterval(() => {
        if (this.$refs.tableBody) {
          this.$refs.tableBody.scrollBy({
            top: this.scrollStep,
            behavior: 'smooth'
          });

          // 当滚动到底部时，回到顶部
          if (
            this.$refs.tableBody.scrollTop >=
            this.$refs.tableBody.scrollHeight - this.$refs.tableBody.offsetHeight
          ) {
            this.$refs.tableBody.scrollTop = 0;
          }
        }
      }, this.scrollInterval);
    },
    clearScrollTimer() {
      if (this.scrollTimer) {
        clearInterval(this.scrollTimer);
        this.scrollTimer = null;
      }
    },
    handleMouseEnter() {
      if (this.autoScroll) {
        this.clearScrollTimer();
      }
    },
    handleMouseLeave() {
      if (this.autoScroll) {
        this.startScroll();
      }
    }
  },
}
</script>

<style lang="less" scoped>
.table {
  .table-header {
    display: flex;
    width: 100%;
    height: 92px;
    line-height: 92px;
    background: #08285E;
    background-size: 100% 100%;
    .th {
      width: 25%;
      color: #CDE7FF;
      font-size: 32px;
      text-align: center;
      box-sizing: border-box;
      padding: 0 24px;
      font-weight: bold;
    }
  }
  .table-body {
    overflow-y: auto;
    height: v-bind('typeof tableHeight === "number" ? tableHeight + "px" : tableHeight');
    .tr {
      display: flex;
      width: 100%;
      height: 92px;
      &:hover {
        background: rgba(91, 180, 255, 0.3);
      }
      .td {
        width: 25%;
        box-sizing: border-box;
        height: 68px;
        line-height: 92px;
        padding: 0 24px;
        text-align: center;
        font-size: 28px;
        color: #DCEFFF;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        &.cursor {
          cursor: pointer;
        }
      }
    }
    .tr-bg {
      background: #08224A;
    }
  }
}
</style>
