<template>
  <div id="zfzs">
    <div class="zfzscontant">
      <div class="zfzstitle">
        <div class="zfzsclose" @click="close()">×</div>
      </div>
      <div class="zfzscontent">
        <el-image
          style="width: 500px; height: 300px"
          :src="url"
          :preview-src-list="srcList"
        >
        </el-image>
        <div class="zfzsright">
          <p class="zfzsstation">名称：{{data.station}}</p>
          <p>地址：{{data.street}}{{data.address}}</p>
          <p>执法队队员：{{data.num}}人</p>
        </div>
      </div>
      <div class="zfzsbottom">
        <el-button type="primary" plain @click="queryVideo"
          >查看附近监控</el-button
        >
        <el-button type="primary" @click="queryPeople">人员信息</el-button>
      </div>
    </div>
    <div class="zfzsbottoms">
      <img
        class="breath-light"
        src="/static/images/tcgl/video-dian.png"
        alt=""
      />
      <canvas
        id="canvas"
        ref="canvas"
        style="
          z-index: 100;
          position: fixed;
          top: 0;
          width: 641px;
          height: 250px;
          position: absolute;
          transform: rotate(180deg);
        "
      ></canvas>
    </div>
  </div>
</template>

<script>
import mapService from '@/components/Map/index.js'
export default {
    name:'tcgl',
    props: {
      data: {
        type: Object,
        default: {}
      },
    },
    components: {},
    data() {
        return {
          url: "",
          srcList: [],
          station: "",
          address: "",
          street: "",
          num: 0,
          point: "",
          canvas: "",
          ctx: "",
          W: "",
          H: "",
          angle: 0,
          mp: 3000,
          particles: [],
          t: 0,
        };
    },
    mounted() {
    },
    methods: {
      queryVideo() {
          let that = this;
          let type = "type=zbjk";
          let sgPoint = this.point;
          let distance = 1;
          axios({
            method: "get",
            url: baseURL.url + "/jhyjzh-server/screen_api/home/<USER>",
            params: {
              type: type,
              distance: distance,
              point: sgPoint,
            },
          }).then(function (data) {
            let dataArr = [];
            if (type == "type=zbjk") {
              dataArr = data.data.data.zbjk.pointData.filter((item) => {
                return item.is_online != "离线";
              });
              arr = dataArr.map((item) => {
                return {
                  ...item,
                  pointType: that.getPointType(item.is_online, item.cameraType),
                };
              });
              that.getManyPoint(arr);
            } else {
              dataArr = data.data.data;
              dataArr.forEach((item) => {
                that.addPointFun(item);
              });
            }
          });
      },
      getPointType(is_online, cameraType) {
        let arr = is_online + "-" + cameraType;
        let obj = {
          枪机在线: "在线-枪机",
          球机在线: "在线-球机",
          半球机在线: "在线-半球机",
          高点在线: "在线-未知",
        };
        for (key in obj) {
          if (obj[key] == arr) {
            return key;
          }
        }
      },
      //一次绘制多种不同类型的点
      getManyPoint(pointData) {
        if (pointData.length > 0) {
          mapService.loadPointLayer({
            data: pointData,
            layerid: "camera-load-icon",
            cluster: true, //是否定义为聚合点位：true/false
            iconcfg: {
              image: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-qiangji.png`,
              iconSize: 0.5,
              iconlist: {
                field: "pointType",
                list: [
                  {
                    value: "枪机在线",
                    size: "50",
                    src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-qiangji.png`,
                  },
                  {
                    value: "球机在线",
                    size: "50",
                    src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-qiuji.png`,
                  },
                  {
                    value: "半球机在线",
                    size: "50",
                    src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-banqiu.png`,
                  },
                  {
                    value: "高点在线",
                    size: "50",
                    src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-gaodian.png`,
                  },
                ],
              },
            },
            onclick: this.openPointMassage,
            // onblur: this.onblur
          });
        }
      },
      openPointMassage(abc, list) {
        mapService.removeLayer("syr");
        mapService.removeLayer("syr1");
        mapService.flyTo({
          destination: [abc.esX, abc.esY],
          offset: [0, -666],
        });
        let item = {
          obj: {
            chn_name: abc.data.addinfo.labels,
            chn_url: abc.data.addinfo.chncode,
            pointList: list,
          },
          video_code: abc.data.addinfo.chncode,
          csrk: true,
        };
        window.parent.lay.openIframe({
          type: "openIframe",
          name: "video_main_code",
          src:
            baseURL.url +
            "/static/citybrain/commonts/tcgl/video_main_code.html",
          width: "100%",
          height: "100%",
          left: "0",
          top: "0",
          zIndex: "1000",
          argument: item,
        });
      },
      // 添加点位方法
      addPointFun(data) {
        mapService.loadPointLayer({
          data: data.pointData,
          layerid: data.pointId, //图层id--zhddzx_map_video_zbjk
          iconcfg: { image: data.pointType, iconSize: 0.4 }, //图标
          popcfg: {
            offset: [50, -100],
            show: false,
          },
        });
      },
      queryPeople() {
        window.parent.lay.openIframe({
          type: "openIframe", //指令
          name: "ryxxDetail",
          src:
            baseURL.url + "/static/citybrain/commonts/tcgl/ryxxDetail.html",
          left: "calc(50% - 600px)",
          top: "725px",
          width: "1200px",
          height: "680px",
          zIndex: 667,
          argument: {
            station: this.station,
          },
        });
      },
      close() {
        this.$emit('close')
        mapService.removeAllLayers([
          "syr",
          "syr1",
          "camera-load-icon",
        ]);
      },
      _initCavas() {
        this.canvas = document.getElementById("canvas");
        this.ctx = this.canvas.getContext("2d");

        //canvas dimensions
        this.W = window.innerWidth - 30;
        this.H = window.innerHeight - 10;
        this.canvas.width = this.W;
        this.canvas.height = this.H;

        //snowflake particles
        //雪花数量
        this.mp = 100;
        this.particles = [];
        for (var i = 0; i < this.mp; i++) {
          this.particles.push({
            x: Math.random() * this.W * 5, //x-coordinate
            y: Math.random() * this.H, //y-coordinate
            //改变大小
            r: Math.random() * 10 + 5, //radius
            d: Math.random() * this.mp, //density
          });
        }
        clearInterval(localStorage.getItem("interval"));
        localStorage.setItem("interval", setInterval(this.draw, 25));
      },
      draw() {
        this.ctx.clearRect(0, 0, this.W, this.H);
        this.ctx.fillStyle = "rgba(146,192,227,1)";
        this.ctx.fillStyle = "border: 1px solid rgb(37, 211, 236,0.2)";
        this.ctx.fillStyle =
          "box-shadow: 0px 0px 10px 5px rgba(145,198,239,1)";
        this.ctx.beginPath();
        for (var i = 0; i < this.mp; i++) {
          var p = this.particles[i];
          this.ctx.moveTo(p.x, p.y);
          this.ctx.arc(p.x, p.y, p.r, 0, Math.PI * 2, true);
        }
        this.ctx.fill();
        this.update();
      },
      update() {
        // this.angle += 0.01;
        for (var i = 0; i < this.mp; i++) {
          var p = this.particles[i];
          p.y += Math.cos(this.angle + p.d) + 1 + p.r / 2;
          p.x += Math.sin(this.angle) * 2;

          if (p.x > this.W || p.x < 0 || p.y > this.H) {
            if (i % 3 > 0) {
              this.particles[i] = {
                x: Math.random() * this.W,
                y: -10,
                r: p.r,
                d: p.d,
              };
            } else {
              if (Math.sin(this.angle) > 0) {
                //Enter fromth
                this.particles[i] = {
                  x: -5,
                  y: Math.random() * this.H,
                  r: p.r,
                  d: p.d,
                };
              } else {
                this.particles[i] = {
                  x: this.W + 5,
                  y: Math.random() * this.H,
                  r: p.r,
                  d: p.d,
                };
              }
            }
          }
        }
      },
    },
    watch: {
  },
      
}
</script>

<style scoped lang="scss">
    body {
      margin: 0;
      padding: 0;
    }

    #zfzs{
      width:800px;
      position: absolute;
      box-sizing: border-box;
      left: 39.55%;
      top: 20%;
    }
    .zfzscontant {
    background-image: url("@/assets/zhdd/bg_panel.png");
    background-size: 100% 100%;
    }

    .zfzstitle {
    font-size: 32px;
    padding: 25px 40px;
    display: flex;
    justify-content: end;
    align-items: center;
    }

    .zfzsclose {
    width: 40px;
    height: 40px;
    font-size: 50px;
    color: #fff;
    font-weight: 600;
    cursor: pointer;
    }

    .zfzscontent {
    width: 100%;
    display: flex;
    padding: 0px 60px;
    box-sizing: border-box;
    }

    .zfzsright {
    width: 70%;
    margin-left: 30px;
    }

    .zfzsright > p {
    font-size: 30px;
    color: #fff;
    }

    .zfzsstation {
    font-size: 30px !important;
    color: #3aa3bb !important;
    }

    .zfzsbottom {
    padding: 40px 60px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-around;
    }

    .el-button {
    font-size: 25px;
    border-radius: 10px;
    width: 200px;
    }
    .zfzsbottoms {
    width: 641px;
    height: 160px;
    margin: 0 auto;
    background: url("@/assets/tcgl/video-bottom.png") no-repeat 0 -90px;
    position: relative;
    clip-path: polygon(0 0, 50% 100%, 100% 0);
    }

    .zfzsbottoms > img {
    position: absolute;
    left: calc(50% - 86px);
    top: -10px;
    }
</style>