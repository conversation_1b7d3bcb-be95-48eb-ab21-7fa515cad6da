BODY
{
	FONT-FAMILY: "Verdana";
	FONT-SIZE: 12px;
	SCROLLBAR-HIGHLIGHT-COLOR: #f5f9ff;
	SCROLLBAR-SHADOW-COLOR: #828282;
	SCROLLBAR-3DLIGHT-COLOR: #828282;
	SCROLLBAR-ARROW-COLOR: #797979;
	SCROLLBAR-TRACK-COLOR: #ffffff;
	SCROLLBAR-FACE-COLOR: #66b7ef;
	SCROLLBAR-DARKSHADOW-COLOR: #ffffff
}
.DivAgent{
	 width:50px;height:66px;border:#909090 1px solid;background:#00ff00;color:#333;
	 filter:progid:DXImageTransform.Microsoft.Shadow(color=#909090,direction=120,strength=3);/*ie*/
	 -moz-box-shadow: 2px 2px 10px #909090;/*firefox*/
	 -webkit-box-shadow: 2px 2px 10px #909090;/*safari或chrome*/
	 box-shadow:2px 2px 10px #909090;/*opera或ie9*/
	 margin:5px;font-size: small;text-align:center;vertical-align:middle;
}
.cmenu
{
	position:absolute;
	top:100px;
	left:100px;
	width:120px;
	height:200px;
	background-color:lightgoldenrodyellow;
	border:1px solid lightblue;
    border-radius:5px;
}
.liAble
{
	font-family:"宋体";
	color:black;
	margin-left:10px;
	margin-top:5px;
	list-style-type:none;
	cursor:default;
}
.liDisable
{
	font-family:"宋体";
	color:grey;
	margin-left:10px;
	margin-top:5px;
	list-style-type:none;
	cursor:default;
}
.liMouseOver
{
	margin-left:10px;
	margin-top:5px;
	background-color:#CCFFFF;
	list-style-type:none;
	cursor:default;
}
.liAgentTiltle
{
	font-family:"宋体";
	color:white;
	background-color:#AE57A4;
	margin-left:0px;
	margin-top:0px;
	padding-top: 5px;
	height: 18px;
	list-style-type:none;
	border-radius:2px;
	cursor:default;
}
.liGroupTitle
{
	font-family:"宋体";
	color:black;
	background-color:#4Dffff;
	margin-left:0px;
	margin-top:0px;
	padding-top: 5px;
	height: 18px;
	list-style-type:none;
	cursor:default;
}
.liAgentContent
{
	font-family:"宋体";
	color:black;
	margin-left:0px;
	margin-top:0px;
	list-style-type:none;
	cursor:default;
}
.liAgentContentWhite
{
	font-family:"宋体";
	color:white;
	margin-left:0px;
	margin-top:0px;
	list-style-type:none;
	cursor:default;
}
.DivBackground{
	width:50px;height:66px;border:#909090 1px solid;background:#00ff00;color:#333;
	filter:progid:DXImageTransform.Microsoft.Shadow(color=#909090,direction=120,strength=3);/*ie*/
	-moz-box-shadow: 2px 2px 10px #909090;/*firefox*/
	-webkit-box-shadow: 2px 2px 10px #909090;/*safari或chrome*/
	box-shadow:2px 2px 10px #909090;/*opera或ie9*/
	margin:5px;font-size: small;text-align:center;vertical-align:middle;
}

.linear{

	width:100%;

	height:100%;

	FILTER: progid:DXImageTransform.Microsoft.Gradient(gradientType=0,startColorStr=#b8c4cb,endColorStr=red); /*IE 6 7 8*/

	background: -ms-linear-gradient(top, #fff,  #0000ff);        /* IE 10 */

	background:-moz-linear-gradient(top,#b8c4cb,#f6f6f8);/*火狐*/

	background:-webkit-gradient(linear, 0% 0%, 0% 100%,from(#b8c4cb), to(#f6f6f8));/*谷歌*/

	background: -webkit-gradient(linear, 0% 0%, 0% 100%, from(#fff), to(#0000ff));      /* Safari 4-5, Chrome 1-9*/

	background: -webkit-linear-gradient(top, #fff, #ECFFFF);   /*Safari5.1 Chrome 10+*/

	background: -o-linear-gradient(top, #fff, #0000ff);  /*Opera 11.10+*/

}

