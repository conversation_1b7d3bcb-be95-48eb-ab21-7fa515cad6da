<template>
  <div id="yybz">
    <div class="contant">
      <div class="title">
        <div class="s-c-yellow-gradient s-w7">城市风貌</div>
        <!-- <div class="close" @click="close()">×</div> -->
      </div>
      <p class="bottom_title s-c-blue-gradient s-w7">
        <img src="@/assets/tcgl/yybz/circle.png" alt="" />
        案件查询
      </p>
      <el-form ref="form" label-width="180px">
        <el-form-item label="查询时间：">
          <el-radio-group v-model="radio">
            <el-radio :label="1">本周</el-radio>
            <el-radio :label="2">本月</el-radio>
            <el-radio :label="3">历史</el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- <el-form-item label="大类：">
        <el-select v-model="type1" placeholder="请选择大类">
            <el-option label="市容环境设施" :value="1"></el-option>
        </el-select>
        </el-form-item>
        <el-form-item label="小类：">
        <el-select v-model="type2" placeholder="请选择小类">
            <el-option label="私乱扩建" :value="1"></el-option>
        </el-select>
        </el-form-item>
        <el-form-item label="地区：">
        <el-select v-model="area" placeholder="请选择地区">
            <el-option label="街道/社区" :value="1"></el-option>
        </el-select>
        </el-form-item> -->
        <el-button type="primary" @click="queryData(true)">查询</el-button>
      </el-form>
      <p class="bottom_title s-c-blue-gradient s-w7 s-m-t-20">
        <img src="@/assets/tcgl/yybz/circle.png" alt="" />
        案件总量：{{ total }}条
      </p>
      <div id="echarts" style="width: 100%; height: 500px"></div>
      <!-- <div class="bottom" style="display: none">
        <p
        class="bottom_title s-c-blue-gradient s-w7"
        style="margin-top: 20px"
        >
        <img src="/static/images/tcgl/yybz/circle.png" alt="" />
        查询列表
        </p>
        <div class="list">
        <div
            class="item"
            v-for="(item,index) in 5"
            @click="itemClick(index)"
            :class="itemIndex==index?'item_active':''"
        >
            <img
            class="item_img"
            src="/static/images/tcgl/commont/bm.png"
            alt=""
            />
            <div style="margin-top: 20px">
            <div>
                <span class="item_label">案件编号：</span>[2023307283723482]
            </div>
            <div><span class="item_label">问题来源：</span>上报</div>
            <div><span class="item_label">问题描述：</span>人行道破损</div>
            </div>
        </div>
        </div>
    </div> -->
    </div>
  </div>
</template>

<script>
import mapService from '@/components/Map/index.js'
import { indexApi } from '@/api/indexApi'
export default {
  name: 'yybz',
  props: [],
  components: {},
  data() {
    return {
      radio: 1,
      type1: 1,
      type2: 1,
      area: 1,
      itemIndex: -1,
      total: 0,
      tcglID: null,
    }
  },
  mounted() {
    this.queryData(false)
  },
  methods: {
    itemClick(item, index) {
      this.itemIndex = index
      console.log(item)
      // let pos = [item.longitude, item.latitude]
      // this.flyTo(pos)
    },
    close() {
      this.$emit('close')
    },
    queryData(flag) {
      mapService.removeAllLayers(['yybz_point_event'])
      indexApi('/xzzf_yybz_cfba_lb', {
        time: this.radio == '3' ? null : this.radio == 2 ? 'month' : 'week',
      }).then((res) => {
        this.total = res.data.length
        let pointData = []
        res.data.map((item) => {
          pointData.push({
            id: item.hash_unique,
            lng: (item.latitude && item.latitude) || '-',
            lat: (item.longitude && item.longitude) || '-',
            // data:item
          })
        })
        this.addPoint(pointData)
        if (flag) {
          let pos = [pointData[0].lng, pointData[0].lat]
          this.flyTo(pos)
        }
      })
      indexApi('/xzzf_yybz_cfba_fl', {
        time: this.radio == '3' ? null : this.radio == 2 ? 'month' : 'week',
      }).then((res) => {
        this.getChart02('echarts', res)
      })
    },
    addPoint(pointData, layerid, icon, callBack) {
      let this_ = this
      mapService.loadPointLayer({
        data: pointData, //点位数据
        layerid: 'yybz_point_event', //图层id
        iconcfg: {
          image: `./pointAssets/icon/spritesImage/icon-yybz.png`,
          iconSize: 64,
        },
        popcfg: {
          offset: [50, -100],
          show: false, //关闭按钮
        },
        cluster: false,
        onclick: this_.pointClick,
      })
    },
    flyTo(pos) {
      mapService.flyTo({
        destination: pos,
        zoom: 15,
      })
    },
    pointClick(e) {
      let pos = [e.esX, e.esY]
      this.flyTo(pos)
      this.$bus.$emit('ShowFmtsPop', e.id)
    },
    getChart02(id, echartsData) {
      echarts.init(document.getElementById(id)).dispose()
      let myChart = echarts.init(document.getElementById(id))
      let selectedIndex = ''
      let hoveredIndex = ''
      let colors = ['#00c0ff', '#ffd461', '#80c342', '#037bd5', '#ffcc41', '#ba8400', '#3ed0b2']
      console.log('echartsData', echartsData)
      var option = getPie3D(echartsData, 0.59)
      // 生成扇形的曲面参数方程
      function getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, h) {
        // 计算
        const midRatio = (startRatio + endRatio) / 2

        const startRadian = startRatio * Math.PI * 2
        const endRadian = endRatio * Math.PI * 2
        const midRadian = midRatio * Math.PI * 2

        // 如果只有一个扇形，则不实现选中效果。
        if (startRatio === 0 && endRatio === 1) {
          // eslint-disable-next-line no-param-reassign
          isSelected = false
        }

        // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
        // eslint-disable-next-line no-param-reassign
        k = typeof k !== 'undefined' ? k : 1 / 3

        // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
        const offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0
        const offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0

        // 计算高亮效果的放大比例（未高亮，则比例为 1）
        const hoverRate = isHovered ? 1.05 : 1

        // 返回曲面参数方程
        return {
          u: {
            min: -Math.PI,
            max: Math.PI * 3,
            step: Math.PI / 32,
          },

          v: {
            min: 0,
            max: Math.PI * 2,
            step: Math.PI / 20,
          },

          x(u, v) {
            if (u < startRadian) {
              return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate
            }
            if (u > endRadian) {
              return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate
            }
            return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate
          },

          y(u, v) {
            if (u < startRadian) {
              return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate
            }
            if (u > endRadian) {
              return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate
            }
            return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate
          },

          z(u, v) {
            if (u < -Math.PI * 0.5) {
              return Math.sin(u)
            }
            if (u > Math.PI * 2.5) {
              return Math.sin(u) * h * 0.1
            }
            // 当前图形的高度是Z根据h（每个value的值决定的）
            return Math.sin(v) > 0 ? 1 * h * 0.1 : -1
          },
        }
      }
      // 生成模拟 3D 饼图的配置项
      function getPie3D(pieData, internalDiameterRatio) {
        const series = []
        // 总和
        let sumValue = 0
        let startValue = 0
        let endValue = 0
        const legendData = []
        let legend = []
        const k =
          typeof internalDiameterRatio !== 'undefined'
            ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio)
            : 1 / 3
        // 为每一个饼图数据，生成一个 series-surface 配置
        for (let i = 0; i < pieData.length; i += 1) {
          sumValue += pieData[i].num
          const seriesItem = {
            name: typeof pieData[i].type === 'undefined' ? `series${i}` : pieData[i].type,
            type: 'surface',
            parametric: true,
            wireframe: {
              show: false,
            },
            pieData: pieData[i],
            pieStatus: {
              selected: false,
              hovered: false,
              k,
            },
          }

          if (typeof pieData[i].itemStyle !== 'undefined') {
            const { itemStyle } = pieData[i]

            // eslint-disable-next-line no-unused-expressions
            typeof pieData[i].itemStyle.color !== 'undefined' ? (itemStyle.color = pieData[i].itemStyle.color) : null
            // eslint-disable-next-line no-unused-expressions
            typeof pieData[i].itemStyle.opacity !== 'undefined'
              ? (itemStyle.opacity = pieData[i].itemStyle.opacity)
              : null

            seriesItem.itemStyle = itemStyle
          }
          series.push(seriesItem)
        }
        // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
        // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
        for (let i = 0; i < series.length; i += 1) {
          endValue = startValue + series[i].pieData.num

          series[i].pieData.startRatio = startValue / sumValue
          series[i].pieData.endRatio = endValue / sumValue
          series[i].parametricEquation = getParametricEquation(
            series[i].pieData.startRatio,
            series[i].pieData.endRatio,
            false,
            false,
            k,
            // 我这里做了一个处理，使除了第一个之外的值都是10
            series[i].pieData.num === series[0].pieData.num ? 35 : 10
          )

          startValue = endValue

          legendData.push(series[i].type)
        }
        // 准备待返回的配置项，把准备好的 legendData、series 传入。
        const option = {
          color: colors,
          // animation: false,
          tooltip: {
            formatter: (params) => {
              if (params.seriesName !== 'mouseoutSeries') {
                return `${
                  params.seriesName
                }<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                  params.color
                };"></span>${option.series[params.seriesIndex].pieData.num}`
              }
              return ''
            },
            textStyle: {
              color: '#ffff',
              fontSize: 24,
            },
            borderWidth: 0,
            backgroundColor: 'rgba(51, 51, 51, 0.7)',
          },
          legend: {
            top: '58%',
            orient: 'vertical',
            itemGap: pieData.length > 8 ? 10 : 20,
            textStyle: {
              rich: {
                name: {
                  fontSize: 26,
                  color: '#ffffff',
                  padding: [0, 0, 0, 15],
                },
                value: {
                  fontSize: 26,
                  color: '#e9d0ab',
                  padding: [10, 5, 0, 15],
                },
                value1: {
                  fontSize: 26,
                  color: '#e9d0ab',
                  padding: [10, 5, 0, 15],
                },
              },
            },
            formatter: function (name) {
              var data = option.series //获取series中的data
              var total = 0
              var tarValue
              var zbValue
              for (var i = 0, l = data.length; i < l; i++) {
                total += data[i].pieData.num
                if (data[i].pieData.type == name) {
                  tarValue = data[i].pieData.num
                }
              }
              var p = ((tarValue / total) * 100).toFixed(2)
              return '{name|' + name + '} {value1|' + tarValue + '}'
            },
            // padding: [0, 600, 0, 200],
          },
          xAxis3D: {
            min: -1,
            max: 1,
          },
          yAxis3D: {
            min: -1,
            max: 1,
          },
          zAxis3D: {
            min: -1,
            max: 1,
          },
          grid3D: {
            show: false,
            z: 1,
            boxHeight: 10,
            top: '-25%',
            left: '2%',
            viewControl: {
              // 3d效果可以放大、旋转等，请自己去查看官方配置
              alpha: 25,
              // beta: 30,
              rotateSensitivity: 1,
              zoomSensitivity: 0,
              panSensitivity: 0,
              autoRotate: true,
              distance: 190,
            },
            // 后处理特效可以为画面添加高光、景深、环境光遮蔽（SSAO）、调色等效果。可以让整个画面更富有质感。
            postEffect: {
              // 配置这项会出现锯齿，请自己去查看官方配置有办法解决
              enable: false,
              bloom: {
                enable: true,
                bloomIntensity: 0.1,
              },
              SSAO: {
                enable: true,
                quality: 'medium',
                radius: 2,
              },
              // temporalSuperSampling: {
              //   enable: true,
              // },
            },
          },
          series,
        }
        return option
      }
      //  修正取消高亮失败的 bug
      // 监听 mouseover，近似实现高亮（放大）效果

      // 修正取消高亮失败的 bug

      myChart.setOption(option)
    },
  },
}
</script>

<style scoped lang="less">
body {
  margin: 0;
  padding: 0;
}

#yybz {
  position: absolute;
  width: 700px;
  font-size: 30px;
  left: 65px;
  top: 550px;
}

.contant {
  width: 700px;
  /* height: 1395px; */
  /* height: 390px; */
  position: relative;
  background: url('@/assets/tcgl/yybz/yybz_bg.png');
  background-size: 100% 100%;
  padding: 30px 40px;
  box-sizing: border-box;
}

.title {
  font-size: 36px;
  padding: 0px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.close {
  width: 46px;
  height: 78px;
  font-size: 60px;
  color: #fff;
  font-weight: 600;
  cursor: pointer;
}

.bottom_title {
  font-size: 32px;
  margin: 10px 0;
}

.list {
  width: 100%;
  height: 950px;
  overflow-y: scroll;
}

.item {
  width: 100%;
  height: 212px;
  margin: 10px 0 20px;
  padding: 10px 20px;
  box-sizing: border-box;
  font-size: 24px;
  color: #fff;
  line-height: 50px;
  background: rgba(10, 97, 158, 0.1);
  border-radius: 10px;
  display: flex;
  position: relative;
  white-space: nowrap;
}

.item_active {
  background: rgba(10, 97, 158, 0.3);
}

.item_label {
  display: inline-block;
  width: 140px;
  text-align: right;
  color: #abceef;
}

.item_img {
  width: 190px;
  height: 155px;
  margin-top: 20px;
}

/* 设置滚动条的样式 */
::v-deep ::-webkit-scrollbar {
  width: 10px;
  cursor: pointer;
}

/* 滚动槽 */
::v-deep ::-webkit-scrollbar-track {
  border-radius: 5px;
}

/* 滚动条滑块 */
::v-deep ::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: rgba(35, 144, 207, 0.4);
}

/* el */
::v-deep .el-form-item__label {
  font-size: 30px;
  color: rgb(192, 214, 237);
  line-height: 60px;
}

::v-deep .el-radio-group {
  padding: 0 25px 0 0;
  box-sizing: border-box;
  font-size: 0;
  display: flex;
  justify-content: space-around;
}

::v-deep .el-radio__label {
  font-size: 28px !important;
}

::v-deep.el-radio {
  line-height: 60px;
  color: rgb(192, 214, 237);
}

::v-deep .el-radio__input {
  line-height: 60px;
}

::v-deep .el-radio__inner {
  width: 20px;
  height: 20px;
  border-radius: 3px;
}

::v-deep .el-radio__inner::after {
  box-sizing: content-box;
  content: '';
  border: 1px solid #fff;
  border-left: 0;
  border-top: 0;
  height: 15px;
  left: 5px;
  position: absolute;
  top: -2px;
  width: 6px;
  background: none;
  border-radius: 0;
}

::v-deep .el-radio__input.is-checked .el-radio__inner::after {
  transform: rotate(45deg) scaleY(1);
}

::v-deep .el-input {
  font-size: 26px;
}

::v-deep .el-input__inner {
  height: 60px;
  line-height: 60px;
  background-color: #052347;
  border-color: #314662 !important;
  border-radius: 5px;
  color: #fff;
}

::v-deep .el-input--suffix .el-input__inner {
  padding-right: 65px;
}

::v-deep .el-select .el-input .el-select__caret {
  color: #fff;
  font-size: 26px;
  margin-right: 10px;
  line-height: 60px;
}

::v-deep .el-select-dropdown {
  background-color: #052347;
  border-color: #314662 !important;
}

::v-deep .el-select-dropdown__item.hover,
::v-deep .el-select-dropdown__item:hover {
  border-color: #409eff;
  background-color: #052347;
  color: #409eff !important;
}

::v-deep .el-select-dropdown__item.selected {
  color: #409eff;
  font-weight: normal;
}

::v-deep .el-select-dropdown__item {
  font-size: 26px;
  color: #fff;
  height: 50px;
  line-height: 50px;
}

::v-deep .popper__arrow {
  display: none !important;
}

::v-deep .el-button {
  font-size: 24px;
  width: 565px;
  margin: 10px 0 0 25px;
}
</style>
