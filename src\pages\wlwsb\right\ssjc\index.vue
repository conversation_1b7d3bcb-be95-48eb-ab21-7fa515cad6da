<template>
  <div>
    <CommonTitle text="实时监测"></CommonTitle>
    <div class="chart-container">
      <TabSwitch class="tab-wraper" :type="2" :tabList="list" :activeIndex="index" @tab-change="changeTab" />
      <div class="lineChart" id="lineChart"></div>
    </div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import TabSwitch from '@/components/TabSwitch'
import * as echarts from 'echarts'
import { getRealTimeData } from '@/api/wlwsb/index.js'

export default {
  name: 'index',
  components: {
    CommonTitle,
    TabSwitch,
  },
  data() {
    return {
      //图表
      list: [],
      listData: [],
      index: 0,
      chartsData: [],
    }
  },
  computed: {},
  mounted() {
    this.getData()
  },
  methods: {
    changeTab(i) {
      this.index = i
      this.initData()
    },
    getData() {
      getRealTimeData({ deviceCode: '330782100015010003300361180000000000' }).then((res) => {
        if (res.data) {
          this.list = res.data.map((item, idx) => {
            return {
              name: item.key,
              value: idx,
            }
          })
          this.listData = res.data
          this.initData()
        }
      })
    },
    initData() {
      this.chartsData = this.listData[this.index].dataList.map((item) => {
        return {
          name: item.key,
          value: item.value,
        }
      })
      this.initCharts()
    },
    initCharts() {
      const chartDom = document.getElementById('lineChart')
      const myChart = echarts.init(chartDom)

      const option = {
        grid: {
          left: '7%',
          right: '7%',
          bottom: '5%',
          top: '14%',
          containLabel: true,
        },
        tooltip: {
          trigger: 'axis',
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          formatter: '{b}: <br/> {c}',
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.chartsData.map((item) => item.name),
          axisLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.6)',
            },
          },
          axisLabel: {
            textStyle: {
              fontSize: 24,
            },
          },
        },
        yAxis: {
          name: this.listData[this.index].key,
          nameTextStyle: {
            color: 'rgba(255,255,255,0.6)',
            padding: [20, 10, 10, 10],
            fontSize: 24,
          },
          min:65.6,
          type: 'value',
          axisLabel: {
            textStyle: {
              fontSize: 24,
            },
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.6)',
            },
          },
          splitLine: {
            lineStyle: {
              color: '#4A6C89',
            },
          },
        },
        series: [
          {
            data: this.chartsData.map((item) => item.value),
            type: 'line',
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(0,255,255,0.3)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(0,255,255,0)',
                  },
                ],
              },
            },
            lineStyle: {
              color: '#00ffff',
            },
            smooth: true,
            symbol: 'none',
          },
        ],
      }

      myChart.setOption(option)

      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
.chart-container {
  width: 100%;
  // height: 600px;
  .tab-wraper {
    margin: 40px;
  }
  .lineChart {
    width: 100%;
    height: 480px;
  }
}
</style>