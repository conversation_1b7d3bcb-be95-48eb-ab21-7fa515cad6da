import Point from "@arcgis/core/geometry/Point.js";
import Graphic from "@arcgis/core/Graphic.js";
import FeatureLayer from "@arcgis/core/layers/FeatureLayer.js";
import LabelClass from "@arcgis/core/layers/support/LabelClass.js";

function _getFields(objectId, attributes) {
  const fields = [{ name: objectId, alias: "OBJECTID", type: "oid" }];
  for (let key in attributes) {
    if (key.toUpperCase() !== objectId) {
      if (typeof attributes[key] === "string") {
        fields.push({
          name: key,
          alias: key,
          type: "string",
        });
      } else if (typeof attributes[key] === "number") {
        if (attributes[key] % 1 == 0) {
          fields.push({
            name: key,
            alias: key,
            type: "integer",
          });
        } else {
          fields.push({
            name: key,
            alias: key,
            type: "double",
          });
        }
      }
      // 日期格式设置为Date报错？
      else if (attributes[key] instanceof Date) {
        fields.push({
          name: key,
          alias: key,
          type: "string",
        });
      }
    }
  }

  return fields;
}

export function load3DTextLayer({
  view,
  data,
  fontSize = 20,
  fontColor = "white",
  textIcon,
  iconSize = 126,
  labelPlacement = "center-center",
  elevationInfo = {
    mode: "relative-to-scene",
  },
  halo = {},
}) {
  if (!view) {
    throw new Error("参数view为必传！");
  }
  const graphics = [];
  const labelClassArr = [];
  for (let i = 0; i < data.length; i++) {
    let item = data[i];
    item = { ...item, OBJECTID: i };
    const { pos } = item;
    const point = new Point({
      x: pos[0],
      y: pos[1],
      z: pos[2],
      spatialReference: view.spatialReference,
    });
    const graphic = new Graphic({
      geometry: point,
      attributes: {
        ...item,
      },
    });
    graphics.push(graphic);

    //
    const labelClass = new LabelClass({
      symbol: {
        type: "label-3d",
        symbolLayers: [
          {
            type: "text",
            material: {
              color: item?.color || fontColor,
            },
            size: `${fontSize}px`,
            halo,
          },
        ],
      },
      labelPlacement,
      labelExpressionInfo: {
        expression: "$feature.text",
      },
      where: `OBJECTID = ${i}`,
    });
    labelClassArr.push(labelClass);
  }
  let renderer = textIcon
    ? {
        type: "simple",
        symbol: {
          type: "point-3d",
          symbolLayers: [
            {
              type: "icon",
              resource: {
                href: textIcon,
              },
              size: iconSize,
              outline: {
                color: "white",
                size: 2,
              },
              anchor: "relative",
              anchorPosition: {
                x: 0,
                y: 0.5,
              },
            },
          ],
        },
      }
    : {
        type: "simple",
        symbol: {
          type: "simple-marker",
          color: [255, 255, 0, 0],
          size: "30px", // pixels
          outline: {
            color: [255, 255, 0, 0],
            width: 10,
          },
        },
      };
  const fields = _getFields("OBJECTID", data[0]);

  const layer = new FeatureLayer({
    objectIdField: "OBJECTID",
    outFields: ["*"],
    fields: fields,
    source: graphics,
    renderer: renderer,
    elevationInfo,
    labelingInfo: labelClassArr,
  });
  view.map.add(layer);
  return layer;
}

export async function remove3DTextById(layer, ids) {
  const { source } = layer;
  const data = source.toArray();
  const deleteFeatures = [];
  for (let i = 0; i < data.length; i++) {
    const { attributes } = data[i];
    if (ids.indexOf(attributes.id) !== -1) {
      deleteFeatures.push(data[i]);
    }
  }

  layer.applyEdits({
    deleteFeatures,
  });
}
