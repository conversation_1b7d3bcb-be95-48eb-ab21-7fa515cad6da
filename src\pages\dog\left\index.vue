<template>
  <div class='left'>
    <qlzb></qlzb>
    <qzdjzt></qzdjzt>
    <qzslfx></qzslfx>
  </div>
</template>

<script>
import qlzb from '@/pages/dog/left/qlzb'
import qzdjzt from '@/pages/dog/left/qzdjzt'
import qzslfx from '@/pages/dog/left/qzslfx'
export default {
  name: 'index',
  components: {
    qlzb,
    qzdjzt,
    qzslfx
  },
  data() {
    return {

    }
  },
  computed: {},
  mounted() {

  },
  methods: {

  },
  watch: {}
}
</script>

<style scoped lang='less'>

</style>