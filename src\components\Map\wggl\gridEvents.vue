<template>  
    <div id="wgsj">
      <div class="contant">
        <div class="title">
          <div class="s-c-yellow-gradient s-w7">网格事件</div>
        </div>
        <p class="bottom_title s-c-blue-gradient s-w7">
          <img src="@/assets/tcgl/yybz/circle.png" alt="" />
          事件总数查询
        </p>
        <el-form ref="form" label-width="180px">
          <el-form-item label="查询时间：">
            <el-radio-group v-model="radio">
              <el-radio :label="1">本周</el-radio>
              <el-radio :label="2">本月</el-radio>
              <el-radio :label="3">历史</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-button type="primary" @click="queryData()">查询</el-button>
        </el-form>
        <p class="bottom_title s-c-blue-gradient s-w7 s-m-t-20">
          <img src="@/assets/tcgl/yybz/circle.png" alt="" />
          事件总数：{{total}}条
        </p>
      </div>
    </div>
</template>
    
<script>
import { indexApi } from '@/api/indexApi'
export default {
    name:'gridEvents',
    props: [],
    components: { },
    data() {
        return {
            radio: 1,
            type1: 1,
            type2: 1,
            area: 1,
            itemIndex: -1,
            total: 0,
        };
    },
    mounted() {
        this.queryData();
    },
    methods: {
        itemClick(item, index) {
          this.itemIndex = index;
          console.log(item);
        },
        queryData() {
          let checkType = this.radio == "3" ? '历史' : this.radio == 2 ? "本月" : "本周"
          let qhCheck = JSON.parse(sessionStorage.getItem('qhCheck')) //勾选的区划
          // qhCheck = qhCheck.map(item => "'" + item + "'")
          sessionStorage.setItem("wgsjType",checkType)   //查询时间

          let area_name = (qhCheck.filter(item => item.type == "区划").map(item2 => "'" + item2.name + "'")).join(",")
          let xz = (qhCheck.filter(item => item.type == '乡镇街道').map(item2 => "'" + item2.name + "'")).join(",")
          let sq = (qhCheck.filter(item => item.type == '村社').map(item2 => "'" + item2.name + "'")).join(",")
          let wg = (qhCheck.filter(item => item.type == '网格').map(item2 => "'" + item2.name + "'")).join(",")


          if (qhCheck.length > 0) {
            indexApi("csdn_yjyp_wgsjs_xz",{
              area_name: area_name,
              xz: xz,
              sq:sq,
              wg:wg,
              tjzq:checkType
            }).then((res) => {
              console.log(res);
              if (res[0]) {
                this.total = res[0].wgsjs;
              } else {
                this.total = 0
              }
            });
          }
        },
      },
}
</script>

<style scoped lang='less'>

    #wgsj {
    width: 650px;
    font-size: 30px;
    position: absolute;
    left: 60px;
    top: 900px;
    }

    .contant {
    width: 700px;
    /* height: 1395px; */
    /* height: 390px; */
    position: relative;
    background: url("@/assets/tcgl/yybz/yybz_bg.png");
    background-size: 100% 100%;
    padding: 30px 40px;
    box-sizing: border-box;
    }

    .title {
    font-size: 36px;
    padding: 0px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    }

    .close {
    width: 46px;
    height: 78px;
    font-size: 60px;
    color: #fff;
    font-weight: 600;
    cursor: pointer;
    }

    .bottom_title {
    font-size: 32px;
    margin: 10px 0;
    }

    .list {
    width: 100%;
    height: 950px;
    overflow-y: scroll;
    }

    .item {
    width: 100%;
    height: 212px;
    margin: 10px 0 20px;
    padding: 10px 20px;
    box-sizing: border-box;
    font-size: 24px;
    color: #fff;
    line-height: 50px;
    background: rgba(10, 97, 158, 0.1);
    border-radius: 10px;
    display: flex;
    position: relative;
    white-space: nowrap;
    }

    .item_active {
    background: rgba(10, 97, 158, 0.3);
    }

    .item_label {
    display: inline-block;
    width: 140px;
    text-align: right;
    color: #abceef;
    }

    .item_img {
    width: 190px;
    height: 155px;
    margin-top: 20px;
    }
     /* 设置滚动条的样式 */
  ::-webkit-scrollbar {
    width: 10px;
    cursor: pointer;
  }

  /* 滚动槽 */
  ::-webkit-scrollbar-track {
    border-radius: 5px;
  }

  /* 滚动条滑块 */
  ::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background: rgba(35, 144, 207, 0.4);
  }

  /* el */
  ::v-deep .el-form-item__label {
    font-size: 30px;
    color: rgb(192, 214, 237) !important;
    line-height: 60px;
  }

  ::v-deep .el-radio-group {
    padding: 0 25px 0 0;
    box-sizing: border-box;
    font-size: 36px;
    display: flex;
    justify-content: space-around;
  }

  ::v-deep .el-radio__label {
    font-size: 30px !important;
  }

  ::v-deep .el-radio {
    line-height: 60px;
    color: rgb(192, 214, 237);
  }

  ::v-deep .el-radio__input {
    line-height: 60px;
  }

  ::v-deep .el-radio__inner {
    width: 20px;
    height: 20px;
    border-radius: 3px;
  }

  ::v-deep .el-radio__inner::after {
    box-sizing: content-box;
    content: "";
    border: 1px solid #fff;
    border-left: 0;
    border-top: 0;
    height: 15px;
    left: 5px;
    position: absolute;
    top: -2px;
    width: 6px;
    background: none;
    border-radius: 0;
  }

  ::v-deep .el-radio__input.is-checked .el-radio__inner::after {
    transform: rotate(45deg) scaleY(1);
  }

  ::v-deep .el-input {
    font-size: 26px;
  }

  ::v-deep .el-input__inner {
    height: 60px;
    line-height: 60px;
    background-color: #052347;
    border-color: #314662 !important;
    border-radius: 5px;
    color: #fff;
  }

  ::v-deep .el-input--suffix .el-input__inner {
    padding-right: 65px;
  }

  ::v-deep .el-select .el-input .el-select__caret {
    color: #fff;
    font-size: 26px;
    margin-right: 10px;
    line-height: 60px;
  }

  ::v-deep .el-select-dropdown {
    background-color: #052347;
    border-color: #314662 !important;
  }

  ::v-deep .el-select-dropdown__item.hover,
  ::v-deep .el-select-dropdown__item:hover {
    border-color: #409eff;
    background-color: #052347;
    color: #409eff !important;
  }

  ::v-deep .el-select-dropdown__item.selected {
    color: #409eff;
    font-weight: normal;
  }

  ::v-deep .el-select-dropdown__item {
    font-size: 26px;
    color: #fff;
    height: 50px;
    line-height: 50px;
  }

  ::v-deep .popper__arrow {
    display: none !important;
  }

  ::v-deep .el-button {
    font-size: 24px;
    width: 565px;
    margin: 10px 0 0 25px;
  }
</style>