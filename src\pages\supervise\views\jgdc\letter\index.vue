<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" :inline="true" label-width="100px">
      <!-- <el-form-item label="来源类型">
        <el-input
          v-model="queryParams.sourceType"
          placeholder="请输入来源类型"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="投诉工单号">
        <el-input
          v-model="queryParams.complainCode"
          placeholder="请输入投诉工单号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="督查人">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入督查人"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="信访状态">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small"> -->
      <!-- 状态 1-新建中 2-处理中 3-反馈 9-已办结 -->
      <!-- <el-option label="全部" value="" />
          <el-option label="新建中" value="1" />
          <el-option label="处理中" value="2" />
          <el-option label="反馈" value="3" />
          <el-option label="已办结" value="9" />
        </el-select>
      </el-form-item>
      <el-form-item label="投诉人">
        <el-input
          v-model="queryParams.complainName"
          placeholder="请输入投诉人"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="联系电话">
        <el-input
          v-model="queryParams.complainPhone"
          placeholder="请输入联系电话"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="承办部门">
        <el-input
          v-model="queryParams.undertakeDeptName"
          placeholder="请输入承办部门"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="下派人员">
        <el-input
          v-model="queryParams.userNames"
          placeholder="请输入下派人员"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="经办人">
        <el-input
          v-model="queryParams.handleUserName"
          placeholder="请输入经办人"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="事件类型">
        <el-select
          v-model="queryParams.type"
          placeholder="请选择事件类型"
          clearable
          size="small"
          style="width: 205px;"
        >
          <el-option
            v-for="dict in typeOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="类型明细">
        <el-input
          v-model="queryParams.typeDetail"
          placeholder="请输入类型明细"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否有责">
        <el-select v-model="queryParams.isDuty" placeholder="请选择是否有责" clearable size="small">
          <el-option label="无责" value="0" />
          <el-option label="有责" value="1" />
        </el-select>
      </el-form-item> -->

      <el-form-item label="信访日期">
        <el-date-picker
          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px;"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="getList"
        />
      </el-form-item>
      <el-form-item label="关键词">
        <el-input v-model="queryParams.searchValue" type="text" placeholder="请输入内容" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 导出 -->
    <el-form v-show="showSearch" :inline="true" label-width="100px">
      <el-form-item label="选择导出月份">
        <el-date-picker
          v-model="exportMonth"
          value-format="yyyy-MM-dd"
          type="month"
          placeholder="选择月"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="success" icon="el-icon-upload2" size="mini" @click="handleExport">导出</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:letter:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >
          新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:letter:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-col>

      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="letterList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="来源类型" align="center" prop="sourceType" />
      <el-table-column label="日期" align="center" prop="happenTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.happenTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="投诉工单号" align="center" prop="complainCode" />
      <el-table-column label="督查人" align="center" prop="userName" />
      <el-table-column label="投诉人" align="center" prop="complainName" />
      <el-table-column label="联系电话" align="center" prop="complainPhone" />
      <el-table-column label="事件类型" align="center" prop="type" />
      <el-table-column label="类型明细" align="center" prop="typeDetail" />
      <el-table-column label="承办部门" align="center" prop="undertakeDeptName" />
      <el-table-column label="下派人员" align="center" prop="userNames" />
      <!-- <el-table-column label="是否有责" align="center" prop="isDuty">
      <template slot-scope="scope">{{ scope.row.isDuty | duty }}</template>
      </el-table-column> -->
      <el-table-column label="经办人" align="center" prop="handleUserName" />
      <!-- <el-table-column label="处理结果" align="center" prop="feedback" /> -->
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">{{ scope.row.status | status }}</template>
      </el-table-column>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.status == 9"
            v-hasPermi="['business:record:edit']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            style="color: #e6a23c;"
            @click="handleUpdate(scope.row, 'disabled')"
          >
            详情
          </el-button>

          <el-button
            v-if="scope.row.status != 9"
            v-hasPermi="['business:letter:edit']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >
            修改
          </el-button>
          <el-button
            v-hasPermi="['business:letter:remove']"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改信访记录对话框 -->
    <div>
      <el-dialog class="m-dialog" :title="title" :visible.sync="open" @close="closeDialog">
        <el-scrollbar style="height: 100%;">
          <el-row :gutter="15" style="margin-right: 10px;">
            <el-form ref="form" v-loading="formLoading" :model="form" :rules="rules" label-width="100px" :disabled="formDisabled">
              <h3 class="title">信访信息</h3>
              <el-col :span="12">
                <el-form-item label="督查人" prop="userName">
                  <el-input v-model="form.userName" disabled placeholder="请输入督查人" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="来源类型" prop="sourceType">
                  <!-- <el-input v-model="form.sourceType" :disabled="disable" placeholder="请输入来源" /> -->
                  <el-select
                    v-model="form.sourceType"
                    :disabled="disable"
                    placeholder="请选择来源类型"
                    clearable
                    size="small"
                    style="width: 100%;"
                  >
                    <el-option
                      v-for="dict in sourceTypeOptions"
                      :key="dict.dictValue"
                      :label="dict.dictLabel"
                      :value="dict.dictValue"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="日期" prop="happenTime">
                  <el-date-picker
                    v-model="form.happenTime"
                    :disabled="disable"
                    clearable
                    size="small"
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="选择日期"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="投诉工单号" prop="complainCode">
                  <el-input v-model="form.complainCode" :disabled="disable" placeholder="请输入投诉工单号" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="投诉人" prop="complainName">
                  <el-input v-model="form.complainName" :disabled="disable" placeholder="请输入投诉人" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系电话" prop="complainPhone">
                  <el-input v-model="form.complainPhone" :disabled="disable" placeholder="请输入联系电话" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="事件类型" prop="type">
                  <el-select
                    v-model="form.type"
                    :disabled="disable"
                    placeholder="请选择事件类型"
                    clearable
                    size="small"
                    style="width: 100%;"
                  >
                    <el-option
                      v-for="dict in typeOptions"
                      :key="dict.dictValue"
                      :label="dict.dictLabel"
                      :value="dict.dictValue"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="类型明细" prop="typeDetail">
                  <el-input v-model="form.typeDetail" :disabled="disable" placeholder="请输入类型明细" />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="承办部门" prop="undertakeDeptName">
                  <el-input v-model="form.undertakeDeptName" :disabled="disable" placeholder="请输入承办部门名称">
                    <el-button slot="append" type="primary" :disabled="disable" @click="deptVisible = true">选择</el-button>
                  </el-input>
                </el-form-item>
                <!-- 承办部门 -->
                <Userselect v-model="deptVisible" name="bumen" :multiple="false" :select-user-keys="[form.undertakeDeptId]" :default-expanded-keys="form.undertakeDeptId ? [form.undertakeDeptId] : ['100']" @confirm="handleConfirmDept" />
              </el-col>
              <el-col :span="12">
                <el-form-item label="下派人员" prop="userNames">
                  <el-input v-model="form.userNames" :disabled="disable" placeholder="请输入考核队员名称">
                    <el-button slot="append" type="primary" :disabled="disable" @click="userVisible = true">选择</el-button>
                  </el-input>
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="事件描述" prop="content">
                  <el-input v-model="form.content" type="textarea" :disabled="disable" placeholder="请输入事件描述" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="备注" prop="remark">
                  <el-input v-model="form.remark" type="textarea" :disabled="disable" placeholder="请输入内容" />
                </el-form-item>
              </el-col>
              <!-- 信访文件 :limit="4" -->
              <el-col :span="24">
                <el-form-item label="信访文件" prop="mainFiles">
                  <FileUpAndDown ref="mainFile" :disabled="disable" :accept="accept" :ex-data="exData" :file-list="mainFileList" not-upload-msg="请上传文件" @uploadSucces="handleFileSuccess" @error="fileError" />
                </el-form-item>
              </el-col>

              <h3 class="title">反馈信息</h3>
              <el-col :span="24">
                <el-form-item label="经办人" prop="handleUserName">
                  <el-input v-model="form.handleUserName" disabled placeholder="请输入经办人名称" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="处理结果" prop="feedback">
                  <el-input v-model="form.feedback" type="textarea" placeholder="请输入处理结果" />
                </el-form-item>
              </el-col>
              <!-- 反馈文件 -->
              <el-col :span="24">
                <el-form-item label="反馈文件" prop="feedbackFile">
                  <FileUpAndDown ref="feedbackFile" :accept="accept" :ex-data="exData" :file-list="feedbackFileList" not-upload-msg="请上传反馈文件" @uploadSucces="handleFileSuccess" @error="fileError" />
                </el-form-item>
              </el-col>

              <h3 class="title">核定信息</h3>
              <el-col :span="24">
                <el-form-item label="是否有责" prop="isDuty">
                  <el-select v-model="form.isDuty" :disabled="disable" placeholder="请选择有无责任" :style="{ width: '100%' }">
                    <el-option label="无责" value="0" />
                    <el-option label="有责" value="1" />
                  </el-select>
                </el-form-item>
              </el-col>
              <!-- 核定文件 -->
              <el-col :span="24">
                <el-form-item label="核定文件" prop="checkFile">
                  <FileUpAndDown ref="checkFile" :disabled="disable" :accept="accept" :ex-data="exData" :file-list="checkFileList" not-upload-msg="请上传核定文件" @uploadSucces="handleFileSuccess" @error="fileError" />
                </el-form-item>
              </el-col>
            </el-form>
          </el-row>
        </el-scrollbar>
        <div slot="footer" class="dialog-footer">
          <!-- 状态 1-新建中 2-处理中 3-反馈 9-已办结 -->
          <el-button v-if="form.status == 0 || form.status == 1" type="primary" :loading="formLoading" @click="submitForm(1)">暂存</el-button>
          <el-button v-if="form.status == 0 || form.status == 1" type="primary" :loading="formLoading" @click="subOverSubmit(2)">提交</el-button>
          <el-button v-if="form.status == 2" type="primary" :loading="formLoading" @click="subOverSubmit(3)">反馈</el-button>
          <el-button v-if="form.status == 0 || (form.status != 9 && form.userId == $store.getters.uid)" type="primary" :loading="formLoading" @click="subOverSubmit(9)">办结</el-button>
          <el-button :loading="formLoading" @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
    </div>
    <!-- 用户选择-下派人员 -->
    <userselect v-model="userVisible" :select-user-keys="form.userIds ? form.userIds.split(',') : []" :default-expanded-keys="form.userIds ? form.userIds.split(',') : ['100']" @confirm="handleUserConfirm" />
  </div>
</template>

<script>
import { listLetter, getLetter, delLetter, addLetter, updateLetter, exportLetter } from '@/api/supervise/letter'
import userselect from '@/components/userselect/index.vue'
import { getFiles } from '@/api/supervise/swit'
import Userselect from '@/components/userselect/index.vue'
import FileUpAndDown from '@/components/FileUpAndDown/index'

export default {
  name: 'Letter',
  components: {
    userselect,
    FileUpAndDown,
    Userselect
  },
  filters: {
    duty(value) {
      const name = {0: '无责', 1: '有责'}
      return name[value]
    },
    status(value) {
      const statusName = {1: '新建中', 2: '处理中', 3: '反馈', 9: '已办结'}
      return statusName[value]
    }
  },
  data() {
    return {
      exportMonth: new Date(),
      // 不可编辑
      disable: false,
      accept: '.bmp, .gif, .jpg, .jpeg, .png, .doc, .docx, .xls, .xlsx, .ppt, .pptx, .html, .htm, .txt, .rar, .zip, .gz, .bz2, .mp4, .avi, .rmvb, .pdf',
      // 类型数据字典
      // 来源类型
      sourceTypeOptions: [],
      // 事件类型
      typeOptions: [],
      // 部门选择框
      deptVisible: false,
      // 选中督查人id数组
      userIds: [],
      formDisabled: false,
      userVisible: false,
      exData: { status: 2, tableName: 'supervision_letter' },
      // feedbackExData: { status: 3, tableName: 'supervision_letter' },
      // checkExData: { status: 9, tableName: 'supervision_letter' },
      mainFileList: [],
      feedbackFileList: [],
      checkFileList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 信访记录表格数据
      letterList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dateRange: [],
        searchValue: null,
        type: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        happenTime: [
          { required: true, message: '日期不能为空', trigger: 'change' }
        ],
        // complainPhone: [
        //   {
        //     pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
        //     message: '请输入正确的手机号码',
        //     trigger: 'blur'
        //   }
        // ],
        content: [
          { required: true, message: '事件描述不能为空', trigger: 'blur' }
        ]
      },
      formLoading: false

    }
  },
  created() {
    this.getList()
    // 来源类型
    this.getDicts('sup_letter_type').then(response => {
      this.sourceTypeOptions = response.data
    })
    // 事件类型
    this.getDicts('sup_case_type').then(response => {
      this.typeOptions = response.data
    })
  },
  methods: {
    // 确认下派人员
    handleUserConfirm({ id, name }) {
      this.form = { ...this.form, userIds: id, userNames: name }
    },
    /** 查询信访记录列表 */
    getList() {
      this.loading = true
      const {  pageNum, pageSize, type, dateRange, searchValue} = this.queryParams
      let params = { pageNum, pageSize }
      if (dateRange && dateRange.length) params = { ...params, searchStartTime: dateRange[0], searchEndTime: dateRange[1] }
      if (type) params = { ...params, type }
      if (searchValue) params = { ...params, searchValue }

      listLetter(params).then(response => {
        this.letterList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        userId: null,
        userName: this.$store.getters.nickName,
        sourceType: null,
        happenTime: this.parseTime(new Date()),
        complainCode: null,
        complainName: null,
        complainPhone: null,
        type: null,
        typeDetail: null,
        content: null,
        undertakeDeptId: null,
        undertakeDeptName: null,
        userNames: null,
        userIds: null,
        isDuty: null,
        remark: null,
        handleUserId: null,
        handleUserName: this.$store.getters.nickName,
        feedback: null,
        status: '0',

        // 无用
        code: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        deptId: null,
        deptName: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        dateRange: [],
        searchValue: null,
        type: null
      },
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.userIds = selection.map(item => item.userId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.formDisabled = false
      this.reset()
      this.disable = false
      this.open = true
      this.title = '添加信访记录'
    },
    // 选择部门
    handleConfirmDept({id, name}) {
      this.form = { ...this.form, undertakeDeptId: id, undertakeDeptName: name }
    },
    /** 修改按钮操作 */
    async handleUpdate(row, type) {
      this.reset()
      const id = row.id || this.ids
      this.formLoading = true
      this.open = true
      const [formRes, fileRes] = await Promise.all([
        getLetter(id),
        getFiles({ businessId: id, tableName: 'supervision_letter' })
      ])
      // 数据请求完成
      if (type === 'disabled') this.formDisabled = true
      else this.formDisabled = false
      if (formRes) {
        this.form = formRes.data
        if (this.form.status == 2) {
          this.disable = true
          this.form.handleUserId = this.$store.getters.uid
          this.form.handleUserName = this.$store.getters.nickName
        } else {
          this.disable = false
        }
      }
      this.title = '修改信访记录'
      this.formLoading = false
      // 图片请求
      // this.mainFileList = fileRes.rows.map(item => {
      //   return { name: item.displayName, url: `/zqzfj${item.filePath}`, ...item }
      // })
      fileRes.rows.forEach(item => {
        const files =  { name: item.displayName, url: `/zqzfj${item.filePath}`, ...item }
        if (item.status == 2) {
          this.mainFileList.push(files)
        } else if (item.status == 3) {
          this.feedbackFileList.push(files)
        } else {
          this.checkFileList.push(files)
        }
      })
    },
    subOverSubmit(sta) {
      this.$confirm('是否确认提交？', '提示', { type: 'warning' }).then(() => {
        this.$refs['form'].validate(valid => {
          if (valid) {
            this.submitForm(sta)
          }
        })
      }).catch(() => {})
    },
    /** 提交按钮 */
    async submitForm(sta) {

      // 状态 1-新建中 2-处理中 3-反馈 9-已办结
      if (this.form.status == 1 || this.form.status == 3) {
        if (this.form.userId != this.$store.getters.uid) return this.msgError('您不是督查人，没有操作权限！')
      } else if (this.form.status == 2) {
        console.log(this.form.userIds.split(','))
        if (this.form.userId != this.$store.getters.uid && !this.form.userIds.split(',').includes(this.$store.getters.uid.toString())) return this.msgError('您不是督查人或者下派人员，没有操作权限！')
      }

      if (sta == 2) {
        if (!this.$refs.mainFile.$refs.upload.uploadFiles[0]) return this.msgError('信访文件不能为空！')
        if (!this.form.userNames) return this.msgError('下派人员不能为空！')
      } else if (sta == 3) {
        if (!this.form.feedback) return this.msgError('处理结果不能为空！')
        if (!this.$refs.feedbackFile.$refs.upload.uploadFiles[0]) return this.msgError('反馈文件不能为空！')
      } else if (sta == 9) {
        if (!this.$refs.mainFile.$refs.upload.uploadFiles[0]) return this.msgError('信访文件不能为空！')
        if (!this.$refs.feedbackFile.$refs.upload.uploadFiles[0]) return this.msgError('反馈文件不能为空！')
        if (!this.form.feedback) return this.msgError('处理结果不能为空！')
        if (!this.form.isDuty) return this.msgError('请核定是否有责！')
      }
      const formParams = {...this.form, status: sta}
      this.formLoading = true
      if (this.form.id != null) {
        await updateLetter(formParams).then(() => {
          this.exData.businessId = this.form.id
          this.$refs.mainFile.submitFile()
        }).catch(() => this.formLoading = false)
      } else {
        await addLetter(formParams).then(res => {
          this.exData.businessId = res.data.id
          this.$refs.mainFile.submitFile()
        }).catch(() => this.formLoading = false)
      }

    },
    /** 删除按钮操作 */
    handleDelete(row) {
      if (this.userIds.length != 0) {
        if (!this.userIds.every(item => item == this.$store.getters.uid)) return this.msgError('您不是督查人，没有操作权限！')
      } else {
        if (row.userId != this.$store.getters.uid) return this.msgError('您不是督查人，没有操作权限！')
      }
      const ids = row.id || this.ids
      this.$confirm('是否确认删除信访记录编号为"' + ids + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return delLetter(ids)
      }).then(() => {
        this.getList()
        this.msgSuccess('删除成功')
      })
    },
    handleFileSuccess() {
      if (this.exData.status == 2) {
        this.exData.status = 3
        this.$refs.feedbackFile.submitFile()
      } else if (this.exData.status == 3) {
        this.exData.status = 9
        this.$refs.checkFile.submitFile()
      } else {
        this.msgSuccess('操作成功')
        this.formLoading = false
        this.open = false

        this.getList()
      }
    },
    fileError() {
      this.formLoading = false
    },
    // 关闭弹窗的回调
    closeDialog() {
      this.exData = { status: 2, tableName: 'supervision_letter' }
      this.reset()
      this.feedbackFileList = []
      this.mainFileList = []
      this.checkFileList = []
    },
    /** 导出按钮操作 */
    handleExport() {
      let params = {searchStartTime: this.parseTime(this.exportMonth)}

      this.$confirm('是否确认导出信访记录数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        exportLetter(params)
      }).then(response => {
        this.download(response.msg)
      })
    }
  }
}
</script>
