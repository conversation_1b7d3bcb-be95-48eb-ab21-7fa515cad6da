<template>
  <div class="wrap">
    <CommonTitle2 text="交通流量监测"></CommonTitle2>
    <div class="yearChange">
      <el-date-picker
        v-model="datas"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="queryData"
        :append-to-body="false"
      ></el-date-picker>
    </div>
    <div id="jtlljc" style="width: 100%; height: 480px"></div>
  </div>
</template>

<script>
import CommonTitle2 from '@/components/CommonTitle2'
import moment from 'moment'
import { indexApi } from '@/api/indexApi'

export default {
  name: 'index',
  components: {
    CommonTitle2,
  },
  data() {
    return {
      datas: [new Date().getFullYear() + '-01-01', moment(new Date()).format('YYYY-MM-DD')],
      lineChartData: [
        { name: '0时', value: 300 },
        { name: '0时', value: 300 },
        { name: '0时', value: 400 },
        { name: '0时', value: 300 },
        { name: '0时', value: 300 },
        { name: '0时', value: 300 },
        { name: '0时', value: 300 },
      ],
    }
  },
  computed: {},
  mounted() {
    this.queryData()
  },
  methods: {
    queryData() {
      indexApi('/jthl-srsccltj', {
        startdate: this.datas[0].replace(/-/g, ''),
        enddate: this.datas[1].replace(/-/g, ''),
      }).then((res) => {
        this.lineChartData = res.data.map((item) => {
          return {
            name: item.days,
            value: item.in_num,
            value1: item.out_num,
          }
        })
        this.initCharts(this.lineChartData)
      })
    },
    initCharts() {
      const chartDom = document.getElementById('jtlljc')
      const myChart = this.$echarts.init(chartDom)

      const option = {
        grid: {
          left: '7%',
          right: '7%',
          bottom: '5%',
          top: '18%',
          containLabel: true,
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(51, 51, 51, 0.7)',
          borderWidth: 0,
          axisPointer: {
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          textStyle: {
            color: 'white',
            fontSize: '24',
          },
        },
        legend: {
          orient: 'horizontal',
          icon: 'circle',
          top: '5%',
          right: '5%',
          padding: [5, 10, 10, 0],
          itemGap: 45,
          textStyle: {
            color: '#D6E7F9',
            fontSize: 28,
          },
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.lineChartData.map((item) => item.name),
          axisLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.6)',
            },
          },
          axisLabel: {
            textStyle: {
              fontSize: 24,
            },
          },
        },
        yAxis: {
          name: '单位(辆)',
          nameTextStyle: {
            color: 'rgba(255,255,255,0.6)',
            padding: [20, 10, 10, 10],
            fontSize: 24,
          },
          type: 'value',
          axisLabel: {
            textStyle: {
              fontSize: 24,
            },
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.6)',
            },
          },
          splitLine: {
            lineStyle: {
              color: '#4A6C89',
            },
          },
        },
        series: [
          {
            name: '输入车辆数',
            data: this.lineChartData.map((item) => item.value),
            type: 'line',
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(62, 215, 253, 0.30)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(62, 215, 253, 0)',
                  },
                ],
              },
            },
            lineStyle: {
              color: '#3ED7FD',
            },
            smooth: true,
            symbol: 'none',
          },
          {
            name: '驶出车辆数',
            data: this.lineChartData.map((item) => item.value1),
            type: 'line',
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(224, 214, 34, 0.30)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(224, 214, 34, 0)',
                  },
                ],
              },
            },
            lineStyle: {
              color: '#E0D622',
            },
            smooth: true,
            symbol: 'none',
          },
        ],
      }

      myChart.setOption(option)

      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
    initCharts2(data) {
      let myChart = this.$echarts.init(document.getElementById('jtlljc'))
      let option = {
        tooltip: {
          trigger: 'axis',
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        grid: {
          left: '8%',
          right: '6%',
          top: '18%',
          bottom: '10%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            data: data.map((item) => item.name),
            axisLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)', // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              textStyle: {
                color: '#ffffffcc',
                fontSize: 28,
                padding: [10, 0],
              },
            },
          },
        ],
        yAxis: [
          {
            name: '单位(辆/日)',
            type: 'value',
            nameTextStyle: {
              fontSize: 24,
              color: '#ffffffcc',
              padding: 20,
            },
            splitLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)',
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 28,
                color: '#ffffffcc',
              },
            },
          },
        ],
        series: [
          {
            name: '',
            type: 'bar',
            barWidth: '14%',
            barBorderRadius: 8,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: '#E0D722',
                  },
                  {
                    offset: 1,
                    color: '#E0D72200',
                  },
                ]),
                barBorderRadius: 4,
              },
            },
            data: data.map((item) => item.value),
          },
        ],
      }
      myChart.setOption(option)
      myChart.getZr().on('mousemove', (param) => {
        myChart.getZr().setCursorStyle('default')
      })
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
.wrap {
  position: relative;
  margin-bottom: 40px;
  /deep/ .yearChange {
    position: absolute;
    right: 10px;
    top: 0px;
    .el-input__inner {
      height: 48px !important;
      background-color: #132c4e !important;
      border: 2px solid #afdcfb !important;
      color: #fff !important;
      border-radius: 15px !important;
      font-size: 24px;
    }
    .el-picker-panel {
      top: 270px !important;
    }
  }
}

.flex-b {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>