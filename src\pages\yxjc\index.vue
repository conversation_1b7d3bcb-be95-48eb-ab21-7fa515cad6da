<template>
  <div class="pageContainer-Map">
    <left @openDialog="dialogShow = true"></left>
    <center></center>
    <right @openDialog="dialogShow2 = true"></right>

    <zhyxtsbgDialog
      :visible="dialogShow"
      @close="dialogShow = false"
      @checkReport="checkReport"
      @savePdf="savePdf"
      @handlePrint="handlePrint"
    ></zhyxtsbgDialog>
    <sjlbDialog :visible="dialogShow2" @close="dialogShow2 = false"></sjlbDialog>
    <bgxqDialog
      :visible="reportShow"
      :reportMonth="reportMonth"
      :isPrint="isPrint"
      :isPdf="isPdf"
      @close="reportShow = false"
    ></bgxqDialog>
  </div>
</template>

<script>
import left from './left'
import right from './right'
import center from './center'
import zhyxtsbgDialog from './dialog/zhyxtsbgDialog'
import sjlbDialog from './dialog/sjlbDialog'
import bgxqDialog from './dialog/bgxqDialog'
export default {
  name: 'index',
  components: {
    left,
    right,
    center,
    zhyxtsbgDialog,
    sjlbDialog,
    bgxqDialog,
  },
  data() {
    return {
      dialogShow: false,
      dialogShow2: false,
      reportShow: false,
      reportMonth: '',
      isPrint: false,
      isPdf: false,
    }
  },
  computed: {},
  mounted() {},
  methods: {
    checkReport(i) {
      this.reportShow = true
      this.reportMonth = i
      this.isPrint = false
      this.isPdf = false
    },
    handlePrint(i) {
      this.reportShow = true
      this.reportMonth = i
      this.isPrint = true
      this.isPdf = false
    },
    savePdf(i) {
      this.reportShow = true
      this.reportMonth = i
      this.isPrint = false
      this.isPdf = true
    },
  },
  watch: {},
}
</script>

<style scoped>
</style>