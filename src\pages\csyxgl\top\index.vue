<template>
  <div class="top">
    <div class="item">
      <div
        class="number"
        v-for="(item, i) in num1"
        :key="i"
        style="color: #1dfcfe"
      >
          <span class="numbg" v-if="item!=',' && item!='.'">
            <count-to
              :start-val="0"
              :end-val="Number(item)"
              :duration="3000"
            ></count-to>
          </span>
        <span v-else>{{item}}</span>
      </div>
      <p class="title">绿地面积</p>
    </div>
    <div class="line"></div>
    <div class="item">
      <div
        class="number"
        v-for="(item, i) in num2"
        :key="i"
        style="color: #1dfcfe"
      >
          <span class="numbg" v-if="item!=',' && item!='.'">
            <count-to
              :start-val="0"
              :end-val="Number(item)"
              :duration="3000"
            ></count-to>
          </span>
        <span v-else>{{item}}</span>
      </div>
      <p class="title">树木数量</p>
    </div>
    <div class="line"></div>
    <div class="item">
      <div
        class="number"
        v-for="(item, i) in num3"
        :key="i"
        style="color: #1dfcfe"
      >
        <span>{{item}}</span>
      </div>
      <p class="title">绿化覆盖率</p>
    </div>
  </div>
</template>

<script>
import { indexApi } from '@/api/indexApi'
import countTo from 'vue-count-to'
export default {
  name: 'index',
  components: {
    countTo
  },
  data() {
    return {
      city: "",
      year: "",
      num1: "36,547",
      num2: "23,553",
      num3: "52%",
      topname: [
        { name: "金华市", code: 330700 },
        { name: "婺城区", code: 330702 },
        { name: "金东区", code: 330703 },
        { name: "兰溪市", code: 330781 },
        { name: "东阳市", code: 330783 },
        { name: "义乌市", code: 330782 },
        { name: "永康市", code: 330784 },
        { name: "浦江县", code: 330726 },
        { name: "武义县", code: 330723 },
        { name: "磐安县", code: 330727 },
      ],
    }
  },
  computed: {},
  mounted() {
    this.$bus.$on('cityChange', (city) => {
      this.city = city;
      this.initApi(city,localStorage.getItem("year"));
    })
    this.$bus.$on('yearChange', (year) => {
      this.year = year;
      this.initApi(localStorage.getItem("city"),year);
    })
    this.initApi(localStorage.getItem("city"),localStorage.getItem("year"));
  },
  methods: {
    initApi(city) {
      indexApi("/csdn_yjyp11", { area_name: city }).then((res) => {
        this.num1 = this.setAct(res.data[0].area_num);
      });
      let code = this.topname.find((a) => a.name == city).code;
      if (code) {
        indexApi("/csrk_ssrsldrs", { addressCode: code }).then((res) => {
          this.num2 = this.setAct(Number(res.data[0].population_count));
        });
      } else {
        this.num2 = "00000";
      }
    },
    //科学计数法
    setAct(val) {
      if (!val) return val;
      var logo = "";
      var num = val;
      num = typeof (num) === 'string' ? num : String(num)
      if (Number(val) < 0) {
        logo = "-";
        num = val.split('-')[1];
      }
      const result = num.split("");
      let position = result.indexOf(".");
      position = position !== -1 ? position : result.length;
      while (position > 3) {
        position -= 3;
        result.splice(position, 0, ",");
      }
      return logo + result.join("")
    }
  },
  watch: {}
}
</script>

<style scoped lang='less'>
.top {
  width: 828px;
  height: 158px;
  padding: 20px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  background: linear-gradient(
    90deg,
    rgba(5, 36, 64, 0.5) 0%,
    rgba(5, 36, 64, 0.99) 45%,
    rgba(5, 36, 64, 0.49) 50%
  );
  position: absolute;
  left: calc(50% - 414px);
  top: 250px;
  z-index: 666;
}

.line {
  width: 2px;
  height: 130px;
  background: linear-gradient(
    0deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(211, 234, 255, 0.99) 48%,
    rgba(255, 255, 255, 0) 100%
  );
}

.item {
  width: 414px;
  height: 120px;
  text-align: center;
  margin-top: 10px;
}

.title {
  color: #e3edff;
  font-size: 30px;
}

.number {
  display: inline-block;
  font-size: 45px;
}

.number .numbg {
  display: inline-block;
  width: 20px;
  height: 48px;
  font-weight: 700;
  line-height: 48px;
  text-align: center;
  margin: 0 4px;
  border-radius: 8px;
}
</style>