<template>
  <div class="m-table">
    <div class="m-header">
      <span>小组</span>
      <span>姓名</span>
      <span>终端号</span>
      <span>执法记录仪</span>
    </div>
    <!-- 主体内容 -->
    <div v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.8)" class="m-body">
      <vueSeamlessScroll :data="tableList" :class-option="defaultOption">
        <div v-for="item in tableList" :key="item.id" class="m-tr">
          <span>{{ item.deptName }}</span>
          <span>{{ item.userName }}</span>
          <span>{{ item.terminalNo }}</span>
          <span>
            <el-tooltip class="item" :open-delay="500" effect="dark" content="视频呼叫" placement="top">
              <el-button type="text" style="padding: 0;" icon="el-icon-video-camera" @click="handleOpenVideo(item.terminalNo)" />
            </el-tooltip>
            <el-tooltip class="item" :open-delay="500" effect="dark" content="语音呼叫" placement="top">
              <el-button type="text" style="padding: 0;" icon="el-icon-phone-outline" @click="handleOpenVoice(item.terminalNo)" />
            </el-tooltip>
          </span>
        </div>
        <div v-if="!tableList.length" class="m-tr">
          暂无数据
        </div>
      </vueSeamlessScroll>
    </div>
    <!-- 视频监控 -->
    <video-box :visible.sync="$store.getters.videoVisible" :terminal-no="terminalNo" :modal="false" :close-on-click-modal="false" />
  </div>
</template>

<script>
// import { getLocaleUsersList } from '@/api/board/dataView/index'
import { fn_start, fn_Exit } from '@/assets/idt/Idt'
import videoBox from '@/pages/board/views/components/windowInfo/videoBox/index.vue'
import vueSeamlessScroll from 'vue-seamless-scroll'
import { listRecorder } from '@/api/system/recorder'

export default {
  components: {
    vueSeamlessScroll,
    videoBox
  },
  filters: {
    statusType(status) {
      // 0-离线 1-在线 2-未佩戴
      const name = {0: '离线', 1: '在线', 2: '未佩戴'}
      return name[status]
    }
  },
  data() {
    return {
      tableList: [],
      loading: false,
      terminalNo: ''
    }
  },
  computed: {
    defaultOption() {
      return {
        step: 0.2, // 数值越大速度滚动越快
        limitMoveNum: 8, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 1000 // 单步运动停止的时间(默认值1000ms)
      }
    }
  },
  mounted() {
    this.fetchData()
    fn_start()
  },
  destroyed() {
    fn_Exit()
  },
  methods: {
    handleOpenVideo(terminalNo) {
      this.terminalNo = terminalNo
      this.$store.commit('board/SET_CALL_TYPE', { type: 'callRecv', ARx: 1, ATx: 1, VRx: 1, VTx: 0 }) // 修改为呼出状态
      this.$store.commit('board/SET_VIDEO_VISIBLE', true)
    },
    handleOpenVoice(terminalNo) {
      this.terminalNo = terminalNo
      this.$store.commit('board/SET_CALL_TYPE', { type: 'callRecv', ARx: 1, ATx: 1, VRx: 0, VTx: 0 }) // 修改为呼出状态
      this.$store.commit('board/SET_VIDEO_VISIBLE', true)
    },
    fetchData() {
      this.loading = true
      listRecorder({ status: 1 }).then(res => {
        this.tableList = res.rows
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style scoped lang="scss">
.m-table {
  height: 100%;
  padding-top: pxtorem(10);
  .m-header {
    height: pxtorem(28);
    line-height: pxtorem(28);
    background: url(@/assets/images/m-header.png) no-repeat center center / 100% 100%;
    font-size: pxtorem(14);
    display: flex;
    padding: 0 10px 0 10px;
    span {
      text-align: center;
      flex: 1;
    }
  }
  .m-body {
    height: calc(100% - 0.14583rem);
    overflow: hidden;
    .m-tr {
      overflow: hidden;
      height: pxtorem(26);
      line-height: pxtorem(26);
      font-size: pxtorem(12);
      color: #00f7ff;
      background: rgba(0, 0, 0, 0.3);
      padding: 0 10px;
      margin-top: 5px;
      text-align: center;
      cursor: pointer;
      span {
        width: 25%;
        height: pxtorem(26);
        float: left;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        ::v-deep .el-button--text {
          color: #00f7ff;
        }
      }
    }
  }
}
</style>
