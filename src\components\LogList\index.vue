<template>
  <el-dialog
    :close-on-click-modal="false"
    append-to-body
    title="操作日志"
    :visible.sync="value"
    width="400px"
    :before-close="handleClose"
    @open="handleOpen"
  >
    <div class="block">
      <el-scrollbar wrap-class="scrollbar-wrapper" style="height: 100%;">
        <el-timeline v-loading="loading">
          <el-timeline-item v-for="item in msgData" :key="item.id" :timestamp="item.createTime" placement="top">
            <el-card>
              <h4 style="margin-bottom: 10px;">流程节点：{{ item.operaDescn | statusData }}</h4>
              <p>{{ item.operaDescn }}</p>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </el-scrollbar>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleClose">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {getLogList} from '@/api/case/synthetical/capture'
export default {
  filters: {
    statusData(type) {
      // let data = {1: '待处理', 3: ' 下发中队长', 4: ' 下发队员', 5: ' 出警', 6: ' 警情反馈', 9: ' 已完结'}
      // if (type) return data[type]
      if (type && typeof type == 'string') {
        return type.split(' ')[0]
      }
    }
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    caseId: {
      type: Number,
      default: 0
    },
    eventType: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      loading: false,
      msgData: []
    }
  },
  methods: {
    fetchData() {
      this.loading = true
      getLogList({ caseId: this.caseId }).then(res => {
        this.loading = false
        this.msgData = res.rows
      }).catch(() => {
        this.loading = false
      })
    },
    handleClose() {
      this.$emit('input', false)
    },
    handleOpen() {
      this.fetchData()
    }
  }
}
</script>

<style scoped lang="scss">
.block {
  height: 350px;
  ::v-deep {
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }
}
</style>
