import SketchViewModel from "@arcgis/core/widgets/Sketch/SketchViewModel.js";
import SimpleMarkerSymbol from "@arcgis/core/symbols/SimpleMarkerSymbol.js";
import SimpleLineSymbol from "@arcgis/core/symbols/SimpleLineSymbol.js";
import SimpleFillSymbol from "@arcgis/core/symbols/SimpleFillSymbol.js";
import GraphicsLayer from "@arcgis/core/layers/GraphicsLayer.js";
import Graphic from "@arcgis/core/Graphic.js";
import drawArrow from "./drawArrow/index.js";

/**
 * 绘图工具
 * 1. 绘制后不能编辑
 * 2.
 */
class Draw {
  // 构造函数
  constructor({
    view,
    mode, // 'clear':绘制后会清除之前的
    graphicLayer,
    spatialReference,
  }) {
    this.view = view;
    this.mode = mode;
    this.spatialReference = spatialReference;
    this.defaultSimpleMarkerSymbol = null;
    this.defaultSimpleLineSymbol = null;
    this.defaultSimpleFillSymbol = null;
    this.sketchVM = null;
    if (!graphicLayer) {
      this._initGraphicLayer(); //初始化graphicLayer
    } else {
      this.drawLayer = graphicLayer;
    }
    this._initDefaultSymbol();
  }

  // 初始化symbol
  _initDefaultSymbol() {
    this.defaultSimpleMarkerSymbol = new SimpleMarkerSymbol({
      color: [255, 0, 0],
      width: 1.5,
    });
    this.defaultSimpleLineSymbol = new SimpleLineSymbol({
      color: [255, 0, 0],
      width: 2,
    });

    this.defaultSimpleFillSymbol = new SimpleFillSymbol({
      color: [0, 0, 0, 0.25],
      outline: {
        color: [255, 0, 0],
        width: 1.5,
      },
    });
  }

  _initGraphicLayer() {
    this.drawLayer = this.view.map.findLayerById("drawLayer");
    if (!this.drawLayer) {
      this.drawLayer = new GraphicsLayer({
        id: "drawLayer",
        listMode: "hide",
        spatialReference: this.spatialReference,
      });
      this.view.map.add(this.drawLayer);
    }
  }

  /**
   * 绘制点、多点、线、面、矩形、圆等
   * @param {String} type 绘制的图形类型: point |polyline|polygon| circle|rectangle
   * @param {String} symbol 样式
   * @returns
   * @memberof Draw
   */
  draw(type, symbol) {
    let that = this;
    if (type === "general_arrow" || type === "swallowtail_arrow") {
      return new Promise((resolve, reject) => {
        drawArrow(type).then(({ graphic, sketchViewModel }) => {
          that.sketchViewModel = sketchViewModel;
          sketchViewModel.cancel();
          resolve(graphic);
        });
      });
    }
    return new Promise((resolve, reject) => {
      if (that.mode === "clear") {
        that.drawLayer.removeAll();
      }
      if (that.sketchVM) {
        that.sketchVM.destroy();
      }
      that.sketchVM = new SketchViewModel({
        layer: that.drawLayer,
        view: that.view,
        pointSymbol: symbol ? symbol : that.defaultSimpleMarkerSymbol,
        polylineSymbol: symbol ? symbol : that.defaultSimpleLineSymbol,
        polygonSymbol: symbol ? symbol : that.defaultSimpleFillSymbol,
        updateOnGraphicClick: false, // 去除编辑

        defaultCreateOptions: {
          hasZ: false, // default value
        },
        defaultUpdateOptions: {
          enableZ: false, // default value
        },
      });
      that.sketchVM.create(type);
      that.sketchVM.on("create", function (e) {
        if (e.state === "complete") {
          resolve(e.graphic);
          that.sketchVM.create(type);
        }
      });
    });
  }

  // 清除所有的绘制
  clear() {
    this.drawLayer.removeAll();
  }
  cancel() {
    if (this.sketchVM) {
      this.sketchVM.cancel();
    }
  }
  destroy() {
    if (this.sketchVM) {
      this.sketchVM.destroy();
    }
    if (this.sketchViewModel) {
      this.sketchViewModel.cancel();
    }

    this.view.map.remove(this.drawLayer);
  }
}

export default Draw;
