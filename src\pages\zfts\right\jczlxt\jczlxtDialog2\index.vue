<template>
  <ygfDialog :visible='visible' width='1250px'>
    <div id="zlxt" class="rwgz-tc">
      <div class="rw-title flex-between">
        <div class="fs-44 text-mid-yellow dialogTitle" id="rwTitle" style="margin-left: 20px;">{{name}}</div>
        <div class="close cursor" @click="close" style="margin-right: 20px;"></div>
      </div>
      <div class="content">
        <div class="toolBar">
          <div class="search-box">
            <el-autocomplete class="inline-input" suffix-icon="el-icon-search" v-model="searchInput"
                             :fetch-suggestions="querySearch" placeholder="请输入关键词" value-key="depart" :trigger-on-focus="false"
                             @select="handleSelect" @change="handleChange"></el-autocomplete>
            <div class="search" @click="handleChange">搜索</div>
          </div>
        </div>
        <div class="table">
          <div class="table-line">
            <div class="table-column borderLeft" style="flex: 2">
              <span class="text title-text" >区域</span>
            </div>
            <div class="table-column" style="flex: 2">
              <span class="text title-text" >部门</span>
            </div>
            <div class="table-column" style="flex: 2">
              <span class="text title-text" v-show="name == '网格上报数'">网格上报数</span>
              <span class="text title-text" v-show="name == '协同数'">协同数</span>
              <span class="text title-text" v-show="name == '办结数'">办结数</span>
            </div>
          </div>
          <div class="table-container">
            <div class="table-line" v-for="(tableItem,i) in ['金华市','婺城区','金东区','兰溪市','义乌市','东阳市','永康市','浦江县','武义县','磐安县','金华开发区']" v-show="getAreaCity(tableItem).length > 0">
              <div class="area borderLeft" style="flex: 2">
                <div class="table-column" style="flex: 1" :style="{height: 'calc(80px * '+ getAreaCity(tableItem).length +' - 2px)',lineHeight: 'calc(80px * '+ getAreaCity(tableItem).length +' - 2px) '}">
                  <div class="text">{{tableItem}}</div>
                </div>
              </div>
              <div class="line-list" style="flex: 4">
                <div class="table-line" v-for="(item,i) in getAreaCity(tableItem)">
                  <div class="table-column" style="flex: 2">
                    <div class="text" :title="item.depart" style="width: 300px">{{item.depart}}</div>
                  </div>
                  <div class="table-column" style="flex: 2">
                    <div class="text">{{item.num}}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ygfDialog>
</template>

<script>
import ygfDialog from '@/components/ygfDialog'
import { indexApi } from '@/api/indexApi'
export default {
  name: 'index',
  props: ['name','time','visible'],
  components: {
    ygfDialog
  },
  data() {
    return {
      searchInput:"",
      TableData: [],
    }
  },
  computed: {
    city() {
      return localStorage.getItem("city")
    }
  },
  mounted() {
    // 监听城市变化
    this.$bus.$on('cityChange', (city) => {
      this.city = city;
      this.getDetail(this.name,this.time)
    });
    // 监听年份变化
    this.$bus.$on('yearChange', (year) => {
      this.year = year;
      this.getDetail(this.name,this.time)
    });
    this.getDetail(this.name,this.time)
  },
  methods: {
    //获取区划对应的部门,数量 方便表格做合并单元格展示
    getAreaCity(area) {
      return this.TableData.filter(item => item.area == area)
    },
    //获取数据
    getDetail(name,time) {
      switch (name) {
        case "网格上报数":
          this.getWgsbist(time);
          break;
        case "协同数":
          this.getXtsList(time);
          break;
        case "办结数":
          this.getBjsList(time);
          break;
      }
    },
    //网格上报数
    getWgsbist(time) {
      indexApi("csdn_yjyp_wgsbs1",{area_name:this.city,sj1:time[0],sj2:time[1],cybm:this.searchInput}).then(res => {
        this.TableData = res.data.map(item => {return {
          area:item.qxwd,
          depart:item.cybm,
          num:item.wgsbs
        }})
      })
    },
    //协同数
    getXtsList(time) {
      indexApi("csdn_yjyp_xts1",{area_name:this.city,sj1:time[0],sj2:time[1],cybm:this.searchInput}).then(res => {
        this.TableData = res.data.map(item => {return {
          area:item.qxwd,
          depart:item.cybm,
          num:item.xts
        }})
      })
    },
    //办结数
    getBjsList(time) {
      indexApi("csdn_yjyp_bjs1",{area_name:this.city,sj1:time[0],sj2:time[1]}).then(res => {
        this.TableData = res.data.map(item => {return {
          area:item.qxwd,
          depart:item.cybm,
          num:item.bjs
        }})
      })
    },
    querySearch(queryString, cb) {
      var restaurants = this.TableData
      var results = queryString
        ? restaurants.filter(this.createFilter(queryString.replace(/\s*/g, '')))
        : restaurants
      // 调用 callback 返回建议列表的数据
      cb(results)
    },
    createFilter(queryString) {
      return (restaurant) => {
        return restaurant.depart.toLowerCase().indexOf(queryString.toLowerCase()) !== -1
      }
    },
    handleSelect() {
      this.getDetail(this.name,this.time)
    },
    handleChange() {
      this.getDetail(this.name,this.time)
    },
    close() {
      this.$emit('close')
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.getDetail(this.name,this.time)
      }
    }
  }
}
</script>

<style scoped lang='less'>

::-webkit-scrollbar {
  display: none;
}


ul,
ul li {
  list-style: none;
}

.rwgz-tc {
  width: 1387px;
  height: 820px;
  background: url("@/assets/zhdd/dialogBg.png") no-repeat;
  background-size: 100% 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-direction: column;
}

.rw-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 46px 3% 0;
  width: 95%;
  height: 60px;
  line-height: 60px;
}

.close {
  background: url("@/assets/zhdd/close.png") no-repeat;
  width: 34px;
  height: 34px;
}

.ql-indent-1 img {
  width: 100%;
}

img {
  width: 100%;
}

.content {
  width: 1296px;
  height: 850px;
  margin-top: 10px;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  flex-direction: column;
}

::-webkit-scrollbar {
  width: 0;
  height: 0;
}

/deep/ .el-input {
  /*position: absolute;*/
  /*top: 229px;*/
  /*left: 1630px;*/
  width: 537px;
  height: 72px;
}

/deep/ .el-input__icon {
  height: 100%;
  width: 25px;
  text-align: center;
  -webkit-transition: all .3s;
  transition: all .3s;
  line-height: 75px;
}

/deep/ .el-scrollbar {
  overflow: hidden;
  /*position: relative;*/
  height: 500px;
  background: #020b28;
  border-radius: 10px;
}

/deep/ .el-autocomplete-suggestion__wrap {
  max-height: unset !important;
  padding: 10px 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

/deep/ .el-input--suffix .el-input__inner {
  border: 1px solid #359CF8;
  border-radius: 8px;
  padding-right: 30px;
  height: 70px;
  font-family: MicrosoftYaHei;
  font-size: 28px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 1px;
  color: #bbe5fd !important;
  background: #020b28;
}

/deep/ .el-input.is-active .el-input__inner, .el-input__inner:focus {
  border: 1px solid #bbe5fd;
  outline: 0;
}

/deep/ .el-input__suffix-inner {
  pointer-events: all;
  font-size: 28px;
  margin: 15px 20px 0 0;
  color: #bbe5fd !important;
}

/deep/ .el-autocomplete-suggestion li {
  padding: 0 20px;
  line-height: 34px;
  cursor: pointer;
  color: #bbe5fd;
  font-size: 28px;
  list-style: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin: 10px 0 25px 0;
}

/deep/ .el-autocomplete-suggestion li:hover {
  background: unset !important;
}

.search-box {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.search {
  width: 100px;
  height: 65px;
  line-height: 65px;
  text-align: center;
  background: #0A619E;
  border: 1px solid #359CF8;
  border-radius: 8px;
  font-size: 28px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FEFEFE;
  margin-left: 10px;
  cursor: pointer;
}

.toolBar {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.table {
  width: 1296px;
  height: fit-content;
  border-top: 2px solid #335176;
  /*border-left: 2px solid #335176;*/
  margin-top: 31px;
}

.area {
  display: flex;
  flex-direction: column;
}

.borderLeft {
  border-left: 2px solid #335176;
}

.table-line {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  overflow: hidden;
}

.table-container {
  height: 486px;
  overflow-y: scroll;
}

.text {
  font-size: 30px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  margin-left: 40px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.title-text {
  font-weight: bold;
}

.table-column {
  background: rgba(50,134,248,0.15);
  border-right: 2px solid #335176;
  border-bottom: 2px solid #335176;
  height: 78px;
  line-height: 78px;
}

.table-column-title {
  background: rgba(50,134,248,0.25);
}
</style>