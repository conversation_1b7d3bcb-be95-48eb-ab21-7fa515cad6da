<template>
  <div>
    <CommonTitle text='执法装备'></CommonTitle>
    <div class='wrap-container'>
      <div class='wrap-container-box'>
        <div class='wrap-container-box-item' v-for='(item,i) in list' :key='i'>
          <div class='wrap-container-box-item-top'>{{item.name}}</div>
          <div class='wrap-container-box-item-bottom'>{{item.value}}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
export default {
  name: 'index',
  components: {
    CommonTitle
  },
  data() {
    return {
      list: [
        {
          name:"装备一",
          value: 30
        },
        {
          name:"装备一",
          value: 30
        },
        {
          name:"装备一",
          value: 30
        },
        {
          name:"装备一",
          value: 30
        },
        {
          name:"装备一",
          value: 30
        },
        {
          name:"装备一",
          value: 30
        },
        {
          name:"装备一",
          value: 30
        },
        {
          name:"装备一",
          value: 30
        }
      ]
    }
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang='less'>
  ::-webkit-scrollbar {
    width: 0;
  }
  .wrap-container {
    width: 100%;
    height: 400px;
    margin-bottom: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    .wrap-container-box {
      width: 987px;
      height: 394px;
      overflow-y: scroll;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      flex-wrap: wrap;
      border-top: 1px solid #0B64C3;
      border-left: 1px solid #0B64C3;
      .wrap-container-box-item {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        border-right: 1px solid #0B64C3;
        border-bottom: 1px solid #0B64C3;
        .wrap-container-box-item-top {
          width: 328px;
          height: 84px;
          background: rgba(11,100,195,0.3);
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 500;
          font-size: 32px;
          color: #FFFFFF;
          line-height: 84px;
          text-align: center;
          font-style: normal;
          text-transform: none;
        }
        .wrap-container-box-item-bottom {
          width: 328px;
          height: 112px;
          text-align: center;
          line-height: 112px;
          font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
          font-weight: 400;
          font-size: 48px;
          font-style: normal;
          text-transform: none;
          background: linear-gradient(180deg, #FFFFFF 32%, #52C3F7 100%);
          -webkit-background-clip: text;
          color: transparent;
        }
      }
    }
  }
</style>