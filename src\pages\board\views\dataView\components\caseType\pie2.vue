<template>
  <div ref="chart" class="pie" />
</template>

<script>
export default {
  props: {
    chartColor: {
      type: Array,
      default: () => {
        return ['#54c9e4', '#2766e3', '#2766e3', '#54c9e3', '#6691e1', '#14d4ca']
      }
    },
    seriesName: {
      type: String,
      default: ''
    },
    seriesData: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      myChart: null
    }
  },
  computed: {
    filterSeriesData() {
      return this.seriesData.filter(item => {
        return item.value
      })
    },
    options() {
      return {
        color: this.chartColor,
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        series: [
          {
            name: this.seriesName,
            type: 'pie',
            radius: '50%',
            data: this.filterSeriesData,
            labelLine: {
              show: true,
              length: 5,
              length2: 2,
              smooth: true,
              lineStyle: {
                color: '#6d6d6d'
              }
            },
            label: {
              fontSize: 12,
              color: '#00f7ff'
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
    }
  },
  watch: {
    seriesData() {
      this.myChart.setOption(this.options)
    }
  },
  mounted() {
    this.initChart()
    window.addEventListener('resize', this.resizeChart)
  },
  destroyed() {
    window.removeEventListener('resize', this.resizeChart)
  },
  methods: {
    initChart() {
      this.myChart = this.$echarts.init(this.$refs.chart)
      this.myChart.setOption(this.options)
    },
    resizeChart() {
      setTimeout(() => {
        this.myChart.resize()
      }, 500)
    }
  }
}
</script>

<style scoped>
.pie {
  width: 100%;
  height: 100%;
}
</style>
