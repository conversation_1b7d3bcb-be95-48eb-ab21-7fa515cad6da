import Graphic from "@arcgis/core/Graphic.js";
import GraphicsLayer from "@arcgis/core/layers/GraphicsLayer.js";

export default class RouteLayer {
  constructor({
    view,
    data,
    lineStyle = {
      color: [226, 119, 40],
      width: 5,
    },
    effectStyle = {
      url: "https://static.arcgis.com/images/Symbols/Shapes/BlackStarLargeB.png",
      width: "25px",
      height: "25px",
    },
    speed = 1,
  }) {
    this.view = view;
    this.paths = data;
    this.lineStyle = { ...lineStyle };
    this.MoveLayer = null;
    this.startingPoint = {};
    this.startNum = 0;
    this.endNum = 1;
    this.moving = null;
    this.effectStyle = { ...effectStyle };
    this.init();
    this.speed = speed;
  }

  init() {
    let polyline = {
      type: "polyline", // autocasts as new Polyline()
      paths: this.paths,
      spatialReference: this.view.spatialReference,
    };
    let lineSymbol = {
      type: "simple-line", // autocasts as SimpleLineSymbol()
      ...this.lineStyle,
    };
    let polylineGraphic = new Graphic({
      geometry: polyline,
      symbol: lineSymbol,
    });
    let modelCar = this.initModelCar();
    this.moveCarGra = new Graphic({
      geometry: this.startingPoint,
      symbol: modelCar,
    });
    this.moveLayer = new GraphicsLayer({
      graphics: [polylineGraphic, this.moveCarGra],
      id: "moveLayer",
      elevationInfo: {
        mode: "on-the-ground",
      },
    });
    this.view.map.add(this.moveLayer);
  }
  start() {
    if (!this.moving) {
      this.move(this.startNum, this.endNum);
    }
  }
  // 初始化车辆
  initModelCar() {
    const carPaths = this.paths;
    let x1 = carPaths[0][0];
    let y1 = carPaths[0][1];
    let x2 = carPaths[1][0];
    let y2 = carPaths[1][1];
    // 设置起点
    this.startingPoint = {
      type: "point",
      x: carPaths[0][0],
      y: carPaths[0][1],
      z: carPaths[0][2],
      spatialReference: this.view.spatialReference,
    };
    const modelNewCar = this.setModelCar(x1, y1, x2, y2);

    return modelNewCar;
  }
  // 根据坐标计算角度
  calcAngle(x1, y1, x2, y2) {
    let tan = (Math.atan(Math.abs((y2 - y1) / (x2 - x1))) * 180) / Math.PI + 90;
    if (x2 > x1 && y2 > y1) {
      return -tan + 180;
    } else if (x2 > x1 && y2 < y1) {
      return tan;
    } else if (x2 < x1 && y2 > y1) {
      return tan - 180;
    } else {
      return -tan;
    }
  }
  pause() {
    if (this.moving) {
      clearInterval(this.moving);
      this.moving = null;
    }
  }

  stop() {
    if (this.moving) {
      clearInterval(this.moving);
      this.moving = null;
    }
    this.moveLayer.remove(this.moveCarGra);
    let modelCar = this.initModelCar();
    this.moveCarGra = new Graphic({
      geometry: this.startingPoint,
      symbol: modelCar,
    });
    this.moveLayer.add(this.moveCarGra);
  }
  // 创建车辆
  setModelCar(x1, y1, x2, y2) {
    let modelNewCar = {
      type: "picture-marker",

      // yoffset: 4,
      // xoffset: -5,
      angle: this.calcAngle(x1, y1, x2, y2) + 90, //朝向
      ...this.effectStyle,
    };
    // let modelNewCar = {
    //   type: "point-3d", // autocasts as new PointSymbol3D()
    //   symbolLayers: [
    //     {
    //       type: "icon", // autocasts as new IconSymbol3DLayer()
    //       size: 8, // points
    //       resource: { primitive: "circle" },
    //       material: { color: "red" },
    //       anchorPosition: { x: 2, y: 10 },
    //     },
    //   ],
    // };
    return modelNewCar;
  }
  clear() {
    if (this.moving) {
      clearInterval(this.moving);
      this.moving = null;
    }
    this.view.map.remove(this.moveLayer);
    this.moveLayer = null;
  }
  move(start, end) {
    const carPaths = this.paths;
    let x1 = carPaths[start][0];
    let y1 = carPaths[start][1];
    let x2 = carPaths[end][0];
    let y2 = carPaths[end][1];

    //斜率
    let p = (y2 - y1) / (x2 - x1);
    //速度
    let v = (0.00012 * this.speed).toFixed(8); // 过小会导致线路偏移
    // console.log(v);
    const movingCallback = () => {
      this.moveLayer.remove(this.moveCarGra);
      this.startNum = start;
      this.endNum = end;
      //分别计算 x,y轴的方向和速度
      if (Math.abs(p) == Number.POSITIVE_INFINITY) {
        //垂直的时候斜率无穷大
        this.moveCarGra.geometry.y += v;
      } else {
        if (x2 < x1) {
          this.moveCarGra.geometry.x -= (1 / Math.sqrt(1 + p * p)) * v;
          this.moveCarGra.geometry.y -= (p / Math.sqrt(1 + p * p)) * v;
          // 创建行驶车辆
          let modelNewCar = this.setModelCar(x1, y1, x2, y2);
          this.moveCarGra = new Graphic({
            geometry: this.moveCarGra.geometry,
            symbol: modelNewCar,
          });
          this.moveLayer.add(this.moveCarGra);
        } else {
          this.moveCarGra.geometry.x += (1 / Math.sqrt(1 + p * p)) * v;
          this.moveCarGra.geometry.y += (p / Math.sqrt(1 + p * p)) * v;
          // 创建行驶车辆
          let modelNewCar = this.setModelCar(x1, y1, x2, y2);
          this.moveCarGra = new Graphic({
            geometry: this.moveCarGra.geometry,
            symbol: modelNewCar,
          });
          this.moveLayer.add(this.moveCarGra);
        }
      }
      if (
        Math.abs(this.moveCarGra.geometry.x - x2) <= v &&
        Math.abs(this.moveCarGra.geometry.y - y2) <= v
      ) {
        clearInterval(this.moving);
        this.moving = null;
        this.startNum = start++;
        this.endNum = end++;
        if (end < carPaths.length) {
          this.move(start, end);
        }
      }
    };
    this.moving = setInterval(movingCallback, 500);
  }
}
