<template>
  <div>
    <CommonTitle text='回访概况'></CommonTitle>
    <div class="hfgk-container">
      <div style="position: absolute; top: 40px; left: 680px; z-index: 2" v-show="year == $currentYear" class='yearChange'>
        <el-date-picker
          v-model="value1"
          type="monthrange"
          @change="(range) => getHfgk(range,city,year)"
          range-separator="-"
          start-placeholder="开始月份"
          value-format="yyyy-MM"
          end-placeholder="结束月份"
          :append-to-body='false'>
        </el-date-picker>
      </div>
      <div class="hfgk-container-indexs">
        <div class="hfgk-container-index" v-for='(item,i) in indexList' :key='i'>
          <div class='hfgk-container-index-inner-img' :style="{background: 'url(' + item.img + ') no-repeat'}"></div>
          <div class="hfgk-container-index-inner" @click="i == 0 || i == 1?showDialog(item.name):false">
            <div class="hfgk-container-index-inner-name">{{item.name}}</div>
            <div class="hfgk-container-index-inner-value">{{ item.value }} <span class='unit'>{{item.unit}}</span></div>
          </div>
        </div>
      </div>
    </div>

    <hfgkDialog :dialog-title='name' :visible='visible' @close='visible = false' @openAjListDialog='showAjListDialog'></hfgkDialog>
    <ajListDialog :dialog-title='name2' :type='type' :nationRegionName='nationRegionName' :followStatus='followStatus' :visible='visible2' @close='visible2 = false' @showAjDetail='showAjDetail' @showAjDetailTable='showAjDetailTable'></ajListDialog>
    <caseDetailDialog :case-no='caseNo' :visible='showAjDetailDialog' @close='showAjDetailDialog = false'></caseDetailDialog>
    <caseDetailTableDialog :case-no='caseNo' :visible='showAjDetailTableDialog' @close='showAjDetailTableDialog = false'></caseDetailTableDialog>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import hfgkDialog from './hfgkDialog'
import ajListDialog from './ajListDialog'
import caseDetailDialog from './caseDetailDialog'
import caseDetailTableDialog from './caseDetailTableDialog'
import moment from 'moment'
import { getHfgkList } from '@/api/ajhf'
import { registerCityYearChangeEvents } from '@/utils'
export default {
  name: 'index',
  components: {
    CommonTitle,
    hfgkDialog,
    ajListDialog,
    caseDetailDialog,
    caseDetailTableDialog
  },
  data() {
    return {
      year: localStorage.getItem("year"),
      city: localStorage.getItem("city"),
      value1: [
        new Date().getFullYear() + "-01-01",
        moment(new Date()).format("YYYY-MM-DD"),
      ],
      currentHfgkData: {},
      indexList: [
        {
          name:'案件总数',
          value:'',
          unit:'件',
          img: require('@/assets/ajhf/ajzs.png')
        },
        {
          name:'回访总数',
          value:'',
          unit:'件',
          img: require('@/assets/ajhf/fhzs.png')
        },
        {
          name:'回访比例',
          value:'',
          unit:'%',
          img: require('@/assets/ajhf/fhbl.png')
        },
        {
          name:'满意度',
          value:'',
          unit:'%',
          img: require('@/assets/ajhf/myd.png')
        }
      ],

      name:"",
      visible: false,

      name2:"",
      type:"",
      nationRegionName: "",
      followStatus:"",
      visible2: false,
      caseNo:"",
      showAjDetailDialog: false,
      showAjDetailTableDialog: false
    }
  },
  computed: {},
  mounted() {
    registerCityYearChangeEvents(this,this.initApi)
  },
  methods: {
    initApi(city,year) {
      this.getHfgk(this.value1, city);
    },
    //获取回访概况信息
    getHfgk(range, city, year) {
      getHfgkList({
        xsq: city,
        startTime: range[0],
        endTime: range[1],
      }).then(res => {
        if (res.code == 200) {
          this.indexList = [
            {
              name:'案件总数',
              value:res.data.ajzs,
              unit:'件',
              img: require('@/assets/ajhf/ajzs.png')
            },
            {
              name:'回访总数',
              value:res.data.fhzs,
              unit:'件',
              img: require('@/assets/ajhf/fhzs.png')
            },
            {
              name:'回访比例',
              value:res.data.fhbl,
              unit:'%',
              img: require('@/assets/ajhf/fhbl.png')
            },
            {
              name:'满意度',
              value:res.data.myd,
              unit:'%',
              img: require('@/assets/ajhf/myd.png')
            }
          ]
        }
      })
    },
    showDialog(name) {
      this.name = name;
      this.visible =true
    },
    showAjListDialog(res) {
      this.name2 = res.dialogTitle
      this.type = res.type
      this.nationRegionName = res.nationRegionName
      this.followStatus = res.followStatus
      this.visible2 = true
    },
    showAjDetail(res) {
      this.caseNo = res
      this.showAjDetailDialog = true
    },
    showAjDetailTable(res) {
      this.caseNo = res
      this.showAjDetailTableDialog = true
    }
  },
  watch: {}
}
</script>

<style scoped lang='less'>
.hfgk-container {
  width: 100%;
  height: 488px;
  margin-bottom: 49px;
  /deep/ .yearChange {
    .el-input__inner {
      height: 48px !important;
      background-color: #132c4e !important;
      border: 2px solid #afdcfb !important;
      color: #fff !important;
      border-radius: 15px !important;
    }
  }
}

.hfgk-container-tabs,
.hfjd-container-tabs {
  width: 100%;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  margin-top: 27px;
}

.hfgk-container-tab,
.hfjd-container-tab {
  width: 210.3px;
  height: 59px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  font-size: 36px;
  color: #ABCEEF;
  font-style: italic;
  text-align: center;
  cursor: pointer;
}

.hfgkContainerActiveTab {
  width: 210.3px;
  height: 59px;
  font-family: Source Han Sans CN;
  font-weight: bold;
  font-size: 36px;
  color: #DAEDFF;
  font-style: italic;
  background: url('@/assets/ajhf/activeBg.png') no-repeat;
  background-size: cover;
  text-align: center;
}

.hfgk-container-indexs {
  width: 100%;
  display: flex;
  justify-content: space-evenly;
  flex-wrap: wrap;
  align-items: center;
  margin-top: 40px;
}

.hfgk-container-index {
  width: 450px;
  height: 201px;
  background: rgba(0,58,144,0.3);
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 50px;
}

.hfgk-container-index-inner-img {
  width: 134px;
  height: 134px;
  background-size: 100% 100% !important;
  margin-left: 40px;
}

.hfgk-container-index-inner {
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  align-items: flex-start;
  margin-left: 40px;
}

.hfgk-container-index-inner-name {
  font-family: Source Han Sans CN;
  font-weight: 400;
  font-size: 36px;
  color: #D1D6DF;
  line-height: 64px;
  //font-style: italic;
  background: linear-gradient(0deg, #ACDDFF 0%, #FFFFFF 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.hfgk-container-index-inner-value {
  font-family: DINCond-Bold;
  font-weight: bold;
  font-size: 60px;
  //font-style: italic;
  background: linear-gradient(to bottom, #ffeccb, #ffffff, #ffc460, #ffe2b0, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
}

.unit {
  font-size: 32px;
}

.hfgk-container-indexs-ballLine {
  width: 100%;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
}

.hfgk-container-indexs-ball {
  width: 464px;
  height: 470px;
  background: url("@/assets/ajhf/ball.png") no-repeat;
  background-size: cover;
  text-align: center;
}

.hfgk-container-indexs-ball-percent {
  font-family: DINCond-Bold;
  font-weight: 400;
  font-size: 80px;
  color: #FFFFFF;
  text-shadow: 0px 2px 8px rgba(5, 28, 55, 0.42);
  background: linear-gradient(0deg, rgba(14, 197, 236, 1) 0%, rgba(49, 190, 255, 1) 0%, rgba(239, 252, 254, 1) 58.7646484375%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-top: 163px;
}

.hfgk-container-indexs-ball-text {
  font-family: Microsoft YaHei;
  font-weight: 400;
  font-size: 32px;
  color: #FEFFFF;
  margin-top: 15px;
}
</style>