<template>
  <div class="container">
    <el-row v-loading="loading" style="margin-right: 10px;">
      <el-form ref="elForm" :model="form" size="medium" label-width="120px">
        <el-col :span="24">
          <h3 class="title">
            <span>当事人信息</span>
          </h3>
        </el-col>
        <el-col :span="12">
          <el-form-item label="当事人" prop="party">
            <span>{{ form.party }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="phone">
            <span>{{ form.phone }}</span>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="24">
          <el-form-item label="身份证" prop="identityCard">
            <span>{{ form.identityCard }}</span>
          </el-form-item>
        </el-col> -->
        <el-col :span="12">
          <el-form-item label="车辆车牌" prop="carNo">
            <span>{{ form.carNo }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="车辆品牌" prop="models">
            <span>{{ form.models }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="从业资格证号" prop="certificateNo">
            <span>{{ form.certificateNo }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="道路运输证号" prop="transportCertificate">
            <span>{{ form.transportCertificate }}</span>
          </el-form-item>
        </el-col>
        <!-- 违规信息 -->

        <el-col :span="24">
          <h3 class="title">违规信息</h3>
        </el-col>
        <el-col :span="24">
          <el-form-item label="违规内容" prop="content">
            <span>{{ form.content }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="违规人员" prop="userName">
            <span>{{ form.userName }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="违规时间" prop="inspectionTime">
            <span>{{ form.inspectionTime }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="车辆类型" prop="carType">
            <span>{{ form.carTypeName }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="违规类型" prop="inspectionType">
            <span>{{ form.inspectionTypeName }}</span>
          </el-form-item>
        </el-col>
        <el-col v-if="isShowFTaxiType" :span="12">
          <el-form-item label="网络预约出租汽车驾驶员证" prop="onlineCarDriver">
            <span>{{ form.onlineCarDriver | problemName }}</span>
          </el-form-item>
        </el-col>
        <el-col v-if="isShowSTaxiType" :span="12">
          <el-form-item label="网络预约出租汽车运输证类型" prop="onlineCarTarnsport">
            <span>{{ form.onlineCarTarnsport | problemName }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="地址" prop="address">
            <span>{{ form.address }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="经度" prop="longitude">
            <span>{{ form.longitude }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="纬度" prop="latitude">
            <span>{{ form.latitude }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="上传图片" prop="files">
            <el-image v-for="(src,idx) in formFiles" :key="idx" style="width: 200px; height: 200px;" fit="cover" :src="src" @load="() => handleLoadImg()" />
          </el-form-item>
        </el-col>
        <!-- 额外文件 -->
        <el-col v-for="(item, idx) in exFile" :key="idx" :span="24" :style="(idx == exFile.length - 1 ? 'border-botom: 1px solid #ccc;' : '')">
          <el-form-item :label="item.label" prop="files">
            <el-image v-for="(src,index) in item.fileList" :key="index" style="width: 200px; height: 200px;" fit="cover" :src="src" @load="() => handleLoadImg()" />
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
  </div>
</template>

<script>
import { getTransport } from '@/api/case/pipe/dayCheck'
import { getFiles } from '@/api/supervise/swit'

export default {
  problemName(type) {
    const names = { 1: '有', 0: '无' }
    return names[type]
  },
  data() {
    return {
      loading: false,
      form: {},
      formFiles: [],
      exFile: [],
      fileIdx: 0,
      exFileLength: 0,
      isShowFTaxiType: false,
      isShowSTaxiType: false
    }
  },
  mounted() {
    const params = this.$route.query
    if (params.id) {
      this.loading = true
      Promise.all([
        getTransport(params.id),
        getFiles({businessId: params.id, tableName: 'case_transport'})
      ])
        .then(resAry => {
          const [formData, fileData] = resAry
          this.form = formData.data
          this.setFileType(this.form.carType)
          if (this.form.carType == 4 && this.form.inspectionType == 1) {
            this.isShowFTaxiType = true
          } else if (this.form.carType == 4 && this.form.inspectionType == 2) {
            this.isShowSTaxiType = true
          }
          fileData.rows.map(file => {
            const url =  `/zqzfj${file.filePath}`
            if (file.status == 1) {
              this.formFiles.push(url)
            } else {
              this.exFileLength++
              this.exFile.forEach(item => {
                if (item.data.status == file.status) {
                  item.fileList.push(url)
                }
              })
            }
          })
          this.loading = false
        }).catch(() => {
          this.loading = false
        })
    }
  },
  methods: {
    setFileType(type) {
      if (type == 4) {
        /* 网约车，修改上传文件类型 */
        this.exFile = [
          { key: 2, label: '现场笔录', data: { tableName: 'case_transport', status: 2 }, fileList: [] },
          { key: 3, label: '乘客询问笔录', data: { tableName: 'case_transport', status: 3 }, fileList: [] },
          { key: 4, label: '驾驶员询问笔录', data: { tableName: 'case_transport', status: 4 }, fileList: [] },
          { key: 5, label: '调查通知书', data: { tableName: 'case_transport', status: 5 }, fileList: [] },
          { key: 8, label: '查封通知书', data: { tableName: 'case_transport', status: 8 }, fileList: [] },
          { key: 7, label: '图片证据', data: { tableName: 'case_transport', status: 7 }, fileList: [] }
        ]
      } else if (type == 5) {
        /* 黑车，修改上传文件类型 */
        this.exFile = [
          { key: 2, label: '现场笔录', data: { tableName: 'case_transport', status: 2 }, fileList: [] },
          { key: 3, label: '乘客询问笔录', data: { tableName: 'case_transport', status: 3 }, fileList: [] },
          { key: 4, label: '驾驶员询问笔录', data: { tableName: 'case_transport', status: 4 }, fileList: [] },
          { key: 5, label: '调查通知书', data: { tableName: 'case_transport', status: 5 }, fileList: [] },
          { key: 9, label: '行政强制措施现场笔录', data: { tableName: 'case_transport', status: 9 }, fileList: [] },
          { key: 10, label: '扣押决定书', data: { tableName: 'case_transport', status: 10 }, fileList: [] },
          { key: 7, label: '图片证据', data: { tableName: 'case_transport', status: 7 }, fileList: [] }
        ]
      } else {
        /* 其余为默认 */
        this.exFile = [
          { key: 2, label: '现场笔录', data: { tableName: 'case_transport', status: 2 }, fileList: [] },
          { key: 3, label: '乘客询问笔录', data: { tableName: 'case_transport', status: 3 }, fileList: [] },
          { key: 4, label: '驾驶员询问笔录', data: { tableName: 'case_transport', status: 4 }, fileList: [] },
          { key: 5, label: '调查通知书', data: { tableName: 'case_transport', status: 5 }, fileList: [] },
          { key: 6, label: '先行登记保存证据通知书', data: { tableName: 'case_transport', status: 6 }, fileList: [] },
          { key: 7, label: '图片证据', data: { tableName: 'case_transport', status: 7 }, fileList: [] }
        ]
      }
    },
    policeTypeDataName(type) {
      const res = this.policeTypeData.find(item => type == item.dictValue)
      if (res) {
        return res.dictLabel
      } else {
        return ''
      }
    },
    policeCategoryDataName(type) {
      const res = this.policeCategoryData.find(item => type == item.dictValue)
      if (res) {
        return res.dictLabel
      } else {
        return ''
      }
    },
    handleLoadImg() {
      this.fileIdx++
      console.log(this.fileIdx)
      if (this.fileIdx == this.formFiles.length + this.exFileLength) {
        this.pagePrint()
      }
    },
    pagePrint() {
      this.$nextTick(() => {
        window.print()
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .container {
    .title {
      padding-left: 10px;
    }
    ::v-deep {
      .el-form-item {
        margin-bottom: 0;
      }
      .el-row {
        border-right: 1px solid #ccc;
      }
      [class*=el-col-] {
        border-top: 1px solid #ccc;
        border-left: 1px solid #ccc;
      }
      .el-form-item--medium .el-form-item__label {
        padding-left: 10px;
      }
      .el-form-item--medium .el-form-item__content {
        padding-left: 10px;
        border-left: 1px solid #ccc;
      }
    }
  }
</style>
