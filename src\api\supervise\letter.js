import {request} from '@/utils/request'

// 查询信访记录列表
export function listLetter(query) {
  return request({
    url: '/business/supervision/letter/list',
    method: 'get',
    params: query
  })
}

// 查询信访记录详细
export function getLetter(id) {
  return request({
    url: '/business/supervision/letter/' + id,
    method: 'get'
  })
}

// 新增信访记录
export function addLetter(data) {
  return request({
    url: '/business/supervision/letter/add',
    method: 'post',
    data: data
  })
}

// 修改信访记录
export function updateLetter(data) {
  return request({
    url: '/business/supervision/letter/edit',
    method: 'post',
    data: data
  })
}

// 删除信访记录
export function delLetter(id) {
  return request({
    url: '/business/supervision/letter/remove/' + id,
    method: 'post'
  })
}

// 导出信访记录
export function exportLetter(query) {
  return request({
    url: '/business/supervision/letter/export',
    method: 'get',
    params: query
  })
}
