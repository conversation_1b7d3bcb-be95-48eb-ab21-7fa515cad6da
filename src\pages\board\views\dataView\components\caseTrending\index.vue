<template>
  <div ref="chart" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.8)" class="line" />
</template>

<script>
import { getCaseTrending } from '@/api/board/dataView/index'

export default {
  data() {
    return {
      loading: false,
      myChart: null,
      firstData: [],
      secondData: [],
      thirdData: []
    }
  },
  computed: {
    options() {
      return {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          show: false,
          top: 10,
          left: 40,
          bottom: 20,
          right: 85

        },
        legend: {
          data: ['综合执法', '运管执法', '交警执法'],
          textStyle: {
            color: '#fff',
            fontSize: this.getChangePx(14)
          },
          orient: 'vertical',  // 垂直显示
          y: 'center',    // 延Y轴居中
          x: 'right', // 居右显示
          align: 'left'
        },
        xAxis: [
          {
            type: 'category',
            axisTick: { show: false },
            axisLabel: {
              textStyle: {
                color: '#fff',
                fontSize: this.getChangePx(14)
              }
            },
            data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
          }
        ],
        yAxis: [
          {
            type: 'value',
            axisLabel: {
              textStyle: {
                color: '#fff',
                fontSize: this.getChangePx(12)
              }
            },
            splitLine: {
              lineStyle: {
                opacity: 0.1
              }
            }
          }
        ],
        series: [
          {
            name: '综合执法',
            type: 'bar',
            barGap: 0,
            // label: {
            //   show: true,
            //   position: 'insideBottom',
            //   rotate: 90,
            //   align: 'left',
            //   verticalAlign: 'middle',
            //   formatter: '{c}',
            //   fontSize: 12,
            //   // distance: 15,
            //   color: '#FFFFFF',
            //   textBorderColor: 'yerrow',
            //   textBorderWidth: 2
            // },
            emphasis: {
              focus: 'series'
            },
            data: this.firstData
          },
          {
            name: '运管执法',
            type: 'bar',
            emphasis: {
              focus: 'series'
            },
            data: this.secondData
          },
          {
            name: '交警执法',
            type: 'bar',
            emphasis: {
              focus: 'series'
            },
            data: this.thirdData
          }
        ]
      }
    }
  },
  mounted() {
    this.initChart()
    window.addEventListener('resize', this.resizeChart)
    this.fetchData()
  },
  destroyed() {
    window.removeEventListener('resize', this.resizeChart)
  },
  methods: {
    getChangePx(px) {
      const fontSize = parseFloat(window.document.documentElement.style.fontSize)
      return parseFloat((px / 192 * fontSize).toFixed(1))
    },
    fetchData() {
      this.loading = false
      getCaseTrending().then(res => {
        this.loading = false
        this.firstData = res.data.firstData
        this.secondData = res.data.secondData
        this.thirdData = res.data.thirdData
        this.myChart.setOption(this.options)
      }).catch(() => {
        this.loading = false
      })
    },
    initChart() {
      this.myChart = this.$echarts.init(this.$refs.chart)
      this.myChart.setOption(this.options)
    },
    resizeChart() {
      setTimeout(() => {
        this.myChart.resize()
      }, 500)
    }
  }
}
</script>

<style scoped>
.line {
  width: 100%;
  height: 100%;
}
</style>
