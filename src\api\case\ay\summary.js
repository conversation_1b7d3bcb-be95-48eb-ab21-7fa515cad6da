import {request} from '@/utils/request'

// 查询案由列表
export function listSummary(query) {
  return request({
    url: '/business/summary/list',
    method: 'get',
    params: query
  })
}

// 查询案由详细
export function getSummary(summaryId) {
  return request({
    url: '/business/summary/' + summaryId,
    method: 'get'
  })
}

// 新增案由
export function addSummary(data) {
  return request({
    url: '/business/summary/add',
    method: 'post',
    data: data
  })
}

// 修改案由
export function updateSummary(data) {
  return request({
    url: '/business/summary/edit',
    method: 'post',
    data: data
  })
}

// 删除案由
export function delSummary(summaryId) {
  return request({
    url: '/business/summary/remove/' + summaryId,
    method: 'post'
  })
}

// 导出案由
export function exportSummary(query) {
  return request({
    url: '/business/summary/export',
    method: 'get',
    params: query
  })
}

// 查询案由列表
export function treeselect(params) {
  return request({
    url: '/business/summary/treeselect',
    method: 'get',
    params
  })
}
