<template>
  <el-dialog :close-on-click-modal="false" v-bind="$attrs" title="申诉列表" width="1000px" v-on="$listeners" @close="onClose">
    <el-table
      v-loading="loading"
      class="m-table"
      :data="listData"
      row-key="checkStandardId"
      default-expand-all
      height="500px"
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      @row-click="handleCurrentChange"
    >
      <el-table-column label="申诉时间" align="center" prop="happenTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.happenTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申诉人" align="center" prop="appealUserName" />
      <el-table-column label="申诉原因" align="center" prop="reason" />
      <el-table-column label="申诉结果" align="center" prop="result" />
      <el-table-column prop="status" label="申诉状态" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.status | statusName }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 按钮 -->
    <div slot="footer" class="dialog-footer">
      <el-button :loading="loading" @click="close">取 消 </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getAppealList } from '@/api/supervise/check'
import { debounce } from '@/utils/index'
export default {
  name: 'AppealList',
  filters: {
    statusName(type) {
      const name = { 1: '申诉中', 9: '通过', 10: '驳回' }
      return name[type] || '申诉中'
    }
  },
  props: {
    checkRecordId: {
      type: [Number, String],
      require: true
    }
  },
  data() {
    return {
      loading: false,
      listData: [],
      checkRow: {}
    }
  },
  watch: {
    '$attrs.visible'(nVal) {
      if (nVal) {
        this.getList()
      }
    }
  },
  methods: {
    onClose() {
      this.checkRow = {}
    },
    getList() {
      this.loading = true
      getAppealList({checkRecordId: this.checkRecordId}).then(response => {
        this.listData = response.rows
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    handleCurrentChange(row) {
      console.log(row)
      debounce(() => {
        console.log(row)
        if (row.status == 1) {
          this.checkRow = row
          const listData = this.listData.map(item => {
            return { ...item, checked: item.checkStandardId === row.checkStandardId }
          })
          this.standardList = this.handleTree(listData, 'checkStandardId')
        }
      }, 100, false)()
    },
    close() {
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style scoped lang="scss">
.m-table {
  ::v-deep {
    .tb-cell {
      // width: 150;
      display: inline-flex;
      &-text {
        margin-left: 10px;
        width: 160px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}

</style>
