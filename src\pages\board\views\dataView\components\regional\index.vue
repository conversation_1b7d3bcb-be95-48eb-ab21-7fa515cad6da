<template>
  <div class="m-table">
    <div class="m-header">
      <span>上报人</span>
      <span>网格小组</span>
      <span>上报时间</span>
      <span>内容</span>
    </div>
    <!-- 主体内容 -->
    <div v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.8)" class="m-body" @click="clickItem">
      <vueSeamlessScroll :data="tableList" :class-option="defaultOption">
        <div v-for="item in tableList" :key="item.id" class="m-tr" :data-id="item.inspectionId" :data-name="item.userName">
          <span>{{ item.userName }}</span>
          <span>{{ item.deptName }}</span>
          <span>{{ item.happenDate }}</span>
          <span>{{ item.inspectionContent }}</span>
        </div>
        <div v-if="!tableList.length" class="m-tr">
          暂无数据
        </div>
      </vueSeamlessScroll>
    </div>

    <!-- 详情弹窗 -->
    <!-- <from-list :visible.sync="open" :title="title" :detail-id="detailId" :form-disabled="formDisabled" /> -->
    <!-- 添加或修改角色配置对话框 -->
    <el-dialog :close-on-click-modal="false" title="详情" :visible.sync="open" width="1000px">
      <fromList v-if="open" ref="fromlist" :detail-id="detailId" :form-disabled="formDisabled" :user-options="userOptions" :qd="qd" :is-board="true" @oncancel="open = false" />
    </el-dialog>
  </div>
</template>

<script>
import { getDynamicList } from '@/api/board/dataView/index'
import vueSeamlessScroll from 'vue-seamless-scroll'
import fromList from '@/pages/case/views/synthetical/xcfxrcxc/components/fromlist'
import {userList} from '@/api/supervise/swit'

export default {
  components: {
    vueSeamlessScroll,
    fromList
  },
  data() {
    return {
      tableList: [],
      loading: false,
      // 是否显示弹出层
      open: false,
      title: '',
      // 是否可编辑表单
      formDisabled: true,
      // 详情ID
      detailId: null,
      userOptions: [],
      qd: true
    }
  },
  computed: {
    defaultOption() {
      return {
        step: 0.2, // 数值越大速度滚动越快
        limitMoveNum: 10, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 1000 // 单步运动停止的时间(默认值1000ms)
      }
    }
  },
  mounted() {
    this.getuserList()
    this.fetchData()
  },
  methods: {
    getuserList() {
      userList().then(res => {
        this.userOptions = res.data
      })
    },
    fetchData() {
      this.loading = true
      // const searchTime = this.parseTime(new Date())
      getDynamicList({pageSize: 20, pageNum: 1, status: 9, type: 0}).then(res => {
        this.tableList = res.rows
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    // 点击弹窗
    clickItem(e) {
      const {id, name} = e.target.parentNode.dataset
      if (id) {
        this.title = `${name}的工作动态`
        this.detailId = parseInt(id)
        this.open = true
      }
    }
  }
}
</script>

<style scoped lang="scss">
.m-table {
  height: 100%;
  padding-top: pxtorem(10);
  .m-header {
    height: pxtorem(28);
    line-height: pxtorem(28);
    background: url(@/assets/images/m-header.png) no-repeat center center / 100% 100%;
    font-size: pxtorem(14);
    display: flex;
    padding: 0 10px 0 10px;
    span {
      text-align: center;
      flex: 1;
    }
  }
  .m-body {
    height: calc(100% - 0.14583rem);
    overflow: hidden;
    .m-tr {
      overflow: hidden;
      height: pxtorem(26);
      line-height: pxtorem(26);
      font-size: pxtorem(12);
      color: #00f7ff;
      background: rgba(0, 0, 0, 0.3);
      padding: 0 10px;
      margin-top: 5px;
      text-align: center;
      cursor: pointer;
      span {
        width: 25%;
        height: pxtorem(26);
        float: left;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
  }
}
</style>
