<template>
  <div>
    <CommonTitle text="在线设备列表"></CommonTitle>
    <div class="selectCon flex-c">
      <div class="selectItem flex-c">
        <div class="label">名称</div>
        <el-input v-model="name" placeholder="请输入" @keyup.enter.native="getList" />
      </div>
      <div class="selectItem flex-c">
        <div class="label">状态</div>
        <el-select v-model="status" placeholder="请选择">
          <el-option v-for="(item, i) in statusOptions" :label="item.name" :value="item.value" :key="i"></el-option>
        </el-select>
      </div>
      <div class="selectItem flex-c">
        <div class="label">所属区域</div>
        <el-select v-model="area" placeholder="请选择">
          <el-option v-for="(item, i) in areaOptions" :label="item.name" :value="item.name" :key="i"></el-option>
        </el-select>
      </div>
      <div class="selectItem flex-c" style="justify-content: flex-end">
        <el-button type="primary" class="btn" @click="getData">查询</el-button>
      </div>
    </div>
    <div class="wrap-container">
      <TableComponent :thConfig="thConfig" :tableData="tableData" :tableHeight="460"></TableComponent>
    </div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import TableComponent from '@/components/TableComponent'
import { getOnlineDevices } from '@/api/wlwsb/index.js'
export default {
  name: 'index',
  components: {
    CommonTitle,
    TableComponent,
  },
  data() {
    return {
      name: '',
      status: '',
      area: '',
      statusOptions: [
        { name: '在线', value: '1' },
        { name: '离线', value: '0' },
      ],
      areaOptions: [
        { name: '金华市', value: '' },
        { name: '市本级', value: '市本级' },
        { name: '婺城区', value: '婺城区' },
        { name: '金东区', value: '金东区' },
        { name: '兰溪市', value: '兰溪市' },
        { name: '东阳市', value: '东阳市' },
        { name: '义乌市', value: '义乌市' },
        { name: '永康市', value: '永康市' },
        { name: '浦江县', value: '浦江县' },
        { name: '武义县', value: '武义县' },
        { name: '磐安县', value: '磐安县' },
        { name: '开发区', value: '开发区' },
      ],

      //表格
      tableData: [],
      thConfig: [
        {
          th: '设备名称',
          field: 'deviceName',
          width: '40%',
          hover: false,
        },
        {
          th: '类型',
          field: 'typeName',
          width: '30%',
          hover: true,
        },
        // {
        //   th: '状态',
        //   field: 'status',
        //   width: '14%',
        //   hover: true,
        // },
        {
          th: '所属区域',
          field: 'administrativeDivisionName',
          width: '30%',
          hover: false,
        },
        // {
        //   th: '最后活跃时间',
        //   field: 'lastConnectionTime',
        //   width: '30%',
        //   hover: false,
        // },
      ],
    }
  },
  computed: {},
  mounted() {
    this.getData()
  },
  methods: {
    getData() {
      getOnlineDevices({area:this.area,status:this.status,deviceName:this.name}).then((res) => {
        if (res.data) {
          this.tableData=res.data
        }
      })
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
.selectCon {
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 20px 20px 20px 40px;
  box-sizing: border-box;
  .selectItem {
    margin-right: 20px;
    margin-bottom: 30px;
    &:nth-child(2n + 2) {
      margin-right: 0;
    }
    .label {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 28px;
      color: #ffffff;
      line-height: 32px;
      margin-right: 30px;
      width: 100px;
      white-space: nowrap;
      text-align: right;
    }
    .btn {
      width: 335px;
      height: 60px;
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 28px;
      color: #cfd7e5;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
.wrap-container {
  width: 100%;
  // height:600px;
  padding: 0 20px;
  box-sizing: border-box;
}

::v-deep .el-input,
::v-deep .el-select {
  width: 335px;
  .el-input__inner {
    background: #132c4ecc;
    border: 1px solid #afdcfb;
    color: #cfd7e5;
    font-size: 28px;
    height: 60px;
    line-height: 50px;
    border-radius: 4px;
    transition: all 0.3s;

    &:hover,
    &:focus {
      border-color: #00d4ff;
      box-shadow: 0 0 10px rgba(0, 212, 255, 0.2);
    }

    &::placeholder {
      color: #cfd7e5;
    }
  }
}
.flex-c {
  display: flex;
  align-items: center;
}
</style>