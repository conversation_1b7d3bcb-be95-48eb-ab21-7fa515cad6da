<template>
  <div>
    <CommonTitle text='接种医院排行'>
      <TabSwitch
        :tabList="list"
        :activeIndex="index"
        @tab-change="handleTabChange"
      />
    </CommonTitle>
    <div class='wrap-container' id='chartsjzyyph'></div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import TabSwitch from '@/components/TabSwitch'
export default {
  name: 'index',
  components: {
    CommonTitle,
    TabSwitch
  },
  data() {
    return {
      index: 0,
      list: [{name:"本月",value:"1"},{name:"本季",value:"2"},{name:"本年",value:"3"}],
      chartsData: [
        {
          name:"嘉雯宠物医院",
          value: 6000
        },
        {
          name:"唯一宠物",
          value: 5800
        },
        {
          name:"诺伊宠物",
          value: 5500
        },
        {
          name:"新宠宠物",
          value: 5000
        },
        {
          name:"不二宠物",
          value: 5000
        }
      ]
    }
  },
  computed: {},
  mounted() {
    setTimeout(() => {
      this.initCharts()
    }, 300)
  },
  methods: {
    handleTabChange(i) {
      this.index = i
    },
    initCharts() {
      let myChart = this.$echarts.init(document.getElementById("chartsjzyyph"));
      let option = {
        tooltip: {
          trigger: "axis",
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
          formatter: function (params) {
            return params.find(item => item.seriesName != "背景").axisValue + ": " + params.find(item => item
              .seriesName != "背景").value + "只";
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '32',
          },
        },
        grid: {
          top: "8%",
          left: "5%",
          right: "5%",
          bottom: "-10%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          show: false,
        },
        yAxis: {
          name: "",
          type: "category",
          triggerEvent: false,
          inverse: true,
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
            length: 20,
          },
          axisLabel: {
            show: true,
            inside: true,    // 将标签放置在坐标轴内部
            verticalAlign: 'bottom',  // 垂直对齐方式
            padding: [0, 0, 20, 0],   // 上右下左的内边距，调整标签位置
            textStyle: {
              color: "#FFFFFF",
              fontSize: 32,
            },
          },
          data: this.chartsData.map(item => item.name),
        },
        series: [{
          type: "bar",
          name: "",
          showBackground: true,
          backgroundStyle: {
            color: 'transparent'
          },
          itemStyle: {
            normal: {
              barBorderRadius: 30,
              color: new this.$echarts.graphic.LinearGradient(0, 0, 1, 1, [{
                offset: 0,
                color: "rgba(60,253,255,0.2)",
              },
                {
                  offset: 1,
                  color: "#3CFDFF",
                },
              ]),
            }
          },
          label: {
            show: true,
            position: [640, -46],
            color: "#fff",
            formatter: function (params) {
              return "疫苗接种数 " + params.value;
            },
            fontSize: 32,
            textStyle: {
              color: '#FFFFFF',
              fontWeight: 'normal',
              fontFamily: 'Source Han Sans CN'
            }
          },
          barWidth: 20,
          color: "#539FF7",
          data: this.chartsData.map(item => item.value),
        }, {
          name: "背景",
          type: "bar",
          barWidth: 20,
          barGap: "-100%",
          data: this.chartsData.map(item => 7000),
          itemStyle: {
            normal: {
              barBorderRadius: 30,
              color: "#094471",
            },
          },
          z: 0,
        }]
      }
      myChart.setOption(option)
    },
  },
  watch: {}
}
</script>

<style scoped lang='less'>
  .wrap-container {
    width: 100%;
    height: 460px;
    margin-bottom: 40px;
  }
</style>