<template>
  <div class="container">
    <data-header />
    <div class="container-inner">
      <div class="container-left">
        <div class="left-top">
          <div class="title">
            <span class="t-top">区域动态</span>
            <span class="t-bottom">ESSENTIAL INFORMATION</span>
          </div>
          <div class="content">
            <!-- <realTimeCase /> -->
            <regional ref="regional" />
          </div>
        </div>
        <div class="left-bottom">
          <div class="title">
            <span class="t-top">现场执勤人员</span>
            <span class="t-bottom">ESSENTIAL INFORMATION</span>
          </div>
          <div class="content">
            <!-- <clockOn /> -->
            <localeUsers ref="localeUsers" />
          </div>
        </div>
      </div>
      <div class="container-right">
        <div class="right-top">
          <div class="top">
            <div class="box">
              <div class="title">
                <span class="t-top">案件统计</span>
                <span class="t-bottom">ESSENTIAL INFORMATION</span>
              </div>
              <div class="content">
                <!-- <caseType /> -->
                <caseStatistics ref="caseStatistics" />
              </div>
            </div>
            <div class="box">
              <div class="title">
                <span class="t-top">资源整合</span>
                <span class="t-bottom">ESSENTIAL INFORMATION</span>
              </div>
              <div class="content">
                <!-- <taix /> -->
                <resources />
              </div>
            </div>
          </div>
          <div class="bottom">
            <div class="box">
              <div class="title">
                <span class="t-top">事件列表</span>
                <span class="t-bottom">ESSENTIAL INFORMATION</span>
              </div>
              <div class="content">
                <event ref="event" />
                <!-- <device /> -->
              </div>
            </div>
            <div class="box">
              <div class="title">
                <span class="t-top">568志愿者</span>
                <span class="t-bottom">ESSENTIAL INFORMATION</span>
              </div>
              <div class="content">
                <!-- <countVol /> -->
                <vol-statistics ref="volStatistics" />
              </div>
            </div>
          </div>
        </div>
        <div class="right-bottom">
          <div class="right-bottom-box clearfix">
            <div class="title">
              <span class="t-top">案件趋势</span>
              <span class="t-bottom">ESSENTIAL INFORMATION</span>
            </div>
            <div class="content">
              <caseTrending ref="caseTrending" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import dataHeader from '../components/dataHeader/index.vue'
import regional from './components/regional'
import localeUsers from './components/localeUsers'
import caseStatistics from './components/caseStatistics/index.vue'
import event from './components/event/index.vue'
import volStatistics from './components/volStatistics/index.vue'
// import taix from './components/taix/index.vue'
// import device from './components/device/index.vue'
// import clockOn from './components/clockOn/index.vue'
// import caseType from './components/caseType/index.vue'
import caseTrending from './components/caseTrending/index.vue'
import resources from './components/resources/index.vue'
// import countVol from './components/countVol/index.vue'

export default {
  components: {
    dataHeader,
    regional,
    localeUsers,
    caseStatistics,
    caseTrending,
    event,
    volStatistics,
    resources
  },
  data() {
    return {
      componList: [
        'regional',
        'localeUsers',
        'caseStatistics',
        'event',
        'volStatistics',
        'caseTrending'
      ]
    }
  },
  mounted() {
    this.setIntervalData()
  },
  methods: {
    setIntervalData() {
      this.componList.forEach(name => {
        setInterval(() => {
          if (this.$refs[name].fetchData) {
            this.$refs[name].fetchData()
          }
        }, 30000)
      })
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  height: 100%;
  color: #fff;
  background: #0e0d33;
  .title {
    height: pxtorem(66);
    padding-top: pxtorem(30);
    padding-left: pxtorem(56);
    background: url(@/assets/images/title-icon.png) no-repeat pxtorem(10) bottom / pxtorem(36);
    span {
      display: block;
    }
    .t-top {
      font-size: pxtorem(18);
      text-shadow: 0 0 6px #00f7ff;
    }
    .t-bottom {
      font-size: 12px;
      transform: scale(0.6);
      transform-origin: left;
    }
  }
  .content {
    height: calc(100% - 0.31771rem);
    padding: 0 pxtorem(40) pxtorem(45) pxtorem(15);
  }
  &-inner {
    height: calc(100% - 0.52083rem);
    display: flex;
    padding: pxtorem(40) pxtorem(30) pxtorem(30);
    .container-left {
      width: pxtorem(600);
      height: 100%;
      &-top {
        overflow: hidden;
        height: 48.5%;
        background: url(@/assets/images/left-box.png) no-repeat center top / 100% 100%;
        margin-bottom: 3%;
      }
      &-bottom {
        overflow: hidden;
        height: 48.5%;
        background: url(@/assets/images/left-box.png) no-repeat center top / 100% 100%;
      }
    }
    .container-right {
      flex: 1;
      height: 100%;
      margin-left: pxtorem(30);
      &-top {
        height: 60%;
        .top {
          height: 40.5%;
          margin-bottom: 1%;
        }
        .bottom {
          height: 58.5%;
        }
        .top,
        .bottom {
          display: flex;
          justify-content: space-between;
          .box {
            width: pxtorem(600);
            background: url(@/assets/images/right-m-box.png) no-repeat center top / 100% 100%;
          }
        }
      }
      &-bottom {
        height: 37.5%;
        padding-top: 2.5%;
        .right-bottom-box {
          // overflow: hidden;
          height: 100%;
          background: url(@/assets/images/right-b-box.png) no-repeat center top / 100% 100%;
        }
      }
    }
  }
}
</style>
