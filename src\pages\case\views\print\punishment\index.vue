<template>
  <div class="container">
    <el-form ref="form" v-loading="loading" label-position="left" :model="form" label-width="140px">
      <el-row>
        <el-col :span="24">
          <h3 class="title" style="position: relative;">
            <span>基本信息</span>
          </h3>
        </el-col>
        <el-col :span="24">
          <el-form-item label="处罚对象" prop="userType">
            <span>{{ form.userType | userTypeName }}</span>
          </el-form-item>
        </el-col>
        <div v-if="form.userType == 2">
          <el-col :span="12">
            <el-form-item label="是否个体工商户">
              <span>{{ form.isSelfEmployed | selfEmployedName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单位名称" prop="companyName">
              <span>{{ form.companyName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="统一社会信用代码" prop="companyCode">
              <span>{{ form.companyCode }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="单位地址" prop="companyAddress">
              <span>{{ form.companyAddress }}</span>
            </el-form-item>
          </el-col>
        </div>
        <el-col :span="12">
          <el-form-item label="姓名" prop="party">
            <span>{{ form.party }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="phone">
            <span>{{ form.phone }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="性别" prop="gender">
            <span>{{ form.gender | sexName }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="年龄" prop="age">
            <span>{{ form.age }}</span>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="24">
          <el-form-item label="身份证" prop="identityCard">
            <span>{{ form.identityCard }}</span>
          </el-form-item>
        </el-col> -->
        <el-col :span="24">
          <h3 class="title">案件信息</h3>
        </el-col>
        <el-col :span="24">
          <el-form-item label="案发时间" prop="caseTime">
            <span>{{ form.caseTime }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="案由名称" prop="summaryName">
            <span>{{ form.summaryName }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="案件地址" prop="address">
            <span>{{ form.address }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="立案简介" prop="caseContent">
            <span>{{ form.caseContent }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="图片" prop="files">
            <el-image v-for="(src,idx) in fileList" :key="idx" style="width: 200px; height: 200px;" fit="cover" :src="src" @load="() => handleLoadImg(idx)" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <h3 class="title">处罚信息</h3>
        </el-col>
        <el-col :span="24">
          <el-form-item label="处罚类型" prop="punishType">
            <span>{{ form.punishType | punishTypeName }}</span>
          </el-form-item>
        </el-col>
        <el-col v-if="form.punishType == 1" :span="24">
          <el-form-item label="处罚金额" prop="punishMoney">
            <span>{{ form.punishMoney }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row style="border-bottom: 1px solid #ccc;">
        <el-col :span="12">
          <el-form-item label="主办人" prop="userName">
            <span>{{ form.userName }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="协办人" prop="userIds">
            <span>{{ form.userNames }}</span>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import {punishmentOne} from '@/api/case/synthetical/punishment'
import { getFiles } from '@/api/supervise/swit'

export default {
  filters: {
    sexName(type) {
      const names = { 1: '男', 2: '女' }
      return names[type]
    },
    userTypeName(type) {
      const names = { 1: '公民', 2: '法人、其它组织' }
      return names[type]
    },
    selfEmployedName(type) {
      const names = { 1: '是', 2: '否' }
      return names[type]
    },
    punishTypeName(type) {
      const names = { 1: '警告', 0: '罚款' }
      return names[type]
    }
  },
  data() {
    return {
      loading: false,
      form: {},
      fileList: []
    }
  },
  mounted() {
    const params = this.$route.query
    if (params.id) {
      this.loading = true
      Promise.all([
        punishmentOne(params.id),
        getFiles({businessId: params.id, tableName: 'case_punishment'})
      ])
        .then(resAry => {
          const [formData, fileData] = resAry
          this.form = formData.data
          this.fileList = fileData.rows.map(file => {
            return `/zqzfj${file.filePath}`
          })
          this.loading = false
        }).catch(() => {
          this.loading = false
        })
    }
  },
  methods: {
    handleLoadImg(idx) {
      if (idx == this.fileList.length - 1) {
        this.pagePrint()
      }
    },
    pagePrint() {
      this.$nextTick(() => {
        window.print()
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .container {
    .title {
      padding-left: 10px;
    }
    ::v-deep {
      .el-form-item {
        margin-bottom: 0;
      }
      .el-row {
        border-right: 1px solid #ccc;
      }
      [class*=el-col-] {
        border-top: 1px solid #ccc;
        border-left: 1px solid #ccc;
      }
      .el-form-item--medium .el-form-item__label {
        padding-left: 10px;
      }
      .el-form-item--medium .el-form-item__content {
        padding-left: 10px;
        border-left: 1px solid #ccc;
      }
    }
  }
</style>
