<template>
  <ygfDialog :visible="visible" width="2034px">
    <div class="zhyxts-dialog">
      <div class="rw-title flex-between" style="margin-top: 60px">
        <div class="fs-44 titleText" style="margin-left: 20px">事件列表</div>
        <div class="close cursor" @click="close" style="margin-right: 20px"></div>
      </div>
      <div class="content">
        <div class="table">
          <div class="tableHead flex-c">
            <div class="td">事件编号</div>
            <div class="td" style="flex: 2">事件名称</div>
            <div class="td">事件类型</div>
            <div class="td">发生时间</div>
            <div class="td" style="flex: 2">事件地址</div>
            <div class="td" style="flex: 0.5">操作</div>
          </div>
          <div
            class="tableLine flex-c"
            v-for="(item, i) in dataList"
            :key="i"
            :class="{ tableLine_active: i % 2 == 1 }"
          >
            <div class="td">{{ item.eventNum }}</div>
            <div class="td" style="flex: 2">{{ item.eventName }}</div>
            <div class="td">{{ item.eventType }}</div>
            <div class="td">{{ item.eventTime }}</div>
            <div class="td" style="flex: 2">{{ item.eventAddress }}</div>
            <div class="td" style="color: #22e097; flex: 0.5; cursor: pointer">操作</div>
          </div>
        </div>
        <div class="pagi">
          <el-pagination
            background
            layout="total,prev, pager, next,jumper"
            :current-page.sync="params.pageNum"
            :total="total"
            :page-size="10"
            @current-change="changePage"
          ></el-pagination>
        </div>
      </div>
    </div>
  </ygfDialog>
</template>

<script>
import ygfDialog from '@/components/ygfDialog'
import { geteventList } from '@/api/yxjc/index.js'

export default {
  name: 'index',
  props: ['visible'],
  components: {
    ygfDialog,
  },
  data() {
    return {
      params: {
        pageNum: 1,
      },
      total: 0,
      dataList: [],
    }
  },
  computed: {},
  mounted() {
    this.getDetail()
  },
  methods: {
    getDetail() {
      geteventList(this.params).then((res) => {
        if (res.code == 200) {
          this.dataList = res.rows
          this.total=res.total
        }
      })
    },
    changePage(e) {
      this.params.pageNum = e
      this.getDetail()
    },
    close() {
      this.$emit('close')
    },
  },
  watch: {
    visible(val) {
      if (val) {
        // this.getDetail()
      }
    },
  },
}
</script>

<style scoped lang='less'>
::-webkit-scrollbar {
  display: none;
}

ul,
ul li {
  list-style: none;
}

.zhyxts-dialog {
  width: 2030px;
  height: 1320px;
  background: url('@/assets/yxjc/dialogBg.png') no-repeat;
  background-size: 100% 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-direction: column;
}

.rw-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 46px 3% 0;
  width: 95%;
  height: 60px;
  line-height: 60px;
}

.close {
  background: url('@/assets/zhdd/close.png') no-repeat;
  width: 34px;
  height: 34px;
}

.ql-indent-1 img {
  width: 100%;
}

img {
  width: 100%;
}

::-webkit-scrollbar {
  width: 0;
  height: 0;
}

.scrollTab {
  width: 928px;
  height: 70px;
}

.content {
  width: 100%;
  padding: 40px 60px 60px 60px;
  box-sizing: border-box;
}

.table {
  width: 100%;
  height: 900px;
  overflow: scroll;
  .td {
    padding: 20px 30px;
    box-sizing: border-box;
    flex: 1;
    text-align: left;
  }
  .tableHead {
    height: 80px;
    background-color: #0a6cff33;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 500;
    font-size: 32px;
    color: #cde7ff;
    text-align: left;
  }
  .tableLine {
    min-height: 80px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 500;
    font-size: 32px;
    color: #cde7ff;
    text-align: left;
  }
  .tableLine_active {
    background-color: #0a6cff33;
  }
}
.pagi {
  display: flex;
  justify-content: flex-end;
  margin-top: 40px;
}

.flex-c {
  display: flex;
  align-items: center;
}

/* 分页 */
/deep/ .el-pagination button {
  height: 60px;
  width: 40px;
  background: #5f7b96 !important;
  // background: transparent !important;
}

/deep/ .el-pagination button .el-icon {
  font-size: 32px !important;
  // color: #c1e2fa;
  color: #c1e2fa !important;
  font-weight: 400;
}

/deep/ ul li {
  border: 2px solid transparent;
  margin-left: 10px !important;
  // height: 60px;
  height: fit-content;
  padding: 10px 10px !important;
  box-sizing: border-box;
  font-size: 32px !important;
  color: #c1e2fa !important;
  // background: transparent !important;
  background: #5f7b96 !important;
  font-weight: 500;
  line-height: 36px !important;
  border-radius: 4px;
}

/deep/ li.active {
  // margin: 0;
  // padding: 0;
  color: #fff !important;
  /* border: 2px solid #035b86; */
  background-color: #0166a6 !important;
}
/deep/ .el-pagination__total {
  color: #fff;
  font-size: 32px !important;
  padding-top: 15px;
  padding-right: 20px;
}

/deep/ .el-pagination button,
/deep/ .el-pagination span:not([class*='suffix']) {
  font-size: 32px;
  color: #fff;
}

/deep/ .el-pagination__editor.el-input .el-input__inner {
  height: 60px;
  font-size: 32px;
  background-color: transparent;
  color: #fff;
  border-color: #6f788a;
}

/deep/ .el-pagination__editor.el-input {
  width: 100px;
  height: 60px;
  margin: 0 10px;
}
</style>