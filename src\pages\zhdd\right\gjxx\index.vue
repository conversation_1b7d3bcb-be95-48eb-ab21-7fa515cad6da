<template>
  <div style='margin-bottom: 40px;'>
    <CommonTitle text='告警信息'>
      <div class='yearChange'>
        <el-date-picker
          v-model="datas1"
          type="daterange"
          value-format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="queryData1"
          :append-to-body='false'
        >
        </el-date-picker>
      </div>
    </CommonTitle>
    <div class="table-bottom">
      <div class="sos_box">
        <img
          v-show="showSOSno"
          src="@/assets/zhdd/back.png"
          width="50px"
          style="margin-right: 40px"
          @click="showSOSno=false;gjxxData=sos.sosNOdata"
        />
        <img
          src="@/assets/zhdd/bell.png"
          width="50px"
          @click="showSOSno=true;gjxxData=sos.sosISdata"
        />
        <i class="icon_num" @click="showSOSno=true;gjxxData=sos.sosISdata">
          {{sos.sosISdata.length}}
        </i>
      </div>
      <div class="th">
        <div class="th_td" style="flex: 0.1; text-align: center">序号</div>
        <div class="th_td" style="flex: 0.35; text-align: center">
          报警发生时间
        </div>
        <div class="th_td" style="flex: 0.3; text-align: center">
          告警单位
        </div>
        <div class="th_td" style="flex: 0.15; text-align: center">队员</div>
        <div
          class="th_td"
          style="flex: 0.2; text-align: center"
          v-show="showSOSno">
          现场连线
        </div>
      </div>
      <div class="tr" v-show="gjxxData.length==0">暂无数据</div>
      <div
        class="tbody"
        id="box"
        @mouseenter="mouseenterEvent"
        @mouseleave="mouseleaveEvent">
        <div
          class="tr"
          v-for="(item,index) in gjxxData"
          :key="index"
          :class="item.isLight===1?'tr_light':'tr_noLight'">
          <div
            class="tr_td"
            :style="{color:item.isHave?'red':'',flex: '0.1', textAlign: 'center',cursor:'pointer'}"
            @click="SOSfun(item)">
            {{index+1}}
          </div>
          <div class="tr_td" style="flex: 0.35; text-align: center">
            {{item.msgtime}}
          </div>
          <div
            class="tr_td"
            style="flex: 0.3; text-align: center"
            :title="item.unitname">
            {{item.unitname}}
          </div>
          <div
            class="tr_td"
            style="flex: 0.15; text-align: center"
            :title="item.hostname">
            {{item.hostname}}
          </div>
          <div
            class="tr_td"
            style="flex: 0.2; text-align: center"
            v-show="showSOSno">
            <div
              style="
                  display: flex;
                  justify-content: space-around;
                  align-items: center;">
                <span
                  :style="{cursor:item.deviceid?'pointer':'no-drop'}"
                  class="iconPhone2"
                  @click="zfryPopFun({type:'调度呼叫',sys_depart:item.deviceid,county: item.county,hostcode: item.hostcode})"
                ></span>
                <span
                  :style="{cursor:item.deviceid?'pointer':'no-drop'}"
                  class="iconVideo2"
                  @click="zfryPopFun({type:'调度呼叫',sys_depart:item.deviceid,county: item.county,hostcode: item.hostcode})"
                ></span>
              </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import { indexApi } from '@/api/indexApi'
import moment from 'moment'
export default {
  name: 'index',
  components: {
    CommonTitle
  },
  data() {
    return {
      year: localStorage.getItem("year"),
      city: localStorage.getItem("city"),
      datas1: [
        moment().add("month", -2).format("YYYY-MM-DD"),
        moment(new Date()).format("YYYY-MM-DD"),
      ],
      showSOSno: false,
      sos: {
        sosNOdata: [], //未处理
        sosISdata: [], //已处理
      },
      gjxxData: [],
      dom1: null,
      time1: null
    }
  },
  computed: {},
  mounted() {
    this.$bus.$on('cityChange', (city) => {
      this.city = city;
      this.init(city,localStorage.getItem("year"));
    })
    this.$bus.$on('yearChange', (year) => {
      this.year = year;
      this.init(localStorage.getItem("city"),year);
    })
    this.$bus.$on('updateWarningList', (e) => {
      this.init(localStorage.getItem("city"),localStorage.getItem("year"));
    })
    this.init(localStorage.getItem("city"),localStorage.getItem("year"));
    // 表格滚动
    this.dom1 = document.getElementById("box");
    this.mouseleaveEvent();

    this.$bus.$on('glid', (id) => {
      // this.linghtRwgz(id);
    })
  },
  methods: {
    init(city,year) {
      let this_ = this;
      this.showSOSno = false;
      indexApi("/xzzf_zfy_sos", {
        county: city == "金华市" ? "" : city,
        endTime: (this_.datas1 && this_.datas1[1] + " 24:00:00") || "",
        startTime: (this_.datas1 && this_.datas1[0] + " 00:00:00") || "",
      }).then((res) => {
        this_.sos.sosISdata = res.data.filter(
          (a) => a.alarmStatus == 1
        );
        this_.sos.sosNOdata = this_.gjxxData = res
          .data.filter(
            (a) => a.alarmStatus == 0
          );
        this_.gjxxData.forEach((item) => {
          this_.$parent.$refs.rwgz.tableData.forEach((value) => {
            if (value.detailsId == item.id) {
              item.isHave = true;
            }
          });
        });
      });
    },
    queryData1() {
      this.init("金华市");
    },
    mouseenterEvent() {
      clearInterval(this.time1);
    },
    mouseleaveEvent() {
      let dom1 = document.getElementById("box");
      this.time1 = setInterval(() => {
        dom1.scrollBy({
          top: 97,
          behavior: "smooth",
        });
        if (
          dom1.scrollTop >=
          dom1.scrollHeight - dom1.offsetHeight
        ) {
          dom1.scrollTop = 0;
        }
      }, 1500);
    },
    //sos告警列表-定位上点
    SOSfun(item) {
      // this.linghtRwgz(item.id);
      // if (item.id != this.rowClickIndex) {
      //   this.rowClickIndex = item.id;
      //   window.parent.mapUtil.flyTo({
      //     destination: [item.longitude, item.latitude], //飞行中心点
      //     zoom: 16, //飞行层级
      //   });
      //   this.SOSpoint(item);
      //   this.video_point = item.longitude + "," + item.latitude;
      //   console.log(this.video_point);
      //   // localStorage.setItem("point", this.video_point);
      //   this.findVideoByPoint(this.video_point, true);
      //   this.zfryPoint(this.video_point);
      //   this.qyopen(item);
      // } else {
      //   window.parent.mapUtil.removeAllLayers(["zfry_point", "zfry_sos"]);
      //   window.parent.mapUtil.removeLayer("syr");
      //   window.parent.mapUtil.removeLayer("syr1");
      //   window.parent.mapUtil.removeLayer("camera-load-icon");
      //   this.rowClickIndex = null;
      //   this.openorclose=!this.openorclose
      //   this.qyopen(item);
      //   this.openorclose=!this.openorclose
      // }
    },
    zfryPopFun(item) {

    }
  },
  watch: {}
}
</script>

<style scoped lang='less'>
/deep/ .yearChange {
  position: relative;
  bottom: 35px;
  left: 20px;
  .el-input__inner {
    height: 48px !important;
    background-color: #132c4e !important;
    border: 2px solid #afdcfb !important;
    color: #fff !important;
    border-radius: 15px !important;
  }
  .el-picker-panel {
    left: -170px !important;
  }
}

.table-bottom {
  width: 100%;
  height: 385px;
  padding: 10px;
  box-sizing: border-box;
}

.table-bottom .th {
  width: 100%;
  height: 85px;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  font-weight: 700;
  font-size: 32px;
  line-height: 60px;
  color: #CDE7FF;
  background: #082B62;
}

.table-bottom .th_td {
  letter-spacing: 0px;
  text-align: left;
}

.table-bottom .tbody {
  width: 100%;
  height: calc(100% - 85px);
  /* overflow-y: auto; */
  overflow: hidden;
}

.table-bottom .tbody:hover {
  overflow-y: auto;
}

.table-bottom .tbody::-webkit-scrollbar {
  width: 4px;
  /*滚动条整体样式*/
  height: 4px;
  /*高宽分别对应横竖滚动条的尺寸*/
}

.table-bottom .tbody::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: #20aeff;
  height: 8px;
}

.table-bottom .tr {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  height: 85px;
  line-height: 70px;
  font-size: 28px;
  color: #CDE7FF;
  cursor: pointer;
  border-top: 1px solid #959aa1;
  border-image: linear-gradient(to right, #e9f5ff3b, #f5ffffd4, #e9f5ff3b) 1;
  box-sizing: border-box;
}

.table-bottom .tr:nth-child(2n) {
  background: #072249;
}

.table-bottom .tr:nth-child(2n + 1) {
  background: #081B3C;
}

.table-bottom .tr:hover {
  background-color: #0074da75;
}

.table-bottom .tr_td {
  letter-spacing: 0px;
  text-align: left;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.sos_box {
  position: absolute;
  right: 40px;
  top: 50px;
  cursor: pointer;
}
.icon_num {
  min-width: 30px;
  line-height: 18px;
  background: red;
  position: absolute;
  font-style: initial;
  text-align: center;
  border-radius: 50%;
  color: #fff;
  font-weight: 700;
  font-size: 25px;
  padding: 10px 5px;
  right: -9px;
  top: -11px;
}
</style>