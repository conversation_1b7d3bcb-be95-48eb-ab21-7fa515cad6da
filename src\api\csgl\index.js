import { request } from '@/utils/request'

//事件统揽
export function getEventOverview(params) {
  return request({
    url: `/screen/urban/eventOverview`,
    method: 'get',
    params
  })
}
//高发事件/部件统计Top榜
export function getEventTop(params) {
  return request({
    url: `/screen/urban/eventTop`,
    method: 'get',
    params
  })
}
//趋势预测分析
export function getTrendAnalysis(params) {
  return request({
    url: `/screen/urban/trendAnalysis`,
    method: 'get',
    params
  })
}
//指标晾晒
export function getIndicatorDrying(params) {
  return request({
    url: `/screen/urban/indicatorDrying`,
    method: 'get',
    params
  })
}
//区县考核分析
export function getCountyAnalysis(params) {
  return request({
    url: `/screen/urban/countyAnalysis`,
    method: 'get',
    params
  })
}
//部门考核分析
export function getDeptAnalysis(params) {
  return request({
    url: `/screen/urban/deptAnalysis`,
    method: 'get',
    params
  })
}
//中间
export function getMidMap(params) {
  return request({
    url: `/screen/urban/midMap`,
    method: 'get',
    params
  })
}

//案件事件总数
export function getAJtotal(params) {
  return request({
    url: `/zhcg/event/getCountyNum`,
    method: 'get',
    params
  })
}
