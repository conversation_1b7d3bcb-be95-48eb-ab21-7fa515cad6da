<template>
  <div class="bottom-menu" :class="{ 'opne': open, 'rMenu': isRight }">
    <ul v-if="open" class="left">
      <li :class="allCheck ? 'menu-1 checked': 'menu-1'" @click="handleAllCheck">全部</li>
      <li v-for="idx in left" :key="idx" clas :class="menuList[idx].checked ? `${menuList[idx].className} checked` : menuList[idx].className" @click="handleClick(menuList[idx])">{{ menuList[idx].name }}</li>
    </ul>
    <div class="title" @click="open = !open" @dblclick="isRight = !isRight">
      <p class="c-title">数据图层</p>
      <p class="e-title">Scheduling command</p>
    </div>
    <ul v-if="open">
      <li v-for="idx in right" :key="idx" clas :class="menuList[idx].checked ? `${menuList[idx].className} checked` : menuList[idx].className" @click="handleClick(menuList[idx])">{{ menuList[idx].name }}</li>
    </ul>
  </div>
</template>

<script>
import { changeOverlays } from '@/pages/board/views/map/setMarker'

export default {
  props: {
    mapObj: Object
  },
  data() {
    return {
      isRight: true,
      open: true,
      allCheck: true,
      left: [0, 1],
      right: [2, 3],
      menuList: [
        { key: 'monitor', className: 'menu-2', name: '监控', checked: true },
        { key: 'taxi', className: 'menu-11', name: '出租车', checked: true },
        { key: 'event', className: 'menu-6', name: '事件', checked: true },
        { key: 'tout', className: 'menu-13', name: '黄牛事件', checked: true }
      ]
    }
  },
  methods: {
    handleClick(menu) {
      menu.checked = !menu.checked
      const nMenu = this.menuList.filter(item => item.checked != menu.checked)
      if (nMenu.length) {
        this.allCheck = false
      } else {
        this.allCheck = menu.checked
      }
      changeOverlays([menu.key], this.mapObj, menu.checked)
    },
    handleAllCheck() {
      let keys = []
      this.allCheck = !this.allCheck
      this.menuList = this.menuList.map(item => {
        if (item.checked != this.allCheck) {
          keys.push(item.key)
        }
        item.checked = this.allCheck
        return item
      })
      changeOverlays(keys, this.mapObj, this.allCheck)
    }
  }
}
</script>

<style scoped lang="scss">
.bottom-menu {
  width: pxtorem(210);
  height: pxtorem(108);
  background: #fff;
  border-radius: 10px;
  position: absolute;
  bottom: pxtorem(30);
  left: 50%;
  margin-left: pxtorem(-105);
  z-index: 500;
  display: flex;
  justify-content: space-around;
  padding: pxtorem(14) pxtorem(20);
  overflow: hidden;
  transition: all 0.3s;
  box-shadow: 0 4px 14px rgba(0, 0, 0, 0.2);
  &.opne {
    width: pxtorem(800);
    margin-left: pxtorem(-400);
  }
  .title {
    width: pxtorem(170);
    height: pxtorem(80);
    flex-shrink: 0;
    background: url(../../../../../assets/images/menu-btn.png) no-repeat center center / 100% 100%;
    color: #fff;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    p {
      margin: 0;
    }
    .c-title {
      font-size: pxtorem(24);
      margin-bottom: pxtorem(10);
    }
    .e-title {
      font-size: pxtorem(12);
    }
  }
  ul {
    width: pxtorem(450);
    display: flex;
    // justify-content: space-between;
    margin: 0;
    padding: 0;
    &.left {
      justify-content: flex-end;
    }
    li {
      list-style: none;
      width: pxtorem(90);
      height: pxtorem(80);
      display: flex;
      justify-content: center;
      align-items: flex-end;
      cursor: pointer;
      opacity: 0.3;
      font-size: pxtorem(14);
      &.menu-1 {
        background: url(../../../../../assets/images/menu-1-s.png) no-repeat center pxtorem(5);
      }
      &.menu-2 {
        background: url(../../../../../assets/images/menu-2-s.png) no-repeat center pxtorem(5);
      }
      &.menu-3 {
        background: url(../../../../../assets/images/menu-3-s.png) no-repeat center pxtorem(5);
      }
      &.menu-4 {
        background: url(../../../../../assets/images/menu-4-s.png) no-repeat center pxtorem(5);
      }
      &.menu-6 {
        background: url(../../../../../assets/images/menu-6-s.png) no-repeat center pxtorem(5);
      }
      &.menu-8 {
        background: url(../../../../../assets/images/menu-8-s.png) no-repeat center pxtorem(5);
      }
      &.menu-10 {
        background: url(../../../../../assets/images/menu-10-s.png) no-repeat center pxtorem(5);
      }
      &.menu-11 {
        background: url(../../../../../assets/images/menu-11-s.png) no-repeat center pxtorem(5);
      }
      &.menu-12 {
        background: url(../../../../../assets/images/menu-12-s.png) no-repeat center pxtorem(5);
      }
      &.menu-13 {
        background: url(../../../../../assets/images/menu-13-s.png) no-repeat center pxtorem(5);
      }
      &.checked {
        opacity: 1;
      }

      background-size: pxtorem(40) pxtorem(40);
    }
  }
  &.rMenu {
    top: pxtorem(30);
    bottom: auto;
    right: pxtorem(30);
    left: auto;
    margin-left: auto;
    flex-wrap: wrap;
    &.opne {
      // width: pxtorem(1200);
      // margin-left: pxtorem(-600);
      width: pxtorem(210);
      height: pxtorem(400);
    }
    ul {
      width: pxtorem(85);
      order: 5;
      flex-direction: column;
      &.left {
        justify-content: flex-start;
      }
      li {
        margin-bottom: pxtorem(10);
      }
    }
  }
}
</style>
