<template>
  <el-dialog
    :close-on-click-modal="false"
    title="选择联合执法"
    :visible.sync="value"
    width="40%"
    :before-close="handleClose"
    append-to-body
    @open="handleOpen"
  >
    <el-scrollbar v-loading="loading" style="height: 100%;" :element-loading-text="formLoadingText">
      <el-row>
        <el-col v-for="(v,i) in unionData" :key="i" :span="24" class="suffix" @click.native="onConfirms(v)">
          <div class="list">
            <strong class="labels">任务名称:</strong>
            <span>{{ v.title }}</span>
          </div>
          <div class="list">
            <strong class="labels">开始时间:</strong>
            <span>{{ v.startTime }}</span>
          </div>
          <div class="list">
            <strong class="labels">结束时间:</strong>
            <span>{{ v.endTime }}</span>
          </div>
        </el-col>
      </el-row>
    </el-scrollbar>
  </el-dialog>
</template>

<script>
import rwfq from '@/api/case/union/rwfq'
export default {
  name: 'UnionLow',
  props: {
    value: Boolean
  },
  data() {
    return {
      loading: false,
      formLoadingText: '数据加载中',
      unionData: []
    }
  },
  methods: {
    handleOpen() {
      this.loading = true
      rwfq.List({status: 2}).then(res => {
        console.log(res)
        this.unionData = res.rows
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    onConfirms(data) {
      console.log(data)
      this.$emit('onConfirm', data)
    },
    handleClose() {
      this.$emit('update:value', false)
    }
  }
}
</script>
<style lang="scss" scoped>
.suffix {
  padding: 10px 0 10px 30px;
  margin-bottom: 20px;
  border: 1px solid #e4e7ec;
  box-shadow: 0 2px 12px 0 rgba(0 0 0 / 10%);
  border-radius: 8px;
}
.suffix .list {
  width: 100%;
  overflow: hidden;
  margin-bottom: 20px;
}
.list .labels {
  margin-right: 4%;
  width: 20%;
  text-align: right;
  display: inline-block;
}

</style>

