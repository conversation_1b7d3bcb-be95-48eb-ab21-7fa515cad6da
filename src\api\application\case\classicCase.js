import {request} from '@/utils/request'

// 查询经典案例列表
export function listCase(query) {
  return request({
    url: '/business/classic/case/list',
    method: 'get',
    params: query
  })
}

// 查询经典案例详细
export function getCase(id) {
  return request({
    url: '/business/classic/case/' + id,
    method: 'get'
  })
}

// 新增经典案例
export function addCase(data) {
  return request({
    url: '/business/classic/case/add',
    method: 'post',
    data: data
  })
}

// 修改经典案例
export function updateCase(data) {
  return request({
    url: '/business/classic/case/edit',
    method: 'post',
    data: data
  })
}

// 删除经典案例
export function delCase(id) {
  return request({
    url: '/business/classic/case/remove/' + id,
    method: 'post'
  })
}

// 导出经典案例
export function exportCase(query) {
  return request({
    url: '/business/classic/case/export',
    method: 'get',
    params: query
  })
}
