<template>
  <div>
    <el-dialog class="m-dialog shop-dialog" :close-on-click-modal="false" v-bind="$attrs" :title="title" fullscreen v-on="$listeners" @close="onClose" @open="handleOpen">
      <el-scrollbar v-loading="formLoading" style="height: 100%;" :element-loading-text="formLoadingText">
        <div style="margin-right: 10px;">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form ref="form" :model="form" :rules="rules" :disabled="formDisabled" size="medium" label-width="120px">
                <h3 class="title">负责人信息</h3>
                <el-row>
                  <el-col :span="8">
                    <el-form-item label="负责人名称" prop="userName">
                      <el-input v-model="form.userName" placeholder="请选择上报人名称" readonly>
                        <el-button slot="append" type="primary" @click="launchOpen = true">选择</el-button>
                      </el-input>
                      <userSelect id="userName" v-model="launchOpen" :multiple="false" :select-user-keys="form.userId ? [form.userId] : []" :default-expanded-keys="form.userId ? [form.userId] : ['100']" @confirm="launchConfirm" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="分配部门名称" prop="assignDeptNames">
                      <el-input v-model="form.assignDeptNames" placeholder="请选择分配部门名称" readonly>
                        <el-button slot="append" type="primary" @click="deptOpen = true">选择</el-button>
                      </el-input>
                      <userSelect id="deptNames" v-model="deptOpen" :name="'bumen'" :select-user-keys="form.assignDeptIds?(form.assignDeptIds+'').split(','):[]" :default-expanded-keys="form.assignDeptIds?(form.assignDeptIds+'').split(','):['100']" @confirm="deptConfirm" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="指派时间">
                      <el-tooltip :disabled="!form.assignDate" class="item" effect="dark" :content="form.assignDate" placement="top">
                        <el-date-picker v-model="form.assignDate" disabled type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="请选择指派时间" clearable />
                      </el-tooltip>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="巡查分类">
                      <el-select v-model="form.shopType" :style="{ width: '100%' }" @change="handleChangeShopType">
                        <el-option v-for="(item,idx) in shopTypeList" :key="idx" :label="item.dictLabel" :value="item.dictValue" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="巡查店铺">
                      <el-tooltip :disabled="!form.assignShopNames" class="item" effect="dark" :content="form.assignShopNames" placement="top">
                        <el-input v-model="form.assignShopNames" placeholder="请选择巡查店铺" readonly>
                          <el-button slot="append" type="primary" @click="showShop = true">选择</el-button>
                        </el-input>
                      </el-tooltip>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="内容">
                      <el-input v-model="form.content" type="textarea" placeholder="请输入案件内容" />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </el-col>
            <el-col :span="24">
              <el-row v-loading="shopLoading">
                <el-col :span="24" style="position: relative;">
                  <div ref="map" class="t-map" />
                  <div v-if="!detailId" class="btns">
                    <el-button v-if="!isDraw" type="primary" @click="handleDraw">绘制区域</el-button>
                    <el-button v-else type="primary" @click="handleReDraw">重新绘制</el-button>
                  </div>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
        </div>
      </el-scrollbar>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handelOver">确 定</el-button>
        <el-button @click="close">取 消 </el-button>
      </div>
    </el-dialog>
    <!-- 商铺弹窗 -->
    <shop-window-info ref="shopWindowInfo" />
    <!-- 商铺列表 -->
    <shopList :visible.sync="showShop" :default-selection="form.assignShopIds" @confirm="confirmShop" />
  </div>
</template>

<script>

// import shop from '@/api/case/synthetical/shop'
import userSelect from '@/components/userselect/index'
import {getLsrwList, lsrwEdit, lsrwAdd} from '@/api/case/synthetical/lsrw'
import shop from '@/api/case/synthetical/shop'
import shopLine from '@/assets/images/shop-l.png'
import shopSucces from '@/assets/images/shop-s.png'
import shopProcess from '@/assets/images/shop-p.png'
import { loadMap } from '@/utils/amapLoad'
import shopWindowInfo from '@/pages/board/views/components/windowInfo/shop/index.vue'
import shopList from './shopList.vue'

export default {
  name: 'FromList',
  components: {
    userSelect,
    shopWindowInfo,
    shopList
  },
  props: {
    title: String,
    detailId: Number,
    formDisabled: Boolean,
    typeData: Array
  },
  data() {
    return {
      shopTypeList: [],
      formLoadingText: '数据上传中',
      formLoading: false,
      shopTypeData: [],
      form: {},
      deptOpen: false,
      launchOpen: false,
      rules: {
        userName: [{ required: true, message: '请选择负责人', trigger: 'change' }],
        assignDeptNames: [{ required: true, message: '请分配部门', trigger: 'change' }]
      },
      $map: null,
      PolygonTool: null,
      isDraw: false,
      shopList: [],
      shopLoading: false,
      showShop: false
    }
  },
  watch: {
    '$attrs.visible'(nVal) {
      if (nVal) this.getShopList()
      if (nVal && this.detailId) {
        this.getData()
      }
    }
  },
  async mounted() {
    await this.getData()
  },
  methods: {
    handleChangeShopType(val) {
      const res = this.shopTypeList.find(item => item.dictValue == val)
      this.form.shopTypeName = res.dictLabel
    },
    getShopList() {
      this.getDicts('shop_patrol_type').then(res => {
        this.shopTypeList = res.data
      })
    },
    confirmShop(rows) {
      let ids = [], names = []
      rows.forEach(item => {
        ids.push(item.shopId)
        names.push(item.shopName)
      })
      this.form = { ...this.form, assignShopIds: ids.join(','), assignShopNames: names.join(',') }
    },
    getData() {
      this.formLoading = true
      this.formLoadingText = '加载中'
      Promise.all([
        getLsrwList(this.detailId)
      ]).then(res => {
        this.form = {...res[0].data}
        this.addInsMarker(this.form.inspections)
        this.formLoading = false
      }).catch(err => { console.log(err), this.formLoading = false, this.msgError('加载失败') })
    },
    addInsMarker(list) {
      if (!this.$map) {
        setTimeout(() => {
          this.addInsMarker(list)
        }, 1000)
        return
      }
      if (Array.isArray(list) && list.length) {
        const llAry = list.map(item => {
          if (item.shopLongitude && item.shopLatitude) {
            let img = shopLine
            if (item.status == 2) img = shopProcess
            if (item.status == 9) img = shopSucces
            const icon = new window.T.Icon({
              iconUrl: img,
              iconSize: new window.T.Point(30, 30),
              iconAnchor: new window.T.Point(15, 15)
            })

            const lnglat = new window.T.LngLat(item.shopLongitude, item.shopLatitude)
            let marker = new window.T.Marker(lnglat, { icon })
            marker.dataSet = item
            marker.addEventListener('click', e => {
              // 打开巡查弹窗
              console.log(e)
            })
            this.$map.addOverLay(marker)
            return lnglat
          }
        })
        const cz = this.$map.getViewport(llAry)
        if (cz) this.$map.centerAndZoom(cz.center, cz.zoom)
      }
    },
    getAMap() {
      return new Promise((resolve, reject) => {
        loadMap(['AMap.GeometryUtil']).then(
          AMap => resolve(AMap),
          () => reject()
        )
      })
    },
    openWindowInfo(marker, windowName) {
      this.$refs[windowName].$el.style = 'display: block;'
      const windowInfo = new window.T.InfoWindow(this.$refs[windowName].$el, {
        autoPan: true,
        autoPanPadding: [10, 10],
        closeOnClick: true
      })
      windowInfo.setMaxWidth(1000)
      marker.openInfoWindow(windowInfo)
      this.$refs[windowName].windowDataInit(marker.dataSet.shopId, windowInfo)
    },
    handleOpen() {
      this.$nextTick(() => {
        if (!this.$map) {
          this.$map = new window.T.Map(this.$refs.map)
          this.$map.centerAndZoom(new window.T.LngLat(119.63126, 29.11181), 18)
        }
        if (!this.detailId) {
          // 新增临时任务
          this.PolygonTool = new window.T.PolygonTool(this.$map, {
            showLabel: false, color: 'blue', weight: 3, opacity: 0.5, fillColor: '#FFFFFF', fillOpacity: 0.5
          })
          this.PolygonTool.addEventListener('draw', e => {
            this.getAMap().then(AMap => {
              const path = e.currentLnglats.map(item => {
                return [item.lng, item.lat]
              })
              const resultShop = this.shopList.filter(item => {
                if (item.longitude && item.latitude) {
                  return AMap.GeometryUtil.isPointInRing([item.longitude, item.latitude], path)
                } else {
                  return false
                }
              })
              if (resultShop.length) {
                let ids = [], names = []
                resultShop.forEach(item => {
                  ids.push(item.shopId)
                  names.push(item.shopName)
                })
                this.form = { ...this.form, assignShopIds: ids.join(','), assignShopNames: names.join(',') }
              } else {
                this.$message.warning('请将店铺纳入绘制区域')
              }
            })
          })
          this.shopLoading = true
          shop.list().then(res => {
            this.shopLoading = false
            this.shopList = res.rows.filter(item => item.latitude && item.longitude)
            const icon = new window.T.Icon({
              iconUrl: shopLine,
              iconSize: new window.T.Point(30, 30),
              iconAnchor: new window.T.Point(15, 15)
            })
            const llAry = this.shopList.map(item => {
              const lnglat = new window.T.LngLat(item.longitude, item.latitude)
              let marker = new window.T.Marker(lnglat, { icon })
              marker.dataSet = item
              marker.addEventListener('click', e => {
                this.openWindowInfo(e.target, 'shopWindowInfo')
              })
              this.$map.addOverLay(marker)
              return lnglat
            })
            const cz = this.$map.getViewport(llAry)
            if (cz) {
              this.$map.centerAndZoom(cz.center, cz.zoom)
            }
          }).catch(() => {
            this.shopLoading = false
            this.$message.error('店铺信息获取失败')
          })
        }
      })
    },
    handleDraw() {
      if (this.PolygonTool) {
        this.PolygonTool.open()
        this.isDraw = true
      } else {
        this.$message.error('绘制工具未准备好，请重试')
      }
    },
    handleReDraw() {
      if (this.$map && this.PolygonTool) {
        this.PolygonTool.clear()
        this.handleDraw()
      } else {
        this.$message.error('发生错误，请重试')
      }
    },
    handelOver() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (!this.form.assignShopIds) {
            this.$message.error('请在地图中框选巡查店铺')
            return
          }
          if (!this.form.temporaryId) {
            this.form.assignDate = this.parseTime(new Date())
            this.form.status = 2
          }
          let params = {...this.form}
          this.formLoading = true
          this.formLoadingText = '数据上传中'
          let api = params.temporaryId ? lsrwEdit(params) : lsrwAdd(params)
          api.then(() => {
            this.$emit('reLoad')
            this.close()
            this.$message.success('操作成功')
            this.formLoading = false
          }).catch(() =>  this.formLoading = false)
        } else {
          return false
        }
      })

    },
    deptConfirm(e) {    // 上报人名称
      this.form = {...this.form, assignDeptNames: e.name, assignDeptIds: e.id}
    },
    launchConfirm(e) {    //  发起人名称
      this.form = {...this.form, userName: e.name, userId: e.id}
    },
    reset() {
      this.form = {}
      this.isDraw = false
      if (this.$map) this.$map.clearOverLays()
    },
    onClose() {
      this.reset()
      this.formFiles = []
      this.formLoadingText = '数据上传中'
      this.$refs.form.clearValidate()
    },
    close() {
      this.$emit('update:visible', false)
      this.$refs.form.clearValidate()
    }
  }
}
</script>

<style scoped lang="scss">
.shop-dialog {
  ::v-deep {
    .el-dialog {
      width: 100%;
      .el-dialog__body {
        height: 85vh;
      }
    }
  }
}
.t-map {
  width: 100%;
  height: 50vh;
  background: #ddd;
}
.btns {
  position: absolute;
  top: 0;
  right: 0;
  padding: 10px;
  background: #fff;
  box-shadow: 0 0 10px #ccc;
  z-index: 999;
}
</style>
