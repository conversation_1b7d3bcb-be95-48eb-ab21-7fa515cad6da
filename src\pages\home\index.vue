<template>
  <div class='pageContainer'>
    <animated-transition
      enter="fadeInLeft"
      leave="fadeOutLeft"
    >
      <left v-show="showPage"></left>
    </animated-transition>
    <animated-transition
      enter="fadeInRight"
      leave="fadeOutRight"
    >
      <right v-show="showPage"></right>
    </animated-transition>
    <animated-transition
      enter="fadeInUp"
      leave="fadeOutDown"
    >
      <bottom v-show="showPage"></bottom>
    </animated-transition>
    <top></top>
  </div>
</template>

<script>
import left from './left'
import right from './right'
import bottom from './bottom'
import top from './top'
import AnimatedTransition from '@/components/AnimatedTransition'

export default {
  name: 'index',
  components: {
    AnimatedTransition,
    left,
    right,
    bottom,
    top
  },
  data() {
    return {
      showPage: false
    }
  },
  mounted() {
    this.$bus.$on("showPage", res => {
      this.showPage = res
    })
    setTimeout(() => {
       this.showPage = !this.showPage
    }, 100)
  },
  computed: {},
  methods: {
  },
}
</script>

<style lang="less" scoped>
.container {
}
</style>
