<template>
  <div>
    <CommonTitle text="环境监测">
      <div class="yearChange">
        <el-select
          v-model="value"
          placeholder="请选择"
          style="width: 220px; position: relative; right: 20px"
          @change="queryData"
        >
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </div>
    </CommonTitle>
    <div class="wrap-container">
      <TabSwitch class="tab-wraper" :type="2" :tabList="list" :activeIndex="index" @tab-change="handleTabChange" />
    </div>
    <div class="hjjcChart" id="hjjcChart"></div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import TabSwitch from '@/components/TabSwitch'
export default {
  name: 'index',
  components: {
    CommonTitle,
    TabSwitch,
  },
  data() {
    return {
      index: 0,
      list: [
        { name: 'AQI', value: '1' },
        { name: 'PM2.5', value: '2' },
        { name: 'PM10', value: '3' },
        { name: 'SO2', value: '4' },
        { name: 'NO2', value: '5' },
      ],
      value: '婺城区',
      options: [
        {
          label: '婺城区',
          value: '婺城区',
        },
        {
          label: '开发区',
          value: '开发区',
        },
      ],
      chartsData: [
        {
          name: '婺城',
          value: 280,
        },
        {
          name: '金东',
          value: 380,
        },
        {
          name: '兰溪',
          value: 200,
        },
        {
          name: '东阳',
          value: 300,
        },
        {
          name: '义乌',
          value: 300,
        },
        {
          name: '永康',
          value: 300,
        },
        {
          name: '武义',
          value: 300,
        },
        {
          name: '浦江',
          value: 300,
        },
        {
          name: '磐安',
          value: 300,
        },
      ],
    }
  },
  computed: {},
  mounted() {
    this.initCharts()
  },
  methods: {
    handleTabChange(i) {
      this.index = i
    },
    queryData() {},
    initCharts() {
      const chartDom = document.getElementById('hjjcChart')
      const myChart = this.$echarts.init(chartDom)

      const option = {
        grid: {
          left: '7%',
          right: '7%',
          bottom: '5%',
          top: '14%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.chartsData.map((item) => item.name),
          axisLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.6)',
            },
          },
          axisLabel: {
            textStyle: {
              fontSize: 24,
            },
          },
        },
        yAxis: {
          name: '',
          nameTextStyle: {
            color: 'rgba(255,255,255,0.6)',
            padding: [20, 10, 10, 10],
            fontSize: 24,
          },
          type: 'value',
          axisLabel: {
            textStyle: {
              fontSize: 24,
            },
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.6)',
            },
          },
          splitLine: {
            lineStyle: {
              color: '#4A6C89',
            },
          },
        },
        series: [
          {
            data: this.chartsData.map((item) => item.value),
            type: 'line',
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(62, 215, 253, 0.30)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(62, 215, 253, 0)',
                  },
                ],
              },
            },
            lineStyle: {
              color: '#00ffff',
            },
            smooth: true,
            symbol: 'none',
          },
        ],
      }

      myChart.setOption(option)

      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
.wrap-container {
  width: 100%;
  // height: 600px;
  .tab-wraper {
    margin: 10px 40px;
  }
}
/deep/ .yearChange {
  position: relative;
  right: 10px;
  bottom: 30px;
  .el-input__inner {
    height: 48px !important;
    background-color: #132c4e !important;
    border: 2px solid #afdcfb !important;
    color: #fff !important;
    border-radius: 15px !important;
    font-size: 24px;
  }
}
.hjjcChart {
  width: 100%;
  height: 400px;
}
</style>