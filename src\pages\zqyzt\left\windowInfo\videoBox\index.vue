<template>
  <div id="dialog" v-dialogDrag>
    <el-dialog id="video-dialog" ref="dialog__wrapper" :close-on-click-modal="false" :title="$store.getters.videoTitle" :before-close="handleClose" v-bind="$attrs" top="0" v-on="$listeners">
      <div class="dialog-body">
        <!-- 弹窗内容 -->
        <video v-show="false" id="id_video_m" ref="videoMy" controls autoplay />
        <video id="id_video_peer_v" ref="videoPeer" controls autoplay />
        <!-- <div class="ctrl">
          <svg-icon icon-class="full-screen" class-name="ctrl-btn" @click="handleFullScreen" />
          <svg-icon icon-class="hang-up" class-name="ctrl-btn" @click="handleRel" />
        </div> -->
      </div>
      <div slot="footer">
        <el-button type="danger" @click="handleRel">挂断</el-button>
        <!--用于拖拽窗口大小的点（位于窗口右下角）-->
        <div v-dialogDragWidth="'video-dialog'" class="pointRB">
          <svg-icon icon-class="brDrag" />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { callMakeOut, fn_rel, fn_fullscreen, fn_answer } from '@/assets/idt/Idt'

export default {
  inheritAttrs: false,
  props: {
    terminalNo: String
  },
  data() {
    return {

    }
  },
  watch: {
    '$attrs.visible'(nVal) {
      if (nVal && this.$store.getters.callType == 'callRecv') {
        this.$nextTick(() => {
          callMakeOut(this.terminalNo, this.$refs.videoPeer, this.$store.state.board.callArgs)
        })
      } else if (nVal && this.$store.getters.callType == 'callIn') {
        this.$nextTick(() => {
          fn_answer(this.$refs.videoMy, this.$refs.videoPeer, this.$store.state.board.callArgs)
        })
      }
    }
  },
  methods: {
    handleClose() {
      this.$store.commit('board/SET_VIDEO_VISIBLE', false)
      fn_rel()
    },
    handleRel() {
      fn_rel()
    },
    handleFullScreen() {
      fn_fullscreen(this.$refs.videoPeer)
    }
  }
}
</script>

<style scoped lang="scss">
#id_video_peer_v {
  width: 100%;
  height: 100%;
}
#dialog {
  ::v-deep.el-dialog__wrapper {
    top: pxtorem(110);
    left: pxtorem(240);
    right: unset;
    bottom: unset;
    width: 1066px;
    height: 600px;
    min-width: 533px;
    min-height: 300px;
    box-shadow: 0 4px 14px rgb(0 0 0 / 20%);
    overflow: hidden;
    .el-dialog {
      margin: 0;
      top: 0;
      bottom: 0;
      right: 0;
      left: 0;
      position: absolute;
      width: auto;
      .el-dialog__body {
        position: relative;
        height: calc(100% - 110px);
        padding: 0;
        .dialog-body {
          height: 100%;
          position: relative;
          .ctrl {
            width: 100%;
            height: 40px;
            background: rgba(0, 0, 0, 0.5);
            position: absolute;
            bottom: 0;
            .ctrl-btn {
              font-size: 25px;
              margin-top: 5px;
              float: right;
              margin-right: 20px;
              cursor: pointer;
            }
          }
        }
      }
      .el-dialog__footer {
        padding: 10px;
        .pointRB {
          width: 14px;
          height: 14px;
          position: absolute;
          right: 0;
          bottom: 0;
          z-index: 2;
          font-size: 14px;
        }
      }
    }
  }
}
</style>
