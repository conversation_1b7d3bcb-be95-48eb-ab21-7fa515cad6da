import {request} from '@/utils/request'

/**
 * 查询树图列表
 * @param {Object} params - 查询参数，包含parentId
 * @returns {Promise} - 返回请求结果
 */
export function TreeList(params = {}) {
  // 确保params至少是一个空对象
  const queryParams = params || {}
  return request({
    url: '/dh/getDhOrg',
    method: 'get',
    params: queryParams
  })
}

/**
 * 获取视频标签详情
 * @param {String} id - ID
 * @returns {Promise} - 返回请求结果
 */
export function getVideoTagDetail(id) {
  return request({
    url: `/system/dhVideosTag/${id}`,
    method: 'get'
  })
}

//获取设备列表
export function getVideoDeviceList(params) {
  return request({
    url: `/dh/getDhVideoByOrgCode`,
    method: 'get',
    params
  })
}

//添加标签
export function addVideoTag(data) {
  return request({
    url: `/system/dhVideosTag`,
    method: 'post',
    data
  })
}

//删除标签
export function deleteVideoTag(ids) {
  return request({
    url: `/system/dhVideosTag/remove/${ids}`,
    method: 'put'
  })
}