<template>
  <ygfDialog :visible="visible" width="2034px">
    <div class="zhyxts-dialog">
      <div class="rw-title flex-between" style="margin-top: 60px">
        <div class="fs-44 titleText" style="margin-left: 20px">综合运行态势报告</div>
        <div class="close cursor" @click="close" style="margin-right: 20px"></div>
      </div>
      <div class="content">
        <div class="table">
          <div class="tableHead flex-c">
            <div class="td">名称</div>
            <div class="td">创建时间</div>
            <div class="td">操作</div>
          </div>
          <div
            class="tableLine flex-c"
            v-for="(item, i) in dataList"
            :key="i"
            :class="{ tableLine_active: i % 2 == 1 }"
          >
            <div class="td">{{ item.name }}</div>
            <div class="td">{{ item.createTime }}</div>
            <div class="td" style="display: flex; align-items: center; justify-content: center">
              <div class="btn" @click="checkReport(item.monthDate)">查看</div>
              <div class="btn" @click="savePdf(item.monthDate)">下载</div>
              <div class="btn" @click="handlePrint(item.monthDate)">打印</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ygfDialog>
</template>

<script>
import ygfDialog from '@/components/ygfDialog'
import { getreportList } from '@/api/yxjc/index.js'

export default {
  name: 'index',
  props: ['visible'],
  components: {
    ygfDialog,
  },
  data() {
    return {
      dataList: [
        // { name: 'XXXXX', time: '2024-03-11 05:06:20', id: '1' },
        // { name: 'XXXXX', time: '2024-03-11 05:06:20', id: '2' },
        // { name: 'XXXXX', time: '2024-03-11 05:06:20', id: '3' },
      ],
    }
  },
  computed: {},
  mounted() {},
  methods: {
    savePdf(i) {
      this.$emit('savePdf', i)
    },
    handlePrint(i) {
      this.$emit('handlePrint', i)
    },
    getDetail() {
      getreportList().then((res) => {
        if (res.code == 200) {
          this.dataList = res.data
        }
      })
    },
    checkReport(i) {
      this.$emit('checkReport', i)
    },
    close() {
      this.$emit('close')
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.getDetail()
      }
    },
  },
}
</script>

<style scoped lang='less'>
::-webkit-scrollbar {
  display: none;
}

ul,
ul li {
  list-style: none;
}

.zhyxts-dialog {
  width: 2030px;
  height: 1320px;
  background: url('@/assets/yxjc/dialogBg.png') no-repeat;
  background-size: 100% 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-direction: column;
}

.rw-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 46px 3% 0;
  width: 95%;
  height: 60px;
  line-height: 60px;
}

.close {
  background: url('@/assets/zhdd/close.png') no-repeat;
  width: 34px;
  height: 34px;
}

.ql-indent-1 img {
  width: 100%;
}

img {
  width: 100%;
}

::-webkit-scrollbar {
  width: 0;
  height: 0;
}

.scrollTab {
  width: 928px;
  height: 70px;
}

.content {
  width: 100%;
  padding: 40px 60px 60px 60px;
  box-sizing: border-box;
}

.table {
  width: 100%;
  height: 1000px;
  overflow: scroll;
  .td {
    flex: 1;
    text-align: center;
    padding: 20px 30px;
    box-sizing: border-box;
  }
  .tableHead {
    height: 80px;
    background-color: #0a6cff33;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 500;
    font-size: 32px;
    color: #cde7ff;
    text-align: left;
  }
  .tableLine {
    min-height: 160px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 500;
    font-size: 32px;
    color: #cde7ff;
    text-align: left;
  }
  .tableLine_active {
    background-color: #0a6cff33;
  }
  .btn {
    width: fit-content;
    padding: 10px 26px;
    box-sizing: border-box;
    border-radius: 6px;
    border: 2px solid #608ec4;
    background: linear-gradient(360deg, rgba(35, 160, 255, 0) 0%, rgba(35, 116, 255, 0.5) 100%);
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 350;
    font-size: 26px;
    color: #fefefe;
    margin-right: 20px;
    cursor: pointer;
    &:last-child {
      margin-right: 0;
    }
  }
}
.flex-c {
  display: flex;
  align-items: center;
}
</style>