<template>
  <div class="resources_container">
    <div v-for="item in data" :key="item.id" class="item">
      <span class="num">{{ item.num }}</span>
      <span>{{ item.name }}</span>
    </div>
  </div>
</template>

<script>

export default {
  name: 'Resources',
  components: {

  },
  data() {
    return {
      data: [
        { id: 0, name: '自建监控', num: 17 },
        { id: 1, name: '城投监控', num: 216 },
        { id: 2, name: '运管监控', num: 118 },
        { id: 3, name: '出租车监控', num: 3601}
      ]
    }
  },
  mounted() {
  },
  methods: {
  }
}
</script>

<style scoped lang="scss">
  .resources_container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 30px;
    .item {
      display: flex;
      flex-direction: column;
      align-items: center;
      // cursor: pointer;
      font-size: pxtorem(14);
      .num {
        color: #00f7ff;
        font-size: pxtorem(28);
        font-weight: 700;
      }
    }
    // .full-dialog {
    //   position: fixed;
    //   top: 0;
    //   left: 0;
    //   right: 0;
    //   bottom: 0;
    //   z-index: 999;
    //   .mask {
    //     width: 100%;
    //     height: 100%;
    //     background: rgba(14,12,51,0.8);
    //     display: flex;
    //     justify-content: center;
    //     align-items: center;
    //     .dialog {
    //       width: 70%;
    //       height: 80%;
    //       background: url(@/assets/images/full-dialog-bg.png) no-repeat 0 0 / 100% 100%;
    //     }
    //   }
    // }
  }
</style>
