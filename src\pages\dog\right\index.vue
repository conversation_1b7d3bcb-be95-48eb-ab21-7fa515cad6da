<template>
  <div class='right'>
    <ymjzslfx></ymjzslfx>
    <jzyyph></jzyyph>
    <ymcqtx></ymcqtx>
  </div>
</template>

<script>
import ymjzslfx from '@/pages/dog/right/ymjzslfx'
import jzyyph from '@/pages/dog/right/jzyyph'
import ymcqtx from '@/pages/dog/right/ymcqtx'
export default {
  name: 'index',
  components: {
    ymjzslfx,
    jzyyph,
    ymcqtx
  },
  data() {
    return {}
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped>

</style>