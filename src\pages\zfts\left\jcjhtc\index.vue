<template>
  <div>
    <CommonTitle text='检查计划统筹'></CommonTitle>
    <div class="jcjh">
      <div class="jhzs" @click="showDialog('年度计划数')">
        <li class="text">{{jhwcjdData[0].label}}</li>
        <li class="value">
          <count-to
            :start-val="0"
            :end-val="Number(jhwcjdData[0].num)"
            :duration="3000"
            class="count-toNum s-c-yellow-gradient"
          >
          </count-to>
          <!-- {{jhwcjdData[0].num}} -->
          <span class="unit">{{jhwcjdData[0].unit}}</span>
        </li>
      </div>
      <div class="zdbm jhzs" @click="showDialog('未完成计划数')">
        <li class="text">{{jhwcjdData[1].label}}</li>
        <li class="value" style="color: #3cfdff">
          <count-to
            :start-val="0"
            :end-val="Number(jhwcjdData[1].num)"
            :duration="3000"
            class="count-toNum s-c-yellow-gradient"
          >
          </count-to>
          <!-- {{jhwcjdData[1].num}} -->
          <span class="unit"> {{jhwcjdData[1].unit}}</span>
        </li>
      </div>
    </div>
    <CommonTitle2 text='计划完成进度'></CommonTitle2>
    <div class="jhwcjd">
      <div class="jd-part">
        <div
          class="first jd"
          v-for="(item,index) in jhwcjdData.slice(2)"
          :key="index"
          @click="showPlanNumDialog(item.label)"
        >
          <li class="text" style="font-size: 28px">{{item.label}}</li>
          <li :class="`jd${index} value`" style="font-size: 40px">
            {{item.num}}<span class="unit" style="font-size: 28px"
          >{{item.unit}}
              </span>
          </li>
        </div>
      </div>
      <div class="jhjd-bg">
        <div id="jhjd"></div>
      </div>
    </div>
    <CommonTitle2 text='检查计划类型'></CommonTitle2>
    <div class="s-flex">
      <div class="jhlx-con">
        <div id="jhlx"></div>
        <div class="jhlx-bg"></div>
      </div>
      <div class="jhlx-con">
        <div id="jhlx1"></div>
        <div class="jhlx-bg"></div>
      </div>
    </div>

    <jcjhtcDialog :dialog-title="name" :visible='dialogVisible' @close='dialogVisible = false'></jcjhtcDialog>
    <planNumDialog :type="label" :visible='dialogVisible2' @close='dialogVisible2 = false'></planNumDialog>
  </div>
</template>

<script>
import countTo from 'vue-count-to'
import CommonTitle from '@/components/CommonTitle'
import CommonTitle2 from '@/components/CommonTitle2'
import jcjhtcDialog from './jcjhtcDialog'
import planNumDialog from './planNumDialog'
import {indexApi} from '@/api/indexApi'
import {registerCityYearChangeEvents} from '@/utils/index.js'
export default {
  name: 'index',
  components: {
    CommonTitle,
    CommonTitle2,
    countTo,
    jcjhtcDialog,
    planNumDialog
  },
  data() {
    return {
      year: localStorage.getItem("year"),
      city: localStorage.getItem("city"),
      //计划完成进度
      jhwcjdData: [
        {
          name: "已完成检查计划",
          num: "000",
          unit: "%",
        },
        {
          name: "正在实施计划",
          num: "000",
          unit: "%",
        },
      ],
      //检查计划类型
      jhlxData: [
        {
          name: "双随机抽查计划",
          value: 651,
          zb: 94.48,
          itemStyle: {},
        },
        {
          name: "重点监管计划",
          value: 38,
          zb: 5.52,
          itemStyle: {},
        },
      ],
      jhlx1Data: [
        {
          name: "跨部门",
          value: 163,
          zb: 23.66,
          itemStyle: {},
        },
        {
          name: "单部门",
          value: 526,
          zb: 76.34,
          itemStyle: {},
        },
      ],


      //dialog
      name:"",
      dialogVisible: false,
      label:"",
      dialogVisible2: false
    }
  },
  computed: {},
  mounted() {
    registerCityYearChangeEvents(this, this.initApi);
  },
  methods: {
    initApi(city,year) {
      //检查计划统筹
      indexApi("/csdn_yjyp4", { area_code: city,sjwd2: year }).then((res) => {
        this.jhwcjdData = this.sortArr(res.data, "orderid");
        let obj = res.data.slice(2);
        this.getChart01(
          "jhjd",
          [Number(obj[0].num)],
          [Number(obj[1].num)],
          [Number(obj[2].num)]
        );
      });
      indexApi("/csdn_yjyp8", { area_code: city,sjwd2: year }).then((res) => {
        this.jhlxData[0].value = Number(
          res.data.find((a) => a.label.includes("双随机") && a.unit == "个").num
        );
        this.jhlxData[0].zb = Number(
          res.data.find((a) => a.label.includes("双随机") && a.unit == "%").num
        );
        this.jhlxData[1].value = Number(
          res.data.find((a) => a.label.includes("重点") && a.unit == "个").num
        );
        this.jhlxData[1].zb = Number(
          res.data.find((a) => a.label.includes("重点") && a.unit == "%").num
        );
        this.getChart02("jhlx", this.jhlxData, ["#00c0ff", "#ffd461"]);
      });
      indexApi("/csdn_yjyp9", { area_code: city,sjwd2: year }).then((res) => {
        this.jhlx1Data[0].value = Number(
          res.data.find((a) => a.label.includes("跨部门") && a.unit == "个").num
        );
        this.jhlx1Data[0].zb = Number(
          res.data.find((a) => a.label.includes("跨部门") && a.unit == "%").num
        );
        this.jhlx1Data[1].value = Number(
          res.data.find((a) => a.label.includes("单部门") && a.unit == "个").num
        );
        this.jhlx1Data[1].zb = Number(
          res.data.find((a) => a.label.includes("单部门") && a.unit == "%").num
        );
        this.getChart02("jhlx1", this.jhlx1Data, ["#22e8e8", "#a9db52"]);
      });
    },
    //将数组中的每个对象根据字段orderid进行排序
    sortArr(arr, field) {
      return arr.sort((a, b) => {
        return Number(a[field]) - Number(b[field]);
      });
    },
    //计划完成进度
    getChart01(id, data1, data2, data3) {
      let myEc = this.$echarts.init(document.getElementById(id));
      let option = {
        grid: {
          left: "2%",
          top: "25%",
          right: "2.8%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          splitLine: {
            show: false,
            lineStyle: {
              color: "rgba(255,255,255,0.3)",
            },
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            show: false,
            // margin: 10,
            color: "#a4a8b4",
          },
        },
        yAxis: {
          type: "category",
          data: ["E"],
          axisLine: {
            show: false,
            lineStyle: {
              color: "rgba(255,255,255,0.3)",
            },
          },
          splitLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: false,
            // margin: 10,
            color: "#a4a8b4",
          },
        },
        color: [
          new this.$echarts.graphic.LinearGradient(1, 0, 0, 0, [
            {
              offset: 0,
              color: "#18E1FF",
            },
            {
              offset: 0.8,
              color: "rgba(106,185,242,.8)",
            },
            {
              offset: 1,
              color: "rgba(106,185,242,0)",
            },
          ]),
          new this.$echarts.graphic.LinearGradient(1, 0, 0, 0, [
            {
              offset: 0,
              color: "#FFC435",
            },
            {
              offset: 0.8,
              color: "rgba(106,185,242,.8)",
            },
            {
              offset: 1,
              color: "rgba(106,185,242,0)",
            },
          ]),
          new this.$echarts.graphic.LinearGradient(1, 0, 0, 0, [
            {
              offset: 0,
              color: "#18FFB6",
            },
            {
              offset: 0.8,
              color: "rgba(106,185,242,.8)",
            },
            {
              offset: 1,
              color: "rgba(106,185,242,0)",
            },
          ]),
        ],
        series: [
          {
            name: "已完成检查计划",
            type: "bar",
            stack: "Tik Tok",
            barWidth: 30,
            data: data1,
          },
          {
            name: "正在实施计划",
            type: "bar",
            stack: "Tik Tok",
            barWidth: 30,
            data: data2,
          },
          {
            name: "待实施计划",
            type: "bar",
            stack: "Tik Tok",
            barWidth: 30,
            data: data3,
          },
        ],
      };

      myEc.setOption(option);
      myEc.getZr().on("mousemove", (param) => {
        myEc.getZr().setCursorStyle("default");
      });
    },
    //计划类型
    getChart02(id, echartsData, colors) {
      let myChart = this.$echarts.init(document.getElementById(id));
      let selectedIndex = "";
      let hoveredIndex = "";
      console.log("echartsData", echartsData);
      let option = getPie3D(echartsData, 0.59);
      // 生成扇形的曲面参数方程
      function getParametricEquation(
        startRatio,
        endRatio,
        isSelected,
        isHovered,
        k,
        h
      ) {
        // 计算
        const midRatio = (startRatio + endRatio) / 2;

        const startRadian = startRatio * Math.PI * 2;
        const endRadian = endRatio * Math.PI * 2;
        const midRadian = midRatio * Math.PI * 2;

        // 如果只有一个扇形，则不实现选中效果。
        if (startRatio === 0 && endRatio === 1) {
          // eslint-disable-next-line no-param-reassign
          isSelected = false;
        }

        // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
        // eslint-disable-next-line no-param-reassign
        k = typeof k !== "undefined" ? k : 1 / 3;

        // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
        const offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
        const offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;

        // 计算高亮效果的放大比例（未高亮，则比例为 1）
        const hoverRate = isHovered ? 1.05 : 1;

        // 返回曲面参数方程
        return {
          u: {
            min: -Math.PI,
            max: Math.PI * 3,
            step: Math.PI / 32,
          },

          v: {
            min: 0,
            max: Math.PI * 2,
            step: Math.PI / 20,
          },

          x(u, v) {
            if (u < startRadian) {
              return (
                offsetX +
                Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate
              );
            }
            if (u > endRadian) {
              return (
                offsetX +
                Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate
              );
            }
            return (
              offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate
            );
          },

          y(u, v) {
            if (u < startRadian) {
              return (
                offsetY +
                Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate
              );
            }
            if (u > endRadian) {
              return (
                offsetY +
                Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate
              );
            }
            return (
              offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate
            );
          },

          z(u, v) {
            if (u < -Math.PI * 0.5) {
              return Math.sin(u);
            }
            if (u > Math.PI * 2.5) {
              return Math.sin(u) * h * 0.1;
            }
            // 当前图形的高度是Z根据h（每个value的值决定的）
            return Math.sin(v) > 0 ? 1 * h * 0.1 : -1;
          },
        };
      }
      // 生成模拟 3D 饼图的配置项
      function getPie3D(pieData, internalDiameterRatio) {
        const series = [];
        // 总和
        let sumValue = 0;
        let startValue = 0;
        let endValue = 0;
        const legendData = [];
        let legend = [];
        const k =
          typeof internalDiameterRatio !== "undefined"
            ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio)
            : 1 / 3;

        // 为每一个饼图数据，生成一个 series-surface 配置
        for (let i = 0; i < pieData.length; i += 1) {
          sumValue += pieData[i].value;

          const seriesItem = {
            name:
              typeof pieData[i].name === "undefined"
                ? `series${i}`
                : pieData[i].name,
            type: "surface",
            parametric: true,
            wireframe: {
              show: false,
            },
            pieData: pieData[i],
            pieStatus: {
              selected: false,
              hovered: false,
              k,
            },
          };

          if (typeof pieData[i].itemStyle !== "undefined") {
            const { itemStyle } = pieData[i];

            // eslint-disable-next-line no-unused-expressions
            typeof pieData[i].itemStyle.color !== "undefined"
              ? (itemStyle.color = pieData[i].itemStyle.color)
              : null;
            // eslint-disable-next-line no-unused-expressions
            typeof pieData[i].itemStyle.opacity !== "undefined"
              ? (itemStyle.opacity = pieData[i].itemStyle.opacity)
              : null;

            seriesItem.itemStyle = itemStyle;
          }
          series.push(seriesItem);
        }
        // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
        // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
        for (let i = 0; i < series.length; i += 1) {
          endValue = startValue + series[i].pieData.value;

          series[i].pieData.startRatio = startValue / sumValue;
          series[i].pieData.endRatio = endValue / sumValue;
          series[i].parametricEquation = getParametricEquation(
            series[i].pieData.startRatio,
            series[i].pieData.endRatio,
            false,
            false,
            k,
            // 我这里做了一个处理，使除了第一个之外的值都是10
            series[i].pieData.value === series[0].pieData.value ? 35 : 10
          );

          startValue = endValue;

          legendData.push(series[i].name);
        }

        // 准备待返回的配置项，把准备好的 legendData、series 传入。
        const option = {
          color: colors,
          // animation: false,
          tooltip: {
            formatter: (params) => {
              if (params.seriesName !== "mouseoutSeries") {
                return `${
                  params.seriesName
                }<br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                  params.color
                };"></span>${
                  option.series[params.seriesIndex].pieData.value
                } ${option.series[params.seriesIndex].pieData.zb}%`;
              }
              return "";
            },
            textStyle: {
              color: "#ffff",
              fontSize: 24,
            },
            borderWidth: 0,
            backgroundColor: "rgba(51, 51, 51, 0.7)",
          },
          legend: {
            right: "0%",
            top: "22%",
            orient: "vertical",
            textStyle: {
              rich: {
                name: {
                  fontSize: 30,
                  color: "#ffffff",
                  padding: [0, 0, 0, 15],
                },
                value: {
                  fontSize: 30,
                  color: "#e9d0ab",
                  padding: [10, 5, 0, 15],
                },
                value1: {
                  fontSize: 30,
                  color: "#e9d0ab",
                  padding: [10, 5, 0, 15],
                },
              },
            },
            formatter: function (name) {
              var data = option.series; //获取series中的data
              var total = 0;
              var tarValue;
              var zbValue;
              for (var i = 0, l = data.length; i < l; i++) {
                total += data[i].pieData.value;
                if (data[i].pieData.name == name) {
                  tarValue = data[i].pieData.value;
                  zbValue = data[i].pieData.zb;
                }
              }
              var p = ((tarValue / total) * 100).toFixed(2);
              // return '{name|' + name + '}\n{value|' +p+"%  "+ tarValue + '件'+'}'
              return (
                "{name|" +
                name +
                "}\n{value1|" +
                tarValue +
                "}{value|" +
                zbValue +
                "%}"
              );
            },
            // padding: [0, 600, 0, 200],
          },
          xAxis3D: {
            min: -1,
            max: 1,
          },
          yAxis3D: {
            min: -1,
            max: 1,
          },
          zAxis3D: {
            min: -1,
            max: 1,
          },
          grid3D: {
            show: false,
            z: 1,
            boxHeight: 10,
            top: "-10%",
            left: "-25%",
            viewControl: {
              // 3d效果可以放大、旋转等，请自己去查看官方配置
              alpha: 25,
              // beta: 30,
              rotateSensitivity: 1,
              zoomSensitivity: 0,
              panSensitivity: 0,
              autoRotate: true,
              distance: 190,
            },
            // 后处理特效可以为画面添加高光、景深、环境光遮蔽（SSAO）、调色等效果。可以让整个画面更富有质感。
            postEffect: {
              // 配置这项会出现锯齿，请自己去查看官方配置有办法解决
              enable: false,
              bloom: {
                enable: true,
                bloomIntensity: 0.1,
              },
              SSAO: {
                enable: true,
                quality: "medium",
                radius: 2,
              },
              // temporalSuperSampling: {
              //   enable: true,
              // },
            },
          },
          series,
        };
        return option;
      }
      myChart.setOption(option);
    },
    showDialog(name) {
      this.name = name;
      this.dialogVisible = true
    },
    showPlanNumDialog(label) {
      this.label = label;
      this.dialogVisible2 = true
    }
  },
  watch: {}
}
</script>

<style scoped lang='less'>
.jcjh {
  width: 100%;
  height: 200px;
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 20px 60px;
}
.jhzs {
  width: 431px;
  height: 150px;
  background-image: url('@/assets/zfts/ndjhzs.png');
  background-size: 100% 100%;
  padding-left: 150px;
  padding-top: 15px;
  box-sizing: border-box;
}
.text {
  width: 216px;
  height: 45px;
  font-size: 36px;
  font-family: Source Han Sans CN;
  font-weight: bold;
  font-style: italic;
  color: #d1d6df;
  line-height: 50px;
  background: linear-gradient(0deg, #acddff 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.value {
  width: 260px;
  height: 44px;
  font-size: 60px;
  font-family: DINCondensed;
  font-weight: bold;
  font-style: italic;
  color: #eed252;
}

.unit {
  font-size: 35px;
  margin-left: 10px;
}

.jhzs {
  width: 431px;
  height: 150px;
  background-image: url('@/assets/zfts/ndjhzs.png');
  background-size: 100% 100%;
  padding-left: 150px;
  padding-top: 15px;
  box-sizing: border-box;
}

.zdbm {
  width: 431px;
  height: 150px;
  background-image: url('@/assets/zfts/zdbm.png');
  background-size: 100% 100%;
}
/* 计划完成进度 */
.jhwcjd {
  width: 100%;
  height: 170px;
  box-sizing: border-box;
  padding: 0px 60px;
}

.jd-part {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.first {
  font-size: 35px !important;
}

.jhjd-bg {
  width: 960px;
  height: 48px;
  background-image: url('@/assets/zfts/jhjd-bg.png');
  background-size: 100% 100%;
  margin-top: 15px;
}

#jhjd {
  width: 960px;
  height: 40px;
}

/* 进度类型 */
.jhlx-con {
  width: 100%;
  height: 280px;
  position: relative;
}

#jhlx,#jhlx1{
  width: 100%;
  height: 300px;
  position: absolute;
  z-index: 10;
}

.jhlx-bg {
  position: absolute;z-index: 1;
  top: 100px;
  width: 389px;
  height: 163px;
  background: url("@/assets/zfts/chart-bg.png") no-repeat;background-size: 67% 77%;
}
</style>