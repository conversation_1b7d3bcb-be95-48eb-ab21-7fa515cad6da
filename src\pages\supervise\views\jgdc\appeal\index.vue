<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" :inline="true" label-width="100px">
      <el-form-item label="申诉人">
        <el-input v-model="queryParams.appealUserName" type="text" placeholder="请输入申诉人" />
      </el-form-item>
      <el-form-item label="申诉时间">
        <el-date-picker
          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px;"
          value-format="yyyy-MM-dd hh:mm:ss"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="getList"
        />
      </el-form-item>
      <el-form-item label="关键词">
        <el-input v-model="queryParams.searchValue" type="text" placeholder="请输入内容" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:record:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >
          新增
        </el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:record:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="appealList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="事件描述" align="center" prop="supervisionCheckRecord.content" />
      <el-table-column label="申诉时间" align="center" prop="happenTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.happenTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申诉原因" align="center" prop="reason" />
      <el-table-column label="申诉人" align="center" prop="appealUserName" />
      <el-table-column label="督查人" align="center" prop="supervisionCheckRecord.userName" />
      <!-- <el-table-column label="地址" align="center" prop="supervisionCheckRecord.address" /> -->
      <el-table-column label="考核条款" align="center" prop="supervisionCheckRecord.checkStandardName" show-overflow-tooltip />
      <el-table-column label="积分类型" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.supervisionCheckRecord.type == 0 ? '加分' : '扣分' }}</span>
          <!-- <span v-if="scope.row.type == 0">加分</span> -->
          <!-- <span v-if="scope.row.type == 1">扣分</span> -->
        </template>
      </el-table-column>
      <el-table-column label="积分值" align="center" prop="supervisionCheckRecord.checkStandardScore" />
      <!-- <el-table-column label="备注" align="center" prop="remark" /> -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.status != 1"
            v-hasPermi="['business:record:edit']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            style="color: #e6a23c;"
            @click="handleUpdate(scope.row, 'disabled')"
          >
            详情
          </el-button>
          <el-button
            v-if="scope.row.status == 1"
            v-hasPermi="['business:record:edit']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >
            修改
          </el-button>
          <el-button
            v-hasPermi="['business:record:remove']"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改督查考核记录对话框 -->
    <div>
      <el-dialog class="m-dialog" :close-on-click-modal="false" :title="title" :visible.sync="open" @close="cancel">
        <el-scrollbar style="height: 100%;">
          <el-row :gutter="15" style="margin-right: 10px;">
            <el-form ref="form" v-loading="formLoading" :model="form" :rules="rules" label-width="100px" :disabled="formDisabled">
              <h3 class="title">申诉信息</h3>
              <el-col :span="24">
                <el-form-item label="申诉人" prop="appealUserName">
                  <el-input v-model="form.appealUserName" disabled placeholder="请输入申诉人" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="申诉原因" prop="reason">
                  <el-input v-model="form.reason" disabled type="textarea" placeholder="请输入申诉原因" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="申诉结果" prop="result">
                  <el-input v-model="form.result" :disabled="disable" type="textarea" placeholder="请输入申诉结果" />
                </el-form-item>
              </el-col>
              <h3 class="title">督查信息</h3>
              <el-col :span="24">
                <el-form-item label="督查人" prop="userName">
                  <el-input v-model="form.userName" disabled placeholder="请输入督查人" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="事件描述" prop="content">
                  <el-input v-model="form.content" disabled type="textarea" placeholder="请输入事件描述" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="考核条款" prop="checkStandardName">
                  <el-input v-model="form.checkStandardName" disabled placeholder="请选择督查考核条款">
                    <!-- <el-button slot="append" type="primary" @click="checkVisible = true">选择</el-button> -->
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="考核标准" prop="checkStandardContent">
                  <el-input v-model="form.checkStandardContent" disabled readonly placeholder="请选择督查考核项目" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="积分类型" prop="type">
                  <el-select v-model="form.type" placeholder="请选择考核类型" disabled :style="{ width: '100%' }">
                    <el-option label="加分" value="0" />
                    <el-option label="扣分" value="1" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="积分值" prop="checkStandardScore">
                  <el-input v-model="form.checkStandardScore" disabled placeholder="请输入积分值" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="考核时间" prop="happenTime">
                  <el-date-picker
                    v-model="form.happenTime" clearable
                    size="small"
                    type="datetime"
                    disabled
                    value-format="yyyy-MM-dd hh:mm:ss"
                    placeholder="选择发生时间"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="地址" prop="address">
                  <div>
                    <el-input v-model="form.address" disabled placeholder="请选择案件地址">
                      <svg-icon slot="suffix" icon-class="map" class="svg-icon" @click="openMap = true" />
                    </el-input>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="上传图片" prop="files">
                  <!-- <MFileUpload ref="mainFile" :limit="4" :ex-data="exData" :file-list="mainFileList" not-upload-msg="请上传图片" @uploadSucces="handleFileSuccess" @error="fileError" /> -->
                  <MFileUpload ref="mainFile" :limit="4" disabled :ex-data="exData" :file-list="mainFileList" not-upload-msg="请上传图片" @uploadSucces="handleFileSuccess" @error="fileError" />
                </el-form-item>
              </el-col>
              <el-col v-if="form.groupUserName != null" :span="24">
                <el-form-item label="班长" prop="groupUserName">
                  <el-input v-model="form.groupUserName" disabled placeholder="请输入班长">
                    <!-- <el-button slot="append" type="primary" @click="groupUserVisible = true">选择</el-button> -->
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="责任人" prop="checkUserNames">
                  <el-input v-model="form.checkUserNames" disabled placeholder="请输入责任人">
                    <!-- <el-button slot="append" type="primary" @click="userVisible = true">选择</el-button> -->
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="反馈内容" prop="feedback">
                  <el-input v-model="form.feedback" disabled type="textarea" placeholder="请输入反馈内容" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="反馈图片" prop="feedbackFiles">
                  <MFileUpload ref="feedbackFile" disabled :limit="4" :ex-data="exData" :file-list="feedbackFileList" not-upload-msg="请上传图片" @uploadSucces="handleFileSuccess" @error="fileError" />
                </el-form-item>
              </el-col>
            </el-form>
          </el-row>
        </el-scrollbar>
        <div slot="footer" class="dialog-footer">
          <el-button v-if="form.status == 1" type="success" :loading="formLoading" @click="submitForm(9)">通 过</el-button>
          <el-button v-if="form.status == 1" type="primary" :loading="formLoading" @click="submitForm(10)">驳 回</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
    </div>
    <!-- 用户选择-班长 -->
    <!--<userselect v-model="groupUserVisible" :multiple="false" :select-user-keys="form.groupUserId ? [form.groupUserId]: []" :default-expanded-keys="form.groupUserId ? [form.groupUserId] : ['100']" @confirm="handleGroupUserConfirm" /> -->
    <!-- 用户选择-责任人 -->
    <!-- <userselect v-model="userVisible" :select-user-keys="form.checkUserIds ? form.checkUserIds.split(',') : []" :default-expanded-keys="form.checkUserIds ? form.checkUserIds.split(',') : ['100']" @confirm="handleUserConfirm" /> -->
    <!-- 标准选择 -->
    <!-- <CheckStandard :visible.sync="checkVisible" @confirm="handleCheckConfirm" /> -->
    <!-- 申诉列表 -->
    <!-- <appealList :visible.sync="appealListVisible" :check-record-id="checkRecordId" @confirm="handleAppealListConfirm" /> -->
    <!-- 地图选择 -->
    <!-- <tdtMap ref="bindmap" :visible.sync="openMap" append-to-body :map-search="true" :dw="form" @onlnglat="onlnglat" /> -->
  </div>
</template>

<script>
import { listAppeal, getAppeal, updateAppeal, delAppeal, exportAppeal } from '@/api/supervise/appeal'
import { getFiles } from '@/api/supervise/swit'
// import userselect from '@/components/userselect/index.vue'
// import CheckStandard from './checkStandard.vue'
// import appealList from './appealList.vue'
import MFileUpload from '@/components/MFileUpload/index'
// import tdtMap from '@/components/tdtMap/tdtMap'

export default {
  name: 'Appeal',
  components: {
    // userselect,
    // CheckStandard,
    // appealList,
    MFileUpload
    // tdtMap
  },
  data() {
    var checkGroupUserName = (rule, value, callback) => {
      if (value == null && this.form.checkUserNames == null) {
        callback(new Error('班长和责任人其中一个必填'))
      } else {
        callback()
      }
    }
    var checkUserName = (rule, value, callback) => {
      if (value == null && this.form.groupUserName == null) {
        callback(new Error('班长和责任人其中一个必填'))
      } else {
        callback()
      }
    }
    return {
      typeData: [{id: '0', value: '加分'}, {id: '1', value: '扣分'}],
      exData: { status: 1, tableName: 'supervision_check_record' },
      mainFileList: [],
      feedbackFileList: [],
      groupUserVisible: false,
      userVisible: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 选中督查人id数组
      userIds: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 督查考核记录表格数据
      appealList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        type: null,
        searchValue: '',
        dateRange: [],
        appealUserName: ''
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        userName: [
          { required: true, message: '督查人不能为空', trigger: 'change' }
        ],
        content: [
          { required: true, message: '事件描述不能为空', trigger: 'change' }
        ],
        checkStandardName: [
          { required: true, message: '考核条款不能为空', trigger: 'change' }
        ],
        checkStandardContent: [
          { required: true, message: '考核标准不能为空', trigger: 'change' }
        ],
        type: [
          { required: true, message: '积分类型不能为空', trigger: 'change' }
        ],
        checkStandardScore: [
          { required: true, message: '积分值不能为空', trigger: 'change' }
        ],
        happenTime: [
          { required: true, message: '考核时间不能为空', trigger: 'change' }
        ],
        address: [
          { required: true, message: '地址不能为空', trigger: 'change' }
        ],
        // files: [
        //   { required: true, message: '图片不能为空', trigger: 'change' }
        // ],
        groupUserName: [
          { validator: checkGroupUserName, message: '班长和责任人其中一个必填', trigger: 'blur'}
        ],
        checkUserNames: [
          { validator: checkUserName, message: '班长和责任人其中一个必填', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '状态 0-正常 1-停用不能为空', trigger: 'blur' }
        ],
        delFlag: [
          { required: true, message: '删除标志 0-正常 2-删除不能为空', trigger: 'blur' }
        ],
        reason: [
          { required: true, message: '申诉原因不能为空', trigger: 'change' }
        ],
        result: [
          { required: true, message: '申诉结果不能为空', trigger: 'blur' }
        ]
      },
      checkVisible: false,
      appealListVisible: false,
      formDisabled: false,
      formLoading: false,
      openMap: false,
      // 记录id
      checkRecordId: 0,
      disable: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    handleAppealListConfirm() {
      console.log('1')
    },
    loadAppeal(checkRecordId) {
      this.appealListVisible = true
      this.checkRecordId = checkRecordId
      console.log(this.checkRecordId)
    },
    handleFileSuccess() {
      this.msgSuccess('操作成功')
      this.formLoading = false
      this.open = false
      this.getList()
    },
    onlnglat(lenlat) {      // 经纬度  详细地址
      let {lng: longitude, lat: latitude, address} = lenlat
      this.form = {...this.form, longitude, latitude, address}
      this.openMap = false
    },
    fileError() {
      this.formLoading = false
    },
    handleGroupUserConfirm({ id, name }) {
      this.form = { ...this.form, groupUserId: id, groupUserName: name }
    },
    handleUserConfirm({ id, name }) {
      this.form = { ...this.form, checkUserIds: id, checkUserNames: name }
    },
    handleCheckConfirm(row) {
      this.form = { ...this.form, checkStandardId: row.checkStandardId, checkStandardName: row.title, checkStandardContent: row.content, type: row.type }
    },
    /** 查询督查考核记录列表 */
    getList() {
      this.loading = true
      const { dateRange, type, searchValue, pageNum, pageSize, appealUserName } = this.queryParams
      let params = { pageNum, pageSize }
      if (dateRange && dateRange.length) params = { ...params, searchStartTime: dateRange[0], searchEndTime: dateRange[1] }
      if (type) params = { ...params, type }
      if (searchValue) params = { ...params, searchValue }
      if (appealUserName) params = { ...params, appealUserName }
      listAppeal(params).then(response => {
        this.appealList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.mainFileList = []
      this.feedbackFileList = []
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        userName: null,
        checkRecordId: null,
        content: null,
        groupUserId: null,
        groupUserName: null,
        checkUserIds: null,
        checkUserNames: null,
        userId: null,
        address: null,
        longitude: null,
        latitude: null,
        type: null,
        checkStandardId: null,
        checkStandardName: null,
        checkStandardScore: null,
        happenTime: this.parseTime(new Date()),
        status: '0',
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null,
        appealUserName: null,
        appealHappenTime: this.parseTime(new Date()),
        reason: null,
        result: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        type: null,
        searchValue: '',
        dateRange: []
      }
      // this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.userIds = selection.map(item => item.userId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    // handleAdd() {
    //   this.formDisabled = false
    //   this.reset()
    //   this.open = true
    //   this.title = '添加督查考核记录'
    // },
    /** 修改按钮操作 */
    async handleUpdate(row, type) {
      this.reset()
      const id = row.id
      const checkRecordId = row.checkRecordId
      this.formLoading = true
      this.open = true
      let [formRes, fileRes] = await Promise.all([
        getAppeal(id),
        getFiles({ businessId: checkRecordId, tableName: 'supervision_check_record' })
      ]).catch(() => this.formLoading = false)
      // 数据请求完成
      if (type === 'disabled') this.formDisabled = true
      else this.formDisabled = false
      if (formRes) {
        this.form = { ...formRes.data.supervisionCheckRecord }
        // 申诉人
        this.form.appealUserName = formRes.data.appealUserName
        // 申诉时间
        this.form.appealHappenTime = formRes.data.happenTime
        // 申诉原因
        this.form.reason = formRes.data.reason
        // 申诉结果
        // console.log(formRes.data.result)
        if (formRes.data.result) this.form.result = formRes.data.result
        // 申诉id
        this.form.id = formRes.data.id
        // 申诉状态
        this.form.status = formRes.data.status
        console.log(this.form.status)
        if (this.form.status != 1) this.disable = true
        else this.disable = false
      }
      this.title = '申诉详情'
      this.formLoading = false
      // 图片请求
      // this.mainFileList = fileRes.rows.map(item => {
      //   return { name: item.displayName, url: `/zqzfj${item.filePath}`, ...item }
      // })
      fileRes.rows.forEach(item => {
        const files =  { name: item.displayName, url: `/zqzfj${item.filePath}`, ...item }
        if (item.status == 1) {
          this.mainFileList.push(files)
        } else {
          // this.feedbackFileList.push(files)
          this.feedbackFileList = [files]
        }
      })
    },
    // subOverSubmit(status) {
    //   this.$confirm('是否确认办结？', '提示', { type: 'warning' }).then(() => {
    //     this.submitForm(status)
    //   }).catch(() => {})
    // },
    /** 提交按钮 */
    submitForm(status) {
      if (this.form.userId != this.$store.getters.uid) return this.msgError('您不是督查人，没有操作权限！')

      // this.form.status = status
      const name = {9: '通过', 10: '驳回'}
      this.$confirm(`是否${name[status]}此申请？`, '提示', { type: 'warning' }).then(() => {
        this.$refs['form'].validate(valid => {
          if (valid) {
            this.formLoading = true
            const params = { ...this.form, status }
            updateAppeal(params).then(() => {
              this.formLoading = false
              this.open = false
              this.mainFileList = []
              this.feedbackFileList = []
              this.reset()
              this.getList()

            }).catch(() => this.formLoading = false)

          }

        })
      }).catch(() => {})
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      if (this.userIds.length != 0) {
        if (!this.userIds.every(item => item == this.$store.getters.uid)) return this.msgError('您不是督查人，没有操作权限！')
      } else {
        if (row.userId != this.$store.getters.uid) return this.msgError('您不是督查人，没有操作权限！')
      }

      const id = row.id || this.ids
      this.$confirm('是否确认删除督查考核记录编号为"' + id + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return delAppeal(id)
      }).then(() => {
        this.getList()
        this.msgSuccess('删除成功')
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams
      this.$confirm('是否确认导出所有督查考核记录数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return exportAppeal(queryParams)
      }).then(response => {
        this.download(response.msg)
      })
    }
  }
}
</script>
