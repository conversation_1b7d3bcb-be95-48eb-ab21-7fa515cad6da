import {request} from '@/utils/request'

// 查询人员(游客、志愿者、管理员)列表
export function listUser(query) {
  return request({
    url: '/business/vol/user/list',
    method: 'get',
    params: query
  })
}

// 查询人员(游客、志愿者、管理员)详情
export function getUser(userId) {
  return request({
    url: '/business/vol/user/' + userId,
    method: 'get'
  })
}

// 新增人员(游客、志愿者、管理员)
export function addUser(data) {
  return request({
    url: '/business/vol/user/add',
    method: 'post',
    data: data
  })
}

// 修改人员(游客、志愿者、管理员)
export function updateUser(data) {
  return request({
    url: '/business/vol/user/edit',
    method: 'post',
    data: data
  })
}

// 删除人员(游客、志愿者、管理员)
export function delUser(userId) {
  return request({
    url: '/business/vol/user/remove/' + userId,
    method: 'post'
  })
}

// 导出人员(游客、志愿者、管理员)
export function exportUser(query) {
  return request({
    url: '/business/vol/user/export',
    method: 'get',
    params: query
  })
}
