<template>
  <div class="container">
    <data-header />
    <div class="container-inner">
      <div class="left">
        <!-- 标题 -->
        <div class="title">
          <span class="t-top">视频列表</span>
          <span class="t-bottom">ESSENTIAL INFORMATION</span>
        </div>
        <!-- 树结构搜索 -->
        <div class="left-search">
          <el-input v-model="searchValue">
            <i slot="suffix" class="el-input__icon el-icon-search" style="color: #03c9d7;" />
          </el-input>
        </div>
        <!-- 监控树 -->
        <div class="left-tree">
          <el-scrollbar ref="scrollContainer" class="scroll-container">
            <el-tree ref="tree" v-loading="loading" :data="treeData" :highlight-current="true" :filter-node-method="filterNode" @node-click="handleNodeClick">
              <div slot-scope="{ data }" class="custom-tree-node">
                <span v-if="data.type == 2" class="monitor-icon" />
                <span>{{ data.name }}</span>
              </div>
            </el-tree>
          </el-scrollbar>
        </div>
      </div>
      <div class="right">
        <el-date-picker
          v-model="dateAry"
          class="play-back-btn"
          size="mini"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']"
          @focus="handleFocus"
          @change="handleChange"
          @blur="handleBlur"
        />
        <!-- <el-button class="play-back-btn" type="text" @click="handlePlayBack">设置回放日期</el-button> -->
        <el-button class="change-btn" type="text" @click="handleChangeMode">切换播放模式</el-button>
        <div class="right-video">
          <videoBox ref="videoBox" :camera-index-code.sync="cameraIndexCode" />
        </div>
        <div class="right-his">
          <div class="his-title">播放记录</div>
          <div class="his-list">
            <el-scrollbar ref="scrollContainer" :vertical="false" class="scroll-container" @wheel.native.prevent="handleScroll">
              <div v-for="(item,idx) in hisList" :key="idx" class="item" @click="handleNodeClick(item)">
                <div class="item-inner" />
                <div class="item-name">{{ item.name }}</div>
              </div>
              <div v-if="!hisList.length" class="item">
                <span>暂无记录</span>
              </div>
            </el-scrollbar>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import dataHeader from '../components/dataHeader/index.vue'
import videoBox from '../components/videoBox/index.vue'
import { getTreeData } from '@/api/board/monitor/index'
import '@/assets/styles/vol.scss'
export default {
  components: {
    dataHeader,
    videoBox
  },
  data() {
    return {
      /* 模糊搜索字段 */
      searchValue: '',
      /* 监控编码 */
      cameraIndexCode: '',
      treeData: [],
      loading: false,
      hisList: [],
      showDate: false,
      dateAry: []
    }
  },
  computed: {
    scrollWrapper() {
      return this.$refs.scrollContainer.$refs.wrap
    }
  },
  watch: {
    searchValue(val) {
      this.$refs.tree.filter(val)
    }
  },
  mounted() {
    this.fetchData()
    const hisList = localStorage.getItem('hisList')
    if (hisList) {
      try {
        this.hisList = JSON.parse(hisList)
      } catch (error) {
        this.hisList = []
      }
    }
  },
  methods: {
    fetchData() {
      this.loading = true
      getTreeData().then(res => {
        this.treeData = res.data
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    handleChangeMode() {
      this.$refs.videoBox.changePlayMode()
    },
    handleFocus() {
      this.$refs.videoBox.windowHide()
    },
    handleBlur() {
      this.$refs.videoBox.windowShow()
    },
    handleChange(e) {
      this.$refs.videoBox.setPlayback(e)
    },
    handleNodeClick(data) {
      if (data.type == 2) {
        this.cameraIndexCode = data.nodeKey
        if (this.hisList.length >= 10) {
          this.hisList.unshift({ name: data.name, nodeKey: data.nodeKey, type: 2 })
          this.hisList.pop()
        } else {
          this.hisList.unshift({ name: data.name, nodeKey: data.nodeKey, type: 2 })
        }
        localStorage.setItem('hisList', JSON.stringify(this.hisList))
      }
    },
    filterNode(value, data) {
      if (!value) return true
      return data.name ? data.name.indexOf(value) !== -1 : false
    },
    handleScroll(e) {
      const eventDelta = e.wheelDelta || -e.deltaY * 40
      const $scrollWrapper = this.scrollWrapper
      $scrollWrapper.scrollLeft = $scrollWrapper.scrollLeft - eventDelta / 4
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  height: 100%;
  color: #fff;
  background: #0e0d33;
  .scroll-container {
    white-space: nowrap;
    position: relative;
    overflow: hidden;
    width: 100%;
    height: 100%;
    ::v-deep {
      .el-scrollbar__bar {
        bottom: 0;
      }
      .el-scrollbar__wrap {
        overflow-x: hidden;
      }
      .el-tree-node {
        > .el-tree-node__children {
          overflow: visible !important;
        }
      }
    }
  }
  .title {
    // height: pxtorem(66);
    padding-top: pxtorem(40);
    padding-left: pxtorem(65);
    background: url(@/assets/images/title-icon.png) no-repeat pxtorem(20) bottom / pxtorem(36);
    span {
      display: block;
    }
    .t-top {
      font-size: pxtorem(18);
      text-shadow: 0 0 6px #00f7ff;
    }
    .t-bottom {
      font-size: 12px;
      transform: scale(0.6);
      transform-origin: left;
    }
  }
  &-inner {
    height: calc(100% - 0.52083rem);
    display: flex;
    padding: pxtorem(40) pxtorem(30) pxtorem(30);
  }
  .left {
    width: pxtorem(395);
    height: 100%;
    background: url(@/assets/images/monitor-left.png) no-repeat center center / 100% 100%;
    &-search {
      margin: pxtorem(20) pxtorem(30) pxtorem(0) pxtorem(20);
      ::v-deep input {
        background: none;
        border-color: #03c9d7;
        color: #03c9d7;
      }
    }
    &-tree {
      margin: pxtorem(20) pxtorem(30) pxtorem(0) pxtorem(20);
      // height: pxtorem(205);
      height: calc(100% - 1.06771rem);
      .el-tree {
        background: none;
        color: #fff;
        ::v-deep .monitor-icon {
          display: inline-block;
          width: pxtorem(16);
          height: pxtorem(16);
          margin-right: 5px;
          background: url(@/assets/images/tree-monitor-icon.png) no-repeat center center / 100%;
        }
        ::v-deep .el-tree-node__content:hover {
          background: none;
          color: #c9c9c9;
        }
      }
      .el-tree--highlight-current {
        ::v-deep .el-tree-node.is-current {
          & > .el-tree-node__content {
            background: transparent;
            color: #03c9d7;
          }
        }
      }
    }
  }
  .right {
    width: pxtorem(1435);
    height: 100%;
    margin-left: pxtorem(30);
    background: url(@/assets/images/monitor-right.png) no-repeat center center / 100% 100%;
    position: relative;
    .change-btn {
      position: absolute;
      right: pxtorem(50);
      top: pxtorem(15);
      color: #01e9f4;
    }
    .play-back-btn {
      width: 500px;
      position: absolute;
      left: pxtorem(42);
      top: pxtorem(20);
      background: none;
      border-color: #03c9d7;
      ::v-deep input {
        color: #03c9d7;
        background: none;
      }
      // color: #01e9f4;
    }
    .right-video {
      width: pxtorem(1350);
      height: calc(100% - 1.48438rem);
      margin: pxtorem(50) auto 0;
      background: rgba($color: #000, $alpha: 0.2);
    }
    .right-his {
      width: pxtorem(1350);
      height: pxtorem(160);
      margin: pxtorem(15) auto 0;
      background: rgba($color: #000, $alpha: 0.2);
      .his-title {
        font-size: pxtorem(18);
        color: #fff;
        text-shadow: 0 0 6px #00f7ff;
        padding: pxtorem(15) 0 0 pxtorem(20);
      }
      .his-list {
        margin: pxtorem(10) pxtorem(20) 0 pxtorem(20);
        height: pxtorem(110);
        .item {
          display: inline-block;
          width: pxtorem(135);
          height: pxtorem(100);
          margin-right: pxtorem(20);
          &-inner {
            width: 100%;
            height: pxtorem(80);
            background: #000 url(@/assets/images/play-circle-fill.png) no-repeat center center / pxtorem(32);
            cursor: pointer;
          }
          &-name {
            color: #fff;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-size: pxtorem(14);
            margin-top: pxtorem(5);
            text-align: center;
          }
        }
      }
    }
  }
}
</style>
