<template>
  <div v-loading="loading" class="container">
    <div ref="map" class="map" />
    <mMenu :map-obj="mapObj || {}" />
    <!-- 出租车弹窗 -->
    <taxiWindowInfo ref="taxiWindowInfo" :visible.sync="taxiVisible" :detail-id="taxiId" />
    <!-- 海康视频控件 -->
    <windowVideo v-model="hkVideoVisible" :video-name="winVideoName" :video-id="hkVideoId" />
    <!-- 四位一体详情 -->
    <fourWindow :visible.sync="fourWindowVisible" title="四位一体详情" :hand-disabled="false" :type-data="typeData" :detail-id="fourWindowId" :form-disabled="true" />
    <!-- 黄牛处置 -->
    <el-dialog :close-on-click-modal="false" title="黄牛处置详情" :visible.sync="toutWindowVisible" width="1000px">
      <toutWindow v-if="toutWindowVisible" :detail-id="toutWindowId" :qd="true" @oncancel="toutWindowVisible = false" />
    </el-dialog>
    <!-- 绘制区域 -->
    <div class="draw">
      <!-- <el-button type="primary" @click="handleDrawRect">
        <span class="rect" />
        <span>矩形</span>
      </el-button>
      <el-button type="primary" @click="handleDrawCir">
        <span class="cir" />
        <span>圆形</span>
      </el-button> -->
      <el-button type="primary" @click="handleDrawPol">
        <span class="i-dbx" />
        <span>多边形</span>
      </el-button>
    </div>
    <!-- 框选数据展示 -->
    <div>
      <el-dialog class="m-dialog" :close-on-click-modal="false" title="框选数据" :visible.sync="open" @close="dialogClose">
        <el-scrollbar style="height: 100%;">
          <el-tabs v-model="tabValue" type="border-card">
            <el-tab-pane v-for="(item,idx) in checkData" :key="idx" :label="`${names[item.key]}[${item.list.length}]`" :name="item.key">
              <el-table :data="item.list" height="48vh">
                <el-table-column label="名称" align="center" prop="name" />
                <el-table-column label="类型" align="center">
                  <template><span>{{ names[item.key] }}</span></template>
                </el-table-column>
                <el-table-column label="类型" align="center">
                  <template slot-scope="scope">
                    <el-button type="text" @click="handleOpenWindow(scope.row)">查看</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>
        </el-scrollbar>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { listSuperviseMap } from '@/api/supervise/board'
import { createAllOverLay, eventChangeData } from '@/pages/board/views/map/setMarker'
import taxiWindowInfo from '@/pages/board/views/components/windowInfo/taxi/index.vue'
import windowVideo from '@/pages/board/views/components/windowVideo/index.vue'
import fourWindow from '@/pages/supervise/views/jgdc/quaternity/components/fromlist.vue'
import toutWindow from '@/pages/case/views/tout/hncz/components/fromlist.vue'
import mMenu from './menu/index.vue'
import { createSocket } from '@/utils/websocket'

import { loadMap } from '@/utils/amapLoad'

export default {
  components: {
    taxiWindowInfo,
    windowVideo,
    fourWindow,
    toutWindow,
    mMenu
  },
  data() {
    return {
      loading: false,
      mapObj: null,
      taxiVisible: false,
      taxiId: undefined,
      hkVideoVisible: false,
      winVideoName: '',
      hkVideoId: '',
      typeData: [{id: 1, value: '环卫保洁'}, {id: 2, value: '园林'}, {id: 3, value: '绿化'}, {id: 4, value: '市政'} ],
      fourWindowVisible: false,
      fourWindowId: 0,
      toutWindowVisible: false,
      toutWindowId: 0,
      rectTool: null,
      circleTool: null,
      polygonTool: null,
      allData: {},
      checkData: [],
      open: false,
      names: { taxi: '出租车', monitor: '监控', event: '四位一体', tout: '黄牛处置' },
      tabValue: 'taxi',
      overLayStatus: {
        'monitor': true,
        'taxi': true,
        'event': true,
        'tout': true
      }
    }
  },
  mounted() {
    this.mapInit()
  },
  destroyed() {
    window.removeEventListener('onmessageWS', this.getsocketData)
  },
  methods: {
    mapInit() {
      this.mapObj = new window.T.Map(this.$refs.map)
      this.mapObj.centerAndZoom(new window.T.LngLat(119.63126, 29.11181), 18)
      this.mapObj.setMaxZoom(22)
      // this.mapObj.setStyle('indigo')
      // 初次创建坐标点
      this.overLayInit()
      // 创建绘制工具
      this.rectTool = new window.T.RectangleTool(this.mapObj)
      this.circleTool = new window.T.CircleTool(this.mapObj)
      this.polygonTool = new window.T.PolygonTool(this.mapObj, { showLabel: false })
      // 绘制完成事件
      this.rectTool.addEventListener('draw', e => {
        console.log(e.currentBounds)
      })
      this.circleTool.addEventListener('drawend', e => {
        console.log(e.currentCenter, e.currentRadius)
      })
      this.polygonTool.addEventListener('draw', e => {
        this.getAMap().then(AMap => {
          const path = e.currentLnglats.map(item => {
            return [item.lng, item.lat]
          })
          this.checkData = Object.keys(this.allData).map(key => {
            let item = { key, list: [] }
            if (Array.isArray(this.allData[key]) && this.allData[key]) {
              this.allData[key].forEach(i => {
                const flag = AMap.GeometryUtil.isPointInRing([i.longitude, i.latitude], path)
                if (flag) item.list.push(i)
              })
            }
            return item
          })
          this.open = true
          // 移除绘制的多边形
          if (Array.isArray(e.allPolygons) && e.allPolygons.length) {
            e.allPolygons.forEach(item => {
              this.mapObj.removeOverLay(item)
            })
          }
          console.log(this.checkData)
        })
      })
    },
    handleOpenWindow(row) {
      this.overLayEvent(row)
    },
    dialogClose() {
      this.checkData = []
    },
    getAMap() {
      return new Promise((resolve, reject) => {
        loadMap(['AMap.GeometryUtil']).then(
          AMap => resolve(AMap),
          () => reject()
        )
      })
    },
    handleDrawRect() {
      this.rectTool.open()
    },
    handleDrawCir() {
      this.circleTool.open()
    },
    handleDrawPol() {
      this.polygonTool.open()
    },
    overLayInit() {
      this.loading = true
      listSuperviseMap().then(res => {
        this.loading = false
        // 创建覆盖物
        if (Array.isArray(res.data.event) && res.data.event.length) {
          let tout = []
          const event = res.data.event.filter(item => {
            if (item.caseType == 'tout') {
              tout.push(item)
            }
            return item.caseType != 'tout'
          })
          res.data.event = event
          if (tout.length) res.data.tout = tout
        }
        this.allData = res.data
        createAllOverLay(this.mapObj, res.data, e => {
          this.overLayEvent(e.target.dataSet)
        })

        // 覆盖物渲染完毕，链接websocket
        createSocket({ screen: this.$store.getters.uid })
        window.addEventListener('onmessageWS', this.getsocketData)
      }).catch(() => {
        this.loading = false
      })
    },
    // 接收websocket返回的数据
    getsocketData(e) {
      const data = e && e.detail.data
      try {
        const msgData = JSON.parse(data)
        if (!Array.isArray(msgData)) {
          let type = msgData.type
          if (msgData.caseType == 'four' || msgData.caseType == 'tout' || type == 'taxi') {
            eventChangeData(type, msgData, this.mapObj, this.overLayStatus[type], e => this.overLayEvent(e.target.dataSet))
          }
        }
      } catch (error) {
        this.$message.error('监听数据格式错误，请联系管理员')
      }
    },
    overLayEvent(data) {
      if (data.type == 'taxi') {
        this.taxiVisible = true
        this.taxiId = data.vehicleIndexCode
      } else if (data.type == 'monitor') {
        this.hkVideoVisible = true
        this.winVideoName = data.name
        this.hkVideoId = data.code
      } else if (data.type == 'event') {
        this.openEventWindow(data.caseType, data.id)
      }
    },
    openEventWindow(type, id) {
      if (type == 'four') {
        this.fourWindowVisible = true
        this.fourWindowId = id
      } else if (type == 'tout') {
        this.toutWindowVisible = true
        this.toutWindowId = id
      } else {
        this.$message.error('信息错误，请联系管理员')
      }
    }

  }
}
</script>

<style scoped lang="scss">
.container {
  position: absolute;
  left: 0;
  right: 0;
  top: 84px;
  bottom: 0;
  .map {
    width: 100%;
    height: 100%;
  }
  .draw {
    padding: 10px;
    box-shadow: 0 4px 14px rgb(0 0 0 / 20%);
    position: absolute;
    left: pxtorem(30);
    top: pxtorem(30);
    background: #fff;
    z-index: 900;
    border-radius: 10px;
    .rect {
      display: inline-block;
      width: 20px;
      height: 14px;
      border: 1px solid #fff;
      vertical-align: top;
      margin-right: 5px;
    }
    .cir {
      display: inline-block;
      width: 14px;
      height: 14px;
      border: 1px solid #fff;
      border-radius: 50%;
      vertical-align: top;
      margin-right: 5px;
    }
    .i-dbx {
      display: inline-block;
      width: 14px;
      height: 14px;
      background: url(../../../../assets/images/dbx.png) no-repeat center center / contain;
      vertical-align: top;
      margin-right: 5px;
    }
  }
}
</style>
