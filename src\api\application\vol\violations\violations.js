import {request} from '@/utils/request'

// 查询志愿者违规记录列表
export function listViolations(query) {
  return request({
    url: '/business/vol/violations/list',
    method: 'get',
    params: query
  })
}

// 查询志愿者违规记录详细
export function getViolations(id) {
  return request({
    url: '/business/vol/violations/' + id,
    method: 'get'
  })
}

// 新增志愿者违规记录
export function addViolations(data) {
  return request({
    url: '/business/vol/violations/add',
    method: 'post',
    data: data
  })
}

// 修改志愿者违规记录
export function updateViolations(data) {
  return request({
    url: '/business/vol/violations/edit',
    method: 'post',
    data: data
  })
}

// 删除志愿者违规记录
export function delViolations(id) {
  return request({
    url: '/business/vol/violations/remove/' + id,
    method: 'post'
  })
}

// 导出志愿者违规记录
export function exportViolations(query) {
  return request({
    url: '/business/vol/violations/export',
    method: 'get',
    params: query
  })
}
