<template>
  <div>
    <CommonTitle text='案件回访'></CommonTitle>
    <div
      class="ajhfChart"
      id="chartAjhf"
      style="width: 1030px; height: 770px"
    ></div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import { getAjhfChart, getAjhfChartTotal } from '@/api/zfts'
export default {
  name: 'index',
  components: {
    CommonTitle
  },
  data() {
    return {
      year: localStorage.getItem("year"),
      city: localStorage.getItem("city"),
    }
  },
  computed: {},
  mounted() {
    this.$bus.$on('cityChange', (city) => {
      this.city = city;
      this.initApi(city,localStorage.getItem("year"));
    })
    this.$bus.$on('yearChange', (year) => {
      this.year = year;
      this.initApi(localStorage.getItem("city"),year);
    })
    this.initApi(localStorage.getItem("city"),localStorage.getItem("year"));
  },
  methods: {
    initApi(city,year) {
      if (city == "金华市") {
        getAjhfChartTotal({region:city}).then(res => {
          this.getChartAjhf(localStorage.getItem("city"),'chartAjhf',res.data?res.data:[])
        })
      } else {
        getAjhfChart({region:city}).then(res => {
          this.getChartAjhf(localStorage.getItem("city"),'chartAjhf',res.data?res.data:[])
        })
      }
    },
    getChartAjhf(city, id, chartData) {
      let myEc = this.$echarts.init(document.getElementById(id));
      let xdata = [];
      let ydata = [[], [], [], []];
      if (city == "金华市") {
        chartData.forEach((item) => {
          xdata.push(item.region);
          ydata[0].push(item.hfTotal);
          ydata[1].push(item.hfz);
          ydata[2].push(item.wfh);
          ydata[3].push(item.wxhfs);
        });
      } else {
        chartData.forEach((item) => {
          xdata.push(item.date);
          ydata[0].push(item.yhfs);
          ydata[1].push(item.hfzs);
          ydata[2].push(item.whfs);
          ydata[3].push(item.wxhfs);
        });
      }
      let legend = ["已回访", "回访中", "未回访", "无需回访"];
      let color = ["76,152,251", "172,171,52", "245,102,121", "72,224,64"];
      let option = {
        tooltip: {
          trigger: "axis",
          backgroundColor: "rgba(51, 51, 51, 0.7)",
          borderWidth: 0,
          axisPointer: {
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
          textStyle: {
            color: "white",
            fontSize: "24",
          },
        },
        grid: {
          left: "5%",
          right: "5%",
          top: "15%",
          bottom: "15%",
          containLabel: true,
        },
        legend: {
          top: 10,
          left: "center",
          itemWidth: 16,
          itemHeight: 16,
          itemGap: 50,
          textStyle: {
            fontSize: 24,
            color: "#fff",
            padding: [3, 0, 0, 0],
          },
          // data: legend,
        },
        xAxis: [
          {
            type: "category",
            data: xdata,
            axisLine: {
              lineStyle: {
                color: "rgb(119,179,241,.4)", // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              // rotate: 30,
              textStyle: {
                color: "#D6E7F9",
                fontSize: 20,
              },
            },
          },
        ],
        yAxis: [
          {
            name: "",
            type: "value",
            nameTextStyle: {
              fontSize: 24,
              color: "#D6E7F9",
              padding: 5,
            },
            splitLine: {
              lineStyle: {
                color: "rgb(119,179,241,.4)",
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 24,
                color: "#D6E7F9",
              },
            },
          },
        ],
        series: [],
      };
      for (var i = 0; i < legend.length; i++) {
        option.series.push({
          name: legend[i],
          type: "bar",
          stack: "总量",
          barWidth: "30",
          label: {
            show: false,
            position: "insideRight",
          },
          itemStyle: {
            normal: {
              color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "rgba(" + color[i] + ",0.99)",
                },
                {
                  offset: 1,
                  color: "rgba(" + color[i] + ",0)",
                },
              ]),
              barBorderRadius: 4,
            },
          },
          data: ydata[i],
        });
      }
      myEc.setOption(option);
      myEc.getZr().on("mousemove", (param) => {
        myEc.getZr().setCursorStyle("default");
      });
    },
  },
  watch: {}
}
</script>

<style scoped>

</style>