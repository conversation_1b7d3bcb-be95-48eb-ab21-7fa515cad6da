<template>
  <div class="choosePop" v-show="show">
    <el-radio-group v-model="jlIndex">
      <el-radio :label="item.label" v-for="(item,i) in jlList" :key="i" @change="funJl">
        {{ item.name }}
      </el-radio>
    </el-radio-group>
    <div class="type_btn s-m-t-15">
      <span @click="FunLx(item.label)" v-for="(item,i) in lxList" :key="i" :class="lxIndex == item.label ? 'active_type' : ''">
        {{ item.name }}
      </span>
    </div>
    <div class="table s-m-t-15">
      <div class="theader s-flex">
        <span style="width: 370px">名称</span>
        <span style="width: 11%">距离</span>
        <span style="width: 370px">标签</span>
      </div>
      <div v-show="videoList.length == 0" style="text-align: center; line-height: 300px">暂无数据</div>
      <el-checkbox-group v-model="clickGroup" :max="4">
        <el-checkbox v-for="item in videoList" :label="item.code" :key="item.code" @change="changeVideoList">
          <div class="s-flex tbody">
            <span style="width: 370px; color: #eee">{{ item.name }}</span>
            <span style="width: 11%; text-align: center; color: #fff">{{ item.jl }}km</span>
            <span style="color: #54b5d5; width: 370px">{{ item.bq }}</span>
          </div>
        </el-checkbox>
      </el-checkbox-group>
    </div>
    <div class="overBtn s-flex s-row-around s-m-t-20">
      <span @click="close()">取消</span>
      <span @click="isOpen">确定</span>
    </div>
  </div>
</template>

<script>
import { getDwgzVideoByPoint } from '@/api/zhdd'

export default {
  name: 'index',
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      jlIndex: 3,
      jlList: [
        { name: '1km', label: 1 },
        { name: '2km', label: 2 },
        { name: '3km', label: 3 },
      ],
      lxIndex: '',
      lxList: [
        { name: '高空', label: 1 },
        { name: '治安', label: 2 },
      ],
      clickGroup: [],
      videoListAll: [],
      videoList: [],
      sgid: null,
      point_center: '119.641369, 29.112204',
    }
  },
  computed: {},
  mounted() {
    this.searchVideo({
      sgid: this.$parent.sgId,
      point: this.$parent.video_point,
      videoList: this.$parent.sgVideoList,
    })
  },
  methods: {
    close() {
      this.$emit('close')
    },
    isOpen() {
      let clickGroupData = []
      for (let index = 0; index < this.clickGroup.length; index++) {
        clickGroupData.push(this.videoListAll.find((a) => a.code == this.clickGroup[index]))
      }
      this.$emit('changeVideo', clickGroupData)
      this.close()
    },
    funJl(e) {
      this.videoList = []
      if (this.sgid && this.sgid != '') {
        this.findVideoByid(this.sgid)
      } else {
        this.findVideoByPoint(this.point_center)
      }
    },
    FunLx(e) {
      this.lxIndex = this.lxIndex == e ? '' : e
      if (this.sgid && this.sgid != '') {
        this.findVideoByid(this.sgid)
      } else {
        let obj = this.lxList.filter((a) => a.label == this.lxIndex)
        if (obj.length == 0) {
          this.videoList = this.videoListAll
        } else {
          this.videoList = this.videoListAll.filter((a) => a.bq.indexOf(obj[0].name) > 0)
        }
      }
    },
    changeVideoList(e) {
      // console.log("复选框事件", this.clickGroup, e);
    },
    searchVideo(data) {
      this.clickGroup = data.videoList.map((a) => a.code)
      this.point_center = data.point
      this.sgid = data.sgid
      if (data.sgid && data.sgid !== '') {
        this.findVideoByid(data.sgid)
      } else {
        this.findVideoByPoint(this.point_center)
      }
    },
    findVideoByPoint(point) {
      let this_ = this
      getDwgzVideoByPoint({
        type: 'type=zbjk',
        distance: `${this_.jlIndex}`, //1km=0.1  2km=0.2  3km=0.3
        point: point,
      }).then(function (data) {
        this_.videoListAll = data.data.pointData.map((a) => {
          let addinfo = JSON.parse(a.addinfo)
          return {
            name: a.name || '-',
            code: addinfo.chncode || '-',
            bq: addinfo.labels + '|' + addinfo.hymc || '-',
            jl: a.jl || '-',
          }
        })
        this_.FunLx(this_.lxIndex)
      })
    },
    findVideoByid(sgid) {
      let this_ = this
      getDwgzVideoById({
        sgid: sgid,
        jl: this_.jlIndex,
        lx: this_.lxIndex,
      }).then((res) => {
        // 接口调用不通 临时数据
        var datas = [
          {
            bq: '建筑住宅/楼宇/金义新区/金东鸿泰豪苑楼顶',
            code: '33071007041321087394',
            jl: 0.89,
            name: '曹宅鸿泰豪苑楼顶高倍球_DH201912LT雪亮X94',
          },
          {
            code: '33070354001321081061',
            name: '金东鸿泰豪苑楼顶_DH201912LT雪亮X61',
            jl: 1.45,
            bq: '建筑住宅/楼宇/金义新区/金东鸿泰豪苑楼顶',
          },
          {
            code: '33070399001321047685',
            name: '金汇路葱草街西口南侧行闯_DH201912HS雪亮X85',
            jl: 1.49,
          },
          {
            code: '33070354001321084078',
            name: '金东镇政府西侧路口_DH201912DX雪亮G78',
            jl: 1.52,
          },
          {
            code: '33070354001321085924',
            name: '金东杜宅工业园区黄金畈村_DH201912DX雪亮G24',
            jl: 1.57,
            bq: '-',
          },
          {
            code: '33070354001321086645',
            name: '金东3省道金江路438号_DH201912LT雪亮X45',
            jl: 3,
          },
          {
            code: '33070354001320080067',
            name: '金东大黄村工业区大维电子路口_2DX67',
            jl: 2.83,
          },
          {
            code: '33070354001321086538',
            name: '金东枧头村北村口_DH201912DX雪亮G38',
            jl: 2.7,
            bq: '-',
          },
          {
            code: '33070354001321088717',
            name: '金东六大山村南岔路口_DH201912DX雪亮G17',
            jl: 2.68,
          },
        ]
        this_.videoList = this_.videoListAll = res.data.data
      })
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
.choosePop {
  position: relative;
  width: 900px;
  height: 570px;
  padding: 20px 5px;
  font-size: 28px;
  color: #fff;
  background: url('@/assets/zhdd/diong_bg.png') no-repeat;
  background-size: cover;
  box-sizing: border-box;
  bottom: 680px;
  left: 100px;
  .el-radio-group {
    display: flex;
    justify-content: space-around;
  }
  .el-radio {
    color: #999;
    display: flex;
    align-items: center;
  }
  .el-checkbox {
    padding-left: 30px;
    width: 90%;
    font-size: 20px;
  }
  .el-checkbox__label {
    font-size: 23px;
    line-height: 32px;
  }
  /deep/ .el-radio__label {
    font-size: 28px;
    padding-left: 2px;
  }
  .type_btn {
    display: flex;
    justify-content: space-around;
  }
  .type_btn > span {
    text-align: center;
    border-radius: 10px;
    background: linear-gradient(to bottom, #b4a741, #514927, #312823);
    width: 140px;
    height: 40px;
    line-height: 40px;
  }
  .active_type {
    background: linear-gradient(to bottom, #0578c3, #0a2547) !important;
  }
  .theader > span {
    color: #eeee;
    text-align: center;
  }
  .tbody > span,
  .tbody > span {
    width: 30%;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .el-checkbox-group {
    max-height: 335px;
    overflow-y: scroll;
    overflow-x: clip;
  }
  .el-checkbox-group::-webkit-scrollbar {
    width: 4px;
    /*滚动条整体样式*/
    height: 4px;
    /*高宽分别对应横竖滚动条的尺寸*/
  }
  .el-checkbox__inner {
    width: 25px;
    height: 25px;
  }
  .el-checkbox__inner::after {
    height: 17px;
    left: 11px;
  }
  .el-checkbox-group::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background: #20aeff;
    height: 8px;
  }
  .overBtn {
    position: absolute;
    bottom: 15px;
    width: 100%;
  }
  .overBtn > span:hover {
    color: #ffc460;
    cursor: pointer;
  }
  .el-radio__inner {
    width: 25px;
    height: 25px;
  }
}
</style>