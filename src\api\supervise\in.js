import {request} from '@/utils/request'

// 查询打卡考勤列表
export function listIn(query) {
  return request({
    url: '/business/punchIn/list',
    method: 'get',
    params: query
  })
}

// 查询打卡考勤详细
export function getIn(punchId) {
  return request({
    url: '/business/punchIn/' + punchId,
    method: 'get'
  })
}

// 新增打卡考勤
export function addIn(data) {
  return request({
    url: '/business/punchIn/add',
    method: 'post',
    data: data
  })
}

// 修改打卡考勤
export function updateIn(data) {
  return request({
    url: '/business/punchIn/update',
    method: 'post',
    data: data
  })
}

// 删除打卡考勤
export function delIn(punchId) {
  return request({
    url: '/business/punchIn/remove/' + punchId,
    method: 'post'
  })
}

// 导出打卡考勤
export function exportIn(query) {
  return request({
    url: '/business/in/export',
    method: 'get',
    params: query
  })
}
