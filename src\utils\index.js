/**
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
import { isNumber } from 'lodash';
import moment from 'moment'
export function debounce(func, wait, immediate) {
    let timeout, args, context, timestamp, result

    const later = function() {
      // 据上一次触发时间间隔
      const last = +new Date() - timestamp

      // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
      if (last < wait && last > 0) {
        timeout = setTimeout(later, wait - last)
      } else {
        timeout = null
        // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
        if (!immediate) {
          result = func.apply(context, args)
          if (!timeout) context = args = null
        }
      }
    }

    return function(...args) {
      context = this
      timestamp = +new Date()
      const callNow = immediate && !timeout
      // 如果延时不存在，重新设定延时
      if (!timeout) timeout = setTimeout(later, wait)
      if (callNow) {
        result = func.apply(context, args)
        context = args = null
      }

      return result
    }
  }

export function getYearList(year, precision = 'month') {
  const currentDate = moment();
  const currentYear = currentDate.format('YYYY');

  if (year == currentYear) {
    // 根据精度返回不同格式
    switch (precision) {
      case 'day':
        return [year + '-01-01', currentDate.format('YYYY-MM-DD')];
      case 'month':
        return [year + '-01', currentDate.format('YYYY-MM')];
      default:
        return [year + '-01', currentDate.format('YYYY-MM')];
    }
  } else {
    // 非当前年份返回完整年度
    switch (precision) {
      case 'day':
        return [year + '-01-01', year + '-12-31'];
      case 'month':
        return [year + '-01', year + '-12'];
      default:
        return [year + '-01', year + '-12'];
    }
  }
}

// 日期格式化
export function parseTime(time, pattern) {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if ((typeof time === 'string') && (/^[0-9]+$/.test(time))) {
      time = parseInt(time)
    } else if (typeof time === 'string') {
      time = time.replace(new RegExp(/-/gm), '/')
    }
    if ((typeof time === 'number') && (time.toString().length === 10)) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value] }
    if (result.length > 0 && value < 10) {
      value = '0' + value
    }
    return value || 0
  })
  return time_str
}

export const numberFilter = (num, defaultNum) => {
  const defaultValue = defaultNum || '-';
  return isNumber(num) ? num : defaultValue;
}

/**
 * 注册城市和年份变化的事件监听
 * @param {Object} component - Vue组件实例
 * @param {Function} callback - 回调函数，接收city和year参数
 */
export function registerCityYearChangeEvents(component, callback) {
  // 监听城市变化
  component.$bus.$on('cityChange', (city) => {
    component.city = city;
    callback(city, localStorage.getItem("year"));
  });

  // 监听年份变化
  component.$bus.$on('yearChange', (year) => {
    component.year = year;
    callback(localStorage.getItem("city"), year);
  });

  // 初始化调用
  callback(localStorage.getItem("city"), localStorage.getItem("year"));
}

//姓名脱敏方法
export function handleName(name) {
  let arr = Array.from(name)
  let result = ''
  if (arr.length === 2) {
    result = arr[0] + '*'
  } else if (arr.length > 2) {
    for (let i = 1; i < arr.length - 1; i++) {
      arr[i] = '*'
    }
    result = arr.join("")
  }else {
    return name
  }
  return result
}

export function formatIdentity(number) {
  number += '';
  return number.replace(/(\d{3})\d*(\d{4})/g, '$1***********$2')
}