import {request} from '@/utils/request'

// 查询版本管理列表
export function listVersion(query) {
  return request({
    url: '/system/version/list',
    method: 'get',
    params: query
  })
}

// 查询版本管理详细
export function getVersion(id) {
  return request({
    url: '/system/version/' + id,
    method: 'get'
  })
}

// 新增版本管理
export function addVersion(data) {
  return request({
    url: '/system/version/add',
    method: 'post',
    data: data
  })
}

// 修改版本管理
export function updateVersion(data) {
  return request({
    url: '/system/version/edit',
    method: 'post',
    data: data
  })
}

// 删除版本管理
export function delVersion(id) {
  return request({
    url: '/system/version/remove/' + id,
    method: 'post'
  })
}

// 导出版本管理
export function exportVersion(query) {
  return request({
    url: '/system/version/export',
    method: 'get',
    params: query
  })
}
