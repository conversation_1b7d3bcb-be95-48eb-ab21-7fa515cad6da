<template>
  <div>
    <el-form ref="form" :model="form" label-width="100px">
      <h3 class="title">基本信息</h3>
      <el-row>
        <el-col :span="12">
          <el-tooltip :disabled="!form.name" class="item" effect="dark" :content="form.name" placement="top">
            <el-form-item label="姓名" prop="name">
              <el-input v-model="form.name" placeholder="请输入姓名" />
            </el-form-item>
          </el-tooltip>
        </el-col>
        <el-col :span="12">
          <el-tooltip :disabled="!form.phone" class="item" effect="dark" :content="form.phone" placement="top">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="form.phone" :maxlength="11" oninput="value=value.replace(/[^\d]/g,'')" placeholder="请输入联系电话" />
            </el-form-item>
          </el-tooltip>
        </el-col>
        <!-- <el-col :span="12">
          <el-tooltip :disabled="!form.identityCard" class="item" effect="dark" :content="form.identityCard" placement="top">
            <el-form-item label="身份证" prop="identityCard">
              <el-input v-model="form.identityCard" placeholder="请输入身份证" />
            </el-form-item>
          </el-tooltip>
        </el-col> -->
        <el-col :span="12">
          <el-form-item label="登记时间" prop="registerTime">
            <div class="block">
              <el-date-picker
                v-model="form.registerTime"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="datetime"
                placeholder="选择日期时间"
                default-time="12:00:00"
              />
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <!-- <el-col :span="8">
          <el-tooltip class="item" effect="dark" :content="form.title" placement="top">
            <el-form-item label="标题" prop="title">
              <el-input v-model="form.title" placeholder="请输入标题" />
            </el-form-item>
          </el-tooltip>
        </el-col> -->
        <el-col :span="24">
          <el-tooltip :disabled="!form.address" class="item" effect="dark" :content="form.address" placement="top">
            <el-form-item label="案件地址" prop="address">
              <div>
                <el-input v-model="form.address" placeholder="请选择案件地址">
                  <svg-icon slot="suffix" icon-class="map" class="svg-icon" @click="openMap = true" />
                </el-input>
              </div>
            </el-form-item>
          </el-tooltip>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="案件内容" prop="content">
            <el-input
              v-model="form.content"
              type="textarea"
              placeholder="请输入案件内容"
              maxlength="150"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="上传图片" prop="files">
            <el-upload
              ref="upload"
              multiple
              :limit="4"
              list-type="picture-card"
              class="upload-demo"
              accept=".jpg,.jpeg,.png,.JPG,.JPEG,.PNG"
              action="/zqzfj/system/file/upload"
              :headers="headers"
              :on-preview="handlePictureCardPreview"
              :on-remove="handleRemove"
              :on-error="handleError"
              :on-success="handleSuccess"
              :data="formData"
              :before-remove="beforeRemove"
              :file-list="formFiles"
              :auto-upload="false"
              name="files"
              :on-exceed="exceed"
            >
              <i class="el-icon-plus" />
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <div v-if="openMap" class="map">
        <div class="">
          <tdtMap ref="bindmap" :map-search="true" :styles="{width:'900px',height:'90vh'}" :dw="form" @onlnglat="onlnglat" />
        </div>
      </div> -->
      <tdtMap ref="bindmap" :visible.sync="openMap" append-to-body :map-search="true" :dw="form" @onlnglat="onlnglat" />
    </el-form>
    <!-- 图片预览 -->
    <el-dialog :close-on-click-modal="false" :visible.sync="dialogVisible" append-to-body>
      <img width="100%" :src="dialogImageUrl">
    </el-dialog>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :loading="!fromQd" @click="primary">确 定</el-button>
      <el-button @click="cancel">取 消 </el-button>
    </div>
  </div>
</template>
<script>
import tdtMap from '@/components/tdtMap/tdtMap'
import { getToken } from '@/utils/auth'
import { getFiles, removeFiles} from '@/api/supervise/swit'
export default {
  name: 'Fromlist',
  components: {
    tdtMap
  },
  props: {
    getForm: {
      type: Object,
      default() {
        return null
      }
    },
    qd: {
      type: Boolean,
      default() {
        return true
      }
    }
  },
  data() {
    return {
      headers: {Authorization: 'Bearer ' + getToken()},
      loading: true,
      formData: {businessId: null, tableName: 'tout'},
      form: {files: []},
      fileList: [],
      mapAdderssData: [],
      openMap: false,
      dialogImageUrl: '',
      dialogVisible: false,
      disabled: false,
      formFiles: [], // 文件
      fromQd: this.qd,
      // rules: {
      //   name: [
      //     { required: true, message: '请输入姓名', trigger: 'blur' }
      //   ],
      //   title: [
      //     { required: true, message: '请输入标题', trigger: 'blur' }
      //   ],
      //   content: [
      //     { required: true, message: '请输入案件内容', trigger: 'blur' }
      //   ],
      //   identityCard: [
      //     { required: true, message: '请输入身份证号', trigger: ['blur', 'change'] },
      //     {pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '请输入正确的身份证号'}
      //   ],
      //   phone: [
      //     {required: true, message: '号码不能为空', trigger: 'blur'},
      //     {pattern: /^[1][3,4,5,7,8,9][0-9]{9}$/, message: '请输入正确的手机号'}
      //   ],
      //   registerTime: [
      //     {  required: true, message: '请选择登记时间', trigger: 'change' }
      //   ],
      //   address: [
      //     { required: true, message: '请选择地址', trigger: 'change' }
      //   ]
      // },
      open: ''
    }
  },
  created() {
    if (this.getForm.toutId) {
      console.log(this.getForm)
      this.form = {...this.getForm}
      let {toutId } = this.getForm
      let params = {businessId: toutId, tableName: 'tout'}
      this.getFile(params)
    }
  },
  methods: {
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    getFile(params) {
      getFiles(params).then(res => {
        let filter = res.rows.reduce((arr, v) => {
          let url = {name: v.displayName, url: `/zqzfj${v.filePath}?id=${v.fileId}`, ...v}
          arr.push(url)
          return arr
        }, [])
        this.formFiles = filter
      })
    },
    onlnglat(lenlat) {      // 经纬度  详细地址
      let {lng: longitude, lat: latitude, address} = lenlat
      this.form = {...this.form, longitude, latitude, address}
      this.openMap = false
    },
    // 上传函数
    beforeRemove(file) {
      return new Promise((resolve, reject) => {
        this.$confirm('是否确认删除？ 删除后将无法恢复', '提示', { type: 'warning' }).then(() => {
          console.log(file)
          if (file.fileId) {
            removeFiles(file.fileId).then(resolve).catch(reject)
          } else {
            resolve()
          }
        }).catch(() => {
          reject()
        })
      })
    },
    handleRemove() {   /* 删除图片*/ },
    exceed(files, filesList) {
      if (filesList.length >= 4) this.msgError('已超出最大上传数量')
    },
    handleSuccess(files) {    // 上传成功
      if (files.code == 200) {
        this.msgSuccess('上传成功')
      } else {
        this.msgSuccess('上传失败,请修改后重试')
      }
    },
    handleError(err, file, fileList) { // 上传失败
      console.log(err, file, fileList)
      this.msgError('上传失败,请修改后重试')
    },
    // 上传
    upload(data) {
      console.log(data)
      this.formData.businessId = data.id
      this.formData.status = data.status
      this.$refs.upload.submit()
    },
    // 确定
    primary() {
      // this.$refs['form'].validate(valid => {
      //   if (valid) {
      // if (this.$refs.upload.uploadFiles.length == 0) return this.msgError('请上传图片')
      if (!this.fromQd) return
      this.form.status = 2
      let params = {...this.form}
      this.$emit('onPrimary', params)
      this.openMap = false
      // } else {
      //   return false
      // }
      // })
    },
    // 取消
    cancel() {
      this.openMap = false
      this.form = {}
      this.$emit('oncancel')
    }
    // 表单验证
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  margin-top: 20px;
  text-align: right;
}
.el-upload__tip {
  display: inline-block;
  margin-left: 5%;
}
.mapTable {
  height: 300px;
}
::v-deep .el-upload--picture-card,
::v-deep .el-upload-list--picture-card .el-upload-list__item {
  width: 100px;
  height: 100px;
  line-height: 100px;
}
.map {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10000;
  padding: 5vh 18px 5vh 0;
  background-color: rgba(0, 0, 0, 0.6);
}
.title {
  display: block;
  padding: 10px 0 10px 30px;
  margin-bottom: 20px;
  border-bottom: 1px solid #e4e7ec;
  color: #000;
}
.svg-icon {
  font-size: 25px;
  margin-left: 2px;
}
</style>
