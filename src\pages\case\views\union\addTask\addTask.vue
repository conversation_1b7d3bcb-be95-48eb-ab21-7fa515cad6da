<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      :inline="true"
    >
      <!-- <el-form-item label="执法类型">
        <el-dropdown trigger="click" @command="handleCommand">
          <span class="el-dropdown-link">
            <el-input
              v-model="queryParams.typeName"
              placeholder="请选择执法类型"
              readonly="readonly"
              clearable
              size="small"
              style="width: 240px; margin: 0 5px;"
              @keyup.enter.native="handleQuery"
            />
          </span>
          <el-dropdown-menu slot="dropdown" style="width: 240px;">
            <el-dropdown-item v-for="v in typeData" :key="v.dictSort" :command="v">{{ v.dictLabel }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form-item> -->
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px;"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item>
        <!-- label="创建时间"  -->
        <el-input v-model="queryParams.searchValue" size="small" style="width: 240px; margin: 0 5px;" placeholder="请输入内容" />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">
          重置
        </el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:role:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >
          新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:role:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>
    <el-table
      v-loading="loading"
      :data="dataList"
      @selection-change="handleSelectionChange"
    >
      <!-- :cell-style="cellStyle" -->
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="任务名称" align="center" prop="title" width="150" show-overflow-tooltip />
      <!-- <el-table-column label="执法类型" align="center" prop="type" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.type | types(typeData) }}</span>
        </template>
      </el-table-column> -->
      <el-table-column label="负责人" prop="userName" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="开始时间" align="center" prop="startTime" width="150">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.startTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" align="center" prop="endTime" width="150">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="任务内容" prop="content" show-overflow-tooltip />
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <span :style="{ color: circleColor[scope.row.status] }">{{ scope.row.status | statusName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <!-- <el-button size="mini" type="text" icon="el-icon-edit" @click="handleDetailSelect(scope.row)">子任务详情</el-button> -->
          <el-button v-if="scope.row.status != 9" size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button v-else size="mini" type="text" icon="el-icon-edit" style="color: #e6a23c;" @click="handleQueryData(scope.row)">详情</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 详情弹窗 -->
    <from-list :visible.sync="open" :get-form="form" :type-data="typeData" :title="title" :detail-id="detailId" :form-disabled="formDisabled" @reLoad="handleQuery" />

    <el-dialog :close-on-click-modal="false" title="子任务详情" :visible.sync="detailOpen" width="780px" append-to-body>
      <el-table v-loading="detailLoading" :data="dataDetailList" border>
        <el-table-column label="案件内容" align="center" prop="content" />
        <el-table-column label="操作人" align="center" prop="userName" />
        <el-table-column label="案件类型" align="center" prop="type">
          <template slot-scope="scope">
            <span>{{ scope.row.type | typesName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="案件状态" align="center" prop="status">
          <template slot-scope="scope">
            <span>{{ scope.row.status | statusName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="部门名称" align="center" prop="deptName" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              v-hasPermi="['business:attentione:edit']"
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleSelect(scope.row)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 四位一体详情 -->
    <fourWindow :visible.sync="fourWindowVisible" title="四位一体详情" :hand-disabled="false" :type-data="typeDetailData" :detail-id="fourWindowId" :form-disabled="true" />
    <!-- 监控抓拍详情 -->
    <captureWindow :visible.sync="captureWindowVisible" :form-disabled="true" title="监控抓拍详情" :detail-id="captureWindowId" />
    <!-- 巡查发现详情 -->
    <el-dialog :close-on-click-modal="false" title="巡查发现详情" :visible.sync="inspectionWindowVisible" width="1000px">
      <inspectionWindow v-if="inspectionWindowVisible" :detail-id="inspectionWindowId" :form-disabled="true" :qd="false" @oncancel="inspectionWindowVisible = false" />
    </el-dialog>
    <!-- 简易案件 -->
    <el-dialog :close-on-click-modal="false" title="简易案件详情" :visible.sync="punishWindowVisible" width="1000px">
      <punishWindow v-if="punishWindowVisible" :detail-id="punishWindowId" :form-disabled="true" :qd="false" @oncancel="punishWindowVisible = false" />
    </el-dialog>
    <!-- 黄牛处置 -->
    <el-dialog :close-on-click-modal="false" title="黄牛处置详情" :visible.sync="toutWindowVisible" width="1000px">
      <toutWindow v-if="toutWindowVisible" :detail-id="toutWindowId" :qd="false" @oncancel="toutWindowVisible = false" />
    </el-dialog>
    <!-- 电子抓拍 -->
    <trafficCaptureWindow :visible.sync="trafficCaptureWindowVisible" title="电子抓拍详情" :detail-id="trafficCaptureWindowId" :form-disabled="true" />
    <!-- 违规处置 -->
    <transportWindow :visible.sync="transportWindowVisible" title="违规处置详情" :detail-id="transportWindowVisibleId" :form-disabled="true" :car-type-options="transportWindowTypeData" />
  </div>
</template>

<script>
import rwfq from '@/api/case/union/rwfq'
import {listData as dictType} from '@/api/system/dict/type'
import fromList from '@/pages/case/views/union/addTask/components/fromList'
import fourWindow from '@/pages/supervise/views/jgdc/quaternity/components/fromlist.vue'
import captureWindow from '@/pages/case/views/synthetical/capture/components/fromlist.vue'
import inspectionWindow from '@/pages/case/views/synthetical/patrol/components/fromlist.vue'
import punishWindow from '@/pages/case/views/synthetical/punishment/components/fromlist.vue'
import toutWindow from '@/pages/case/views/tout/hncz/components/fromlist.vue'
import trafficCaptureWindow from '@/pages/case/views/jjzf/dzzp/components/fromList.vue'
import transportWindow from '@/pages/case/views/pipe/illegal/components/fromlist.vue'
export default {
  components: {
    fromList,
    fourWindow,
    captureWindow,
    inspectionWindow,
    punishWindow,
    toutWindow,
    trafficCaptureWindow,
    transportWindow
    // mBMenu
  },
  filters: {
    statusName(status) {
      const statusObj = {1: '已暂存', 2: '处理中', 3: '下发中队长', 4: '下发队员', 5: '出警', 6: '警情反馈', 9: '已办结' }
      return statusObj[status]
    },
    typesName(type) {
      const typeObj = {1: '巡查发现', 2: '监控抓拍', 3: '违规处置', 4: '简易处罚', 5: '四位一体' }
      return typeObj[type]
    },
    types(type, typeData) {
      const Obj = typeData.find(v => v.dictValue == type)
      if (Obj) return Obj.dictLabel
    }
  },
  data() {
    return {
      formDisabled: false,
      detailId: 0,
      title: '',
      open: false,
      // 遮罩层
      loading: true,
      detailLoading: true,
      // 选中数组
      ids: [],
      // 显示搜索条件
      showSearch: true,
      // 是否显示弹出层
      detailOpen: false,
      // 总条数
      total: 0,
      typeData: [],
      dataList: [],
      dataDetailList: [],
      circleColor: {
        1: '#67C23A',
        2: '#FAB71C',
        9: '#bdc3bf'
      },
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        searchValue: '',
        dateRange: '',
        type: ''
      },
      form: {},
      typeDetailData: [{id: 1, value: '环卫保洁'}, {id: 2, value: '园林'}, {id: 3, value: '绿化'}, {id: 4, value: '市政'} ],
      fourWindowVisible: false,
      fourWindowId: 0,
      captureWindowVisible: false,
      captureWindowId: 0,
      inspectionWindowVisible: false,
      inspectionWindowId: 0,
      punishWindowVisible: false,
      punishWindowId: 0,
      toutWindowVisible: false,
      toutWindowId: 0,
      trafficCaptureWindowVisible: false,
      trafficCaptureWindowId: 0,
      transportWindowVisible: false,
      transportWindowVisibleId: 0,
      transportWindowTypeData: []
    }
  },
  computed: {
    listData() {
      let {pageNum, pageSize} = this.queryParams
      let arr = this.roleList.slice((pageNum - 1) * pageSize, (pageNum - 1) * pageSize + pageSize)
      return arr
    }
  },
  async mounted() {
    await dictType({dictType: 'case_union_type'}).then(res => {
      this.typeData = res.rows
    })
    this.getList()

  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true
      let { pageNum, pageSize, type, dateRange, searchValue} = this.queryParams
      let params = { pageNum, pageSize, orderByColumn: 'startTime', isAsc: 'desc' }
      if (type) params.type = type
      if (searchValue) params.searchValue = searchValue
      if (Array.isArray(dateRange) && !dateRange.length == 0) { params = {...params, searchStartTime: dateRange[0], searchEndTime: dateRange[1] } }
      rwfq.List(params).then(res => {
        this.dataList = res.rows
        this.total = res.total
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    // 类型选择
    handleCommand(command) {
      this.queryParams = {...this.queryParams, type: command.dictValue, typeName: command.dictLabel}
      this.handleQuery()
    },
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.queryParams.pageNum = 1
      this.getList()
    },
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.getList()
    },
    handleSelect(row) {
      const caseId = row.id
      this.openEventWindow(row.type, caseId)
    },
    openEventWindow(type, id) {
      if (type == '5') {
        this.fourWindowVisible = true
        this.fourWindowId = id
      } else if (type == '2') {
        this.captureWindowVisible = true
        this.captureWindowId = id
      } else if (type == '1') {
        this.inspectionWindowVisible = true
        this.inspectionWindowId = id
      } else if (type == 'tout') {
        this.toutWindowVisible = true
        this.toutWindowId = id
      } else if (type == '4') {
        this.punishWindowVisible = true
        this.punishWindowId = id
      } else if (type == 'trafficCapture') {
        this.trafficCaptureWindowVisible = true
        this.trafficCaptureWindowId = id
      } else if (type == '3') {
        this.transportWindowVisible = true
        this.transportWindowVisibleId = id
      } else {
        this.$message.error('信息错误，请联系管理员')
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = { pageNum: 1, pageSize: 10, searchValue: '', dateRange: ''}
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.unionId)
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.open = true
      this.title = '新增任务'
      this.detailId = 0
      this.formDisabled = false
    },
    handleDetailSelect(row) {
      const unionId = row.unionId || this.ids
      const detailParams = {unionId: unionId}
      rwfq.detailList(detailParams).then(response => {
        this.dataDetailList = response.rows
        this.total = response.total
        this.detailOpen = true
        this.detailLoading = false
      })
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.open = true
      this.title = '修改任务'
      this.detailId = row.unionId
      this.formDisabled = false
    },
    handleQueryData(row) {
      this.open = true
      this.title = '任务详情'
      this.detailId = row.unionId
      this.formDisabled = true
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const transportIds = row.unionId || this.ids
      if (Array.isArray(transportIds) && !transportIds.length) {
        this.$message.warning('请选择需要删除的数据')
        return
      }
      this.$confirm('是否确认删除?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        rwfq.Remove(transportIds).then(() => {
          this.getList()
          this.msgSuccess('删除成功')
        })
      }).catch(() => {})
    }
  }
}
</script>
