<template>
  <div>
    <CommonTitle :text="title"></CommonTitle>
    <div class="wrap-container" id="chartyx"></div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
export default {
  name: 'index',
  components: {
    CommonTitle,
  },
  data() {
    return {
      title: '有序：84分',
      chartsData: [
        {
          name: '婺城',
          current: 70,
          before: 58,
          value: 70,
        },
        {
          name: '婺城',
          current: 70,
          before: 58,
          value: 60,
        },
        {
          name: '婺城',
          current: 70,
          before: 58,
          value: 50,
        },
        {
          name: '婺城',
          current: 70,
          before: 58,
          value: 60,
        },
        {
          name: '婺城',
          current: 70,
          before: 58,
          value: 40,
        },
        {
          name: '婺城',
          current: 70,
          before: 58,
          value: 30,
        },
        {
          name: '婺城',
          current: 70,
          before: 58,
          value: 70,
        },
      ],
    }
  },
  computed: {},
  mounted() {
    this.initChart()
  },
  methods: {
    initChart() {
      let myChart = this.$echarts.init(document.getElementById('chartyx'))
      let option = {
        tooltip: {
          trigger: 'axis',
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        legend: {
          orient: 'horizontal',
          // icon: "circle",
          right:'10%',
          padding: [30, 10, 10, 10],
          itemGap: 45,
          textStyle: {
            color: '#D6E7F9',
            fontSize: 28,
          },
          data: [
            {
              name: '现状得分',
              icon: 'rect',
              itemStyle: {
                color: '#22E097', // 第一个图例文字颜色
              },
            },
            {
              name: '目标得分',
              icon: 'circle', // 折线图使用圆形图标
              itemStyle: {
                color: '#E0D722', // 第一个图例文字颜色
              },
            },
          ],
        },
        grid: {
          left: '8%',
          right: '10%',
          top: '22%',
          bottom: '1%',
          containLabel: true,
        },
        yAxis: [
          {
            type: 'category',
            data: this.chartsData.map((item) => item.name),
            axisLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)',
                width: 1,
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              textStyle: {
                color: '#D6E7F9',
                fontSize: 28,
              },
            },
          },
        ],
        xAxis: [
          {
            name: '',
            type: 'value',
            max: 100,
            nameTextStyle: {
              fontSize: 24,
              color: '#D6E7F9',
              padding: 20,
            },
            splitLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)',
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 28,
                color: '#D6E7F9',
              },
            },
          },
        ],
        series: [
          {
            name: '现状得分',
            type: 'bar',
            barWidth: '10px',
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                  {
                    offset: 0,
                    color: '#22E197',
                  },
                  {
                    offset: 1,
                    color: 'rgba(34, 225, 151, 0)',
                  },
                ]),
                barBorderRadius: 4,
              },
            },
            data: this.chartsData.map((item) => item.current),
          },
          {
            name: '目标得分',
            data: this.chartsData.map((item) => item.value),
            type: 'line',
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(224, 215, 34, 0.30)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(224, 215, 34, 0)',
                  },
                ],
              },
            },
            lineStyle: {
              color: '#E0D722',
            },
            itemStyle: {
              color: '#E0D722', // 设置图例标记的颜色，与线条颜色保持一致
            },
            smooth: true,
            symbol: 'none',
          },
        ],
      }
      myChart.setOption(option)
      myChart.getZr().on('mousemove', (param) => {
        myChart.getZr().setCursorStyle('default')
      })
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
.wrap-container {
  width: 100%;
  height: 360px;
  margin-bottom: 40px;
}
</style>