<template>
  <div id="app">
    <div class="scale-wrapper" :style="`transform: scale(${scaleWidth}, ${scaleHeight}); width: ${$pageWidth}px; height: ${$pageHeight}px;`">
      <router-view></router-view>
    </div>
  </div>
</template>

<script>


export default {
  name: 'App',
  data () {
    return {
      scaleWidth: 1,
      scaleHeight: 1,
    }
  },
  watch: {
    '$pageWidth': function () {
      this.changePage()
    }
  },
  computed: {

  },
  mounted () {
    this.$nextTick(() => {
      this.resetPageSize()
      window.addEventListener('resize', this.resetPageSize)
    })
  },
  beforeDestroy () {
    window.removeEventListener('resize', this.resetPageSize)
  },
  methods: {
    changePage () {
      this.resetPageSize()
    },
    resetPageSize () {
      const container = document.getElementById('app')
      const scaleW = container.clientWidth / this.$pageWidth
      const scaleH = container.clientHeight / this.$pageHeight

      this.scaleWidth = scaleW
      this.scaleHeight = scaleH
      window.scaleWidth = scaleW
      window.scaleHeight = scaleH
    },

  }
}
</script>

<style>
* {
  padding: 0;
  margin: 0;
}

li {
  list-style: none;
}

html, body {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

#app {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
}

.scale-wrapper {
  transform-origin: left top;
  position: absolute;
  left: 0;
  top: 0;
}
</style>
