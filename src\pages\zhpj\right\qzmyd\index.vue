<template>
  <div>
    <CommonTitle :text="title"></CommonTitle>
    <div class="wrap-container" id="chartcslh"></div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
export default {
  name: 'index',
  components: {
    CommonTitle,
  },
  data() {
    return {
      title: '群众满意度：80分',
      chartsData: [
        {
          name: '婺城',
          current: 70,
          before: 58,
          value: 70,
        },
        {
          name: '婺城',
          current: 70,
          before: 58,
          value: 60,
        },
        {
          name: '婺城',
          current: 70,
          before: 58,
          value: 50,
        },
        {
          name: '婺城',
          current: 70,
          before: 58,
          value: 60,
        },
        {
          name: '婺城',
          current: 70,
          before: 58,
          value: 40,
        },
        {
          name: '婺城',
          current: 70,
          before: 58,
          value: 30,
        },
        {
          name: '婺城',
          current: 70,
          before: 58,
          value: 70,
        },
      ],
    }
  },
  computed: {},
  mounted() {
    this.initChart()
  },
  methods: {
    handleTabChange(i) {
      this.index = i
    },
    initChart() {
      let myChart = this.$echarts.init(document.getElementById('chartcslh'))
      let option = {
        tooltip: {
          trigger: 'axis',
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        legend: {
          orient: 'horizontal',
          icon: "circle",
          left:'18%',
          padding: [30, 10, 10, 10],
          itemGap: 20,
          textStyle: {
            color: '#D6E7F9',
            fontSize: 28,
          },
        },
        grid: {
          left: '8%',
          right: '6%',
          top: '22%',
          bottom: '1%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            data: this.chartsData.map((item) => item.name),
            axisLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)', // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              textStyle: {
                color: '#D6E7F9',
                fontSize: 28,
              },
            },
          },
        ],
        yAxis: [
          {
            name: '满意度',
            type: 'value',
            nameTextStyle: {
              fontSize: 24,
              color: '#D6E7F9',
              padding: 20,
            },
            splitLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)',
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 28,
                color: '#D6E7F9',
              },
            },
          },
        ],
        series: [
          {
            name: '总体满意度',
            type: 'line',
            data: this.chartsData.map((item) => item.current),
            lineStyle: {
              color: '#007BFF',
            },
            itemStyle: {
              color: '#007BFF', // 设置图例颜色与线条颜色一致
            },
            symbol: 'none',
          },
          {
            name: '环境满意度',
            type: 'line',
            data: this.chartsData.map((item) => item.before),
            lineStyle: {
              color: '#00EAFF',
            },
            itemStyle: {
              color: '#00EAFF', // 设置图例颜色与线条颜色一致
            },
            symbol: 'none',
          },
          {
            name: '秩序满意度',
            data: this.chartsData.map((item) => item.value),
            type: 'line',
            lineStyle: {
              color: '#22E197',
            },
            itemStyle: {
              color: '#22E197', // 设置图例颜色与线条颜色一致
            },
            symbol: 'none',
          },
          {
            name: '设施满意度',
            data: this.chartsData.map((item) => item.value),
            type: 'line',
            lineStyle: {
              color: '#E0D722',
            },
            itemStyle: {
              color: '#E0D722', // 设置图例颜色与线条颜色一致
            },
            symbol: 'none',
          },
        ],
      }
      myChart.setOption(option)
      myChart.getZr().on('mousemove', (param) => {
        myChart.getZr().setCursorStyle('default')
      })
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
.wrap-container {
  width: 100%;
  height: 400px;
}
</style>