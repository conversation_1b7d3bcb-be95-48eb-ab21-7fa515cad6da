<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" :inline="true">
      <el-form-item label="日期范围">
        <el-date-picker

          v-model="queryParams.startTime"
          size="small"
          style="width: 240px;"
          value-format="yyyy-MM-dd hh:mm:ss"
          type="year"
          @change="getList"
        />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <!-- 刷新工具 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="20">
        <h2 style="margin: 0;">四位一体数量类型统计图</h2>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>
    <!-- 主体内容 -->
    <div v-loading="loading" class="container" :class="{ hideSearch: !showSearch }">
      <div ref="chart" style="width: 100%; height: 100%;" />
    </div>
  </div>
</template>

<script>
import { getFourType } from '@/api/supervise/four'

export default {
  data() {
    return {
      loading: false,
      showSearch: true,
      queryParams: {
        startTime: '',
        type: ''
      },
      myChart: null,
      firstData: [],
      secondData: [],
      thirdData: [],
      fourthData: [],
      legendData: []
    }
  },
  computed: {
    labelOption() {
      return {
        show: true,
        position: 'insideBottom',
        distance: 15,
        align: 'left',
        verticalAlign: 'middle',
        rotate: 90,
        formatter: '{c}  {name|{a}}',
        fontSize: 16,
        rich: {
          name: {
          }
        }
      }
    },
    options() {
      return {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['Forest', 'Steppe', 'Desert', 'Wetland']
        },
        toolbox: {
          show: true,
          orient: 'vertical',
          left: 'right',
          top: 'center',
          feature: {
            mark: {show: true},
            dataView: {show: true, readOnly: false},
            magicType: {show: true, type: ['line', 'bar', 'stack', 'tiled']},
            restore: {show: true},
            saveAsImage: {show: true}
          }
        },
        xAxis: [
          {
            type: 'category',
            axisTick: {show: false},
            data: this.legendData
          }
        ],
        yAxis: [
          {
            type: 'value'
          }
        ],
        series: [
          {
            name: '环卫保洁',
            type: 'bar',
            barGap: 0,
            label: this.labelOption,
            emphasis: {
              focus: 'series'
            },
            data: this.firstData
          },
          {
            name: '园林',
            type: 'bar',
            label: this.labelOption,
            emphasis: {
              focus: 'series'
            },
            data: this.secondData
          },
          {
            name: '绿化',
            type: 'bar',
            label: this.labelOption,
            emphasis: {
              focus: 'series'
            },
            data: this.thirdData
          },
          {
            name: '市政',
            type: 'bar',
            label: this.labelOption,
            emphasis: {
              focus: 'series'
            },
            data: this.fourthData
          }
        ]

      }
    }
  },
  watch: {
    /* 隐藏搜索框重置图表大小 */
    showSearch() {
      this.resizeChart()
    }
  },
  mounted() {
    this.initChart()
    this.getList()
    window.addEventListener('resize', this.resizeChart)
  },
  destroyed() {
    window.removeEventListener('resize', this.resizeChart)
  },
  methods: {
    /* 获取数据 */
    getList() {
      this.loading = true
      const { startTime, type } = this.queryParams
      let params = {}
      if (startTime) params = { ...params, startTime }
      if (type) params = { ...params, type }
      getFourType(params).then(res => {
        this.legendData = res.data.legendData
        this.firstData = res.data.firstData
        this.secondData = res.data.secondData
        this.thirdData = res.data.thirdData
        this.fourthData = res.data.fourthData
        this.myChart.setOption(this.options)
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    /* 重置数据 */
    resetQuery() {
      this.queryParams = {
        startTime: '',
        type: ''
      }
      this.getList()
    },
    /* 初始化空数据图表 */
    initChart() {
      this.myChart = this.$echarts.init(this.$refs.chart)
      this.myChart.setOption(this.options)
    },
    /* 页面大小变更，图标自适应 */
    resizeChart() {
      setTimeout(() => {
        this.myChart.resize()
      }, 500)
    }
  }
}
</script>
<style scoped lang="scss">
.app-container {
  .container {
    width: 100%;
    height: calc(100vh - 225px);
    &.hideSearch {
      height: calc(100vh - 170px);
    }
  }
}
</style>
