<template>
  <div>
    <CommonTitle text="公用设施"></CommonTitle>
    <div class="wrap-container" ref="chart"></div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
export default {
  name: 'index',
  components: {
    CommonTitle,
  },
  data() {
    return {}
  },
  computed: {},
  mounted() {
    this.initChart()
  },
  methods: {
    initChart() {
      this.chart = this.$echarts.init(this.$refs.chart)

      const option = {
        backgroundColor: 'transparent',
        graphic: {
          type: 'image',
          style: {
            image: require('@/assets/dog/chartBg.png'),
            width: 360,
            height: 360,
          },
          left: '7.5%', // 调整背景图位置，与环形图对齐
          top: 'center',
        },
        title: [
          {
            text: '8800',
            left: '18.5%',
            top: '48%',
            textStyle: {
              color: '#E3F4FF',
              fontSize: 52,
              fontWeight: 'normal',
              fontFamily: 'DIN',
              lineHeight: 72,
            },
            z: 10, // 确保文字在背景图之上
          },
          {
            text: '公用设施',
            left: '19%',
            top: '38%',
            textStyle: {
              color: '#E3F4FF',
              fontSize: 28,
              fontWeight: 'normal',
              lineHeight: 28,
            },
            z: 10, // 确保文字在背景图之上
          },
        ],
        legend: {
          orient: 'vertical',
          left: '55%',
          y: 'center',
          itemWidth: 16,
          itemHeight: 16,
          itemGap: 28,
          icon: 'circle',
          formatter: (name) => {
            var data = option.series[0].data //获取series中的data
            let tarValue = 0
            for (var i = 0, l = data.length; i < l; i++) {
              if (data[i].name == name) {
                tarValue = data[i].value
                return `{name|${name}}{value|${tarValue}}`
              }
            }
          },
          textStyle: {
            rich: {
              name: {
                fontSize: 26,
                color: '#fff',
                padding: [0, 30, 0, 0],
              },
              value: {
                fontSize: 26,
                color: '#fff',
                padding: [0, 0, 0, 0],
              },
            },
          },
        },
        series: [
          {
            type: 'pie',
            radius: ['65%', '75%'],
            center: ['25%', '50%'],
            startAngle: 90,
            itemStyle: {
              borderRadius: 0,
              borderColor: 'rgba(2,47,115,0.5)',
              borderWidth: 2,
            },
            data: [
              {
                value: 3200,
                name: '市政工程设施',
              },
              {
                value: 1200,
                name: '园林绿化设施',
              },
              {
                value: 1600,
                name: '环境卫生设施',
              },
              {
                value: 2400,
                name: '城市道路及其设施',
              },
            ],
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
            emphasis: {
              scale: false,
            },
          },
        ],
      }

      this.chart.setOption(option)
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
.wrap-container {
  width: 100%;
  height: 300px;
  margin-bottom: 40px;
  position: relative;
}
</style>