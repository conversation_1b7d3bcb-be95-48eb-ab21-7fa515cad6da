var WebControl=function(){"use strict";var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t(e)}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}function r(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e}var i=function(e){return e&&e.Math==Math&&e},s=i("object"==("undefined"==typeof globalThis?"undefined":t(globalThis))&&globalThis)||i("object"==("undefined"==typeof window?"undefined":t(window))&&window)||i("object"==("undefined"==typeof self?"undefined":t(self))&&self)||i("object"==t(e)&&e)||function(){return this}()||Function("return this")(),a={},u=function(e){try{return!!e()}catch(e){return!0}},c=!u((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),l=Function.prototype.call,d=l.bind?l.bind(l):function(){return l.apply(l,arguments)},f={},h={}.propertyIsEnumerable,p=Object.getOwnPropertyDescriptor,v=p&&!h.call({1:2},1);f.f=v?function(e){var t=p(this,e);return!!t&&t.enumerable}:h;var b,g,m=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},y=Function.prototype,_=y.bind,w=y.call,C=_&&_.bind(w),k=_?function(e){return e&&C(w,e)}:function(e){return e&&function(){return w.apply(e,arguments)}},S=k,R=S({}.toString),q=S("".slice),I=function(e){return q(R(e),8,-1)},P=k,E=u,O=I,T=s.Object,z=P("".split),A=E((function(){return!T("z").propertyIsEnumerable(0)}))?function(e){return"String"==O(e)?z(e,""):T(e)}:T,U=s.TypeError,D=function(e){if(null==e)throw U("Can't call method on "+e);return e},x=A,W=D,F=function(e){return x(W(e))},M=function(e){return"function"==typeof e},L=M,J=function(e){return"object"==t(e)?null!==e:L(e)},j=s,N=M,B=function(e){return N(e)?e:void 0},Z=function(e,t){return arguments.length<2?B(j[e]):j[e]&&j[e][t]},H=k({}.isPrototypeOf),G=Z("navigator","userAgent")||"",V=s,X=G,Y=V.process,K=V.Deno,$=Y&&Y.versions||K&&K.version,Q=$&&$.v8;Q&&(g=(b=Q.split("."))[0]>0&&b[0]<4?1:+(b[0]+b[1])),!g&&X&&(!(b=X.match(/Edge\/(\d+)/))||b[1]>=74)&&(b=X.match(/Chrome\/(\d+)/))&&(g=+b[1]);var ee=g,te=ee,ne=u,oe=!!Object.getOwnPropertySymbols&&!ne((function(){var e=Symbol();return!String(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&te&&te<41})),re=oe&&!Symbol.sham&&"symbol"==t(Symbol.iterator),ie=Z,se=M,ae=H,ue=re,ce=s.Object,le=ue?function(e){return"symbol"==t(e)}:function(e){var t=ie("Symbol");return se(t)&&ae(t.prototype,ce(e))},de=s.String,fe=function(e){try{return de(e)}catch(e){return"Object"}},he=M,pe=fe,ve=s.TypeError,be=function(e){if(he(e))return e;throw ve(pe(e)+" is not a function")},ge=be,me=function(e,t){var n=e[t];return null==n?void 0:ge(n)},ye=d,_e=M,we=J,Ce=s.TypeError,ke={exports:{}},Se=s,Re=Object.defineProperty,qe=function(e,t){try{Re(Se,e,{value:t,configurable:!0,writable:!0})}catch(n){Se[e]=t}return t},Ie=qe,Pe="__core-js_shared__",Ee=s[Pe]||Ie(Pe,{}),Oe=Ee;(ke.exports=function(e,t){return Oe[e]||(Oe[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.19.1",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"});var Te=D,ze=s.Object,Ae=function(e){return ze(Te(e))},Ue=Ae,De=k({}.hasOwnProperty),xe=Object.hasOwn||function(e,t){return De(Ue(e),t)},We=k,Fe=0,Me=Math.random(),Le=We(1..toString),Je=function(e){return"Symbol("+(void 0===e?"":e)+")_"+Le(++Fe+Me,36)},je=s,Ne=ke.exports,Be=xe,Ze=Je,He=oe,Ge=re,Ve=Ne("wks"),Xe=je.Symbol,Ye=Xe&&Xe.for,Ke=Ge?Xe:Xe&&Xe.withoutSetter||Ze,$e=function(e){if(!Be(Ve,e)||!He&&"string"!=typeof Ve[e]){var t="Symbol."+e;He&&Be(Xe,e)?Ve[e]=Xe[e]:Ve[e]=Ge&&Ye?Ye(t):Ke(t)}return Ve[e]},Qe=d,et=J,tt=le,nt=me,ot=function(e,t){var n,o;if("string"===t&&_e(n=e.toString)&&!we(o=ye(n,e)))return o;if(_e(n=e.valueOf)&&!we(o=ye(n,e)))return o;if("string"!==t&&_e(n=e.toString)&&!we(o=ye(n,e)))return o;throw Ce("Can't convert object to primitive value")},rt=$e,it=s.TypeError,st=rt("toPrimitive"),at=function(e,t){if(!et(e)||tt(e))return e;var n,o=nt(e,st);if(o){if(void 0===t&&(t="default"),n=Qe(o,e,t),!et(n)||tt(n))return n;throw it("Can't convert object to primitive value")}return void 0===t&&(t="number"),ot(e,t)},ut=le,ct=function(e){var t=at(e,"string");return ut(t)?t:t+""},lt=J,dt=s.document,ft=lt(dt)&&lt(dt.createElement),ht=function(e){return ft?dt.createElement(e):{}},pt=ht,vt=!c&&!u((function(){return 7!=Object.defineProperty(pt("div"),"a",{get:function(){return 7}}).a})),bt=c,gt=d,mt=f,yt=m,_t=F,wt=ct,Ct=xe,kt=vt,St=Object.getOwnPropertyDescriptor;a.f=bt?St:function(e,t){if(e=_t(e),t=wt(t),kt)try{return St(e,t)}catch(e){}if(Ct(e,t))return yt(!gt(mt.f,e,t),e[t])};var Rt={},qt=s,It=J,Pt=qt.String,Et=qt.TypeError,Ot=function(e){if(It(e))return e;throw Et(Pt(e)+" is not an object")},Tt=c,zt=vt,At=Ot,Ut=ct,Dt=s.TypeError,xt=Object.defineProperty;Rt.f=Tt?xt:function(e,t,n){if(At(e),t=Ut(t),At(n),zt)try{return xt(e,t,n)}catch(e){}if("get"in n||"set"in n)throw Dt("Accessors not supported");return"value"in n&&(e[t]=n.value),e};var Wt=Rt,Ft=m,Mt=c?function(e,t,n){return Wt.f(e,t,Ft(1,n))}:function(e,t,n){return e[t]=n,e},Lt={exports:{}},Jt=M,jt=Ee,Nt=k(Function.toString);Jt(jt.inspectSource)||(jt.inspectSource=function(e){return Nt(e)});var Bt,Zt,Ht,Gt=jt.inspectSource,Vt=M,Xt=Gt,Yt=s.WeakMap,Kt=Vt(Yt)&&/native code/.test(Xt(Yt)),$t=ke.exports,Qt=Je,en=$t("keys"),tn=function(e){return en[e]||(en[e]=Qt(e))},nn={},on=Kt,rn=s,sn=k,an=J,un=Mt,cn=xe,ln=Ee,dn=tn,fn=nn,hn="Object already initialized",pn=rn.TypeError,vn=rn.WeakMap;if(on||ln.state){var bn=ln.state||(ln.state=new vn),gn=sn(bn.get),mn=sn(bn.has),yn=sn(bn.set);Bt=function(e,t){if(mn(bn,e))throw new pn(hn);return t.facade=e,yn(bn,e,t),t},Zt=function(e){return gn(bn,e)||{}},Ht=function(e){return mn(bn,e)}}else{var _n=dn("state");fn[_n]=!0,Bt=function(e,t){if(cn(e,_n))throw new pn(hn);return t.facade=e,un(e,_n,t),t},Zt=function(e){return cn(e,_n)?e[_n]:{}},Ht=function(e){return cn(e,_n)}}var wn={set:Bt,get:Zt,has:Ht,enforce:function(e){return Ht(e)?Zt(e):Bt(e,{})},getterFor:function(e){return function(t){var n;if(!an(t)||(n=Zt(t)).type!==e)throw pn("Incompatible receiver, "+e+" required");return n}}},Cn=c,kn=xe,Sn=Function.prototype,Rn=Cn&&Object.getOwnPropertyDescriptor,qn=kn(Sn,"name"),In={EXISTS:qn,PROPER:qn&&"something"===function(){}.name,CONFIGURABLE:qn&&(!Cn||Cn&&Rn(Sn,"name").configurable)},Pn=s,En=M,On=xe,Tn=Mt,zn=qe,An=Gt,Un=In.CONFIGURABLE,Dn=wn.get,xn=wn.enforce,Wn=String(String).split("String");(Lt.exports=function(e,t,n,o){var r,i=!!o&&!!o.unsafe,s=!!o&&!!o.enumerable,a=!!o&&!!o.noTargetGet,u=o&&void 0!==o.name?o.name:t;En(n)&&("Symbol("===String(u).slice(0,7)&&(u="["+String(u).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!On(n,"name")||Un&&n.name!==u)&&Tn(n,"name",u),(r=xn(n)).source||(r.source=Wn.join("string"==typeof u?u:""))),e!==Pn?(i?!a&&e[t]&&(s=!0):delete e[t],s?e[t]=n:Tn(e,t,n)):s?e[t]=n:zn(t,n)})(Function.prototype,"toString",(function(){return En(this)&&Dn(this).source||An(this)}));var Fn={},Mn=Math.ceil,Ln=Math.floor,Jn=function(e){var t=+e;return t!=t||0===t?0:(t>0?Ln:Mn)(t)},jn=Jn,Nn=Math.max,Bn=Math.min,Zn=Jn,Hn=Math.min,Gn=function(e){return e>0?Hn(Zn(e),9007199254740991):0},Vn=function(e){return Gn(e.length)},Xn=F,Yn=function(e,t){var n=jn(e);return n<0?Nn(n+t,0):Bn(n,t)},Kn=Vn,$n=function(e){return function(t,n,o){var r,i=Xn(t),s=Kn(i),a=Yn(o,s);if(e&&n!=n){for(;s>a;)if((r=i[a++])!=r)return!0}else for(;s>a;a++)if((e||a in i)&&i[a]===n)return e||a||0;return!e&&-1}},Qn={includes:$n(!0),indexOf:$n(!1)},eo=xe,to=F,no=Qn.indexOf,oo=nn,ro=k([].push),io=function(e,t){var n,o=to(e),r=0,i=[];for(n in o)!eo(oo,n)&&eo(o,n)&&ro(i,n);for(;t.length>r;)eo(o,n=t[r++])&&(~no(i,n)||ro(i,n));return i},so=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],ao=io,uo=so.concat("length","prototype");Fn.f=Object.getOwnPropertyNames||function(e){return ao(e,uo)};var co={};co.f=Object.getOwnPropertySymbols;var lo=Z,fo=Fn,ho=co,po=Ot,vo=k([].concat),bo=lo("Reflect","ownKeys")||function(e){var t=fo.f(po(e)),n=ho.f;return n?vo(t,n(e)):t},go=xe,mo=bo,yo=a,_o=Rt,wo=function(e,t){for(var n=mo(t),o=_o.f,r=yo.f,i=0;i<n.length;i++){var s=n[i];go(e,s)||o(e,s,r(t,s))}},Co=u,ko=M,So=/#|\.prototype\./,Ro=function(e,t){var n=Io[qo(e)];return n==Eo||n!=Po&&(ko(t)?Co(t):!!t)},qo=Ro.normalize=function(e){return String(e).replace(So,".").toLowerCase()},Io=Ro.data={},Po=Ro.NATIVE="N",Eo=Ro.POLYFILL="P",Oo=Ro,To=s,zo=a.f,Ao=Mt,Uo=Lt.exports,Do=qe,xo=wo,Wo=Oo,Fo=function(e,n){var o,r,i,s,a,u=e.target,c=e.global,l=e.stat;if(o=c?To:l?To[u]||Do(u,{}):(To[u]||{}).prototype)for(r in n){if(s=n[r],i=e.noTargetGet?(a=zo(o,r))&&a.value:o[r],!Wo(c?r:u+(l?".":"#")+r,e.forced)&&void 0!==i){if(t(s)==t(i))continue;xo(s,i)}(e.sham||i&&i.sham)&&Ao(s,"sham",!0),Uo(o,r,s,e)}},Mo=io,Lo=so,Jo=Object.keys||function(e){return Mo(e,Lo)},jo=c,No=k,Bo=d,Zo=u,Ho=Jo,Go=co,Vo=f,Xo=Ae,Yo=A,Ko=Object.assign,$o=Object.defineProperty,Qo=No([].concat),er=!Ko||Zo((function(){if(jo&&1!==Ko({b:1},Ko($o({},"a",{enumerable:!0,get:function(){$o(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var e={},t={},n=Symbol(),o="abcdefghijklmnopqrst";return e[n]=7,o.split("").forEach((function(e){t[e]=e})),7!=Ko({},e)[n]||Ho(Ko({},t)).join("")!=o}))?function(e,t){for(var n=Xo(e),o=arguments.length,r=1,i=Go.f,s=Vo.f;o>r;)for(var a,u=Yo(arguments[r++]),c=i?Qo(Ho(u),i(u)):Ho(u),l=c.length,d=0;l>d;)a=c[d++],jo&&!Bo(s,u,a)||(n[a]=u[a]);return n}:Ko,tr=er;Fo({target:"Object",stat:!0,forced:Object.assign!==tr},{assign:tr});var nr=s;nr.Object.assign;var or,rr=!u((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype})),ir=s,sr=xe,ar=M,ur=Ae,cr=rr,lr=tn("IE_PROTO"),dr=ir.Object,fr=dr.prototype,hr=cr?dr.getPrototypeOf:function(e){var t=ur(e);if(sr(t,lr))return t[lr];var n=t.constructor;return ar(n)&&t instanceof n?n.prototype:t instanceof dr?fr:null},pr=s,vr=M,br=pr.String,gr=pr.TypeError,mr=k,yr=Ot,_r=function(e){if("object"==t(e)||vr(e))return e;throw gr("Can't set "+br(e)+" as a prototype")},wr=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,n={};try{(e=mr(Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set))(n,[]),t=n instanceof Array}catch(e){}return function(n,o){return yr(n),_r(o),t?e(n,o):n.__proto__=o,n}}():void 0),Cr=Rt,kr=Ot,Sr=F,Rr=Jo,qr=c?Object.defineProperties:function(e,t){kr(e);for(var n,o=Sr(t),r=Rr(t),i=r.length,s=0;i>s;)Cr.f(e,n=r[s++],o[n]);return e},Ir=Z("document","documentElement"),Pr=Ot,Er=qr,Or=so,Tr=nn,zr=Ir,Ar=ht,Ur=tn("IE_PROTO"),Dr=function(){},xr=function(e){return"<script>"+e+"</"+"script>"},Wr=function(e){e.write(xr("")),e.close();var t=e.parentWindow.Object;return e=null,t},Fr=function(){try{or=new ActiveXObject("htmlfile")}catch(e){}var e,t;Fr="undefined"!=typeof document?document.domain&&or?Wr(or):((t=Ar("iframe")).style.display="none",zr.appendChild(t),t.src=String("javascript:"),(e=t.contentWindow.document).open(),e.write(xr("document.F=Object")),e.close(),e.F):Wr(or);for(var n=Or.length;n--;)delete Fr.prototype[Or[n]];return Fr()};Tr[Ur]=!0;var Mr=Object.create||function(e,t){var n;return null!==e?(Dr.prototype=Pr(e),n=new Dr,Dr.prototype=null,n[Ur]=e):n=Fr(),void 0===t?n:Er(n,t)},Lr=k([].slice),Jr=k,jr=Lr,Nr=Jr("".replace),Br=Jr("".split),Zr=Jr([].join),Hr=String(Error("zxcasd").stack),Gr=/\n\s*at [^:]*:[^\n]*/,Vr=Gr.test(Hr),Xr=/@[^\n]*\n/.test(Hr)&&!/zxcasd/.test(Hr),Yr=J,Kr=Mt,$r=be,Qr=k(k.bind),ei=function(e,t){return $r(e),void 0===t?e:Qr?Qr(e,t):function(){return e.apply(t,arguments)}},ti={},ni=ti,oi=$e("iterator"),ri=Array.prototype,ii={};ii[$e("toStringTag")]="z";var si="[object z]"===String(ii),ai=s,ui=si,ci=M,li=I,di=$e("toStringTag"),fi=ai.Object,hi="Arguments"==li(function(){return arguments}()),pi=ui?li:function(e){var t,n,o;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(n=function(e,t){try{return e[t]}catch(e){}}(t=fi(e),di))?n:hi?li(t):"Object"==(o=li(t))&&ci(t.callee)?"Arguments":o},vi=pi,bi=me,gi=ti,mi=$e("iterator"),yi=function(e){if(null!=e)return bi(e,mi)||bi(e,"@@iterator")||gi[vi(e)]},_i=d,wi=be,Ci=Ot,ki=fe,Si=yi,Ri=s.TypeError,qi=d,Ii=Ot,Pi=me,Ei=ei,Oi=d,Ti=Ot,zi=fe,Ai=function(e){return void 0!==e&&(ni.Array===e||ri[oi]===e)},Ui=Vn,Di=H,xi=function(e,t){var n=arguments.length<2?Si(e):t;if(wi(n))return Ci(_i(n,e));throw Ri(ki(e)+" is not iterable")},Wi=yi,Fi=function(e,t,n){var o,r;Ii(e);try{if(!(o=Pi(e,"return"))){if("throw"===t)throw n;return n}o=qi(o,e)}catch(e){r=!0,o=e}if("throw"===t)throw n;if(r)throw o;return Ii(o),n},Mi=s.TypeError,Li=function(e,t){this.stopped=e,this.result=t},Ji=Li.prototype,ji=function(e,n,o){var r,i,s,a,u,c,l,d=o&&o.that,f=!(!o||!o.AS_ENTRIES),h=!(!o||!o.IS_ITERATOR),p=!(!o||!o.INTERRUPTED),v=Ei(n,d),b=function(e){return r&&Fi(r,"normal",e),new Li(!0,e)},g=function(e){return f?(Ti(e),p?v(e[0],e[1],b):v(e[0],e[1])):p?v(e,b):v(e)};if(h)r=e;else{if(!(i=Wi(e)))throw Mi(zi(e)+" is not iterable");if(Ai(i)){for(s=0,a=Ui(e);a>s;s++)if((u=g(e[s]))&&Di(Ji,u))return u;return new Li(!1)}r=xi(e,i)}for(c=r.next;!(l=Oi(c,r)).done;){try{u=g(l.value)}catch(e){Fi(r,"throw",e)}if("object"==t(u)&&u&&Di(Ji,u))return u}return new Li(!1)},Ni=pi,Bi=s.String,Zi=function(e){if("Symbol"===Ni(e))throw TypeError("Cannot convert a Symbol value to a string");return Bi(e)},Hi=Zi,Gi=m,Vi=!u((function(){var e=Error("a");return!("stack"in e)||(Object.defineProperty(e,"stack",Gi(1,7)),7!==e.stack)})),Xi=Fo,Yi=s,Ki=H,$i=hr,Qi=wr,es=wo,ts=Mr,ns=Mt,os=m,rs=function(e,t){if("string"!=typeof e)return e;if(Vr)for(;t--;)e=Nr(e,Gr,"");else if(Xr)return Zr(jr(Br(e,"\n"),t),"\n");return e},is=function(e,t){Yr(t)&&"cause"in t&&Kr(e,"cause",t.cause)},ss=ji,as=function(e,t){return void 0===e?arguments.length<2?"":t:Hi(e)},us=Vi,cs=$e("toStringTag"),ls=Yi.Error,ds=[].push,fs=function(e,t){var n,o=arguments.length>2?arguments[2]:void 0,r=Ki(hs,this);Qi?n=Qi(new ls(void 0),r?$i(this):hs):(n=r?this:ts(hs),ns(n,cs,"Error")),ns(n,"message",as(t,"")),us&&ns(n,"stack",rs(n.stack,1)),is(n,o);var i=[];return ss(e,ds,{that:i}),ns(n,"errors",i),n};Qi?Qi(fs,ls):es(fs,ls);var hs=fs.prototype=ts(ls.prototype,{constructor:os(1,fs),message:os(1,""),name:os(1,"AggregateError")});Xi({global:!0},{AggregateError:fs});var ps=Mr,vs=Rt,bs=$e("unscopables"),gs=Array.prototype;null==gs[bs]&&vs.f(gs,bs,{configurable:!0,value:ps(null)});var ms,ys,_s,ws=u,Cs=M,ks=hr,Ss=Lt.exports,Rs=$e("iterator"),qs=!1;[].keys&&("next"in(_s=[].keys())?(ys=ks(ks(_s)))!==Object.prototype&&(ms=ys):qs=!0);var Is=null==ms||ws((function(){var e={};return ms[Rs].call(e)!==e}));Is&&(ms={}),Cs(ms[Rs])||Ss(ms,Rs,(function(){return this}));var Ps={IteratorPrototype:ms,BUGGY_SAFARI_ITERATORS:qs},Es=Rt.f,Os=xe,Ts=$e("toStringTag"),zs=function(e,t,n){e&&!Os(e=n?e:e.prototype,Ts)&&Es(e,Ts,{configurable:!0,value:t})},As=Ps.IteratorPrototype,Us=Mr,Ds=m,xs=zs,Ws=ti,Fs=function(){return this},Ms=Fo,Ls=d,Js=In,js=M,Ns=function(e,t,n){var o=t+" Iterator";return e.prototype=Us(As,{next:Ds(1,n)}),xs(e,o,!1),Ws[o]=Fs,e},Bs=hr,Zs=wr,Hs=zs,Gs=Mt,Vs=Lt.exports,Xs=ti,Ys=Js.PROPER,Ks=Js.CONFIGURABLE,$s=Ps.IteratorPrototype,Qs=Ps.BUGGY_SAFARI_ITERATORS,ea=$e("iterator"),ta="keys",na="values",oa="entries",ra=function(){return this},ia=function(e,t,n,o,r,i,s){Ns(n,t,o);var a,u,c,l=function(e){if(e===r&&v)return v;if(!Qs&&e in h)return h[e];switch(e){case ta:case na:case oa:return function(){return new n(this,e)}}return function(){return new n(this)}},d=t+" Iterator",f=!1,h=e.prototype,p=h[ea]||h["@@iterator"]||r&&h[r],v=!Qs&&p||l(r),b="Array"==t&&h.entries||p;if(b&&(a=Bs(b.call(new e)))!==Object.prototype&&a.next&&(Bs(a)!==$s&&(Zs?Zs(a,$s):js(a[ea])||Vs(a,ea,ra)),Hs(a,d,!0)),Ys&&r==na&&p&&p.name!==na&&(Ks?Gs(h,"name",na):(f=!0,v=function(){return Ls(p,this)})),r)if(u={values:l(na),keys:i?v:l(ta),entries:l(oa)},s)for(c in u)(Qs||f||!(c in h))&&Vs(h,c,u[c]);else Ms({target:t,proto:!0,forced:Qs||f},u);return h[ea]!==v&&Vs(h,ea,v,{name:r}),Xs[t]=v,u},sa=F,aa=function(e){gs[bs][e]=!0},ua=ti,ca=wn,la=ia,da="Array Iterator",fa=ca.set,ha=ca.getterFor(da),pa=la(Array,"Array",(function(e,t){fa(this,{type:da,target:sa(e),index:0,kind:t})}),(function(){var e=ha(this),t=e.target,n=e.kind,o=e.index++;return!t||o>=t.length?(e.target=void 0,{value:void 0,done:!0}):"keys"==n?{value:o,done:!1}:"values"==n?{value:t[o],done:!1}:{value:[o,t[o]],done:!1}}),"values");ua.Arguments=ua.Array,aa("keys"),aa("values"),aa("entries");var va=pi,ba=si?{}.toString:function(){return"[object "+va(this)+"]"},ga=si,ma=Lt.exports,ya=ba;ga||ma(Object.prototype,"toString",ya,{unsafe:!0});var _a=s.Promise,wa=Lt.exports,Ca=Z,ka=Rt,Sa=c,Ra=$e("species"),qa=H,Ia=s.TypeError,Pa=$e("iterator"),Ea=!1;try{var Oa=0,Ta={next:function(){return{done:!!Oa++}},return:function(){Ea=!0}};Ta[Pa]=function(){return this},Array.from(Ta,(function(){throw 2}))}catch(e){}var za,Aa,Ua,Da,xa=k,Wa=u,Fa=M,Ma=pi,La=Gt,Ja=function(){},ja=[],Na=Z("Reflect","construct"),Ba=/^\s*(?:class|function)\b/,Za=xa(Ba.exec),Ha=!Ba.exec(Ja),Ga=function(e){if(!Fa(e))return!1;try{return Na(Ja,ja,e),!0}catch(e){return!1}},Va=!Na||Wa((function(){var e;return Ga(Ga.call)||!Ga(Object)||!Ga((function(){e=!0}))||e}))?function(e){if(!Fa(e))return!1;switch(Ma(e)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}return Ha||!!Za(Ba,La(e))}:Ga,Xa=Va,Ya=fe,Ka=s.TypeError,$a=Ot,Qa=function(e){if(Xa(e))return e;throw Ka(Ya(e)+" is not a constructor")},eu=$e("species"),tu=function(e,t){var n,o=$a(e).constructor;return void 0===o||null==(n=$a(o)[eu])?t:Qa(n)},nu=Function.prototype,ou=nu.apply,ru=nu.bind,iu=nu.call,su="object"==("undefined"==typeof Reflect?"undefined":t(Reflect))&&Reflect.apply||(ru?iu.bind(ou):function(){return iu.apply(ou,arguments)}),au=/(?:ipad|iphone|ipod).*applewebkit/i.test(G),uu="process"==I(s.process),cu=s,lu=su,du=ei,fu=M,hu=xe,pu=u,vu=Ir,bu=Lr,gu=ht,mu=au,yu=uu,_u=cu.setImmediate,wu=cu.clearImmediate,Cu=cu.process,ku=cu.Dispatch,Su=cu.Function,Ru=cu.MessageChannel,qu=cu.String,Iu=0,Pu={},Eu="onreadystatechange";try{za=cu.location}catch(e){}var Ou=function(e){if(hu(Pu,e)){var t=Pu[e];delete Pu[e],t()}},Tu=function(e){return function(){Ou(e)}},zu=function(e){Ou(e.data)},Au=function(e){cu.postMessage(qu(e),za.protocol+"//"+za.host)};_u&&wu||(_u=function(e){var t=bu(arguments,1);return Pu[++Iu]=function(){lu(fu(e)?e:Su(e),void 0,t)},Aa(Iu),Iu},wu=function(e){delete Pu[e]},yu?Aa=function(e){Cu.nextTick(Tu(e))}:ku&&ku.now?Aa=function(e){ku.now(Tu(e))}:Ru&&!mu?(Da=(Ua=new Ru).port2,Ua.port1.onmessage=zu,Aa=du(Da.postMessage,Da)):cu.addEventListener&&fu(cu.postMessage)&&!cu.importScripts&&za&&"file:"!==za.protocol&&!pu(Au)?(Aa=Au,cu.addEventListener("message",zu,!1)):Aa=Eu in gu("script")?function(e){vu.appendChild(gu("script")).onreadystatechange=function(){vu.removeChild(this),Ou(e)}}:function(e){setTimeout(Tu(e),0)});var Uu,Du,xu,Wu,Fu,Mu,Lu,Ju,ju={set:_u,clear:wu},Nu=s,Bu=/ipad|iphone|ipod/i.test(G)&&void 0!==Nu.Pebble,Zu=/web0s(?!.*chrome)/i.test(G),Hu=s,Gu=ei,Vu=a.f,Xu=ju.set,Yu=au,Ku=Bu,$u=Zu,Qu=uu,ec=Hu.MutationObserver||Hu.WebKitMutationObserver,tc=Hu.document,nc=Hu.process,oc=Hu.Promise,rc=Vu(Hu,"queueMicrotask"),ic=rc&&rc.value;ic||(Uu=function(){var e,t;for(Qu&&(e=nc.domain)&&e.exit();Du;){t=Du.fn,Du=Du.next;try{t()}catch(e){throw Du?Wu():xu=void 0,e}}xu=void 0,e&&e.enter()},Yu||Qu||$u||!ec||!tc?!Ku&&oc&&oc.resolve?((Lu=oc.resolve(void 0)).constructor=oc,Ju=Gu(Lu.then,Lu),Wu=function(){Ju(Uu)}):Qu?Wu=function(){nc.nextTick(Uu)}:(Xu=Gu(Xu,Hu),Wu=function(){Xu(Uu)}):(Fu=!0,Mu=tc.createTextNode(""),new ec(Uu).observe(Mu,{characterData:!0}),Wu=function(){Mu.data=Fu=!Fu}));var sc=ic||function(e){var t={fn:e,next:void 0};xu&&(xu.next=t),Du||(Du=t,Wu()),xu=t},ac={},uc=be,cc=function(e){var t,n;this.promise=new e((function(e,o){if(void 0!==t||void 0!==n)throw TypeError("Bad Promise constructor");t=e,n=o})),this.resolve=uc(t),this.reject=uc(n)};ac.f=function(e){return new cc(e)};var lc,dc,fc,hc,pc=Ot,vc=J,bc=ac,gc=function(e,t){if(pc(e),vc(t)&&t.constructor===e)return t;var n=bc.f(e);return(0,n.resolve)(t),n.promise},mc=s,yc=function(e){try{return{error:!1,value:e()}}catch(e){return{error:!0,value:e}}},_c="object"==("undefined"==typeof window?"undefined":t(window)),wc=Fo,Cc=s,kc=Z,Sc=d,Rc=_a,qc=Lt.exports,Ic=function(e,t,n){for(var o in t)wa(e,o,t[o],n);return e},Pc=wr,Ec=zs,Oc=function(e){var t=Ca(e),n=ka.f;Sa&&t&&!t[Ra]&&n(t,Ra,{configurable:!0,get:function(){return this}})},Tc=be,zc=M,Ac=J,Uc=function(e,t){if(qa(t,e))return e;throw Ia("Incorrect invocation")},Dc=Gt,xc=ji,Wc=function(e,t){if(!t&&!Ea)return!1;var n=!1;try{var o={};o[Pa]=function(){return{next:function(){return{done:n=!0}}}},e(o)}catch(e){}return n},Fc=tu,Mc=ju.set,Lc=sc,Jc=gc,jc=function(e,t){var n=mc.console;n&&n.error&&(1==arguments.length?n.error(e):n.error(e,t))},Nc=ac,Bc=yc,Zc=wn,Hc=Oo,Gc=_c,Vc=uu,Xc=ee,Yc=$e("species"),Kc="Promise",$c=Zc.get,Qc=Zc.set,el=Zc.getterFor(Kc),tl=Rc&&Rc.prototype,nl=Rc,ol=tl,rl=Cc.TypeError,il=Cc.document,sl=Cc.process,al=Nc.f,ul=al,cl=!!(il&&il.createEvent&&Cc.dispatchEvent),ll=zc(Cc.PromiseRejectionEvent),dl="unhandledrejection",fl=!1,hl=Hc(Kc,(function(){var e=Dc(nl),t=e!==String(nl);if(!t&&66===Xc)return!0;if(Xc>=51&&/native code/.test(e))return!1;var n=new nl((function(e){e(1)})),o=function(e){e((function(){}),(function(){}))};return(n.constructor={})[Yc]=o,!(fl=n.then((function(){}))instanceof o)||!t&&Gc&&!ll})),pl=hl||!Wc((function(e){nl.all(e).catch((function(){}))})),vl=function(e){var t;return!(!Ac(e)||!zc(t=e.then))&&t},bl=function(e,t){if(!e.notified){e.notified=!0;var n=e.reactions;Lc((function(){for(var o=e.value,r=1==e.state,i=0;n.length>i;){var s,a,u,c=n[i++],l=r?c.ok:c.fail,d=c.resolve,f=c.reject,h=c.domain;try{l?(r||(2===e.rejection&&_l(e),e.rejection=1),!0===l?s=o:(h&&h.enter(),s=l(o),h&&(h.exit(),u=!0)),s===c.promise?f(rl("Promise-chain cycle")):(a=vl(s))?Sc(a,s,d,f):d(s)):f(o)}catch(e){h&&!u&&h.exit(),f(e)}}e.reactions=[],e.notified=!1,t&&!e.rejection&&ml(e)}))}},gl=function(e,t,n){var o,r;cl?((o=il.createEvent("Event")).promise=t,o.reason=n,o.initEvent(e,!1,!0),Cc.dispatchEvent(o)):o={promise:t,reason:n},!ll&&(r=Cc["on"+e])?r(o):e===dl&&jc("Unhandled promise rejection",n)},ml=function(e){Sc(Mc,Cc,(function(){var t,n=e.facade,o=e.value;if(yl(e)&&(t=Bc((function(){Vc?sl.emit("unhandledRejection",o,n):gl(dl,n,o)})),e.rejection=Vc||yl(e)?2:1,t.error))throw t.value}))},yl=function(e){return 1!==e.rejection&&!e.parent},_l=function(e){Sc(Mc,Cc,(function(){var t=e.facade;Vc?sl.emit("rejectionHandled",t):gl("rejectionhandled",t,e.value)}))},wl=function(e,t,n){return function(o){e(t,o,n)}},Cl=function(e,t,n){e.done||(e.done=!0,n&&(e=n),e.value=t,e.state=2,bl(e,!0))},kl=function e(t,n,o){if(!t.done){t.done=!0,o&&(t=o);try{if(t.facade===n)throw rl("Promise can't be resolved itself");var r=vl(n);r?Lc((function(){var o={done:!1};try{Sc(r,n,wl(e,o,t),wl(Cl,o,t))}catch(e){Cl(o,e,t)}})):(t.value=n,t.state=1,bl(t,!1))}catch(e){Cl({done:!1},e,t)}}};if(hl&&(ol=(nl=function(e){Uc(this,ol),Tc(e),Sc(lc,this);var t=$c(this);try{e(wl(kl,t),wl(Cl,t))}catch(e){Cl(t,e)}}).prototype,(lc=function(e){Qc(this,{type:Kc,done:!1,notified:!1,parent:!1,reactions:[],rejection:!1,state:0,value:void 0})}).prototype=Ic(ol,{then:function(e,t){var n=el(this),o=n.reactions,r=al(Fc(this,nl));return r.ok=!zc(e)||e,r.fail=zc(t)&&t,r.domain=Vc?sl.domain:void 0,n.parent=!0,o[o.length]=r,0!=n.state&&bl(n,!1),r.promise},catch:function(e){return this.then(void 0,e)}}),dc=function(){var e=new lc,t=$c(e);this.promise=e,this.resolve=wl(kl,t),this.reject=wl(Cl,t)},Nc.f=al=function(e){return e===nl||e===fc?new dc(e):ul(e)},zc(Rc)&&tl!==Object.prototype)){hc=tl.then,fl||(qc(tl,"then",(function(e,t){var n=this;return new nl((function(e,t){Sc(hc,n,e,t)})).then(e,t)}),{unsafe:!0}),qc(tl,"catch",ol.catch,{unsafe:!0}));try{delete tl.constructor}catch(e){}Pc&&Pc(tl,ol)}wc({global:!0,wrap:!0,forced:hl},{Promise:nl}),Ec(nl,Kc,!1),Oc(Kc),fc=kc(Kc),wc({target:Kc,stat:!0,forced:hl},{reject:function(e){var t=al(this);return Sc(t.reject,void 0,e),t.promise}}),wc({target:Kc,stat:!0,forced:hl},{resolve:function(e){return Jc(this,e)}}),wc({target:Kc,stat:!0,forced:pl},{all:function(e){var t=this,n=al(t),o=n.resolve,r=n.reject,i=Bc((function(){var n=Tc(t.resolve),i=[],s=0,a=1;xc(e,(function(e){var u=s++,c=!1;a++,Sc(n,t,e).then((function(e){c||(c=!0,i[u]=e,--a||o(i))}),r)})),--a||o(i)}));return i.error&&r(i.value),n.promise},race:function(e){var t=this,n=al(t),o=n.reject,r=Bc((function(){var r=Tc(t.resolve);xc(e,(function(e){Sc(r,t,e).then(n.resolve,o)}))}));return r.error&&o(r.value),n.promise}});var Sl=d,Rl=be,ql=ac,Il=yc,Pl=ji;Fo({target:"Promise",stat:!0},{allSettled:function(e){var t=this,n=ql.f(t),o=n.resolve,r=n.reject,i=Il((function(){var n=Rl(t.resolve),r=[],i=0,s=1;Pl(e,(function(e){var a=i++,u=!1;s++,Sl(n,t,e).then((function(e){u||(u=!0,r[a]={status:"fulfilled",value:e},--s||o(r))}),(function(e){u||(u=!0,r[a]={status:"rejected",reason:e},--s||o(r))}))})),--s||o(r)}));return i.error&&r(i.value),n.promise}});var El=be,Ol=Z,Tl=d,zl=ac,Al=yc,Ul=ji,Dl="No one promise resolved";Fo({target:"Promise",stat:!0},{any:function(e){var t=this,n=Ol("AggregateError"),o=zl.f(t),r=o.resolve,i=o.reject,s=Al((function(){var o=El(t.resolve),s=[],a=0,u=1,c=!1;Ul(e,(function(e){var l=a++,d=!1;u++,Tl(o,t,e).then((function(e){d||c||(c=!0,r(e))}),(function(e){d||c||(d=!0,s[l]=e,--u||i(new n(s,Dl)))}))})),--u||i(new n(s,Dl))}));return s.error&&i(s.value),o.promise}});var xl=Fo,Wl=_a,Fl=u,Ml=Z,Ll=M,Jl=tu,jl=gc,Nl=Lt.exports;if(xl({target:"Promise",proto:!0,real:!0,forced:!!Wl&&Fl((function(){Wl.prototype.finally.call({then:function(){}},(function(){}))}))},{finally:function(e){var t=Jl(this,Ml("Promise")),n=Ll(e);return this.then(n?function(n){return jl(t,e()).then((function(){return n}))}:e,n?function(n){return jl(t,e()).then((function(){throw n}))}:e)}}),Ll(Wl)){var Bl=Ml("Promise").prototype.finally;Wl.prototype.finally!==Bl&&Nl(Wl.prototype,"finally",Bl,{unsafe:!0})}var Zl=k,Hl=Jn,Gl=Zi,Vl=D,Xl=Zl("".charAt),Yl=Zl("".charCodeAt),Kl=Zl("".slice),$l=function(e){return function(t,n){var o,r,i=Gl(Vl(t)),s=Hl(n),a=i.length;return s<0||s>=a?e?"":void 0:(o=Yl(i,s))<55296||o>56319||s+1===a||(r=Yl(i,s+1))<56320||r>57343?e?Xl(i,s):o:e?Kl(i,s,s+2):r-56320+(o-55296<<10)+65536}},Ql={codeAt:$l(!1),charAt:$l(!0)}.charAt,ed=Zi,td=wn,nd=ia,od="String Iterator",rd=td.set,id=td.getterFor(od);nd(String,"String",(function(e){rd(this,{type:od,string:ed(e),index:0})}),(function(){var e,t=id(this),n=t.string,o=t.index;return o>=n.length?{value:void 0,done:!0}:(e=Ql(n,o),t.index+=e.length,{value:e,done:!1})})),nr.Promise;var sd,ad=ht("span").classList,ud=ad&&ad.constructor&&ad.constructor.prototype,cd=ud===Object.prototype?void 0:ud,ld=s,dd={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},fd=cd,hd=pa,pd=Mt,vd=$e,bd=vd("iterator"),gd=vd("toStringTag"),md=hd.values,yd=function(e,t){if(e){if(e[bd]!==md)try{pd(e,bd,md)}catch(t){e[bd]=md}if(e[gd]||pd(e,gd,t),dd[t])for(var n in hd)if(e[n]!==hd[n])try{pd(e,n,hd[n])}catch(t){e[n]=hd[n]}}};for(var _d in dd)yd(ld[_d]&&ld[_d].prototype,_d);yd(fd,"DOMTokenList");var wd=new Uint8Array(16);function Cd(){if(!sd&&!(sd="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto)))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return sd(wd)}var kd=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;function Sd(e){return"string"==typeof e&&kd.test(e)}for(var Rd=[],qd=0;qd<256;++qd)Rd.push((qd+256).toString(16).substr(1));function Id(e,t,n){var o=(e=e||{}).random||(e.rng||Cd)();if(o[6]=15&o[6]|64,o[8]=63&o[8]|128,t){n=n||0;for(var r=0;r<16;++r)t[n+r]=o[r];return t}return function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=(Rd[e[t+0]]+Rd[e[t+1]]+Rd[e[t+2]]+Rd[e[t+3]]+"-"+Rd[e[t+4]]+Rd[e[t+5]]+"-"+Rd[e[t+6]]+Rd[e[t+7]]+"-"+Rd[e[t+8]]+Rd[e[t+9]]+"-"+Rd[e[t+10]]+Rd[e[t+11]]+Rd[e[t+12]]+Rd[e[t+13]]+Rd[e[t+14]]+Rd[e[t+15]]).toLowerCase();if(!Sd(n))throw TypeError("Stringified UUID is invalid");return n}(o)}var Pd,Ed="3.7.2",Od="function"==typeof atob,Td="function"==typeof btoa,zd="function"==typeof Buffer,Ad="function"==typeof TextDecoder?new TextDecoder:void 0,Ud="function"==typeof TextEncoder?new TextEncoder:void 0,Dd=Array.prototype.slice.call("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="),xd=(Pd={},Dd.forEach((function(e,t){return Pd[e]=t})),Pd),Wd=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/,Fd=String.fromCharCode.bind(String),Md="function"==typeof Uint8Array.from?Uint8Array.from.bind(Uint8Array):function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(e){return e};return new Uint8Array(Array.prototype.slice.call(e,0).map(t))},Ld=function(e){return e.replace(/=/g,"").replace(/[+\/]/g,(function(e){return"+"==e?"-":"_"}))},Jd=function(e){return e.replace(/[^A-Za-z0-9\+\/]/g,"")},jd=function(e){for(var t,n,o,r,i="",s=e.length%3,a=0;a<e.length;){if((n=e.charCodeAt(a++))>255||(o=e.charCodeAt(a++))>255||(r=e.charCodeAt(a++))>255)throw new TypeError("invalid character found");i+=Dd[(t=n<<16|o<<8|r)>>18&63]+Dd[t>>12&63]+Dd[t>>6&63]+Dd[63&t]}return s?i.slice(0,s-3)+"===".substring(s):i},Nd=Td?function(e){return btoa(e)}:zd?function(e){return Buffer.from(e,"binary").toString("base64")}:jd,Bd=zd?function(e){return Buffer.from(e).toString("base64")}:function(e){for(var t=[],n=0,o=e.length;n<o;n+=4096)t.push(Fd.apply(null,e.subarray(n,n+4096)));return Nd(t.join(""))},Zd=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return t?Ld(Bd(e)):Bd(e)},Hd=function(e){if(e.length<2)return(t=e.charCodeAt(0))<128?e:t<2048?Fd(192|t>>>6)+Fd(128|63&t):Fd(224|t>>>12&15)+Fd(128|t>>>6&63)+Fd(128|63&t);var t=65536+1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320);return Fd(240|t>>>18&7)+Fd(128|t>>>12&63)+Fd(128|t>>>6&63)+Fd(128|63&t)},Gd=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,Vd=function(e){return e.replace(Gd,Hd)},Xd=zd?function(e){return Buffer.from(e,"utf8").toString("base64")}:Ud?function(e){return Bd(Ud.encode(e))}:function(e){return Nd(Vd(e))},Yd=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return t?Ld(Xd(e)):Xd(e)},Kd=function(e){return Yd(e,!0)},$d=/[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g,Qd=function(e){switch(e.length){case 4:var t=((7&e.charCodeAt(0))<<18|(63&e.charCodeAt(1))<<12|(63&e.charCodeAt(2))<<6|63&e.charCodeAt(3))-65536;return Fd(55296+(t>>>10))+Fd(56320+(1023&t));case 3:return Fd((15&e.charCodeAt(0))<<12|(63&e.charCodeAt(1))<<6|63&e.charCodeAt(2));default:return Fd((31&e.charCodeAt(0))<<6|63&e.charCodeAt(1))}},ef=function(e){return e.replace($d,Qd)},tf=function(e){if(e=e.replace(/\s+/g,""),!Wd.test(e))throw new TypeError("malformed base64.");e+="==".slice(2-(3&e.length));for(var t,n,o,r="",i=0;i<e.length;)t=xd[e.charAt(i++)]<<18|xd[e.charAt(i++)]<<12|(n=xd[e.charAt(i++)])<<6|(o=xd[e.charAt(i++)]),r+=64===n?Fd(t>>16&255):64===o?Fd(t>>16&255,t>>8&255):Fd(t>>16&255,t>>8&255,255&t);return r},nf=Od?function(e){return atob(Jd(e))}:zd?function(e){return Buffer.from(e,"base64").toString("binary")}:tf,of=zd?function(e){return Md(Buffer.from(e,"base64"))}:function(e){return Md(nf(e),(function(e){return e.charCodeAt(0)}))},rf=function(e){return of(af(e))},sf=zd?function(e){return Buffer.from(e,"base64").toString("utf8")}:Ad?function(e){return Ad.decode(of(e))}:function(e){return ef(nf(e))},af=function(e){return Jd(e.replace(/[-_]/g,(function(e){return"-"==e?"+":"/"})))},uf=function(e){return sf(af(e))},cf=function(e){return{value:e,enumerable:!1,writable:!0,configurable:!0}},lf=function(){var e=function(e,t){return Object.defineProperty(String.prototype,e,cf(t))};e("fromBase64",(function(){return uf(this)})),e("toBase64",(function(e){return Yd(this,e)})),e("toBase64URI",(function(){return Yd(this,!0)})),e("toBase64URL",(function(){return Yd(this,!0)})),e("toUint8Array",(function(){return rf(this)}))},df=function(){var e=function(e,t){return Object.defineProperty(Uint8Array.prototype,e,cf(t))};e("toBase64",(function(e){return Zd(this,e)})),e("toBase64URI",(function(){return Zd(this,!0)})),e("toBase64URL",(function(){return Zd(this,!0)}))},ff={version:Ed,VERSION:"3.7.2",atob:nf,atobPolyfill:tf,btoa:Nd,btoaPolyfill:jd,fromBase64:uf,toBase64:Yd,encode:Yd,encodeURI:Kd,encodeURL:Kd,utob:Vd,btou:ef,decode:uf,isValid:function(e){if("string"!=typeof e)return!1;var t=e.replace(/\s+/g,"").replace(/={0,2}$/,"");return!/[^\s0-9a-zA-Z\+/]/.test(t)||!/[^\s0-9a-zA-Z\-_]/.test(t)},fromUint8Array:Zd,toUint8Array:rf,extendString:lf,extendUint8Array:df,extendBuiltins:function(){lf(),df()}},hf=function(){function e(){n(this,e),this.oBase64=ff}return r(e,[{key:"browser",value:function(){var e=navigator.userAgent.toLowerCase(),t=/(edge)[/]([\w.]+)/.exec(e)||/(chrome)[/]([\w.]+)/.exec(e)||/(safari)[/]([\w.]+)/.exec(e)||/(opera)(?:.*version)?[/]([\w.]+)/.exec(e)||/(trident.*rv:)([\w.]+)/.exec(e)||/(msie) ([\w.]+)/.exec(e)||e.indexOf("compatible")<0&&/(mozilla)(?:.*? rv:([\w.]+))?/.exec(e)||["unknow","0"];t.length>0&&t[1].indexOf("trident")>-1&&(t[1]="msie");var n={};return n[t[1]]=!0,n.version=t[2],n}},{key:"getCreateWndMode",value:function(){var e=navigator.userAgent,t=navigator.platform,n="Win64"===t||"Win32"===t||"Windows"===t,o=this.browser(),r=!0;return window.top!==window?r=!1:n?(e.indexOf("Windows NT 10.0")>-1&&o.mozilla&&(r=!1),o.edge&&(r=!1)):r=!1,r}},{key:"getWndPostion",value:function(e,t,n,o){var r=0,i=0,s=e.ownerDocument.defaultView,a=e.getBoundingClientRect(),u=window.getComputedStyle(e),c={top:a.top+parseInt(u["border-top-width"].slice(0,-2),10),left:a.left+parseInt(u["border-left-width"].slice(0,-2),10)},l=this.getDevicePixelRatio(),d=this.browser().chrome?s.outerWidth/l:s.outerWidth,f=this.browser().chrome?s.outerHeight/l:s.outerHeight;if(t)if(this.browser().msie){var h=s.outerWidth-s.innerWidth-(s.screenLeft-s.screenX);r=c.left+(s.screenLeft-s.screenX)-h,i=c.top+(s.screenTop-s.screenY)}else{var p=0,v=0,b=Math.round((d-s.innerWidth)/2);this.isWindows()&&this.browser().chrome&&(-8===b||s.screen.height-s.outerHeight==0?-8===b&&(p=8,v=8):8===b?p=-5:0===b&&(v=8)),this.browser().mozilla&&(7===b||6===b?p=-6:8===b&&(p=-8)),r=c.left+b+p,i=c.top+(f-s.innerHeight-b)+v}else{var g=window.top,m=0,y=0,_=0,w=0;try{m=d-g.innerWidth,y=f-g.innerHeight,_=g.screenLeft-g.screenX,w=g.screenTop-g.screenY}catch(e){m=n.outerWidth-n.innerWidth,y=n.outerHeight-n.innerHeight,_=n.screenLeft-n.screenX,w=n.screenTop-n.screenY}if(this.browser().msie){0,r=c.left+_-0,i=c.top+w}else{var C=m/2;r=c.left+C,i=c.top+(y-C),this.isWindows()&&this.browser().chrome&&0===C&&(r+=8,i+=8)}r+=o.left,i+=o.top}return this.isWindows()&&(this.browser().chrome||this.browser().safari)&&(r=c.left,i=c.top,r+=o.left,i+=o.top),this.browser().msie&&"10.0"===this.browser().version&&(r+=s.pageXOffset,i+=s.pageYOffset),{left:r=Math.round(r*l),top:i=Math.round(i*l)}}},{key:"detectPort",value:function(e,t,n){if(e!==t){var o="HikCentralWebControlPort:".concat(e,"-").concat(t),r=this,i=0,s=!1,a=null;sessionStorage&&null!==(a=sessionStorage.getItem(o))&&(a=parseInt(a,10));for(var u=[],c=e;c<=t;c++)c!==a&&u.push(c);null!==a&&u.unshift(a);for(var l=[],d=function(){i>0&&clearTimeout(i)},f=function(){for(var e=0,t=l.length;e<t;e++)delete l[e]},h=0,p=(new Date).getTime(),v=function(e,t){setTimeout((function(){l.push(r.createImageHttp(u[t],{timeStamp:p+t,success:function(e){!function(e){sessionStorage&&sessionStorage.setItem(o,e),!s&&n.success&&(d(),f(),n.success(e))}(e)},error:function(){h++,u.length===h&&!s&&n.error&&(d(),f(),n.error())}}))}),100)},b=0,g=u.length;b<g;b++)v(0,b);i=setTimeout((function(){s=!0,n.error&&(f(),n.error())}),6e4)}else n.success(e)}},{key:"createImageHttp",value:function(e,t){var n=new Image;return n.crossOrigin="anonymous",n.onload=function(){t.success&&t.success(e)},n.onerror=function(){t.error&&t.error()},n.onabort=function(){t.abort&&t.abort()},n.src="http://127.0.0.1:".concat(e,"/imghttp/local?update=").concat(t.timeStamp),n}},{key:"utf8to16",value:function(e){for(var t,n,o,r="",i=0,s=e.length;i<s;)switch((t=e.charCodeAt(i++))>>4){case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:r+=e.charAt(i-1);break;case 12:case 13:n=e.charCodeAt(i++),r+=String.fromCharCode((31&t)<<6|63&n);break;case 14:n=e.charCodeAt(i++),o=e.charCodeAt(i++),r+=String.fromCharCode((15&t)<<12|(63&n)<<6|(63&o)<<0)}return r}},{key:"createEventScript",value:function(e,t,n){var o=document.createElement("script");o.htmlFor=e,o.event=t,o.innerHTML=n,document.getElementById(e).appendChild(o)}},{key:"isMacOS",value:function(){return"MacIntel"===navigator.platform}},{key:"isWindows",value:function(){return navigator.platform.indexOf("Win")>-1}},{key:"getDevicePixelRatio",value:function(){var e=1;return this.isMacOS()||(e=window.devicePixelRatio||window.screen.deviceXDPI/window.screen.logicalXDPI),e}},{key:"Base64",value:function(){return this.oBase64||{}}}]),e}(),pf=new hf,vf="　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　";function bf(e){window.top.document.title=window.top.document.title+vf+e}function gf(e){window.top.document.title=window.top.document.title.replace(vf+e,"")}function mf(e,t){(t||bf)(e)}function yf(e,t){(t||gf)(e)}var _f=function(){function e(t){n(this,e),this.oOptions=Object.assign({iPort:-1,cbConnectSuccess:null,cbConnectError:null,cbConnectClose:null},t),this.oWebSocket=null,this.szUUID="",this.szVersion="",this.oRequestList={},this.bNormalClose=!1,this.oWindowControlCallback={},this.oSadpCallback={},this.oSliceCallback={},this.oSerialCallback={},this.oUIControlCallback={},this.oUpgradeCallback={},this.init()}return r(e,[{key:"init",value:function(){var e=this,t=function(){e.oOptions.cbConnectClose&&e.oOptions.cbConnectClose(e.bNormalClose),e.bNormalClose=!1};e.oWebSocket=new WebSocket("ws://127.0.0.1:".concat(e.oOptions.iPort)),e.oWebSocket.onerror=function(){},e.oWebSocket.onopen=function(){var t={sequence:Id(),cmd:"system.connect"},n=JSON.stringify(t);e.oWebSocket.send(n)},e.oWebSocket.onmessage=function(t){var n=t.data,o=JSON.parse(n),r=o.sequence;void 0===r&&void 0===o.cmd?(e.szUUID=o.uuid,e.szVersion=o.version,e.oOptions.cbConnectSuccess&&e.oOptions.cbConnectSuccess()):void 0!==o.cmd?e.parseCmd(o):void 0!==e.oRequestList[r]&&(0===o.errorModule&&0===o.errorCode?e.oRequestList[r].resolve(o):e.oRequestList[r].reject(o),delete e.oRequestList[r])},e.oWebSocket.onclose=function(){e.oWebSocket=null,pf.browser().mozilla?setTimeout((function(){t()}),100):t()}}},{key:"setWindowControlCallback",value:function(e){this.oWindowControlCallback=e}},{key:"setSadpCallback",value:function(e){this.oSadpCallback=e}},{key:"setSliceCallback",value:function(e){this.oSliceCallback=e}},{key:"setSerialCallback",value:function(e){this.oSerialCallback=e}},{key:"setUIControlCallback",value:function(e){this.oUIControlCallback=e}},{key:"setUpgradeCallback",value:function(e){this.oUpgradeCallback=e}},{key:"getServiceVersion",value:function(){return this.szVersion}},{key:"getRequestUUID",value:function(){return this.szUUID}},{key:"disconnect",value:function(){this.bNormalClose=!0,this.oWebSocket&&WebSocket.OPEN===this.oWebSocket.readyState&&(this.oWebSocket.close(),delete this.oWebSocket)}},{key:"sendRequest",value:function(e){var t=this;return new Promise((function(n,o){var r=Id();e.sequence=r,t.oRequestList[r]={resolve:n,reject:o},e.uuid=t.szUUID,e.timestamp="".concat((new Date).getTime());var i=JSON.stringify(e);t.oWebSocket&&WebSocket.OPEN===t.oWebSocket.readyState?t.oWebSocket.send(i):o()}))}},{key:"parseCmd",value:function(e){var t=e.cmd.split("."),n=t[1].replace(/^[a-z]{1}/g,(function(e){return e.toUpperCase()}));"window"===t[0]||"play"===t[0]?this.oWindowControlCallback["cb".concat(n)]&&this.oWindowControlCallback["cb".concat(n)](e):"sadp"===t[0]?this.oSadpCallback["cb".concat(n)]&&this.oSadpCallback["cb".concat(n)](e):"serial"===t[0]?this.oSerialCallback["cb".concat(n)]&&this.oSerialCallback["cb".concat(n)](e):"slice"===t[0]?this.oSliceCallback["cb".concat(n)]&&this.oSliceCallback["cb".concat(n)](e):"ui"===t[0]?this.oUIControlCallback["cb".concat(n)]&&this.oUIControlCallback["cb".concat(n)](e):"upgrade"===t[0]&&this.oUpgradeCallback["cb".concat(n)]&&this.oUpgradeCallback["cb".concat(n)](e)}}]),e}();
/*! pako 2.0.4 https://github.com/nodeca/pako @license (MIT AND Zlib) */function wf(e){for(var t=e.length;--t>=0;)e[t]=0}var Cf=256,kf=286,Sf=30,Rf=15,qf=new Uint8Array([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0]),If=new Uint8Array([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13]),Pf=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7]),Ef=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),Of=new Array(576);wf(Of);var Tf=new Array(60);wf(Tf);var zf=new Array(512);wf(zf);var Af=new Array(256);wf(Af);var Uf=new Array(29);wf(Uf);var Df,xf,Wf,Ff=new Array(Sf);function Mf(e,t,n,o,r){this.static_tree=e,this.extra_bits=t,this.extra_base=n,this.elems=o,this.max_length=r,this.has_stree=e&&e.length}function Lf(e,t){this.dyn_tree=e,this.max_code=0,this.stat_desc=t}wf(Ff);var Jf=function(e){return e<256?zf[e]:zf[256+(e>>>7)]},jf=function(e,t){e.pending_buf[e.pending++]=255&t,e.pending_buf[e.pending++]=t>>>8&255},Nf=function(e,t,n){e.bi_valid>16-n?(e.bi_buf|=t<<e.bi_valid&65535,jf(e,e.bi_buf),e.bi_buf=t>>16-e.bi_valid,e.bi_valid+=n-16):(e.bi_buf|=t<<e.bi_valid&65535,e.bi_valid+=n)},Bf=function(e,t,n){Nf(e,n[2*t],n[2*t+1])},Zf=function(e,t){var n=0;do{n|=1&e,e>>>=1,n<<=1}while(--t>0);return n>>>1},Hf=function(e,t,n){var o,r,i=new Array(16),s=0;for(o=1;o<=Rf;o++)i[o]=s=s+n[o-1]<<1;for(r=0;r<=t;r++){var a=e[2*r+1];0!==a&&(e[2*r]=Zf(i[a]++,a))}},Gf=function(e){var t;for(t=0;t<kf;t++)e.dyn_ltree[2*t]=0;for(t=0;t<Sf;t++)e.dyn_dtree[2*t]=0;for(t=0;t<19;t++)e.bl_tree[2*t]=0;e.dyn_ltree[512]=1,e.opt_len=e.static_len=0,e.last_lit=e.matches=0},Vf=function(e){e.bi_valid>8?jf(e,e.bi_buf):e.bi_valid>0&&(e.pending_buf[e.pending++]=e.bi_buf),e.bi_buf=0,e.bi_valid=0},Xf=function(e,t,n,o){var r=2*t,i=2*n;return e[r]<e[i]||e[r]===e[i]&&o[t]<=o[n]},Yf=function(e,t,n){for(var o=e.heap[n],r=n<<1;r<=e.heap_len&&(r<e.heap_len&&Xf(t,e.heap[r+1],e.heap[r],e.depth)&&r++,!Xf(t,o,e.heap[r],e.depth));)e.heap[n]=e.heap[r],n=r,r<<=1;e.heap[n]=o},Kf=function(e,t,n){var o,r,i,s,a=0;if(0!==e.last_lit)do{o=e.pending_buf[e.d_buf+2*a]<<8|e.pending_buf[e.d_buf+2*a+1],r=e.pending_buf[e.l_buf+a],a++,0===o?Bf(e,r,t):(i=Af[r],Bf(e,i+Cf+1,t),0!==(s=qf[i])&&(r-=Uf[i],Nf(e,r,s)),o--,i=Jf(o),Bf(e,i,n),0!==(s=If[i])&&(o-=Ff[i],Nf(e,o,s)))}while(a<e.last_lit);Bf(e,256,t)},$f=function(e,t){var n,o,r,i=t.dyn_tree,s=t.stat_desc.static_tree,a=t.stat_desc.has_stree,u=t.stat_desc.elems,c=-1;for(e.heap_len=0,e.heap_max=573,n=0;n<u;n++)0!==i[2*n]?(e.heap[++e.heap_len]=c=n,e.depth[n]=0):i[2*n+1]=0;for(;e.heap_len<2;)i[2*(r=e.heap[++e.heap_len]=c<2?++c:0)]=1,e.depth[r]=0,e.opt_len--,a&&(e.static_len-=s[2*r+1]);for(t.max_code=c,n=e.heap_len>>1;n>=1;n--)Yf(e,i,n);r=u;do{n=e.heap[1],e.heap[1]=e.heap[e.heap_len--],Yf(e,i,1),o=e.heap[1],e.heap[--e.heap_max]=n,e.heap[--e.heap_max]=o,i[2*r]=i[2*n]+i[2*o],e.depth[r]=(e.depth[n]>=e.depth[o]?e.depth[n]:e.depth[o])+1,i[2*n+1]=i[2*o+1]=r,e.heap[1]=r++,Yf(e,i,1)}while(e.heap_len>=2);e.heap[--e.heap_max]=e.heap[1],function(e,t){var n,o,r,i,s,a,u=t.dyn_tree,c=t.max_code,l=t.stat_desc.static_tree,d=t.stat_desc.has_stree,f=t.stat_desc.extra_bits,h=t.stat_desc.extra_base,p=t.stat_desc.max_length,v=0;for(i=0;i<=Rf;i++)e.bl_count[i]=0;for(u[2*e.heap[e.heap_max]+1]=0,n=e.heap_max+1;n<573;n++)(i=u[2*u[2*(o=e.heap[n])+1]+1]+1)>p&&(i=p,v++),u[2*o+1]=i,o>c||(e.bl_count[i]++,s=0,o>=h&&(s=f[o-h]),a=u[2*o],e.opt_len+=a*(i+s),d&&(e.static_len+=a*(l[2*o+1]+s)));if(0!==v){do{for(i=p-1;0===e.bl_count[i];)i--;e.bl_count[i]--,e.bl_count[i+1]+=2,e.bl_count[p]--,v-=2}while(v>0);for(i=p;0!==i;i--)for(o=e.bl_count[i];0!==o;)(r=e.heap[--n])>c||(u[2*r+1]!==i&&(e.opt_len+=(i-u[2*r+1])*u[2*r],u[2*r+1]=i),o--)}}(e,t),Hf(i,c,e.bl_count)},Qf=function(e,t,n){var o,r,i=-1,s=t[1],a=0,u=7,c=4;for(0===s&&(u=138,c=3),t[2*(n+1)+1]=65535,o=0;o<=n;o++)r=s,s=t[2*(o+1)+1],++a<u&&r===s||(a<c?e.bl_tree[2*r]+=a:0!==r?(r!==i&&e.bl_tree[2*r]++,e.bl_tree[32]++):a<=10?e.bl_tree[34]++:e.bl_tree[36]++,a=0,i=r,0===s?(u=138,c=3):r===s?(u=6,c=3):(u=7,c=4))},eh=function(e,t,n){var o,r,i=-1,s=t[1],a=0,u=7,c=4;for(0===s&&(u=138,c=3),o=0;o<=n;o++)if(r=s,s=t[2*(o+1)+1],!(++a<u&&r===s)){if(a<c)do{Bf(e,r,e.bl_tree)}while(0!=--a);else 0!==r?(r!==i&&(Bf(e,r,e.bl_tree),a--),Bf(e,16,e.bl_tree),Nf(e,a-3,2)):a<=10?(Bf(e,17,e.bl_tree),Nf(e,a-3,3)):(Bf(e,18,e.bl_tree),Nf(e,a-11,7));a=0,i=r,0===s?(u=138,c=3):r===s?(u=6,c=3):(u=7,c=4)}},th=!1,nh=function(e,t,n,o){Nf(e,0+(o?1:0),3),function(e,t,n,o){Vf(e),o&&(jf(e,n),jf(e,~n)),e.pending_buf.set(e.window.subarray(t,t+n),e.pending),e.pending+=n}(e,t,n,!0)},oh=function(e,t,n,o){var r,i,s=0;e.level>0?(2===e.strm.data_type&&(e.strm.data_type=function(e){var t,n=4093624447;for(t=0;t<=31;t++,n>>>=1)if(1&n&&0!==e.dyn_ltree[2*t])return 0;if(0!==e.dyn_ltree[18]||0!==e.dyn_ltree[20]||0!==e.dyn_ltree[26])return 1;for(t=32;t<Cf;t++)if(0!==e.dyn_ltree[2*t])return 1;return 0}(e)),$f(e,e.l_desc),$f(e,e.d_desc),s=function(e){var t;for(Qf(e,e.dyn_ltree,e.l_desc.max_code),Qf(e,e.dyn_dtree,e.d_desc.max_code),$f(e,e.bl_desc),t=18;t>=3&&0===e.bl_tree[2*Ef[t]+1];t--);return e.opt_len+=3*(t+1)+5+5+4,t}(e),r=e.opt_len+3+7>>>3,(i=e.static_len+3+7>>>3)<=r&&(r=i)):r=i=n+5,n+4<=r&&-1!==t?nh(e,t,n,o):4===e.strategy||i===r?(Nf(e,2+(o?1:0),3),Kf(e,Of,Tf)):(Nf(e,4+(o?1:0),3),function(e,t,n,o){var r;for(Nf(e,t-257,5),Nf(e,n-1,5),Nf(e,o-4,4),r=0;r<o;r++)Nf(e,e.bl_tree[2*Ef[r]+1],3);eh(e,e.dyn_ltree,t-1),eh(e,e.dyn_dtree,n-1)}(e,e.l_desc.max_code+1,e.d_desc.max_code+1,s+1),Kf(e,e.dyn_ltree,e.dyn_dtree)),Gf(e),o&&Vf(e)},rh={_tr_init:function(e){th||(!function(){var e,t,n,o,r,i=new Array(16);for(n=0,o=0;o<28;o++)for(Uf[o]=n,e=0;e<1<<qf[o];e++)Af[n++]=o;for(Af[n-1]=o,r=0,o=0;o<16;o++)for(Ff[o]=r,e=0;e<1<<If[o];e++)zf[r++]=o;for(r>>=7;o<Sf;o++)for(Ff[o]=r<<7,e=0;e<1<<If[o]-7;e++)zf[256+r++]=o;for(t=0;t<=Rf;t++)i[t]=0;for(e=0;e<=143;)Of[2*e+1]=8,e++,i[8]++;for(;e<=255;)Of[2*e+1]=9,e++,i[9]++;for(;e<=279;)Of[2*e+1]=7,e++,i[7]++;for(;e<=287;)Of[2*e+1]=8,e++,i[8]++;for(Hf(Of,287,i),e=0;e<Sf;e++)Tf[2*e+1]=5,Tf[2*e]=Zf(e,5);Df=new Mf(Of,qf,257,kf,Rf),xf=new Mf(Tf,If,0,Sf,Rf),Wf=new Mf(new Array(0),Pf,0,19,7)}(),th=!0),e.l_desc=new Lf(e.dyn_ltree,Df),e.d_desc=new Lf(e.dyn_dtree,xf),e.bl_desc=new Lf(e.bl_tree,Wf),e.bi_buf=0,e.bi_valid=0,Gf(e)},_tr_stored_block:nh,_tr_flush_block:oh,_tr_tally:function(e,t,n){return e.pending_buf[e.d_buf+2*e.last_lit]=t>>>8&255,e.pending_buf[e.d_buf+2*e.last_lit+1]=255&t,e.pending_buf[e.l_buf+e.last_lit]=255&n,e.last_lit++,0===t?e.dyn_ltree[2*n]++:(e.matches++,t--,e.dyn_ltree[2*(Af[n]+Cf+1)]++,e.dyn_dtree[2*Jf(t)]++),e.last_lit===e.lit_bufsize-1},_tr_align:function(e){Nf(e,2,3),Bf(e,256,Of),function(e){16===e.bi_valid?(jf(e,e.bi_buf),e.bi_buf=0,e.bi_valid=0):e.bi_valid>=8&&(e.pending_buf[e.pending++]=255&e.bi_buf,e.bi_buf>>=8,e.bi_valid-=8)}(e)}},ih=function(e,t,n,o){for(var r=65535&e|0,i=e>>>16&65535|0,s=0;0!==n;){n-=s=n>2e3?2e3:n;do{i=i+(r=r+t[o++]|0)|0}while(--s);r%=65521,i%=65521}return r|i<<16|0},sh=new Uint32Array(function(){for(var e,t=[],n=0;n<256;n++){e=n;for(var o=0;o<8;o++)e=1&e?3988292384^e>>>1:e>>>1;t[n]=e}return t}()),ah=function(e,t,n,o){var r=sh,i=o+n;e^=-1;for(var s=o;s<i;s++)e=e>>>8^r[255&(e^t[s])];return-1^e},uh={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"},ch={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_MEM_ERROR:-4,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8},lh=rh._tr_init,dh=rh._tr_stored_block,fh=rh._tr_flush_block,hh=rh._tr_tally,ph=rh._tr_align,vh=ch.Z_NO_FLUSH,bh=ch.Z_PARTIAL_FLUSH,gh=ch.Z_FULL_FLUSH,mh=ch.Z_FINISH,yh=ch.Z_BLOCK,_h=ch.Z_OK,wh=ch.Z_STREAM_END,Ch=ch.Z_STREAM_ERROR,kh=ch.Z_DATA_ERROR,Sh=ch.Z_BUF_ERROR,Rh=ch.Z_DEFAULT_COMPRESSION,qh=ch.Z_FILTERED,Ih=ch.Z_HUFFMAN_ONLY,Ph=ch.Z_RLE,Eh=ch.Z_FIXED,Oh=ch.Z_DEFAULT_STRATEGY,Th=ch.Z_UNKNOWN,zh=ch.Z_DEFLATED,Ah=258,Uh=262,Dh=103,xh=113,Wh=666,Fh=function(e,t){return e.msg=uh[t],t},Mh=function(e){return(e<<1)-(e>4?9:0)},Lh=function(e){for(var t=e.length;--t>=0;)e[t]=0},Jh=function(e,t,n){return(t<<e.hash_shift^n)&e.hash_mask},jh=function(e){var t=e.state,n=t.pending;n>e.avail_out&&(n=e.avail_out),0!==n&&(e.output.set(t.pending_buf.subarray(t.pending_out,t.pending_out+n),e.next_out),e.next_out+=n,t.pending_out+=n,e.total_out+=n,e.avail_out-=n,t.pending-=n,0===t.pending&&(t.pending_out=0))},Nh=function(e,t){fh(e,e.block_start>=0?e.block_start:-1,e.strstart-e.block_start,t),e.block_start=e.strstart,jh(e.strm)},Bh=function(e,t){e.pending_buf[e.pending++]=t},Zh=function(e,t){e.pending_buf[e.pending++]=t>>>8&255,e.pending_buf[e.pending++]=255&t},Hh=function(e,t){var n,o,r=e.max_chain_length,i=e.strstart,s=e.prev_length,a=e.nice_match,u=e.strstart>e.w_size-Uh?e.strstart-(e.w_size-Uh):0,c=e.window,l=e.w_mask,d=e.prev,f=e.strstart+Ah,h=c[i+s-1],p=c[i+s];e.prev_length>=e.good_match&&(r>>=2),a>e.lookahead&&(a=e.lookahead);do{if(c[(n=t)+s]===p&&c[n+s-1]===h&&c[n]===c[i]&&c[++n]===c[i+1]){i+=2,n++;do{}while(c[++i]===c[++n]&&c[++i]===c[++n]&&c[++i]===c[++n]&&c[++i]===c[++n]&&c[++i]===c[++n]&&c[++i]===c[++n]&&c[++i]===c[++n]&&c[++i]===c[++n]&&i<f);if(o=Ah-(f-i),i=f-Ah,o>s){if(e.match_start=t,s=o,o>=a)break;h=c[i+s-1],p=c[i+s]}}}while((t=d[t&l])>u&&0!=--r);return s<=e.lookahead?s:e.lookahead},Gh=function(e){var t,n,o,r,i,s,a,u,c,l,d=e.w_size;do{if(r=e.window_size-e.lookahead-e.strstart,e.strstart>=d+(d-Uh)){e.window.set(e.window.subarray(d,d+d),0),e.match_start-=d,e.strstart-=d,e.block_start-=d,t=n=e.hash_size;do{o=e.head[--t],e.head[t]=o>=d?o-d:0}while(--n);t=n=d;do{o=e.prev[--t],e.prev[t]=o>=d?o-d:0}while(--n);r+=d}if(0===e.strm.avail_in)break;if(s=e.strm,a=e.window,u=e.strstart+e.lookahead,c=r,l=void 0,(l=s.avail_in)>c&&(l=c),n=0===l?0:(s.avail_in-=l,a.set(s.input.subarray(s.next_in,s.next_in+l),u),1===s.state.wrap?s.adler=ih(s.adler,a,l,u):2===s.state.wrap&&(s.adler=ah(s.adler,a,l,u)),s.next_in+=l,s.total_in+=l,l),e.lookahead+=n,e.lookahead+e.insert>=3)for(i=e.strstart-e.insert,e.ins_h=e.window[i],e.ins_h=Jh(e,e.ins_h,e.window[i+1]);e.insert&&(e.ins_h=Jh(e,e.ins_h,e.window[i+3-1]),e.prev[i&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=i,i++,e.insert--,!(e.lookahead+e.insert<3)););}while(e.lookahead<Uh&&0!==e.strm.avail_in)},Vh=function(e,t){for(var n,o;;){if(e.lookahead<Uh){if(Gh(e),e.lookahead<Uh&&t===vh)return 1;if(0===e.lookahead)break}if(n=0,e.lookahead>=3&&(e.ins_h=Jh(e,e.ins_h,e.window[e.strstart+3-1]),n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),0!==n&&e.strstart-n<=e.w_size-Uh&&(e.match_length=Hh(e,n)),e.match_length>=3)if(o=hh(e,e.strstart-e.match_start,e.match_length-3),e.lookahead-=e.match_length,e.match_length<=e.max_lazy_match&&e.lookahead>=3){e.match_length--;do{e.strstart++,e.ins_h=Jh(e,e.ins_h,e.window[e.strstart+3-1]),n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart}while(0!=--e.match_length);e.strstart++}else e.strstart+=e.match_length,e.match_length=0,e.ins_h=e.window[e.strstart],e.ins_h=Jh(e,e.ins_h,e.window[e.strstart+1]);else o=hh(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++;if(o&&(Nh(e,!1),0===e.strm.avail_out))return 1}return e.insert=e.strstart<2?e.strstart:2,t===mh?(Nh(e,!0),0===e.strm.avail_out?3:4):e.last_lit&&(Nh(e,!1),0===e.strm.avail_out)?1:2},Xh=function(e,t){for(var n,o,r;;){if(e.lookahead<Uh){if(Gh(e),e.lookahead<Uh&&t===vh)return 1;if(0===e.lookahead)break}if(n=0,e.lookahead>=3&&(e.ins_h=Jh(e,e.ins_h,e.window[e.strstart+3-1]),n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),e.prev_length=e.match_length,e.prev_match=e.match_start,e.match_length=2,0!==n&&e.prev_length<e.max_lazy_match&&e.strstart-n<=e.w_size-Uh&&(e.match_length=Hh(e,n),e.match_length<=5&&(e.strategy===qh||3===e.match_length&&e.strstart-e.match_start>4096)&&(e.match_length=2)),e.prev_length>=3&&e.match_length<=e.prev_length){r=e.strstart+e.lookahead-3,o=hh(e,e.strstart-1-e.prev_match,e.prev_length-3),e.lookahead-=e.prev_length-1,e.prev_length-=2;do{++e.strstart<=r&&(e.ins_h=Jh(e,e.ins_h,e.window[e.strstart+3-1]),n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart)}while(0!=--e.prev_length);if(e.match_available=0,e.match_length=2,e.strstart++,o&&(Nh(e,!1),0===e.strm.avail_out))return 1}else if(e.match_available){if((o=hh(e,0,e.window[e.strstart-1]))&&Nh(e,!1),e.strstart++,e.lookahead--,0===e.strm.avail_out)return 1}else e.match_available=1,e.strstart++,e.lookahead--}return e.match_available&&(o=hh(e,0,e.window[e.strstart-1]),e.match_available=0),e.insert=e.strstart<2?e.strstart:2,t===mh?(Nh(e,!0),0===e.strm.avail_out?3:4):e.last_lit&&(Nh(e,!1),0===e.strm.avail_out)?1:2};function Yh(e,t,n,o,r){this.good_length=e,this.max_lazy=t,this.nice_length=n,this.max_chain=o,this.func=r}var Kh=[new Yh(0,0,0,0,(function(e,t){var n=65535;for(n>e.pending_buf_size-5&&(n=e.pending_buf_size-5);;){if(e.lookahead<=1){if(Gh(e),0===e.lookahead&&t===vh)return 1;if(0===e.lookahead)break}e.strstart+=e.lookahead,e.lookahead=0;var o=e.block_start+n;if((0===e.strstart||e.strstart>=o)&&(e.lookahead=e.strstart-o,e.strstart=o,Nh(e,!1),0===e.strm.avail_out))return 1;if(e.strstart-e.block_start>=e.w_size-Uh&&(Nh(e,!1),0===e.strm.avail_out))return 1}return e.insert=0,t===mh?(Nh(e,!0),0===e.strm.avail_out?3:4):(e.strstart>e.block_start&&(Nh(e,!1),e.strm.avail_out),1)})),new Yh(4,4,8,4,Vh),new Yh(4,5,16,8,Vh),new Yh(4,6,32,32,Vh),new Yh(4,4,16,16,Xh),new Yh(8,16,32,32,Xh),new Yh(8,16,128,128,Xh),new Yh(8,32,128,256,Xh),new Yh(32,128,258,1024,Xh),new Yh(32,258,258,4096,Xh)];function $h(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=zh,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new Uint16Array(1146),this.dyn_dtree=new Uint16Array(122),this.bl_tree=new Uint16Array(78),Lh(this.dyn_ltree),Lh(this.dyn_dtree),Lh(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new Uint16Array(16),this.heap=new Uint16Array(573),Lh(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new Uint16Array(573),Lh(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}var Qh=function(e){if(!e||!e.state)return Fh(e,Ch);e.total_in=e.total_out=0,e.data_type=Th;var t=e.state;return t.pending=0,t.pending_out=0,t.wrap<0&&(t.wrap=-t.wrap),t.status=t.wrap?42:xh,e.adler=2===t.wrap?0:1,t.last_flush=vh,lh(t),_h},ep=function(e){var t,n=Qh(e);return n===_h&&((t=e.state).window_size=2*t.w_size,Lh(t.head),t.max_lazy_match=Kh[t.level].max_lazy,t.good_match=Kh[t.level].good_length,t.nice_match=Kh[t.level].nice_length,t.max_chain_length=Kh[t.level].max_chain,t.strstart=0,t.block_start=0,t.lookahead=0,t.insert=0,t.match_length=t.prev_length=2,t.match_available=0,t.ins_h=0),n},tp=function(e,t,n,o,r,i){if(!e)return Ch;var s=1;if(t===Rh&&(t=6),o<0?(s=0,o=-o):o>15&&(s=2,o-=16),r<1||r>9||n!==zh||o<8||o>15||t<0||t>9||i<0||i>Eh)return Fh(e,Ch);8===o&&(o=9);var a=new $h;return e.state=a,a.strm=e,a.wrap=s,a.gzhead=null,a.w_bits=o,a.w_size=1<<a.w_bits,a.w_mask=a.w_size-1,a.hash_bits=r+7,a.hash_size=1<<a.hash_bits,a.hash_mask=a.hash_size-1,a.hash_shift=~~((a.hash_bits+3-1)/3),a.window=new Uint8Array(2*a.w_size),a.head=new Uint16Array(a.hash_size),a.prev=new Uint16Array(a.w_size),a.lit_bufsize=1<<r+6,a.pending_buf_size=4*a.lit_bufsize,a.pending_buf=new Uint8Array(a.pending_buf_size),a.d_buf=1*a.lit_bufsize,a.l_buf=3*a.lit_bufsize,a.level=t,a.strategy=i,a.method=n,ep(e)},np=function(e,t){return e&&e.state?2!==e.state.wrap?Ch:(e.state.gzhead=t,_h):Ch},op=function(e,t){var n,o;if(!e||!e.state||t>yh||t<0)return e?Fh(e,Ch):Ch;var r=e.state;if(!e.output||!e.input&&0!==e.avail_in||r.status===Wh&&t!==mh)return Fh(e,0===e.avail_out?Sh:Ch);r.strm=e;var i=r.last_flush;if(r.last_flush=t,42===r.status)if(2===r.wrap)e.adler=0,Bh(r,31),Bh(r,139),Bh(r,8),r.gzhead?(Bh(r,(r.gzhead.text?1:0)+(r.gzhead.hcrc?2:0)+(r.gzhead.extra?4:0)+(r.gzhead.name?8:0)+(r.gzhead.comment?16:0)),Bh(r,255&r.gzhead.time),Bh(r,r.gzhead.time>>8&255),Bh(r,r.gzhead.time>>16&255),Bh(r,r.gzhead.time>>24&255),Bh(r,9===r.level?2:r.strategy>=Ih||r.level<2?4:0),Bh(r,255&r.gzhead.os),r.gzhead.extra&&r.gzhead.extra.length&&(Bh(r,255&r.gzhead.extra.length),Bh(r,r.gzhead.extra.length>>8&255)),r.gzhead.hcrc&&(e.adler=ah(e.adler,r.pending_buf,r.pending,0)),r.gzindex=0,r.status=69):(Bh(r,0),Bh(r,0),Bh(r,0),Bh(r,0),Bh(r,0),Bh(r,9===r.level?2:r.strategy>=Ih||r.level<2?4:0),Bh(r,3),r.status=xh);else{var s=zh+(r.w_bits-8<<4)<<8;s|=(r.strategy>=Ih||r.level<2?0:r.level<6?1:6===r.level?2:3)<<6,0!==r.strstart&&(s|=32),s+=31-s%31,r.status=xh,Zh(r,s),0!==r.strstart&&(Zh(r,e.adler>>>16),Zh(r,65535&e.adler)),e.adler=1}if(69===r.status)if(r.gzhead.extra){for(n=r.pending;r.gzindex<(65535&r.gzhead.extra.length)&&(r.pending!==r.pending_buf_size||(r.gzhead.hcrc&&r.pending>n&&(e.adler=ah(e.adler,r.pending_buf,r.pending-n,n)),jh(e),n=r.pending,r.pending!==r.pending_buf_size));)Bh(r,255&r.gzhead.extra[r.gzindex]),r.gzindex++;r.gzhead.hcrc&&r.pending>n&&(e.adler=ah(e.adler,r.pending_buf,r.pending-n,n)),r.gzindex===r.gzhead.extra.length&&(r.gzindex=0,r.status=73)}else r.status=73;if(73===r.status)if(r.gzhead.name){n=r.pending;do{if(r.pending===r.pending_buf_size&&(r.gzhead.hcrc&&r.pending>n&&(e.adler=ah(e.adler,r.pending_buf,r.pending-n,n)),jh(e),n=r.pending,r.pending===r.pending_buf_size)){o=1;break}o=r.gzindex<r.gzhead.name.length?255&r.gzhead.name.charCodeAt(r.gzindex++):0,Bh(r,o)}while(0!==o);r.gzhead.hcrc&&r.pending>n&&(e.adler=ah(e.adler,r.pending_buf,r.pending-n,n)),0===o&&(r.gzindex=0,r.status=91)}else r.status=91;if(91===r.status)if(r.gzhead.comment){n=r.pending;do{if(r.pending===r.pending_buf_size&&(r.gzhead.hcrc&&r.pending>n&&(e.adler=ah(e.adler,r.pending_buf,r.pending-n,n)),jh(e),n=r.pending,r.pending===r.pending_buf_size)){o=1;break}o=r.gzindex<r.gzhead.comment.length?255&r.gzhead.comment.charCodeAt(r.gzindex++):0,Bh(r,o)}while(0!==o);r.gzhead.hcrc&&r.pending>n&&(e.adler=ah(e.adler,r.pending_buf,r.pending-n,n)),0===o&&(r.status=Dh)}else r.status=Dh;if(r.status===Dh&&(r.gzhead.hcrc?(r.pending+2>r.pending_buf_size&&jh(e),r.pending+2<=r.pending_buf_size&&(Bh(r,255&e.adler),Bh(r,e.adler>>8&255),e.adler=0,r.status=xh)):r.status=xh),0!==r.pending){if(jh(e),0===e.avail_out)return r.last_flush=-1,_h}else if(0===e.avail_in&&Mh(t)<=Mh(i)&&t!==mh)return Fh(e,Sh);if(r.status===Wh&&0!==e.avail_in)return Fh(e,Sh);if(0!==e.avail_in||0!==r.lookahead||t!==vh&&r.status!==Wh){var a=r.strategy===Ih?function(e,t){for(var n;;){if(0===e.lookahead&&(Gh(e),0===e.lookahead)){if(t===vh)return 1;break}if(e.match_length=0,n=hh(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++,n&&(Nh(e,!1),0===e.strm.avail_out))return 1}return e.insert=0,t===mh?(Nh(e,!0),0===e.strm.avail_out?3:4):e.last_lit&&(Nh(e,!1),0===e.strm.avail_out)?1:2}(r,t):r.strategy===Ph?function(e,t){for(var n,o,r,i,s=e.window;;){if(e.lookahead<=Ah){if(Gh(e),e.lookahead<=Ah&&t===vh)return 1;if(0===e.lookahead)break}if(e.match_length=0,e.lookahead>=3&&e.strstart>0&&(o=s[r=e.strstart-1])===s[++r]&&o===s[++r]&&o===s[++r]){i=e.strstart+Ah;do{}while(o===s[++r]&&o===s[++r]&&o===s[++r]&&o===s[++r]&&o===s[++r]&&o===s[++r]&&o===s[++r]&&o===s[++r]&&r<i);e.match_length=Ah-(i-r),e.match_length>e.lookahead&&(e.match_length=e.lookahead)}if(e.match_length>=3?(n=hh(e,1,e.match_length-3),e.lookahead-=e.match_length,e.strstart+=e.match_length,e.match_length=0):(n=hh(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++),n&&(Nh(e,!1),0===e.strm.avail_out))return 1}return e.insert=0,t===mh?(Nh(e,!0),0===e.strm.avail_out?3:4):e.last_lit&&(Nh(e,!1),0===e.strm.avail_out)?1:2}(r,t):Kh[r.level].func(r,t);if(3!==a&&4!==a||(r.status=Wh),1===a||3===a)return 0===e.avail_out&&(r.last_flush=-1),_h;if(2===a&&(t===bh?ph(r):t!==yh&&(dh(r,0,0,!1),t===gh&&(Lh(r.head),0===r.lookahead&&(r.strstart=0,r.block_start=0,r.insert=0))),jh(e),0===e.avail_out))return r.last_flush=-1,_h}return t!==mh?_h:r.wrap<=0?wh:(2===r.wrap?(Bh(r,255&e.adler),Bh(r,e.adler>>8&255),Bh(r,e.adler>>16&255),Bh(r,e.adler>>24&255),Bh(r,255&e.total_in),Bh(r,e.total_in>>8&255),Bh(r,e.total_in>>16&255),Bh(r,e.total_in>>24&255)):(Zh(r,e.adler>>>16),Zh(r,65535&e.adler)),jh(e),r.wrap>0&&(r.wrap=-r.wrap),0!==r.pending?_h:wh)},rp={deflateInit:function(e,t){return tp(e,t,zh,15,8,Oh)},deflateInit2:tp,deflateReset:ep,deflateResetKeep:Qh,deflateSetHeader:np,deflate:op,deflateEnd:function(e){if(!e||!e.state)return Ch;var t=e.state.status;return 42!==t&&69!==t&&73!==t&&91!==t&&t!==Dh&&t!==xh&&t!==Wh?Fh(e,Ch):(e.state=null,t===xh?Fh(e,kh):_h)},deflateSetDictionary:function(e,t){var n=t.length;if(!e||!e.state)return Ch;var o=e.state,r=o.wrap;if(2===r||1===r&&42!==o.status||o.lookahead)return Ch;if(1===r&&(e.adler=ih(e.adler,t,n,0)),o.wrap=0,n>=o.w_size){0===r&&(Lh(o.head),o.strstart=0,o.block_start=0,o.insert=0);var i=new Uint8Array(o.w_size);i.set(t.subarray(n-o.w_size,n),0),t=i,n=o.w_size}var s=e.avail_in,a=e.next_in,u=e.input;for(e.avail_in=n,e.next_in=0,e.input=t,Gh(o);o.lookahead>=3;){var c=o.strstart,l=o.lookahead-2;do{o.ins_h=Jh(o,o.ins_h,o.window[c+3-1]),o.prev[c&o.w_mask]=o.head[o.ins_h],o.head[o.ins_h]=c,c++}while(--l);o.strstart=c,o.lookahead=2,Gh(o)}return o.strstart+=o.lookahead,o.block_start=o.strstart,o.insert=o.lookahead,o.lookahead=0,o.match_length=o.prev_length=2,o.match_available=0,e.next_in=a,e.input=u,e.avail_in=s,o.wrap=r,_h},deflateInfo:"pako deflate (from Nodeca project)"},ip=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},sp=function(e){for(var n=Array.prototype.slice.call(arguments,1);n.length;){var o=n.shift();if(o){if("object"!==t(o))throw new TypeError(o+"must be non-object");for(var r in o)ip(o,r)&&(e[r]=o[r])}}return e},ap=function(e){for(var t=0,n=0,o=e.length;n<o;n++)t+=e[n].length;for(var r=new Uint8Array(t),i=0,s=0,a=e.length;i<a;i++){var u=e[i];r.set(u,s),s+=u.length}return r};try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(e){!1}for(var up=new Uint8Array(256),cp=0;cp<256;cp++)up[cp]=cp>=252?6:cp>=248?5:cp>=240?4:cp>=224?3:cp>=192?2:1;up[254]=up[254]=1;var lp=function(e){if("function"==typeof TextEncoder&&TextEncoder.prototype.encode)return(new TextEncoder).encode(e);var t,n,o,r,i,s=e.length,a=0;for(r=0;r<s;r++)55296==(64512&(n=e.charCodeAt(r)))&&r+1<s&&56320==(64512&(o=e.charCodeAt(r+1)))&&(n=65536+(n-55296<<10)+(o-56320),r++),a+=n<128?1:n<2048?2:n<65536?3:4;for(t=new Uint8Array(a),i=0,r=0;i<a;r++)55296==(64512&(n=e.charCodeAt(r)))&&r+1<s&&56320==(64512&(o=e.charCodeAt(r+1)))&&(n=65536+(n-55296<<10)+(o-56320),r++),n<128?t[i++]=n:n<2048?(t[i++]=192|n>>>6,t[i++]=128|63&n):n<65536?(t[i++]=224|n>>>12,t[i++]=128|n>>>6&63,t[i++]=128|63&n):(t[i++]=240|n>>>18,t[i++]=128|n>>>12&63,t[i++]=128|n>>>6&63,t[i++]=128|63&n);return t};var dp=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0},fp=Object.prototype.toString,hp=ch.Z_NO_FLUSH,pp=ch.Z_SYNC_FLUSH,vp=ch.Z_FULL_FLUSH,bp=ch.Z_FINISH,gp=ch.Z_OK,mp=ch.Z_STREAM_END,yp=ch.Z_DEFAULT_COMPRESSION,_p=ch.Z_DEFAULT_STRATEGY,wp=ch.Z_DEFLATED;function Cp(e){this.options=sp({level:yp,method:wp,chunkSize:16384,windowBits:15,memLevel:8,strategy:_p},e||{});var t=this.options;t.raw&&t.windowBits>0?t.windowBits=-t.windowBits:t.gzip&&t.windowBits>0&&t.windowBits<16&&(t.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new dp,this.strm.avail_out=0;var n=rp.deflateInit2(this.strm,t.level,t.method,t.windowBits,t.memLevel,t.strategy);if(n!==gp)throw new Error(uh[n]);if(t.header&&rp.deflateSetHeader(this.strm,t.header),t.dictionary){var o;if(o="string"==typeof t.dictionary?lp(t.dictionary):"[object ArrayBuffer]"===fp.call(t.dictionary)?new Uint8Array(t.dictionary):t.dictionary,(n=rp.deflateSetDictionary(this.strm,o))!==gp)throw new Error(uh[n]);this._dict_set=!0}}function kp(e,t){var n=new Cp(t);if(n.push(e,!0),n.err)throw n.msg||uh[n.err];return n.result}Cp.prototype.push=function(e,t){var n,o,r=this.strm,i=this.options.chunkSize;if(this.ended)return!1;for(o=t===~~t?t:!0===t?bp:hp,"string"==typeof e?r.input=lp(e):"[object ArrayBuffer]"===fp.call(e)?r.input=new Uint8Array(e):r.input=e,r.next_in=0,r.avail_in=r.input.length;;)if(0===r.avail_out&&(r.output=new Uint8Array(i),r.next_out=0,r.avail_out=i),(o===pp||o===vp)&&r.avail_out<=6)this.onData(r.output.subarray(0,r.next_out)),r.avail_out=0;else{if((n=rp.deflate(r,o))===mp)return r.next_out>0&&this.onData(r.output.subarray(0,r.next_out)),n=rp.deflateEnd(this.strm),this.onEnd(n),this.ended=!0,n===gp;if(0!==r.avail_out){if(o>0&&r.next_out>0)this.onData(r.output.subarray(0,r.next_out)),r.avail_out=0;else if(0===r.avail_in)break}else this.onData(r.output)}return!0},Cp.prototype.onData=function(e){this.chunks.push(e)},Cp.prototype.onEnd=function(e){e===gp&&(this.result=ap(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg};var Sp,Rp,qp,Ip={Deflate:Cp,deflate:kp,deflateRaw:function(e,t){return(t=t||{}).raw=!0,kp(e,t)},gzip:function(e,t){return(t=t||{}).gzip=!0,kp(e,t)},constants:ch}.deflate,Pp=(Sp=function(){function e(t){n(this,e),this.oOptions=Object.assign({iPort:-1,cbConnectSuccess:null,cbConnectError:null,cbConnectClose:null},t),this.szHost="http://127.0.0.1",this.szUUID="",this.szVersion="",this.bNormalClose=!1,this.bConnected=!1,this.bInitConnect=!0,this.iGetErrorCount=0,this.oWindowControlCallback={},this.oSadpCallback={},this.oSliceCallback={},this.oSerialCallback={},this.oUIControlCallback={},this.oUpgradeCallback={},this.init()}return r(e,[{key:"init",value:function(){var e=this,t=Id(),n={sequence:t,cmd:"system.connect"},o=JSON.stringify(n);e.sendImageHttp("".concat(e.szHost,":").concat(e.oOptions.iPort,"/imghttp/local"),o,t,{success:function(t){var n=JSON.parse(t);e.szUUID=n.uuid,e.szVersion=n.version,e.bConnected=!0,e.bInitConnect=!1,setTimeout((function(){e.imageHttpPolling()}),100),e.oOptions.cbConnectSuccess&&e.oOptions.cbConnectSuccess()},error:function(){}})}},{key:"sendImageHttp",value:function(e,t,n,o){var r=this;o=Object.assign({success:null,error:null,abort:null},o);var i=Ip(t);""!==(new Uint8Array).toString()&&(pf.isMacOS()||pf.browser().msie)&&(i=Array.prototype.slice.call(i));for(var s=encodeURIComponent(btoa(i)),a=this.splitStr(s),u=[],c="",l=0,d=a.length;l<d;l++)c=l===d-1?"update=".concat((new Date).getTime(),"&isLast=true&data=").concat(a[l],"&sequence=").concat(n):"update=".concat((new Date).getTime(),"&isLast=false&data=").concat(a[l],"&sequence=").concat(n),u.push(c);u.length>0&&function t(){r.imageHttp("".concat(e,"?").concat(u[0]),{success:function(e){u.shift(),u.length>0?(r.bInitConnect||r.bConnected)&&t():o.success&&o.success(e)},error:function(){o.error&&o.error()},abort:function(){o.abort&&o.abort()}})}()}},{key:"splitStr",value:function(e){for(var t=this.getByteLen(e),n=[],o=1500,r=0,i=Math.ceil(t/o);r<i;r++)n[r]=e.slice(o*r,o*(r+1));return n}},{key:"getByteLen",value:function(e){for(var t=0,n="",o=0,r=e.length;o<r;o++)n=e.charAt(o),/[^\x00-\xff]/.test(n)?t+=2:t+=1;return t}},{key:"imageHttp",value:function(e,t){t=Object.assign({success:null,error:null,abort:null},t);var n=new Image;n.onload=function(){if(t.success){var e=document.createElement("canvas"),o=e.getContext("2d"),r=n.width,i=n.height;e.width=r,e.height=i;try{o.drawImage(n,0,0);for(var s=o.getImageData(0,0,r,i).data,a="",u=-1,c=i-1;c>=0;c--)for(var l=0;l<4*r&&0!==s[u=c*r*4+l];l++)255!==s[u]&&(a+=String.fromCharCode(s[u]));t.success(pf.utf8to16(a))}catch(e){t.error&&t.error()}}},n.onerror=function(){t.error&&t.error()},n.onabort=function(){t.abort&&t.abort()},n.crossOrigin="anonymous",n.src=e}},{key:"setWindowControlCallback",value:function(e){this.oWindowControlCallback=e}},{key:"setSadpCallback",value:function(e){this.oSadpCallback=e}},{key:"setSliceCallback",value:function(e){this.oSliceCallback=e}},{key:"setSerialCallback",value:function(e){this.oSerialCallback=e}},{key:"setUIControlCallback",value:function(e){this.oUIControlCallback=e}},{key:"setUpgradeCallback",value:function(e){this.oUpgradeCallback=e}},{key:"getServiceVersion",value:function(){return this.szVersion}},{key:"getRequestUUID",value:function(){return this.szUUID}},{key:"disconnect",value:function(){var e=this,t=Id(),n={sequence:t,uuid:e.szUUID,cmd:"system.disconnect"},o=JSON.stringify(n);e.bConnected&&e.sendImageHttp("".concat(e.szHost,":").concat(e.oOptions.iPort,"/imghttp/local"),o,t,{success:function(){e.bNormalClose=!0,e.bConnected=!1,e.oOptions.cbConnectClose&&e.oOptions.cbConnectClose(e.bNormalClose)},error:function(){e.bConnected=!1}})}},{key:"imageHttpPolling",value:function(){var e=this,t=Id(),n={sequence:t,uuid:e.szUUID,cmd:"system.get"},o=JSON.stringify(n);e.bConnected&&e.sendImageHttp("".concat(e.szHost,":").concat(e.oOptions.iPort,"/imghttp/local"),o,t,{success:function(t){if(e.iGetErrorCount=0,"timeout"===t)setTimeout((function(){e.imageHttpPolling()}),100);else if("invalid"===t)e.bConnected=!1,e.oOptions.cbConnectError&&e.oOptions.cbConnectError();else if("closed"===t)console.log("connected is disconnected");else{var n=JSON.parse(t);void 0!==n.cmd?e.parseCmd(n):console.log("[jsWebControl]imgHttpPolling push message error:".concat(t)),setTimeout((function(){e.imageHttpPolling()}),100)}},error:function(){5===e.iGetErrorCount?(console.log("[jsWebControl]imageHttpPolling get polling finished"),e.bNormalClose=!1,e.bConnected=!1,e.oOptions.cbConnectClose&&e.oOptions.cbConnectClose(e.bNormalClose)):setTimeout((function(){console.log("[jsWebControl]imgHttpPolling get polling failed"),e.iGetErrorCount++,e.imageHttpPolling()}),100)}})}},{key:"sendRequest",value:function(e){var t=this;return new Promise((function(n,o){var r=e.cmd.split("."),i="";r.length>1?i="laputa"===r[0]?"laputa":"local":o();var s=Id();e.sequence=s,e.uuid=t.szUUID,e.timestamp="".concat((new Date).getTime());var a=JSON.stringify(e);t.bConnected?t.sendImageHttp("".concat(t.szHost,":").concat(t.oOptions.iPort,"/imghttp/").concat(i),a,s,{success:function(e){var t=JSON.parse(e);0===t.errorModule&&0===t.errorCode?n(t):o(t)},error:function(){o()}}):o()}))}},{key:"parseCmd",value:function(e){var t=e.cmd.split("."),n=t[1].replace(/^[a-z]{1}/g,(function(e){return e.toUpperCase()}));"window"===t[0]||"play"===t[0]?this.oWindowControlCallback["cb".concat(n)]&&this.oWindowControlCallback["cb".concat(n)](e):"sadp"===t[0]?this.oSadpCallback["cb".concat(n)]&&this.oSadpCallback["cb".concat(n)](e):"serial"===t[0]?this.oSerialCallback["cb".concat(n)]&&this.oSerialCallback["cb".concat(n)](e):"slice"===t[0]?this.oSliceCallback["cb".concat(n)]&&this.oSliceCallback["cb".concat(n)](e):"ui"===t[0]?this.oUIControlCallback["cb".concat(n)]&&this.oUIControlCallback["cb".concat(n)](e):"upgrade"===t[0]&&this.oUpgradeCallback["cb".concat(n)]&&this.oUpgradeCallback["cb".concat(n)](e)}}]),e}(),Sp),Ep=function(){function e(t){n(this,e),this.oOptions=Object.assign({szPluginContainer:"",iPort:-1,cbConnectSuccess:null,cbConnectError:null,cbConnectClose:null,szClassId:""},t),this.oPlugin=null,this.szPluginId="",this.szUUID="",this.szVersion="",this.oRequestList={},this.bNormalClose=!1,this.aMessage=[],this.oWindowControlCallback={},this.oSadpCallback={},this.oSliceCallback={},this.oSerialCallback={},this.oUIControlCallback={},this.oUpgradeCallback={},this.init()}return r(e,[{key:"init",value:function(){var e=this;e.initPlugin(),e.oPlugin.object&&e.oPlugin.createSocket("ws://127.0.0.1:".concat(e.oOptions.iPort))}},{key:"initPlugin",value:function(){var e=this;this.szPluginId="webActiveX_".concat((new Date).getTime());var t="<object id='".concat(this.szPluginId,"' classid='clsid:").concat(e.oOptions.szClassId,"' codebase='' standby='Waiting...' width='100%' height='100%' align='center' ></object>"),n=e.oOptions.szPluginContainer;if(""===n){n="".concat(this.szPluginId,"_div");var o=document.createElement("div");o.id=n,document.body.parentNode.appendChild(o)}document.getElementById(n).innerHTML=t,e.oPlugin=document.getElementById(this.szPluginId),window.onConnectMessage=function(t,n){n?(e.aMessage.push(t),e.onConnectMessage(e.aMessage.join("")),e.aMessage.length=0):e.aMessage.push(t)},window.onConnectClose=function(){e.onConnectClose()},window.onConnectError=function(){e.onConnectError()},window.onConnectCloseException=function(){e.onConnectCloseException()},window.onConnectOpen=function(){e.onConnectOpen()},pf.createEventScript(this.szPluginId,"onConnectMessage(szData, bLast)","onConnectMessage(szData, bLast);"),pf.createEventScript(this.szPluginId,"onConnectClose()","onConnectClose();"),pf.createEventScript(this.szPluginId,"onConnectError()","onConnectError();"),pf.createEventScript(this.szPluginId,"onConnectCloseException()","onConnectCloseException();"),pf.createEventScript(this.szPluginId,"onConnectOpen()","onConnectOpen();")}},{key:"onConnectMessage",value:function(e){var t=this;if(e){var n=JSON.parse(e),o=n.sequence;void 0===o&&void 0===n.cmd?(t.szUUID=n.uuid,t.szVersion=n.version,t.oOptions.cbConnectSuccess&&t.oOptions.cbConnectSuccess()):void 0!==n.cmd?t.parseCmd(n):void 0!==t.oRequestList[o]&&(0===n.errorModule&&0===n.errorCode?t.oRequestList[o].resolve(n):t.oRequestList[o].reject(n),delete t.oRequestList[o])}}},{key:"onConnectClose",value:function(){if(this.oPlugin=null,""!==this.szPluginId){var e=document.getElementById(this.szPluginId);e.parentNode.removeChild(e);var t=document.getElementById("".concat(this.szPluginId,"_div"));null!==t&&t.parentNode.removeChild(t)}this.oOptions.cbConnectClose&&this.oOptions.cbConnectClose(this.bNormalClose)}},{key:"onConnectCloseException",value:function(){var e=this;setTimeout((function(){e.oPlugin.object&&e.oPlugin.closeSocket()}),1e3)}},{key:"onConnectOpen",value:function(){var e={sequence:Id(),cmd:"system.connect"},t=JSON.stringify(e);this.oPlugin.object&&this.oPlugin.sendRequest(t)}},{key:"onConnectError",value:function(){}},{key:"setWindowControlCallback",value:function(e){this.oWindowControlCallback=e}},{key:"setSadpCallback",value:function(e){this.oSadpCallback=e}},{key:"setSliceCallback",value:function(e){this.oSliceCallback=e}},{key:"setSerialCallback",value:function(e){this.oSerialCallback=e}},{key:"setUIControlCallback",value:function(e){this.oUIControlCallback=e}},{key:"setUpgradeCallback",value:function(e){this.oUpgradeCallback=e}},{key:"getServiceVersion",value:function(){return this.szVersion}},{key:"getRequestUUID",value:function(){return this.szUUID}},{key:"disconnect",value:function(){this.bNormalClose=!0,this.oPlugin&&this.oPlugin.object&&this.oPlugin.closeSocket()}},{key:"sendRequest",value:function(e){var t=this;return"window.hideWnd"===e.cmd?t.oPlugin&&t.oPlugin.object&&(t.oPlugin.style.visibility="hidden"):"window.showWnd"===e.cmd&&t.oPlugin&&t.oPlugin.object&&(t.oPlugin.style.visibility="visible"),new Promise((function(n,o){var r=Id();e.sequence=r,t.oRequestList[r]={resolve:n,reject:o},e.uuid=t.szUUID,e.timestamp="".concat((new Date).getTime());var i=JSON.stringify(e);t.oPlugin&&t.oPlugin.object?t.oPlugin.sendRequest(i):o()}))}},{key:"parseCmd",value:function(e){var t=e.cmd.split("."),n=t[1].replace(/^[a-z]{1}/g,(function(e){return e.toUpperCase()}));"window"===t[0]||"play"===t[0]?this.oWindowControlCallback["cb".concat(n)]&&this.oWindowControlCallback["cb".concat(n)](e):"sadp"===t[0]?this.oSadpCallback["cb".concat(n)]&&this.oSadpCallback["cb".concat(n)](e):"serial"===t[0]?this.oSerialCallback["cb".concat(n)]&&this.oSerialCallback["cb".concat(n)](e):"slice"===t[0]?this.oSliceCallback["cb".concat(n)]&&this.oSliceCallback["cb".concat(n)](e):"ui"===t[0]?this.oUIControlCallback["cb".concat(n)]&&this.oUIControlCallback["cb".concat(n)](e):"upgrade"===t[0]&&this.oUpgradeCallback["cb".concat(n)]&&this.oUpgradeCallback["cb".concat(n)](e)}}]),e}(),Op=(Rp=function(){function e(t){n(this,e),this.oOptions=Object.assign({szPluginContainer:"",cbConnectSuccess:null,cbConnectError:null,cbConnectClose:null,iServicePortStart:-1,iServicePortEnd:-1,szClassId:""},t),this.iPort=-1,this.oRequest=null,this.bInit=!1,this.oCallbacks={},this.init()}return r(e,[{key:"init",value:function(){var e=this;pf.detectPort(e.oOptions.iServicePortStart,e.oOptions.iServicePortEnd,{success:function(t){if(e.iPort=t,pf.browser().msie)"11.0"===pf.browser().version?"https:"===window.location.protocol?e.oRequest=new Pp({iPort:e.iPort,cbConnectSuccess:e.oOptions.cbConnectSuccess,cbConnectError:e.oOptions.cbConnectError,cbConnectClose:e.oOptions.cbConnectClose}):e.oRequest=new _f({iPort:e.iPort,cbConnectSuccess:e.oOptions.cbConnectSuccess,cbConnectError:e.oOptions.cbConnectError,cbConnectClose:e.oOptions.cbConnectClose}):e.oRequest=new Ep({szPluginContainer:e.oOptions.szPluginContainer,iPort:e.iPort,cbConnectSuccess:e.oOptions.cbConnectSuccess,cbConnectError:e.oOptions.cbConnectError,cbConnectClose:e.oOptions.cbConnectClose,szClassId:e.oOptions.szClassId});else if("https:"===window.location.protocol)if(pf.browser().chrome||pf.browser().mozilla)try{e.oRequest=new _f({iPort:e.iPort,cbConnectSuccess:e.oOptions.cbConnectSuccess,cbConnectError:e.oOptions.cbConnectError,cbConnectClose:e.oOptions.cbConnectClose})}catch(t){e.oRequest=new Pp({iPort:e.iPort,cbConnectSuccess:e.oOptions.cbConnectSuccess,cbConnectError:e.oOptions.cbConnectError,cbConnectClose:e.oOptions.cbConnectClose})}else e.oRequest=new Pp({iPort:e.iPort,cbConnectSuccess:e.oOptions.cbConnectSuccess,cbConnectError:e.oOptions.cbConnectError,cbConnectClose:e.oOptions.cbConnectClose});else"WebSocket"in window&&(e.oRequest=new _f({iPort:e.iPort,cbConnectSuccess:e.oOptions.cbConnectSuccess,cbConnectError:e.oOptions.cbConnectError,cbConnectClose:e.oOptions.cbConnectClose}));for(var n in e.bInit=!0,e.oCallbacks)e.oRequest[n](e.oCallbacks[n])},error:function(){e.oOptions.cbConnectError&&e.oOptions.cbConnectError()}})}},{key:"setWindowControlCallback",value:function(e){this.bInit?this.oRequest.setWindowControlCallback(e):this.oCallbacks.setWindowControlCallback=e}},{key:"setSadpCallback",value:function(e){this.bInit?this.oRequest.setSadpCallback(e):this.oCallbacks.setSadpCallback=e}},{key:"setSliceCallback",value:function(e){this.bInit?this.oRequest.setSliceCallback(e):this.oCallbacks.setSliceCallback=e}},{key:"setSerialCallback",value:function(e){this.bInit?this.oRequest.setSerialCallback(e):this.oCallbacks.setSerialCallback=e}},{key:"setUIControlCallback",value:function(e){this.bInit?this.oRequest.setUIControlCallback(e):this.oCallbacks.setUIControlCallback=e}},{key:"setUpgradeCallback",value:function(e){this.bInit?this.oRequest.setUpgradeCallback(e):this.oCallbacks.setUpgradeCallback=e}},{key:"getServiceVersion",value:function(){return this.oRequest.getServiceVersion()}},{key:"getRequestUUID",value:function(){return this.oRequest.getRequestUUID()}},{key:"startService",value:function(e,t){var n={cmd:"system.startService",type:e};return void 0!==t&&(n.options=t),this.oRequest.sendRequest(n)}},{key:"stopService",value:function(e){var t=this;return new Promise((function(n,o){null!==t.oRequest?t.oRequest.sendRequest({cmd:"system.stopService",type:e}).then((function(e){n(e)}),(function(e){o(e)})):o()}))}},{key:"disconnect",value:function(){var e=this;return new Promise((function(t,n){null!==e.oRequest?(e.oRequest.disconnect(),t("cbConnectClose callback is really success")):n()}))}},{key:"openDirectory",value:function(e){return this.oRequest.sendRequest({cmd:"system.openDirectory",path:e})}},{key:"openFile",value:function(e,t,n){return this.oRequest.sendRequest({cmd:"system.openFile",path:e,relative:t,version:n})}},{key:"selectDirectory",value:function(e,t){var n=this;return new Promise((function(o,r){null!==n.oRequest?n.oRequest.sendRequest({cmd:"system.selectDirectory",caption:void 0!==e&&""!==e?pf.Base64().encode(e):"",dir:void 0!==t&&""!==t?pf.Base64().encode(t):""}).then((function(e){""!==e.path&&(e.path=pf.Base64().decode(e.path)),o(e)}),(function(e){r(e)})):r()}))}},{key:"selectFile",value:function(e,t,n){var o=this;return new Promise((function(r,i){null!==o.oRequest?o.oRequest.sendRequest({cmd:"system.selectFile",caption:""!==e?pf.Base64().encode(e):"",dir:""!==t?pf.Base64().encode(t):"",filter:n}).then((function(e){""!==e.path&&(e.path=pf.Base64().decode(e.path)),r(e)}),(function(e){i(e)})):i()}))}},{key:"getLocalConfig",value:function(e){return this.oRequest.sendRequest({cmd:"system.getLocalConfig",default:e})}},{key:"setLocalConfig",value:function(e){return e.cmd="system.setLocalConfig",this.oRequest.sendRequest(e)}},{key:"createWnd",value:function(e,t,n,o,r,i,s){var a=arguments.length>7&&void 0!==arguments[7]?arguments[7]:"";return this.oRequest.sendRequest({cmd:"window.createWnd",rect:{left:e,top:t,width:n,height:o},className:r,embed:i,activeXParentWnd:s,HWND:a})}},{key:"showWnd",value:function(){return this.oRequest.sendRequest({cmd:"window.showWnd"})}},{key:"hideWnd",value:function(){return this.oRequest.sendRequest({cmd:"window.hideWnd"})}},{key:"destroyWnd",value:function(){var e=this;return new Promise((function(t,n){null!==e.oRequest?e.oRequest.sendRequest({cmd:"window.destroyWnd"}).then((function(e){t(e)}),(function(e){n(e)})):n()}))}},{key:"setWndGeometry",value:function(e,t,n,o){return this.oRequest.sendRequest({cmd:"window.setWndGeometry",rect:{left:e,top:t,width:n,height:o}})}},{key:"setWndCover",value:function(e,t){var n=this;return new Promise((function(o,r){null!==n.oRequest?n.oRequest.sendRequest({cmd:"window.setWndCover",position:e,size:t}).then((function(e){o(e)}),(function(e){r(e)})):r()}))}},{key:"cuttingPartWindow",value:function(e,t,n,o,r){var i=this;return new Promise((function(s,a){null!==i.oRequest?i.oRequest.sendRequest({cmd:"window.cuttingPartWindow",rect:{left:e,top:t,width:n,height:o},round:r}).then((function(e){s(e)}),(function(e){a(e)})):a()}))}},{key:"repairPartWindow",value:function(e,t,n,o,r){var i=this;return new Promise((function(s,a){null!==i.oRequest?i.oRequest.sendRequest({cmd:"window.repairPartWindow",rect:{left:e,top:t,width:n,height:o},round:r}).then((function(e){s(e)}),(function(e){a(e)})):a()}))}},{key:"setWndZOrder",value:function(e){return this.oRequest.sendRequest({cmd:"window.setWndZOrder",flag:e})}},{key:"changePlayMode",value:function(e){return this.oRequest.sendRequest({cmd:"window.changePlayMode",type:e})}},{key:"setLanguageType",value:function(e){return this.oRequest.sendRequest({cmd:"window.setLanguageType",type:e})}},{key:"initLoginInfo",value:function(e){return this.oRequest.sendRequest({cmd:"window.initLoginInfo",vsmAddress:e.vsmAddress,vsmPort:e.vsmPort,sessionID:e.sessionID,loginModel:e.loginModel,userType:e.userType,networkType:e.networkType})}},{key:"setTranslateFile",value:function(e){return this.oRequest.sendRequest({cmd:"window.setTranslateFile",url:e})}},{key:"switchToSimple",value:function(e){return this.oRequest.sendRequest({cmd:"window.switchToSimple",simple:e})}},{key:"setVsmToken",value:function(e){return this.oRequest.sendRequest({cmd:"play.setVsmToken",token:e})}},{key:"startPlay",value:function(e,t,n,o,r,i,s,a,u){var c={cmd:"play.startPlay",url:e,username:t,password:n,siteID:o,areaName:pf.Base64().encode(r),cameraName:pf.Base64().encode(i),permission:s,wndIndex:a};return void 0!==u&&(c.options=u,void 0!==c.options.siteName&&(c.options.siteName=pf.Base64().encode(c.options.siteName))),this.oRequest.sendRequest(c)}},{key:"setPreview3DPosition",value:function(e){return this.oRequest.sendRequest({cmd:"play.setPreview3DPosition",open:e})}},{key:"stopTotal",value:function(){var e=this;return new Promise((function(t,n){null!==e.oRequest?e.oRequest.sendRequest({cmd:"play.stopTotal"}).then((function(e){t(e)}),(function(e){n(e)})):n()}))}},{key:"setDragMode",value:function(e){return this.oRequest.sendRequest({cmd:"play.setDragMode",drag:e})}},{key:"showErrorInfoInFullScreen",value:function(e){return this.oRequest.sendRequest({cmd:"play.showErrorInfoInFullScreen",error:pf.Base64().encode(e)})}},{key:"setNumberOfWindows",value:function(e){return this.oRequest.sendRequest({cmd:"play.setNumberOfWindows",number:e})}},{key:"initCardReader",value:function(e){return this.oRequest.sendRequest({cmd:"serial.ACSInitCardReader",param:e})}},{key:"unInitCardReader",value:function(){return this.oRequest.sendRequest({cmd:"serial.ACSUnInitCardReader"})}},{key:"startAutoMode",value:function(){return this.oRequest.sendRequest({cmd:"serial.ACSStartAutoMode"})}},{key:"stopAutoMode",value:function(){return this.oRequest.sendRequest({cmd:"serial.ACSStopAutoMode"})}},{key:"initFingerprint",value:function(e){return this.oRequest.sendRequest({cmd:"serial.ACSInitFingerprint",param:e})}},{key:"unInitFingerprint",value:function(){return this.oRequest.sendRequest({cmd:"serial.ACSUnInitFingerprint"})}},{key:"startCollectFingerprint",value:function(){return this.oRequest.sendRequest({cmd:"serial.ACSStartCollectFingerprint"})}},{key:"stopCollectFingerprint",value:function(){return this.oRequest.sendRequest({cmd:"serial.ACSStopCollectFingerprint"})}},{key:"isCollectingFingerprint",value:function(){return this.oRequest.sendRequest({cmd:"serial.ACSIsCollectingFingerprint"})}},{key:"initVideocapture",value:function(e){return e.majorTitle=pf.Base64().encode(e.majorTitle),e.tip=pf.Base64().encode(e.tip),e.captureBtnTxt=pf.Base64().encode(e.captureBtnTxt),e.USBRemovedTip=pf.Base64().encode(e.USBRemovedTip),this.oRequest.sendRequest({cmd:"serial.ACSStartCollectImage",param:e})}},{key:"unInitVideocapture",value:function(){return this.oRequest.sendRequest({cmd:"serial.ACSStopCollectImage"})}},{key:"registerDeviceType",value:function(e){return this.oRequest.sendRequest({cmd:"sadp.registDeviceType",deviceType:e})}},{key:"activeOnlineDevice",value:function(e,t){return this.oRequest.sendRequest({cmd:"sadp.activeDevice",serialNumber:e,password:t})}},{key:"refreshDeviceList",value:function(){return this.oRequest.sendRequest({cmd:"sadp.refreshDeviceList"})}},{key:"modifyDeviceNetParam",value:function(e,t,n,o,r,i,s){return this.oRequest.sendRequest({cmd:"sadp.modifyDeviceParam",macAddress:e,password:t,ipv4Address:n,ipv4Gateway:o,ipv4SubnetMask:r,port:i,httpPort:s})}},{key:"exportKeyFile",value:function(e){return this.oRequest.sendRequest({cmd:"sadp.exportKeyFile",serialNumber:e})}},{key:"importKeyFile",value:function(){return this.oRequest.sendRequest({cmd:"sadp.importKeyFile"})}},{key:"resetPassword",value:function(e,t,n,o){return this.oRequest.sendRequest({cmd:"sadp.resetPassword",serialNumber:e,password:t,importFileData:n,szCode:o})}},{key:"uploadPicture",value:function(e){return this.oRequest.sendRequest({cmd:"slice.uploadPicture",path:pf.Base64().encode(e)})}},{key:"showSelectMenu",value:function(e,t,n,o,r){return this.oRequest.sendRequest({cmd:"ui.showSelectMenu",items:r,rect:{left:e,top:t,width:n,height:o}})}},{key:"hideSelectMenu",value:function(){return this.oRequest.sendRequest({cmd:"ui.hideSelectMenu"})}},{key:"destroySelectMenu",value:function(){var e=this;return new Promise((function(t,n){null!==e.oRequest?e.oRequest.sendRequest({cmd:"ui.destroySelectMenu"}).then((function(e){t(e)}),(function(e){n(e)})):n()}))}},{key:"deviceConfig",value:function(e){return this.oRequest.sendRequest({cmd:"laputa.encodingDevice",param:e})}},{key:"cloudStorageConfig",value:function(e){return this.oRequest.sendRequest({cmd:"laputa.cloudStorage",param:e})}},{key:"ezvizRemoteConfig",value:function(e){return this.oRequest.sendRequest({cmd:"laputa.ezvizRemote",param:e})}},{key:"showAlarmInfoInFullScreen",value:function(e,t,n){return this.oRequest.sendRequest({cmd:"window.showAlarmInfoInFullScreen",alarmTitle:e,alarmMessage:t,alarmId:n})}},{key:"updateParentWnd",value:function(){return this.oRequest.sendRequest({cmd:"window.updateParentWnd"})}},{key:"restoreWnd",value:function(){return this.oRequest.sendRequest({cmd:"window.restoreWnd"})}},{key:"setImmediatePlaybackTime",value:function(e){return this.oRequest.sendRequest({cmd:"play.setImmediatePlaybackTime",specifyTime:e})}},{key:"setDrawStatus",value:function(e){return this.oRequest.sendRequest({cmd:"draw.setDrawStatus",enable:e})}},{key:"clearRegion",value:function(){return this.oRequest.sendRequest({cmd:"draw.clearRegion"})}},{key:"setDrawShapeInfo",value:function(e,t){return this.oRequest.sendRequest({cmd:"draw.setDrawShapeInfo",drawType:e,drawInfo:t})}},{key:"setGridInfo",value:function(e){return this.oRequest.sendRequest({cmd:"draw.setGridInfo",gridInfo:e})}},{key:"getGridInfo",value:function(){return this.oRequest.sendRequest({cmd:"draw.getGridInfo"})}},{key:"setPolygonInfo",value:function(e){return this.oRequest.sendRequest({cmd:"draw.setPolygonInfo",polygonInfo:e})}},{key:"getPolygonInfo",value:function(){return this.oRequest.sendRequest({cmd:"draw.getPolygonInfo"})}},{key:"setLineInfo",value:function(e){return this.oRequest.sendRequest({cmd:"draw.setLineInfo",lineInfo:e})}},{key:"getLineInfo",value:function(){return this.oRequest.sendRequest({cmd:"draw.getLineInfo"})}},{key:"setRectInfo",value:function(e){return this.oRequest.sendRequest({cmd:"draw.setRectInfo",rectInfo:e})}},{key:"getRectInfo",value:function(){return this.oRequest.sendRequest({cmd:"draw.getRectInfo"})}},{key:"clearShapeByType",value:function(e){return this.oRequest.sendRequest({cmd:"draw.clearShapeByType",type:e})}},{key:"sensitiveEncrypt",value:function(e,t,n){var o={cmd:"laputa.sensitiveEncrypt",encryptType:e,encryptField:t};return void 0!==n&&(o.options=n),this.oRequest.sendRequest(o)}},{key:"sendRequest",value:function(e){return this.oRequest.sendRequest(e)}},{key:"requestInterface",value:function(e){var t={cmd:"window.requestInterface"};return t.requestParams=e,this.oRequest.sendRequest(t)}},{key:"stopPlay",value:function(e){return void 0===e&&(e=-1),this.oRequest.sendRequest({cmd:"play.stopPlay",wndIndex:e})}},{key:"showRemoteConfig",value:function(e){var t=this;return e.cmd="config.showRemoteConfig",new Promise((function(n,o){null!==t.oRequest?t.oRequest.sendRequest(e).then((function(e){n(e)}),(function(e){o(e)})):o()}))}},{key:"video2Picture",value:function(){var e={cmd:"window.video2Picture"};return this.oRequest.sendRequest(e)}},{key:"picture2Video",value:function(){var e={cmd:"window.picture2Video"};return this.oRequest.sendRequest(e)}},{key:"ptzControl",value:function(e){return this.oRequest.sendRequest({cmd:"laputa.ptzControl",param:e})}},{key:"simMouseClickEvent",value:function(e,t){return this.oRequest.sendRequest({cmd:"window.simMouseClickEvent",pointX:e,pointY:t})}},{key:"us_SetMaxJobCount",value:function(e){return this.oRequest.sendRequest({cmd:"upgrade.setMaxJobCount",xml:e})}},{key:"us_GetMaxJobCount",value:function(){return this.oRequest.sendRequest({cmd:"upgrade.getMaxJobCount"})}},{key:"us_AddSchedule",value:function(e){return this.oRequest.sendRequest({cmd:"upgrade.addSchedule",xml:pf.Base64().encode(e)})}},{key:"us_DelSchedule",value:function(e){return this.oRequest.sendRequest({cmd:"upgrade.delSchedule",scheduleId:e})}},{key:"us_GetScheduleList",value:function(e){var t=this;return new Promise((function(n,o){null!==t.oRequest?t.oRequest.sendRequest({cmd:"upgrade.getScheduleList",xml:e}).then((function(e){""!==e.xml&&(e.xml=pf.Base64().decode(e.xml)),n(e)}),(function(e){o(e)})):o()}))}},{key:"us_GetSchedule",value:function(e,t){var n=this;return new Promise((function(o,r){null!==n.oRequest?n.oRequest.sendRequest({cmd:"upgrade.getSchedule",xml:t,scheduleId:e}).then((function(e){""!==e.xml&&(e.xml=pf.Base64().decode(e.xml)),o(e)}),(function(e){r(e)})):r()}))}},{key:"us_UpgradeAction",value:function(e,t){return this.oRequest.sendRequest({cmd:"upgrade.upgradeAction",xml:t,scheduleId:e})}},{key:"us_CheckUpgradeableDevice",value:function(e){return this.oRequest.sendRequest({cmd:"upgrade.checkUpgradeableDevice",param:e})}},{key:"us_CheckUpgradeableDeviceList",value:function(e){return this.oRequest.sendRequest({cmd:"upgrade.checkUpgradeableDeviceList",param:e})}},{key:"us_IsRunningAsyCheckUpgradeable",value:function(){return this.oRequest.sendRequest({cmd:"upgrade.isRunningAsyCheckUpgradeable"})}},{key:"us_StopAsyCheckUpgradeable",value:function(){return this.oRequest.sendRequest({cmd:"upgrade.stopAsyCheckUpgradeable"})}},{key:"getFishEyePTZPreset",value:function(e){return this.oRequest.sendRequest({cmd:"play.getFishEyePTZPreset",wndIndex:e})}},{key:"setFishEyePTZPreset",value:function(e,t,n){return this.oRequest.sendRequest({cmd:"play.setFishEyePTZPreset",wndIndex:e,command:t,presetInfo:n})}},{key:"controlFishEyePTZ",value:function(e,t,n,o){return this.oRequest.sendRequest({cmd:"play.controlFishEyePTZ",wndIndex:e,command:t,stop:n,speed:o})}},{key:"controlFishEyeParol",value:function(e,t,n){return this.oRequest.sendRequest({cmd:"play.controlFishEyeParol",wndIndex:e,command:t,cruisePointList:n})}},{key:"setFirstDayOfWeek",value:function(e){return this.oRequest.sendRequest({cmd:"window.setFirstDayOfWeek",firstDay:e})}},{key:"setEhomePlayInfo",value:function(e,t,n,o,r,i){return this.oRequest.sendRequest({cmd:"play.setEhomePlayInfo",guid:e,protocal:t,session:n,token:o,ip:r,port:i})}},{key:"startPlayPatch",value:function(e){if(e.length>0)for(var t=0,n=e.length;t<n;t++)e[t].areaName=pf.Base64().encode(e[t].areaName),e[t].cameraName=pf.Base64().encode(e[t].cameraName);return this.oRequest.sendRequest({cmd:"play.startPlayPatch",params:e})}},{key:"grabOpen",value:function(){var e=this;return new Promise((function(t,n){null!==e.oRequest?e.oRequest.sendRequest({cmd:"window.grabOpen"}).then((function(e){t(e)}),(function(e){n(e)})):n()}))}},{key:"setWndAutoPanState",value:function(e,t){return this.oRequest.sendRequest({cmd:"play.setWndAutoPanState",wndIndex:e,open:t})}},{key:"enablePrivileges",value:function(){return this.oRequest.sendRequest({cmd:"system.enablePrivileges"})}}]),e}(),Rp),Tp=(qp=function(){function e(t){n(this,e);var o=this;this.oOptions=Object.assign({szPluginContainer:"",cbConnectSuccess:null,cbConnectError:null,cbConnectClose:null,iServicePortStart:16960,iServicePortEnd:16969,szClassId:"55A7329E-FAAD-439a-87BC-75BAB3332E7C"},t),this.bFreeze=!1,this.bFocus=!0,this.bEmbed=pf.getCreateWndMode(),this.szWndId="",this.iCreateWndTimer=-1,this.iUpdateParentWndTimer=-1,this.bDevTool=!1,this.iVCTimeStart=-1,this.iVCTimeEnd=-1,this.oWndCover={left:0,top:0,right:0,bottom:0},this.oDocOffset={left:0,top:0},this.szTitle="",this.oWindowAttr={outerWidth:0,innerWidth:0,outerHeight:0,innerHeight:0,screenTop:0,screenLeft:0,screenX:0,screenY:0},this.iFixedResizeTimer=-1,this.fVisibilityChange=function(){if(pf.isMacOS())document.hidden?o.fHideWnd():o.fShowWnd();else if(document.hidden)o.iVCTimeStart=(new Date).getTime(),o.fHideWnd();else{o.iVCTimeEnd=(new Date).getTime();var e=pf.browser();if(e.chrome||e.mozilla)if(o.iUpdateParentWndTimer>0&&(clearTimeout(o.iUpdateParentWndTimer),o.iUpdateParentWndTimer=-1),o.iVCTimeEnd-o.iVCTimeStart<100){var t=o.oRequest.getRequestUUID();mf(t,o.oOptions.cbSetDocTitle),o.iUpdateParentWndTimer=setTimeout((function(){o.oRequest.updateParentWnd().then((function(){yf(t,o.oOptions.cbUnsetDocTitle),o.bFreeze||o.bDevTool||o.fShowWnd()}),(function(){yf(t,o.oOptions.cbUnsetDocTitle)}))}),100)}else o.bFreeze||o.bDevTool||o.fShowWnd();else o.bFreeze||o.bDevTool||o.fShowWnd()}},this.fHideWnd=function(){o.oRequest.hideWnd().then((function(){}),(function(){}))},this.fShowWnd=function(){o.oRequest.showWnd().then((function(){}),(function(){}))},this.fFocus=function(){o.bFocus=!0,setTimeout((function(){o.removeGrabImage(),document.hidden||o.bFreeze||o.bDevTool||o.fShowWnd()}),200)},this.fBlur=function(){o.bFocus=!1},this.removeGrabImage=function(){if(!pf.isMacOS()){var e=null;if(""!==o.szWndId&&(e=document.getElementById(o.szWndId))){var t=e.querySelectorAll('[data-name="wc-grab-open-image"]');Array.prototype.slice.call(t).forEach((function(e){e.parentNode.removeChild(e)}))}}},this.oRequest=new Op({szPluginContainer:this.oOptions.szPluginContainer,cbConnectSuccess:this.oOptions.cbConnectSuccess,cbConnectError:this.oOptions.cbConnectError,cbConnectClose:function(e){o.iCreateWndTimer>0&&(clearTimeout(o.iCreateWndTimer),o.iCreateWndTimer=-1),o.removeGrabImage(),o.oOptions.cbConnectClose&&o.oOptions.cbConnectClose(e)},iServicePortStart:this.oOptions.iServicePortStart,iServicePortEnd:this.oOptions.iServicePortEnd,szClassId:this.oOptions.szClassId})}return r(e,[{key:"JS_SetWindowControlCallback",value:function(e){var t=this,n={cbSelectWnd:function(t){e.cbSelectWnd&&e.cbSelectWnd(parseInt(t.wndIndex,10),t.cameraID,t.siteID,t.opendFisheye)},cbTogglePTZ:function(t){e.cbTogglePTZ&&e.cbTogglePTZ(t.cameraID,t.siteID)},cbUpdateCameraIcon:function(t){e.cbUpdateCameraIcon&&e.cbUpdateCameraIcon(t.cameraID,parseInt(t.playing,10),t.siteID)},cbGetLastError:function(t){e.cbGetLastError&&e.cbGetLastError(t.error,parseInt(t.type,10))},cbTalkUrlEmpty:function(t){e.cbTalkUrlEmpty&&e.cbTalkUrlEmpty(t.cameraID)},cbGotoPlayback:function(t){e.cbGotoPlayback&&e.cbGotoPlayback(t.cameraID,t.siteID)},cbShowDisplayInfo:function(t){e.cbShowDisplayInfo&&e.cbShowDisplayInfo(parseInt(t.videoWidth,10),parseInt(t.videoHeight,10),parseInt(t.frameRate,10))},cbPreviewWnd3DPostion:function(t){e.cbPreviewWnd3DPostion&&e.cbPreviewWnd3DPostion(parseInt(t.startX,10),parseInt(t.startY,10),parseInt(t.endX,10),parseInt(t.endY,10))},cbStopPlayAll:function(){e.cbStopPlayAll&&e.cbStopPlayAll()},cbWheelEvent:function(t){e.cbWheelEvent&&e.cbWheelEvent(parseInt(t.delta,10))},cbAlarmDetail:function(t){e.cbAlarmDetail&&e.cbAlarmDetail(t.alarmId)},cbQuitedFullScreen:function(){setTimeout((function(){t.fShowWnd()}),100)},cbManuallyClose:function(t){e.cbManuallyClose&&e.cbManuallyClose(t.cameraID,t.siteID,parseInt(t.wndIndex,10))},cbIntegrationCallBack:function(t){e.cbIntegrationCallBack&&e.cbIntegrationCallBack(t)},cbChangeStorage:function(t){e.cbChangeStorage&&e.cbChangeStorage(parseInt(t.storageType,10),t.cameraID,t.siteID)},cbFisheyeExpandChanged:function(t){e.cbFisheyeExpandChanged&&e.cbFisheyeExpandChanged(t.cameraID,t.siteID,parseInt(t.wndIndex,10),t.open)},cbGetEhomePlayInfo:function(t){e.cbGetEhomePlayInfo&&e.cbGetEhomePlayInfo(t.siteID,t.guid)},cbWndPtzControl:function(t){e.cbWndPtzControl&&e.cbWndPtzControl(parseInt(t.wndIndex,10),t.cameraID,t.command,t.speed,t.stop)},cbMessageCallBack:function(n){n=n.data;var o=t.oRequest.getRequestUUID();"menuOpen"===n.type?""!==t.szWndId&&(document.getElementById(t.szWndId).innerHTML="<img data-name='wc-grab-open-image' src='data:image/png;base64,".concat(n.message.image,"' width='100%' height='100%' />")):"changeTitle"===n.type?-1===document.title.indexOf(o)&&(t.szTitle=document.title,mf(o,t.oOptions.cbSetDocTitle),setTimeout((function(){"updateParentWnd"===n.message?t.oRequest.updateParentWnd():"restoreWnd"===n.message&&t.oRequest.restoreWnd()}),300)):"changeTitleDone"===n.type?""!==t.szTitle&&yf(o,t.oOptions.cbUnsetDocTitle):"splitChange"===n.type?e.cbSplitChange&&e.cbSplitChange(n.message.splitType):"showMaximized"===n.type&&e.cbShowMaximized&&e.cbShowMaximized(n.message.showMax)}};this.oRequest.setWindowControlCallback(n)}},{key:"JS_SetSadpCallback",value:function(e){var t={cbDeviceFind:null};Object.assign(t,e),this.oRequest.setSadpCallback(t)}},{key:"JS_SetSliceCallback",value:function(e){var t={cbImageSliced:function(t){e.cbImageSliced&&(""!==t.picName&&(t.picName=pf.Base64().decode(t.picName)),e.cbImageSliced(t))}};this.oRequest.setSliceCallback(t)}},{key:"JS_SetSerialCallback",value:function(e){var t={cbCardFind:function(t){e.cbCardFind&&e.cbCardFind(t)},cbFingerFind:function(t){e.cbFingerFind&&e.cbFingerFind(t.fingerPrint,t.fingerQuality)},cbImageFind:function(t){e.cbImageFind&&e.cbImageFind(t.image)},cbImageErrorFind:function(t){e.cbImageErrorFind&&e.cbImageErrorFind(t.errorModule,t.errorCode)},cbImageWndVisibleFind:function(t){e.cbImageWndVisibleFind&&e.cbImageWndVisibleFind(t.visible)}};this.oRequest.setSerialCallback(t)}},{key:"JS_SetUIControlCallback",value:function(e){var t={cbClickMenuItem:function(t){e.cbClickMenuItem&&e.cbClickMenuItem(t.itemIndex)},cbMenuMouseIn:function(){e.cbMenuMouseIn&&e.cbMenuMouseIn()},cbMenuMouseOut:function(){e.cbMenuMouseOut&&e.cbMenuMouseOut()}};this.oRequest.setUIControlCallback(t)}},{key:"JS_SetUpgradeCallback",value:function(e){var t={cbCheckUpgrade:function(t){e.cbCheckUpgrade&&e.cbCheckUpgrade(t)}};this.oRequest.setUpgradeCallback(t)}},{key:"JS_CheckVersion",value:function(e){var t=this.oRequest.getServiceVersion(),n=[],o=[];""!==t&&(n=(t=t.replace(/,[\s]*/g,".")).split(".")),""!==e&&(o=(e=e.replace(/,[\s]*/g,".")).split("."));var r=!1;if(o.length===n.length)for(var i=0,s=n.length;i<s;i++)if(parseInt(o[i],10)!==parseInt(n[i],10)){if(parseInt(o[i],10)>parseInt(n[i],10)){r=!0;break}break}return r}},{key:"JS_StartService",value:function(e,t){return this.oRequest.startService(e,t)}},{key:"JS_StopService",value:function(e){return this.oRequest.stopService(e)}},{key:"JS_Disconnect",value:function(){return this.oRequest.disconnect()}},{key:"JS_OpenDirectory",value:function(e){return this.oRequest.openDirectory(e)}},{key:"JS_OpenFile",value:function(e,t,n){return this.oRequest.openFile(e,t,n)}},{key:"JS_SelectDirectory",value:function(e,t){return this.oRequest.selectDirectory(e,t)}},{key:"JS_SelectFile",value:function(e,t,n){return this.oRequest.selectFile(e,t,n)}},{key:"JS_GetLocalConfig",value:function(e){return this.oRequest.getLocalConfig(e)}},{key:"JS_SetLocalConfig",value:function(e){return this.oRequest.setLocalConfig(e)}},{key:"JS_SetDocOffset",value:function(e){return e&&(this.oDocOffset=e),!0}},{key:"JS_SetWindowAttr",value:function(e){return e&&(this.oWindowAttr=e),!0}},{key:"JS_CreateWnd",value:function(e,t,n,o){var r=this;this.szWndId=e,void 0!==(o=o||{}).bEmbed&&(this.bEmbed=o.bEmbed);var i=!0;return void 0!==o.bActiveXParentWnd&&(i=o.bActiveXParentWnd),new Promise((function(s,a){var u=document.getElementById(e);if(u){var c="";pf.browser().msie?c="IEFrame":pf.browser().chrome?c="Chrome":pf.browser().safari&&(c=window.top.document.title),o.cbSetDocTitle&&(r.oOptions.cbSetDocTitle=o.cbSetDocTitle),o.cbUnsetDocTitle&&(r.oOptions.cbUnsetDocTitle=o.cbUnsetDocTitle);var l=r.oRequest.getRequestUUID();mf(l,o.cbSetDocTitle),r.iCreateWndTimer=setTimeout((function(){if(!r.bDevTool){var e=pf.getDevicePixelRatio(),d=pf.getWndPostion(u,r.bEmbed,r.oWindowAttr,r.oDocOffset);t=Math.round(t*e),n=Math.round(n*e),r.oRequest.createWnd(d.left,d.top,t,n,c,r.bEmbed,i,o.HWND||"").then((function(){yf(l,o.cbUnsetDocTitle),s()}),(function(e){yf(l,o.cbUnsetDocTitle),5001===e.errorCode?(document.hidden||r.bFreeze||!r.bFocus||r.fShowWnd(),s()):a(e)}))}}),300),document.addEventListener("visibilitychange",r.fVisibilityChange,!1),window.addEventListener("focus",r.fFocus),window.addEventListener("blur",r.fBlur)}else a()}))}},{key:"JS_ShowWnd",value:function(){this.bFreeze=!1,document.hidden||this.bDevTool||this.fShowWnd()}},{key:"JS_HideWnd",value:function(){this.bFreeze=!0,this.fHideWnd()}},{key:"JS_DestroyWnd",value:function(){return document.removeEventListener("visibilitychange",this.fVisibilityChange,!1),window.removeEventListener("focus",this.fFocus),window.removeEventListener("blur",this.fBlur),this.oRequest.destroyWnd()}},{key:"JS_Resize",value:function(e,t,n){var o=this,r=null,i=e,s=t;if(""!==this.szWndId&&(r=document.getElementById(this.szWndId)),r){var a=pf.getWndPostion(r,this.bEmbed,this.oWindowAttr,this.oDocOffset),u=pf.getDevicePixelRatio();(!pf.browser().msie||pf.browser().msie&&"11.0"===pf.browser().version)&&(this.oWndCover.left>0&&(a.left+=Math.round(this.oWndCover.left*u),e-=this.oWndCover.left),this.oWndCover.top>0&&(a.top+=Math.round(this.oWndCover.top*u),t-=this.oWndCover.top),this.oWndCover.right>0&&(e-=this.oWndCover.right),this.oWndCover.bottom>0&&(t-=this.oWndCover.bottom)),e=Math.round(e*u),t=Math.round(t*u),this.oRequest.setWndGeometry(a.left,a.top,e,t),(pf.browser().msie&&"11.0"===pf.browser().version||!pf.isWindows())&&(n&&n.bFixed?this.iFixedResizeTimer=-1:(this.iFixedResizeTimer>-1&&(clearTimeout(this.iFixedResizeTimer),this.iFixedResizeTimer=-1),this.iFixedResizeTimer=setTimeout((function(){o.JS_Resize(i,s,{bFixed:!0})}),300)))}}},{key:"JS_SetWndCover",value:function(e,t){var n=pf.getDevicePixelRatio();return(!pf.browser().msie||pf.browser().msie&&"11.0"===pf.browser().version)&&("left"===e?this.oWndCover.left=t:"top"===e?this.oWndCover.top=t:"right"===e?this.oWndCover.right=t:"bottom"===e&&(this.oWndCover.bottom=t)),t=Math.round(t*n),this.oRequest.setWndCover(e,t)}},{key:"JS_CuttingPartWindow",value:function(e,t,n,o,r){var i=pf.getDevicePixelRatio();return e=Math.round(e*i),t=Math.round(t*i),n=Math.round(n*i),o=Math.round(o*i),r=Math.round(r*i),this.oRequest.cuttingPartWindow(e,t,n,o,r)}},{key:"JS_RepairPartWindow",value:function(e,t,n,o,r){var i=pf.getDevicePixelRatio();return e=Math.round(e*i),t=Math.round(t*i),n=Math.round(n*i),o=Math.round(o*i),r=Math.round(r*i),this.oRequest.repairPartWindow(e,t,n,o,r)}},{key:"JS_ChangePlayMode",value:function(e){return this.oRequest.changePlayMode(e)}},{key:"JS_SetLanguageType",value:function(e){return this.oRequest.setLanguageType(e)}},{key:"JS_InitLoginInfo",value:function(e){return this.oRequest.initLoginInfo(e)}},{key:"JS_SetTranslateFile",value:function(e){return this.oRequest.setTranslateFile(e)}},{key:"JS_SwitchToSimple",value:function(e){return this.oRequest.switchToSimple(e)}},{key:"JS_SetVsmToken",value:function(e){return this.oRequest.setVsmToken(e)}},{key:"JS_Play",value:function(e,t,n,o,r,i,s,a,u){return this.oRequest.startPlay(e,t,n,o,r,i,s,a,u)}},{key:"JS_Enable3DZoom",value:function(e){return this.oRequest.setPreview3DPosition(e)}},{key:"JS_StopTotal",value:function(){return this.oRequest.stopTotal()}},{key:"JS_SetDragMode",value:function(e){return this.oRequest.setDragMode(e)}},{key:"JS_ShowErrorInfoInFullScreen",value:function(e){return this.oRequest.showErrorInfoInFullScreen(e)}},{key:"JS_SetNumberOfWindows",value:function(e){return this.oRequest.setNumberOfWindows(e)}},{key:"JS_InitCardReader",value:function(e){return this.oRequest.initCardReader(e)}},{key:"JS_UnInitCardReader",value:function(){return this.oRequest.unInitCardReader()}},{key:"JS_StartAutoMode",value:function(){return this.oRequest.startAutoMode()}},{key:"JS_StopAutoMode",value:function(){return this.oRequest.stopAutoMode()}},{key:"JS_InitFingerprint",value:function(e){return this.oRequest.initFingerprint(e)}},{key:"JS_UnInitFingerprint",value:function(){return this.oRequest.unInitFingerprint()}},{key:"JS_StartCollectFingerprint",value:function(){return this.oRequest.startCollectFingerprint()}},{key:"JS_StopCollectFingerprint",value:function(){return this.oRequest.stopCollectFingerprint()}},{key:"JS_IsCollectingFingerprint",value:function(){return this.oRequest.isCollectingFingerprint()}},{key:"JS_InitVideocapture",value:function(e){return this.oRequest.initVideocapture(e)}},{key:"JS_UnInitVideocapture",value:function(){return this.oRequest.unInitVideocapture()}},{key:"JS_RegisterDeviceType",value:function(e){return this.oRequest.registerDeviceType(e)}},{key:"JS_ActiveOnlineDevice",value:function(e,t){return this.oRequest.activeOnlineDevice(e,t)}},{key:"JS_RefreshDeviceList",value:function(){return this.oRequest.refreshDeviceList()}},{key:"JS_ModifyDeviceNetParam",value:function(e,t,n,o,r,i,s){return this.oRequest.modifyDeviceNetParam(e,t,n,o,r,i,s)}},{key:"JS_ExportKeyFile",value:function(e){return this.oRequest.exportKeyFile(e)}},{key:"JS_ImportKeyFile",value:function(){return this.oRequest.importKeyFile()}},{key:"JS_ResetPassword",value:function(e,t,n,o){return this.oRequest.resetPassword(e,t,n,o)}},{key:"JS_UploadPicture",value:function(e){return this.oRequest.uploadPicture(e)}},{key:"JS_ShowSelectMenu",value:function(e,t,n,o,r){var i=document.getElementById(e);if(i){var s=pf.getWndPostion(i,!1,this.oWindowAttr,this.oDocOffset);"center"===r?s.left-=Math.round((t-i.offsetWidth)/2):"right"===r&&(s.left-=Math.round(t-i.offsetWidth));var a=pf.getDevicePixelRatio();t=Math.round(t*a),n=Math.round(n*a);var u=1*window.getComputedStyle(i).height.slice(0,-2),c=Math.round(u*a);this.oRequest.showSelectMenu(s.left,s.top+c,t,n,o)}}},{key:"JS_HideSelectMenu",value:function(){this.oRequest.hideSelectMenu()}},{key:"JS_DestroySelectMenu",value:function(){return this.oRequest.destroySelectMenu()}},{key:"JS_DeviceConfig",value:function(e){return this.oRequest.deviceConfig(e)}},{key:"JS_CloudStorageConfig",value:function(e){return this.oRequest.cloudStorageConfig(e)}},{key:"JS_EzvizRemoteConfig",value:function(e){return this.oRequest.ezvizRemoteConfig(e)}},{key:"JS_ShowAlarmInfoInFullScreen",value:function(e,t,n){return this.oRequest.showAlarmInfoInFullScreen(e,t,n)}},{key:"JS_SetImmediatePlaybackTime",value:function(e){return this.oRequest.setImmediatePlaybackTime(e)}},{key:"JS_SetDrawStatus",value:function(e){return this.oRequest.setDrawStatus(e)}},{key:"JS_ClearRegion",value:function(){return this.oRequest.clearRegion()}},{key:"JS_SetDrawShapeInfo",value:function(e,t){return this.oRequest.setDrawShapeInfo(e,t)}},{key:"JS_SetGridInfo",value:function(e){return this.oRequest.setGridInfo(e)}},{key:"JS_GetGridInfo",value:function(){return this.oRequest.getGridInfo()}},{key:"JS_SetPolygonInfo",value:function(e){return this.oRequest.setPolygonInfo(e)}},{key:"JS_GetPolygonInfo",value:function(){return this.oRequest.getPolygonInfo()}},{key:"JS_SetLineInfo",value:function(e){return this.oRequest.setLineInfo(e)}},{key:"JS_GetLineInfo",value:function(){return this.oRequest.getLineInfo()}},{key:"JS_SetRectInfo",value:function(e){return this.oRequest.setRectInfo(e)}},{key:"JS_GetRectInfo",value:function(){return this.oRequest.getRectInfo()}},{key:"JS_ClearShapeByType",value:function(e){return this.oRequest.clearShapeByType(e)}},{key:"JS_SensitiveEncrypt",value:function(e,t,n){return this.oRequest.sensitiveEncrypt(e,t,n)}},{key:"JS_SendRequest",value:function(e){return this.oRequest.sendRequest(e)}},{key:"JS_RequestInterface",value:function(e){return this.oRequest.requestInterface(e)}},{key:"JS_StopPlay",value:function(e){return this.oRequest.stopPlay(e)}},{key:"JS_ShowRemoteConfig",value:function(e){return this.oRequest.showRemoteConfig(e)}},{key:"JS_Video2Picture",value:function(){return this.oRequest.video2Picture()}},{key:"JS_Picture2Video",value:function(){return this.oRequest.picture2Video()}},{key:"JS_PtzControl",value:function(e){return this.oRequest.ptzControl(e)}},{key:"JS_SimMouseClickEvent",value:function(e,t){return this.oRequest.simMouseClickEvent(e,t)}},{key:"JS_US_SetMaxJobCount",value:function(e){return this.oRequest.us_SetMaxJobCount(e)}},{key:"JS_US_GetMaxJobCount",value:function(){return this.oRequest.us_GetMaxJobCount()}},{key:"JS_US_AddSchedule",value:function(e){return this.oRequest.us_AddSchedule(e)}},{key:"JS_US_DelSchedule",value:function(e){return this.oRequest.us_DelSchedule(e)}},{key:"JS_US_GetScheduleList",value:function(e){return this.oRequest.us_GetScheduleList(e)}},{key:"JS_US_GetSchedule",value:function(e,t){return this.oRequest.us_GetSchedule(e,t)}},{key:"JS_US_UpgradeAction",value:function(e,t){return this.oRequest.us_UpgradeAction(e,t)}},{key:"JS_US_CheckUpgradeableDevice",value:function(e){return this.oRequest.us_CheckUpgradeableDevice(e)}},{key:"JS_US_CheckUpgradeableDeviceList",value:function(e){return this.oRequest.us_CheckUpgradeableDeviceList(e)}},{key:"JS_US_IsRunningAsyCheckUpgradeable",value:function(){return this.oRequest.us_IsRunningAsyCheckUpgradeable()}},{key:"JS_US_StopAsyCheckUpgradeable",value:function(){return this.oRequest.us_StopAsyCheckUpgradeable()}},{key:"JS_GetFishEyePTZPreset",value:function(e){return this.oRequest.getFishEyePTZPreset(e)}},{key:"JS_SetFishEyePTZPreset",value:function(e,t,n){return this.oRequest.setFishEyePTZPreset(e,t,n)}},{key:"JS_ControlFishEyePTZ",value:function(e,t,n,o){return this.oRequest.controlFishEyePTZ(e,t,n,o)}},{key:"JS_ControlFishEyeParol",value:function(e,t,n){return this.oRequest.controlFishEyeParol(e,t,n)}},{key:"JS_SetFirstDayOfWeek",value:function(e){return this.oRequest.setFirstDayOfWeek(e)}},{key:"JS_SetEhomePlayInfo",value:function(e,t,n,o,r,i){return this.oRequest.setEhomePlayInfo(e,t,n,o,r,i)}},{key:"JS_PlayPatch",value:function(e){return this.oRequest.startPlayPatch(e)}},{key:"JS_SetWndAutoPanState",value:function(e,t){return this.oRequest.setWndAutoPanState(e,t)}},{key:"JS_EnablePrivileges",value:function(){return this.oRequest.enablePrivileges()}}],[{key:"JS_WakeUp",value:function(e){var t=document.createElement("iframe");t.style.display="none",t.src=e,document.body.appendChild(t),setTimeout((function(){document.body.removeChild(t)}),3e3)}}]),e}(),qp);return Tp.version="1.2.7",Tp}();
