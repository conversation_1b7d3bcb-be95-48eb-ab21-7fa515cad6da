<template>
  <div class="right-Map">
    <hjjc></hjjc>
    <jtll></jtll>
    <xtyxzt></xtyxzt>
  </div>
</template>

<script>
import hjjc from './hjjc'
import jtll from './jtll'
import xtyxzt from './xtyxzt'
export default {
  name: 'index',
  components: { hjjc, jtll, xtyxzt },
  data() {
    return {}
  },
  computed: {},
  mounted() {},
  methods: {},
  watch: {},
}
</script>

<style scoped>
</style>