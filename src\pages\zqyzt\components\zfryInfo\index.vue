<template>
      <div v-if="visible" class="person-info-container">
        <!-- 左侧信息区域 -->
        <div class="left-panel">
          <!-- 人员头像 -->
          <div class="avatar-section">
            <el-image class="avatar-img" :src="dataDetail.avatar" fit="cover"></el-image>
          </div>

          <!-- 人员信息 -->
          <div class="info-section">
            <div class="info-title">人员信息</div>
            <div class="info-item">
              <i class="info-icon user-icon"></i>
              <span class="info-label">姓名：</span>
              <span class="info-value">{{ dataDetail.nickName || '未知' }}</span>
            </div>
            <div class="info-item">
              <i class="info-icon gender-icon"></i>
              <span class="info-label">性别：</span>
              <span class="info-value">{{ dataDetail.sex | sexName }}</span>
            </div>
            <div class="info-item">
              <i class="info-icon dept-icon"></i>
              <span class="info-label">部门：</span>
              <span class="info-value">{{ dataDetail.deptName || '未知' }}</span>
            </div>
            <div class="info-item">
              <i class="info-icon phone-icon"></i>
              <span class="info-label">电话：</span>
              <span class="info-value">{{ dataDetail.phonenumber || '未知' }}</span>
            </div>
          </div>

          <!-- 通讯按钮 -->
          <div class="button-section">
            <button class="comm-btn video-btn" @click="handleOpenVideo">
              <i class="btn-icon video-call-icon"></i>
              视频通话
            </button>
            <button class="comm-btn voice-btn" @click="handleOpenVideo">
              <i class="btn-icon voice-call-icon"></i>
              语音呼叫
            </button>
          </div>
        </div>

        <!-- 右侧地图区域 -->
        <div class="right-panel">
            <!-- 顶部工具栏 -->
            <div class="top-toolbar">
                <div class="date-picker">
                  <el-date-picker
                    v-model="gpsDate"
                    type="datetimerange"
                    size="mini"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    :default-time="['00:00:00', '23:59:59']"
                    :unlink-panels="false"
                    :picker-options="pickerOptions"
                    @change="handleChange"
                  />
                </div>
                <div class="close-btn" @click="handleClose">×</div>
            </div>

            <!-- 地图容器 -->
            <div class="map-container" id="mapContainer"></div>
                  <!-- 控制按钮 -->
            <div v-if="showCarTrack" class="ctrl-btn">
              <el-button
                type="primary"
                plain
                round
                style="font-size: 28px; padding: 15px 30px; margin-left: 20px"
                @click="handleCarstart">开始</el-button>
              <el-button
                type="primary"
                plain
                round
                style="font-size: 28px; padding: 15px 30px; margin-left: 20px"
                @click="handleCarpause">暂停</el-button>
              <el-button
                type="primary"
                plain
                round
                style="font-size: 28px; padding: 15px 30px; margin-left: 20px"
                @click="handleCarstop">结束</el-button>
            </div>
            <div class="bottom-toolbar"></div>
        </div>
      </div>
  </template>

  <script>
  import MapService from '@/components/Map/index.js';
  import Map from "@arcgis/core/Map";
  import SceneView from "@arcgis/core/views/SceneView";
  import { getIRSLayer } from "@/components/Map/MapUtils/basemap.js";
  import { getRecorderInfo, getRecorderGps } from '@/api/zqyzt';
  import recorderImg from '@/assets/zqyzt/windowInfo/recorder.jpg'
  import Polyline from "@arcgis/core/geometry/Polyline.js";
  import Graphic from '@arcgis/core/Graphic.js';
  import routeLayer from '@/components/Map/MapUtils/RouteLayer.js'
  export default {
    name: 'zfryInfo',
    components: {
    },
    filters: {
      sexName(sex) {
        const sexName = { 1: '女', 0: '男', 3: '未知' }
        return sexName[sex || 3]
      }
    },
    props: {
      visible: {
        type: Boolean,
        default: false
      },
      detailId: {
        type: Number
      },
      videoVisible: Boolean
    },
    data() {
      return {
        defaultAvatar: require('@/assets/zqyzt/windowInfo/fulb.png'),
        dateRange: [],
        zfrymap: null,
        zfryview: null,
        routeLayer: null,
        dataDetail: {},
        fSrcList: [],
        gpsDate: [],
        leftLoading: false,
        rightLoading: false,
        terminalNo: null,
        showCarTrack: false,
        pickerOptions: {
          cellClassName: time => {
            const nTime = time.getTime()
            const n = nTime > new Date('2021/09/01 00:00:00').getTime() && nTime < new Date('2021/09/05 23:59:59').getTime()
            const l = nTime > new Date('2021/09/08 00:00:00').getTime() && nTime < new Date('2021/09/10 23:59:59').getTime()
            if (n || l) {
              return 'hasData'
            }
          }
        },
        isMoving: false,
        currentIndex: 0,
        path: []
      }
    },
    mounted() {
    },
    watch: {
      visible(val) {
        if (val) {
          this.fetchData()
          this.$nextTick(() => {
            this.initMap()
          })
        }
      }
    },
    methods: {
      fetchData() {
          // getRecorderInfo(136).then(res => {
          getRecorderInfo(this.detailId).then(res => {
            if (res.userInfo) {
              this.dataDetail = res.userInfo
              if (res.userInfo.dept) {
                res.userInfo.deptName = res.userInfo.dept.deptName
              }
              this.dataDetail.avatar = this.dataDetail.avatar ? `/zqzfj${this.dataDetail.avatar}` : recorderImg
              console.log(this.dataDetail)
            } else {
              this.dataDetail.avatar = recorderImg
              console.log(this.dataDetail)
            }
            if (res.data.terminalNo) {
              this.terminalNo = res.data.terminalNo
            } else {
              this.terminalNo = null
            }
            resolve()
          }).catch(() => {
        })
      },
      initMap() {
        // 创建新的地图实例
        this.zfrymap = new Map({
            basemap: getIRSLayer(),
            ground: {
            opacity: 1,
            surfaceColor: "#08294a",
            },
        });

        // 创建新的视图实例
        this.zfryview = new SceneView({
            container: 'mapContainer',
            map: this.zfrymap,
            camera: {
                position: {
                  spatialReference: {
                  wkid: 4490,
                  },
                  x: 119.63010238256342,
                  y: 29.110005649408036,
                  z: 2226.879426258436,
              },
              heading: 0.26874578434742386,
              tilt: 0.49999999999694683,
            },
            constraints: {
            altitude: {
                min: 10,
            },
            },
            qualityProfile: "low",
        });

        // 禁用默认的UI组件
        this.zfryview.ui.remove('zoom');
        this.zfryview.ui.remove('attribution');
        this.zfryview.popup.autoOpenEnabled = false;
      },
      handleChange() {
        if (!this.gpsDate || !this.gpsDate.length) {
          return
        }
        // 缺少查询条件，重置地图
        if (!this.terminalNo) {
          this.$message.error('未获取到执法记录仪终端号，请重试')
          return
        }
        this.clearCarTrack()
        let params = { terminalNo: 1124 }
        if (this.gpsDate[0]) params.searchStartTime = this.gpsDate[0]
        if (this.gpsDate[1]) params.searchEndTime = this.gpsDate[1]
        getRecorderGps(params).then(res => {
          this.creatCarTrack(res.rows)
        }).catch(() => {
        })
      },
      clearCarTrack() {
        this.path = [];
        this.zfryview.graphics.removeAll();
      },
      creatCarTrack(path) {
        const paths1 = [[119.626434,29.109774], [119.606434,29.159774]]
        const paths = []
        path.forEach(element => {
          paths.push([Number.parseFloat(element.longitude), Number.parseFloat(element.latitude)])
        });
        console.log("路径", paths)
        this.routeLayer = new routeLayer({
          view: this.zfryview,
          data: paths1,
          lineStyle: {
            color: [226, 119, 40],
            width: 5,
          },
          effectStyle: {
            url: "https://static.arcgis.com/images/Symbols/Shapes/BlackStarLargeB.png",
            width: "25px",
            height: "25px",
          },
          speed: 1, // 速度，默认1
        });
        this.showCarTrack = true
      },
      handleCarstart() {
        this.routeLayer.start();
      },
      handleCarpause() {
        this.routeLayer.pause()
      },
      handleCarstop() {
        this.routeLayer.clear()
      },
      handleClose() {
        this.$emit('close')
        if (this.view) {
          this.view.destroy()
          this.view = null
        }
        if (this.map) {
          this.map = null
        }
      },
      handleOpenVideo() {
        this.$store.commit('board/SET_VIDEO_ID', this.detailId)
        this.$store.commit('board/SET_CALL_TYPE', { type: 'callRecv', ARx: 1, ATx: 1, VRx: 1, VTx: 0 }) // 修改为呼出状态
        this.$store.commit('board/SET_VIDEO_VISIBLE', true)
      },
      handleOpenVoice() {
        this.$store.commit('board/SET_VIDEO_ID', this.detailId)
        this.$store.commit('board/SET_CALL_TYPE', { type: 'callRecv', ARx: 1, ATx: 1, VRx: 0, VTx: 0 }) // 修改为呼出状态
        this.$store.commit('board/SET_VIDEO_VISIBLE', true)
      }
    }
  }
  </script>

  <style scoped>
  .person-info-container {
    display: flex;
    width: 2150px;
    height: 1350px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(16, 28, 63, 0.98);
    position: absolute;
    top: 200px;
    left: -1950px;
    border: 2px solid rgba(18, 142, 232, 0.5) !important;
  }

  .left-panel {
    width: 400px;
    padding: 30px;
    background: rgba(16, 28, 63, 0.98) ;
    display: flex;
    flex-direction: column;
  }

  .avatar-section {
    text-align: center;
    margin-bottom: 60px;
    margin-top: 30px;
  }

  .avatar-img {
    width: 300px;
    height: 300px;
    object-fit: cover;
  }

  .info-section {
    flex: 1;
  }

  .info-title {
    font-size: 32px;
    font-weight: bold;
    color: #ffffff;
    margin-bottom: 30px;
  }

  .info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 25px;
    font-size: 28px;
    color: #ffffff;
  }

  .info-icon {
    flex: 0 0 28px;
    width: 28px;
    height: 28px;
    margin-right: 15px;
    background-size: contain;
  }

  .info-label {
    flex: 0 0 100px;
    width: 100px;
  }

  .info-value {
    flex: 1;
    word-break: break-all;
    color: #ffffff;
  }

  .button-section {
    margin-top: 30px;
  }

  .comm-btn {
    width: 100%;
    height: 60px;
    margin-bottom: 20px;
    border: none;
    border-radius: 8px;
    color: #fff;
    font-size: 28px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .video-btn {
    background: #409EFF;
  }

  .voice-btn {
    background: #67C23A;
  }

  .btn-icon {
    width: 28px;
    height: 28px;
    margin-right: 10px;
    background-size: contain;
  }

  .right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  padding: 0 40px 40px 0; /* 增加右边和下边的内边距 */
  background-color: rgba(16, 28, 63, 0.98);
}

.top-toolbar {
  height: 110px;
  padding: 0 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(16, 28, 63, 0.98);
}

.date-picker {
flex: 1;
max-width: 800px;
}

.date-picker :deep(.el-input__inner) {
  width: 500px !important;
  height: 50px;
  line-height: 50px;
  font-size: 32px;
  color: #00d4ff !important;  /* 使用亮蓝色 */
  font-weight: bold;
}

.date-picker :deep(.el-range-separator) {
  font-size: 32px;
  line-height: 50px;
  color: #00d4ff !important;
  font-weight: bold;
}

.date-picker :deep(.el-range-input) {
  font-size: 32px;
  color: #00d4ff !important;
  font-weight: bold;
}

/* 调整日历面板中的文字 */
.date-picker :deep(.el-date-table) {
  font-size: 28px;
}

.date-picker :deep(.el-date-table td span) {
  color: #ffffff !important;  /* 日期颜色 */
  font-weight: bold;
}

.date-picker :deep(.el-date-table th) {
  color: #00d4ff !important;  /* 星期颜色 */
  font-size: 24px;
  font-weight: bold;
}

.date-picker :deep(.el-date-range-picker__header) {
  color: #00d4ff !important;  /* 月份年份颜色 */
  font-size: 28px;
  font-weight: bold;
}

.date-picker :deep(.el-date-range-picker__time-header) {
  color: #00d4ff !important;
  font-size: 28px;
  font-weight: bold;
}

/* 选中日期的样式 */
.date-picker :deep(.el-date-table td.in-range div) {
  background-color: rgba(0, 212, 255, 0.2);
}

.date-picker :deep(.el-date-table td.start-date div),
.date-picker :deep(.el-date-table td.end-date div) {
  background-color: #00d4ff !important;
  color: #000000 !important;
}

/* 占位符文字颜色 */
.date-picker :deep(.el-input__inner::placeholder) {
  color: rgba(0, 212, 255, 0.7) !important;
  font-size: 32px;
}

/* 日期选择器图标颜色 */
.date-picker :deep(.el-input__icon) {
  color: #00d4ff !important;
  font-size: 32px;
  line-height: 50px;
}

.close-btn {
  width: 60px;
  height: 60px;
  line-height: 50px;
  text-align: center;
  background: rgba(0, 0, 0, 0.2);
  color: #fff;
  font-size: 50px;
  border-radius: 25px;
  cursor: pointer;
  margin-right: -45px;
}

.map-container {
  flex: 1;
  width: 100%;
  border-radius: 8px; /* 可选：增加圆角 */
  overflow: hidden; /* 确保圆角生效 */
  box-shadow: 0 0 10px rgba(16, 28, 63, 0.98);
  border: 2px solid rgba(18, 142, 232, 0.5) !important;
}

.ctrl-btn {
  position: absolute;
  top: 140px;
  right: 100px;
  z-index: 500;
  padding: 10px;
  border-radius: 5px;
}
  /* 图标样式 */
  .user-icon { background-image: url('@/assets/zqyzt/windowInfo/fulb.png'); }
.gender-icon { background-image: url('@/assets/zqyzt/windowInfo/xb.png'); }
.dept-icon { background-image: url('@/assets/zqyzt/windowInfo/bm.png'); }
.phone-icon { background-image: url('@/assets/zqyzt/windowInfo/dh.png'); }
.video-call-icon { background-image: url('@/assets/zqyzt/windowInfo/fulb.png'); }
.voice-call-icon { background-image: url('@/assets/zqyzt/windowInfo/fulb.png'); }
  </style>