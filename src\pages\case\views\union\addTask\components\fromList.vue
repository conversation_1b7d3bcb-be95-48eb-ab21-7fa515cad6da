<template>
  <div>
    <el-dialog class="m-dialog" :close-on-click-modal="false" v-bind="$attrs" :title="title" v-on="$listeners" @open="onOpen" @close="onClose">
      <el-scrollbar v-loading="formLoading" style="height: 100%;" :element-loading-text="formLoadingText">
        <div style="margin-right: 10px;">
          <!--:gutter="15"-->
          <el-form ref="form" :model="form" :rules="rules" :disabled="formDisabled" size="medium" label-width="120px">
            <h3 class="title">基本信息</h3>

            <el-col :span="12">
              <el-form-item label="任务名称" prop="title">
                <el-input v-model="form.title" placeholder="请输入任务名称" clearable />
              </el-form-item>
            </el-col>
            <!-- <el-col :span="8">
                <el-form-item label="发起人名称" prop="launchName">
                  <el-input v-model="form.launchName" placeholder="请选择发起人名称" readonly>
                    <el-button slot="append" type="primary" @click="launchOpen = true">选择</el-button>
                  </el-input>
                  <userSelect id="launchName" v-model="launchOpen" :multiple="false" :select-user-keys="[form.launchId+'']" :default-expanded-keys="[form.launchId+'']" @confirm="launchConfirm" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="发起部门名称" prop="launchDeptName">
                  <el-input v-model="form.launchDeptName" placeholder="请选择发起部门名称" readonly>
                    <el-button slot="append" type="primary" @click="launchDeptOpen = true">选择</el-button>
                  </el-input>
                  <userSelect id="launchDeptName" v-model="launchDeptOpen" :multiple="false" :name="'bumen'" :select-user-keys="[form.launchDeptId+'']" :default-expanded-keys="[form.launchDeptId+'']" @confirm="launchDeptConfirm" />
                </el-form-item>
              </el-col> -->
            <!-- 暂时注释 -->
            <!-- <el-col :span="12">
              <el-form-item label="执法类型" prop="typeName">
                <el-select v-model="form.typeName" placeholder="请选择执法类型" style="width: 100%;" @change="launchDept">
                  <el-option
                    v-for="item in typeData"
                    :key="item.dictSort"
                    :label="item.dictLabel"
                    :value="item.dictSort"
                  />
                </el-select>
              </el-form-item>
            </el-col> -->
            <el-col :span="12">
              <el-form-item label="负责人名称" prop="userName">
                <el-input v-model="form.userName" placeholder="请选择负责人名称" readonly>
                  <el-button slot="append" type="primary" @click="userOpen = true">选择</el-button>
                </el-input>
                <userSelect
                  v-model="userOpen"
                  :multiple="false"
                  :select-user-keys="form.userId ? (form.userId+'').split(',') : ['100']"
                  :default-expanded-keys="form.userId ? (form.userId+'').split(',') : ['100']"
                  @confirm="userConfirm"
                />
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="执行部门" prop="deptNames">
                <el-input v-model="form.deptNames" placeholder="请选择负责人部门名称" readonly>
                  <el-button slot="append" type="primary" @click="deptOpen = true">选择</el-button>
                </el-input>
                <taskUserSelect v-model="deptOpen" :select-user-keys="form.userIds ? form.userIds.split(',') : []" :default-expanded-keys="form.userIds ? form.userIds.split(',') : []" @confirm="deptConfirm" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="执行人员" prop="userNames">
                <el-input v-model="form.userNames" type="textarea" placeholder="请选择执行人员" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="开始时间" prop="startTime">
                <el-tooltip class="item" effect="dark" :content="form.startTime" placement="top">
                  <el-date-picker v-model="form.startTime" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="请选择违规时间" clearable />
                </el-tooltip>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="结束时间" prop="endTime">
                <el-tooltip class="item" effect="dark" :content="form.endTime" placement="top">
                  <el-date-picker v-model="form.endTime" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="请选择违规时间" clearable />
                </el-tooltip>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="任务内容" prop="content">
                <el-input v-model="form.content" :autosize="{ minRows: 2, maxRows: 5}" type="textarea" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="任务方案" prop="files">
                <MFileUpload ref="mainFile" :limit="15" :ex-data="exData" :file-list="mainFileList" not-upload-msg="请上传任务方案" @uploadSucces="handleFileSuccess" @error="fileError" />
              </el-form-item>
            </el-col>
            <el-col v-if="form.status" :span="24">
              <el-form-item label="检查结果" prop="doResult">
                <el-input v-model="form.doResult" :autosize="{ minRows: 2, maxRows: 5}" type="textarea" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col v-if="form.status" :span="24">
              <el-form-item label="检查结果图片" prop="files">
                <MFileUpload ref="viceFile" :limit="15" :ex-data="viceExData" :file-list="viceFileList" not-upload-msg="请上传检查结果图片" @uploadSucces="handleViceFileSuccess" @error="fileError" />
              </el-form-item>
            </el-col>
          </el-form>
          <div v-if="unionData.length">
            <h3 class="title">子任务信息</h3>
            <el-row>
              <el-col v-for="(v,i) in unionData" :key="i" :span="10" class="suffix" @click.native="goSubtask(v)">
                <div class="list">
                  <strong class="labels">案件名称:</strong>
                  <span>{{ v.title }}</span>
                </div>
                <div class="list">
                  <strong class="labels">执行人员:</strong>
                  <span>{{ v.userName }}</span>
                </div>
                <div class="list">
                  <strong class="labels">执行部门:</strong>
                  <span>{{ v.deptName }}</span>
                </div>
                <div class="list">
                  <strong class="labels">类型:</strong>
                  <span>{{ v.type|types }}</span>
                </div>
                <div class="list">
                  <strong class="labels">案件详情:</strong>
                  <span>{{ v.content }}</span>
                </div>
                <div class="satae">
                  <div class="cir" :style="{backgroundColor:circleColor[v.status]}" />
                  <span>{{ v.status|statuss }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-scrollbar>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="form.status != 9" type="success" @click="handelOver">办 结</el-button>
        <el-button v-if="form.status != 9" type="primary" @click="primary(2)">保 存</el-button>
        <el-button @click="close">取 消 </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import userSelect from '@/components/userselect/index'
import taskUserSelect from '@/components/taskUserSelect/index'
import { getToken } from '@/utils/auth'
import { getFiles } from '@/api/supervise/swit'
import rwfq from '@/api/case/union/rwfq'
import MFileUpload from '@/components/MFileUpload/index'

export default {
  name: 'FromList',
  components: {
    userSelect,
    taskUserSelect,
    MFileUpload
  },
  filters: {
    types(id) {
      let types = {	1: '巡查发现', 2: '监控抓拍', 3: '日常巡查', 4: '简易处罚', 5: '四位一体'}
      return types[id]
    },
    statuss(id) {
      let statusName = {1: '已下排', 2: '处理中', 9: '已办结'}
      return statusName[id]
    }
  },
  props: {
    title: String,
    detailId: Number,
    formDisabled: Boolean,
    typeData: Array
  },
  data() {
    return {
      headers: {Authorization: 'Bearer ' + getToken()},
      exData: {status: 2, tableName: 'case_union'},
      viceExData: {status: 9, tableName: 'case_union'},
      formLoadingText: '数据上传中',
      typeLoading: false,
      formLoading: false,
      userOpen: false,
      deptOpen: false,
      launchOpen: false,
      launchDeptOpen: false,
      unionData: [],
      circleColor: {
        1: '#25c548',
        2: '#25c548',
        9: '#bdc3bf'
      },
      form: {launchDeptId: '', launchId: '', userId: '', deptIds: ''},
      mainFileList: [],
      viceFileList: [],
      rules: {
        title: [
          { required: true, message: '请输入任务名称', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入任务内容', trigger: 'blur' }
        ],
        launchName: [
          { required: true, message: '请选择发起人名称', trigger: 'change' }
        ],
        launchDeptName: [
          { required: true, message: '请选择发起人部门', trigger: 'change' }
        ],
        userName: [
          { required: true, message: '请选择负责人名称', trigger: 'change' }
        ],
        deptNames: [
          { required: true, message: '请选择负责人部门', trigger: 'change' }
        ],
        startTime: [
          { required: true, message: '请选择开始时间', trigger: 'change' }
        ],
        endTime: [
          { required: true, message: '请选择结束时间', trigger: 'change' }
        ],
        typeName: [
          { required: true, message: '请选择执法类型', trigger: 'change' }
        ],
        doResult: [
          { required: true, message: '请输入检查结果', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    '$attrs.visible'(nVal) {
      if (nVal && this.detailId) {
        this.getData()
      }
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    goSubtask(data) {
      if (data.type == 5) {
        // window.location.
      } else {
        let url = {query: data}
        if (data.type == 1) window.location = '/case/enforceLaw/patrol/?data=' + JSON.stringify(data)
        // if (data.type == 1) url.path = '/case/enforceLaw/patrol'
        if (data.type == 2) url.path = '/case/enforceLaw/capture'
        if (data.type == 3) url.path = '/case/transport/dailyCheck'
        if (data.type == 4) url.path = '/case/enforceLaw/punishment'
        this.$router.push(url)
      }
    },
    getData() {
      this.formLoading = true
      this.formLoadingText = '加载中'
      Promise.all([
        rwfq.OneList(this.detailId),
        getFiles({businessId: this.detailId, tableName: 'case_union'}),
        rwfq.detailList({unionId: this.detailId})
      ]).then(res => {
        this.form = {...res[0].data}
        if (this.form.type) this.launchDept(this.form.type)
        // 文件部分
        res[1].rows.map(item => {
          const obj = { name: item.displayName, url: `/zqzfj${item.filePath}`, ...item }
          if (item.status == 2) {
            this.mainFileList.push(obj)
          } else if (item.status == 9) {
            this.viceFileList.push(obj)
          }
        })
        this.unionData = res[2].rows
        this.formLoading = false
      }).catch(err => { console.log(err), this.formLoading = false, this.msgError('加载失败') })
    },
    launchDept(e) { //	警情类型
      let data = this.typeData.filter(item => { return item.dictSort == e })
      this.form = {...this.form, typeName: data[0].dictLabel, type: data[0].dictSort}
    },
    userConfirm(e) {    //	执行部门名称
      this.form = {...this.form, userName: e.name, userId: e.id}
    },
    deptConfirm(e) {    // 上报人名称
      this.form = {...this.form, deptNames: e.deptNames, deptIds: e.deptIds, userNames: e.name, userIds: e.id }
    },
    launchConfirm(e) {    //  发起人名称
      this.form = {...this.form, launchName: e.name, launchId: e.id}
    },
    launchDeptConfirm(e) {    // 发起部门名称
      this.form = {...this.form, launchDeptName: e.name, launchDeptId: e.id}
    },
    inputs(e) {
      this.launchOpen = e
      this.launchDeptOpen = e
      this.userOpen = e
      this.deptOpen = e
    },
    onOpen() {},
    handelOver() {
      this.$confirm('提示', '是否确认办结？', { type: 'warning' }).then(() => {
        this.$refs['form'].validate(valid => {
          if (valid) {
            this.primary(9)
          } else {
            return false
          }
        })
      }).catch(() => {})
    },
    handleFileSuccess() {
      if (this.$refs.viceFile) {

        this.$refs.viceFile.submitFile()
      } else {
        this.msgSuccess('操作成功')
        this.formLoading = false
        this.$emit('update:visible', false)
        this.$emit('reLoad')
      }
    },
    handleViceFileSuccess() {
      this.msgSuccess('操作成功')
      this.formLoading = false
      this.$emit('update:visible', false)
      this.$emit('reLoad')
    },
    fileError() {
      this.formLoading = false
    },
    primary(status) {
      this.form.status = status
      let params = {...this.form, status}
      this.formLoading = true
      this.formLoadingText = '数据上传中'
      let api = params.unionId ? rwfq.Edit(params) : rwfq.Add(params)
      api.then(res => {
        const id = this.form.unionId || res.data.unionId
        this.exData.businessId = id
        this.viceExData.businessId = id
        if (res.data && res.data.unionId) this.form.unionId = res.data.unionId
        this.$refs.mainFile.submitFile()
      }).catch(() => this.formLoading = false)
    },
    reset() {
      this.form = {}
      this.unionData = {}
      this.resetForm('form')
    },
    onClose() {
      this.reset()
      this.mainFileList = []
      this.viceFileList = []
      this.formLoadingText = '数据上传中'
      this.$refs.form.clearValidate()
    },
    close() {
      this.$emit('update:visible', false)
      this.$refs.form.clearValidate()
    }
  }
}
</script>

<style lang="scss" scoped>
.suffix {
  padding: 10px 0 10px 30px;
  margin-bottom: 20px;
  margin-right: 5%;
  border: 1px solid #e4e7ec;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}
.suffix .list {
  width: 100%;
  overflow: hidden;
  margin-bottom: 20px;
}
.list .labels {
  margin-right: 4%;
  width: 20%;
  text-align: right;
  display: inline-block;
}
.satae {
  position: absolute;
  top: 10px;
  right: 10px;
}
.satae .cir {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #fff;
  margin-right: 10px;
}
</style>
