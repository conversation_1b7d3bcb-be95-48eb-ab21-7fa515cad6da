<template>
  <div>
    <CommonTitle text="趋势预测分析">
      <TabSwitch :tabList="list" :activeIndex="index" @tab-change="handleTabChange" />
    </CommonTitle>
    <div class="wrap-container" id="chartcslh"></div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import TabSwitch from '@/components/TabSwitch'
import { getTrendAnalysis } from '@/api/csgl/index.js'
export default {
  name: 'index',
  components: {
    CommonTitle,
    TabSwitch,
  },
  data() {
    return {
      index: 0,
      list: [
        { name: '本周', value: '2' },
        { name: '本月', value: '3' },
        { name: '本年', value: '4' },
      ],
      chartsData: [],
    }
  },
  computed: {},
  mounted() {
    this.getData()
  },
  methods: {
    handleTabChange(i) {
      this.index = i
      this.getData()
    },
    getData() {
      getTrendAnalysis({ type: this.list[this.index].value }).then((res) => {
        this.chartsData = res.data.map((item) => {
          return {
            name: item.eventTime,
            reportCount: item.reportCount,
            registerCount: item.reportCount,
            completeCount: item.reportCount,
          }
        })
        this.initChart()
      })
    },
    initChart() {
      let myChart = this.$echarts.init(document.getElementById('chartcslh'))
      let option = {
        tooltip: {
          trigger: 'axis',
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        legend: {
          orient: 'horizontal',
          icon: 'rect',
          padding: [30, 10, 10, 10],
          itemGap: 45,
          textStyle: {
            color: '#D6E7F9',
            fontSize: 28,
          },
        },
        grid: {
          left: '8%',
          right: '6%',
          top: '22%',
          bottom: '1%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            data: this.chartsData.map((item) => item.name),
            axisLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)', // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: (this.index == 1 ? 4 : 0),
              rotate: 30,
              textStyle: {
                color: '#D6E7F9',
                fontSize: 28,
              },
            },
          },
        ],
        yAxis: [
          {
            name: '单位：件',
            type: 'value',
            nameTextStyle: {
              fontSize: 24,
              color: '#D6E7F9',
              padding: 20,
            },
            splitLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)',
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 28,
                color: '#D6E7F9',
              },
            },
          },
        ],
        series: [
          {
            name: '上报数',
            type: 'line',
            data: this.chartsData.map((item) => item.reportCount),
            lineStyle: {
              color: '#FF6A00',
            },
            itemStyle: {
              color: '#FF6A00', // 设置图例颜色与线条颜色一致
            },
            symbol: 'none',
          },
          {
            name: '立案数',
            type: 'line',
            data: this.chartsData.map((item) => item.registerCount),
            lineStyle: {
              color: '#22E097',
            },
            itemStyle: {
              color: '#22E097', // 设置图例颜色与线条颜色一致
            },
            symbol: 'none',
          },
          {
            name: '结案数',
            data: this.chartsData.map((item) => item.completeCount),
            type: 'line',
            lineStyle: {
              color: '#3ED7FD',
            },
            itemStyle: {
              color: '#3ED7FD', // 设置图例颜色与线条颜色一致
            },
            symbol: 'none',
          },
        ],
      }
      myChart.setOption(option)
      myChart.getZr().on('mousemove', (param) => {
        myChart.getZr().setCursorStyle('default')
      })
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
.wrap-container {
  width: 100%;
  height: 500px;
}
</style>