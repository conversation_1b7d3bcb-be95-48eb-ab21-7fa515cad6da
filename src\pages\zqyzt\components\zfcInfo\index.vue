<template>

    <div v-if="visible" class="person-info-container">
      <!-- 左侧信息区域 -->
      <div class="left-panel">
        <!-- 人员头像 -->
        <div class="avatar-section">
          <el-image class="avatar-img" :src="fImgSrc" fit="cover"/>
        </div>
        
        <!-- 人员信息 -->
        <div class="info-section">
          <div class="info-title">执法车信息</div>
          <div class="info-item">
            <i class="info-icon icon-cphm"></i>
            <span class="info-label">车牌号码：</span>
            <span class="info-value">{{ dataDetail.lawCarNo }}</span>
          </div>
          <div class="info-item">
            <i class="info-icon icon-cllx"></i>
            <span class="info-label">车辆类型：</span>
            <span class="info-value">{{ dataDetail.lawCarModel }}</span>
          </div>
          <div class="info-item">
            <i class="info-icon icon-xm"></i>
            <span class="info-label">负责人：</span>
            <span class="info-value">{{ dataDetail.leader }}</span>
          </div>
          <div class="info-item">
            <i class="info-icon icon-dh"></i>
            <span class="info-label">电话：</span>
            <span class="info-value">{{ dataDetail.phone }}</span>
          </div>
          <div class="info-item">
            <i class="info-icon icon-bm"></i>
            <span class="info-label">部门：</span>
            <span class="info-value">{{ dataDetail.deptName }}</span>
          </div>
        </div>
      </div>

      <!-- 右侧地图区域 -->
      <div class="right-panel">
          <!-- 顶部工具栏 -->
          <div class="top-toolbar">
              <div class="date-picker">

              <el-date-picker v-model="gpsDate" type="date" placeholder="轨迹查询" value-format="yyyy-MM-dd 23:59:59" /><!-- @change="handleChange" -->
              </div>
              <div class="close-btn" @click="handleClose">×</div>
          </div>
          
          <!-- 地图容器 -->
          <div class="map-container" id="mapContainer"></div>
          <div v-if="showCarTrack" class="ctrl-btn">
              <el-button 
                type="primary" 
                plain 
                round 
                style="font-size: 28px; padding: 15px 30px; margin-left: 20px"
                @click="handleCarstart">开始</el-button>
              <el-button 
                type="primary" 
                plain 
                round 
                style="font-size: 28px; padding: 15px 30px; margin-left: 20px" 
                @click="handleCarpause">暂停</el-button>
              <el-button 
                type="primary" 
                plain 
                round 
                style="font-size: 28px; padding: 15px 30px; margin-left: 20px"
                @click="handleCarstop">结束</el-button>
            </div>
          <div class="bottom-toolbar"></div>
      </div>
    </div>
</template>

<script>
import MapService from '@/components/Map/index.js';
import Map from "@arcgis/core/Map";
import SceneView from "@arcgis/core/views/SceneView";
import { getIRSLayer } from "@/components/Map/MapUtils/basemap.js";
import { getLawInfo, getLawGps } from '@/api/zqyzt'
import lawCarImg from '@/assets/zqyzt/windowInfo/lawCar.jpg'
export default {
  name: 'zfcInfo',
  components: {
  },
  props: {
      visible: {
        type: Boolean,
        default: false
      },
      detailId: {
        type: Number
      },
      videoVisible: Boolean
    },
  data() {
    return {
      defaultAvatar: require('@/assets/zqyzt/windowInfo/fulb.png'),
      dateRange: [],
      map: null,
      view: null,
      dataDetail: {
        lawCarNo: '',
        lawCarModel: '',
        leader: '',
        phone: '',
        deptName: ''
      },
      fImgSrc: lawCarImg,
      fSrcList: [],
      gpsDate: '',
      leftLoading: false,
      rightLoading: false,
      terminalNo: null,
      showCarTrack: false
    }
  },
  mounted() {
  },
  watch: {
    visible(val) {
      if (val) {
        this.fetchData().then(() => {
            this.gpsDate = new Date()
          })
        this.$nextTick(() => {
          this.initMap()
        })
      }
    }
  },
  methods: {
    fetchData() {
      return new Promise(resolve => {
        this.leftLoading = false
        getLawInfo(this.detailId).then(res => {
          this.dataDetail = res.data
          if (res.data.terminalNo) {
            this.terminalNo = res.data.terminalNo
          } else {
            this.terminalNo = null
          }
          this.leftLoading = false
          resolve()
        }).catch(() => {
          this.leftLoading = false
        })
      })
    },
    initMap() {
      // 创建新的地图实例
      this.map = new Map({
          basemap: getIRSLayer(),
          ground: {
          opacity: 1,
          surfaceColor: "#08294a",
          },
      });

      // 创建新的视图实例
      this.view = new SceneView({
          container: 'mapContainer',
          map: this.map,
          camera: {
              position: {
                spatialReference: {
                wkid: 4490,
                },
                x: 119.63010238256342,
                y: 29.110005649408036,
                z: 2226.879426258436,
            },
            heading: 0.26874578434742386,
            tilt: 0.49999999999694683,
          },
          constraints: {
          altitude: {
              min: 10,
          },
          },
          qualityProfile: "low",
      });
      // 禁用默认的UI组件
      this.view.ui.remove('zoom');
      this.view.ui.remove('attribution');
      this.view.popup.autoOpenEnabled = false;
    },
    handleClose() {
      this.$emit('close')
      if (this.view) {
        this.view.destroy()
        this.view = null
      }
      if (this.map) {
        this.map = null
      }
    },
    handleChange() {
      let gpsDate = ''
      if (typeof this.gpsDate == 'object') {
        gpsDate = this.parseTime(this.gpsDate)
      } else {
        gpsDate = this.gpsDate
      }
      // 缺少查询条件，重置地图
      if (!this.terminalNo) {
        this.$message.error('未获取到终端号，请重试')
        return
      }
      // 没有时间，清楚地图元素
      console.log(gpsDate)
      if (!gpsDate) {
        this.carTrackClear()
        return
      }
      this.rightLoading = true
      getLawGps({ gpsDate: gpsDate, terminalNo: this.terminalNo }).then(res => {
        this.creatCarTrack(res.rows)
        this.rightLoading = false
      }).catch(() => {
        this.rightLoading = false
      })
    },
  }
}
</script>

<style scoped>
.person-info-container {
  display: flex;
  width: 2150px;
  height: 1350px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(16, 28, 63, 0.98);
  position: absolute;
  top: 200px;
  left: -1950px;
  border: 2px solid rgba(18, 142, 232, 0.5) !important;
}

.left-panel {
  width: 400px;
  padding: 30px;
  background: rgba(16, 28, 63, 0.98) ;
  display: flex;
  flex-direction: column;
}

.avatar-section {
  text-align: center;
  margin-bottom: 60px;
  margin-top: 30px;
}

.avatar-img {
  width: 300px;
  height: 210px;
  object-fit: cover;
}

.info-section {
  flex: 1;
}

.info-title {
  font-size: 32px;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 30px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
  font-size: 28px;
  color: #ffffff;
}

.info-icon {
  width: 28px;
  height: 28px;
  margin-right: 15px;
  background-size: contain;
}

.info-label {
  width: 140px;
}

.info-value {
  color: #ffffff;
}

.button-section {
  margin-top: 30px;
}

.comm-btn {
  width: 100%;
  height: 60px;
  margin-bottom: 20px;
  border: none;
  border-radius: 8px;
  color: #fff;
  font-size: 28px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-btn {
  background: #409EFF;
}

.voice-btn {
  background: #67C23A;
}

.btn-icon {
  width: 28px;
  height: 28px;
  margin-right: 10px;
  background-size: contain;
}

.right-panel {
flex: 1;
display: flex;
flex-direction: column;
position: relative;
padding: 0 40px 40px 0; /* 增加右边和下边的内边距 */
background-color: rgba(16, 28, 63, 0.98);
}

.top-toolbar {
height: 110px;
padding: 0 40px;
display: flex;
justify-content: space-between;
align-items: center;
background: rgba(16, 28, 63, 0.98);
}

.date-picker {
flex: 1;
max-width: 800px;
}

.date-picker :deep(.el-input__inner) {
  width: 500px !important; 
  height: 50px;
  line-height: 50px;
  font-size: 32px;
  color: #00d4ff !important;  /* 使用亮蓝色 */
  font-weight: bold;
}

.date-picker :deep(.el-range-separator) {
  font-size: 32px;
  line-height: 50px;
  color: #00d4ff !important;
  font-weight: bold;
}

.date-picker :deep(.el-range-input) {
  font-size: 32px;
  color: #00d4ff !important;
  font-weight: bold;
}

/* 调整日历面板中的文字 */
.date-picker :deep(.el-date-table) {
  font-size: 28px;
}

.date-picker :deep(.el-date-table td span) {
  color: #ffffff !important;  /* 日期颜色 */
  font-weight: bold;
}

.date-picker :deep(.el-date-table th) {
  color: #00d4ff !important;  /* 星期颜色 */
  font-size: 24px;
  font-weight: bold;
}

.date-picker :deep(.el-date-range-picker__header) {
  color: #00d4ff !important;  /* 月份年份颜色 */
  font-size: 28px;
  font-weight: bold;
}

.date-picker :deep(.el-date-range-picker__time-header) {
  color: #00d4ff !important;
  font-size: 28px;
  font-weight: bold;
}

/* 选中日期的样式 */
.date-picker :deep(.el-date-table td.in-range div) {
  background-color: rgba(0, 212, 255, 0.2);
}

.date-picker :deep(.el-date-table td.start-date div),
.date-picker :deep(.el-date-table td.end-date div) {
  background-color: #00d4ff !important;
  color: #000000 !important;
}

/* 占位符文字颜色 */
.date-picker :deep(.el-input__inner::placeholder) {
  color: rgba(0, 212, 255, 0.7) !important;
  font-size: 32px;
}

/* 日期选择器图标颜色 */
.date-picker :deep(.el-input__icon) {
  color: #00d4ff !important;
  font-size: 32px;
  line-height: 50px;
}

.close-btn {
width: 60px;
height: 60px;
line-height: 50px;
text-align: center;
background: rgba(0, 0, 0, 0.2);
color: #fff;
font-size: 50px;
border-radius: 25px;
cursor: pointer;
margin-right: -45px;
}

.map-container {
flex: 1;
width: 100%;
border-radius: 8px; /* 可选：增加圆角 */
overflow: hidden; /* 确保圆角生效 */
box-shadow: 0 0 10px rgba(16, 28, 63, 0.98);
border: 2px solid rgba(18, 142, 232, 0.5) !important;
}  

.ctrl-btn {
  position: absolute;
  top: 140px;
  right: 100px;
  z-index: 500;
  padding: 10px;
  border-radius: 5px;
}
/* 图标样式 */
.icon-cphm { background-image: url('@/assets/zqyzt/windowInfo/cphm.png'); }
.icon-cllx { background-image: url('@/assets/zqyzt/windowInfo/cllx.png'); }
.icon-xm { background-image: url('@/assets/zqyzt/windowInfo/fulb.png'); }
.phone-icon { background-image: url('@/assets/zqyzt/windowInfo/fulb.png'); }
.icon-dh { background-image: url('@/assets/zqyzt/windowInfo/dh.png'); }
.icon-bm { background-image: url('@/assets/zqyzt/windowInfo/bm.png'); }
</style>