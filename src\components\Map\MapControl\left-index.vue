<template>
  <div class="btn-tool">
    <div class="tcgl-container" @click="handleTcgl()">
      <div v-show="!tcActive" class="btn2">
        <p>图层</p>
        <img src="@/components/Map/MapAssets/Common/video-search-btn-lab.png" alt="" />
      </div>
      <div v-show="tcActive">
        <div class="btn2-active btn2">
          <p>图层</p>
          <img src="@/components/Map/MapAssets/Common/video-search-btn-lab-active.png" alt="" />
        </div>
      </div>
    </div>
    <tcgl v-show="tcActive"></tcgl>
    <yybzDetail v-if="showYybzDetail"></yybzDetail>
    <!-- <div class="tcgl-container" @click="handleVideo()">
      <div v-show="!heatmapActive" class="btn2">
        <p>视频</p>
        <img src="@/components/Map/MapAssets/Common/video-search-btn-lab.png" alt="" />
      </div>
      <div v-show="heatmapActive">
        <div class="btn2-active btn2">
          <p>视频</p>
          <img src="@/components/Map/MapAssets/Common/video-search-btn-lab-active.png" alt="" />
        </div>
      </div>
    </div>

    <div class="tcgl-container" @click="handleWangge()">
      <div v-show="!wgActive" class="btn1">
        <p>
          全科
          <br />
          网格
        </p>
        <img src="@/components/Map/MapAssets/Common/video-search-btn-lab.png" alt="" />
      </div>
      <div v-show="wgActive">
        <div class="btn1-active btn1">
          <p>
            全科
            <br />
            网格
          </p>
          <img src="@/components/Map/MapAssets/Common/video-search-btn-lab-active.png" alt="" />
        </div>
      </div>
    </div> -->
    <!-- <wggl v-if="wgActive"></wggl> -->
    <!-- <gridEvents v-if="wgActive"></gridEvents> -->
  </div>
</template>

<script>
import tcgl from '@/components/Map/MapControl/tcgl.vue'
import wggl from '@/components/Map/MapControl/wggl.vue'
import gridEvents from '@/components/Map/wggl/gridEvents.vue'
import yybzDetail from '@/components/Map/tcgl/yybzDetail.vue'
export default {
  name: 'leftIndex',
  components: { tcgl, wggl, yybzDetail, gridEvents },
  data() {
    return {
      tcActive: false,
      wgActive: false,
      heatmapActive: false, // 热力图按钮激活状态 // 人员列表按钮激活状态
      showYybzDetail: false,
    }
  },
  mounted() {
    this.$bus.$on('ShowYybzDetail', (res) => {
      this.showYybzDetail = true
    })
    this.$bus.$on('noShowYybzDetail', (res) => {
      this.showYybzDetail = false
    })
  },
  methods: {
    handleTcgl() {
      this.tcActive = !this.tcActive
    },
    handleVideo() {
      this.heatmapActive = !this.heatmapActive
    },
    handleWangge() {
      this.wgActive = !this.wgActive
      if (this.wgActive) {
        this.$bus.$emit('showPage', false)
        if (document.getElementsByClassName('btn-tool')[0]) document.getElementsByClassName('btn-tool')[0].style.left = '10px'
        if (document.getElementsByClassName('zhdd_mapIcon')[0]) document.getElementsByClassName('zhdd_mapIcon')[0].style.bottom = '20px'
        if (document.getElementsByClassName('rightBtn')[0]) document.getElementsByClassName('rightBtn')[0].style.right = '10px'
      } else {
        this.$bus.$emit('showPage', true)
        if (document.getElementsByClassName('btn-tool')[0]) document.getElementsByClassName('btn-tool')[0].style.left = '1050px'
        if (document.getElementsByClassName('zhdd_mapIcon')[0]) document.getElementsByClassName('zhdd_mapIcon')[0].style.bottom = '630px'
        if (document.getElementsByClassName('rightBtn')[0]) document.getElementsByClassName('rightBtn')[0].style.right = '1000px'
      }
    },
    // close() {
    //   this.showYybzDetail = false
    //   this.$bus.$emit("showPage", true)
    //   document.getElementsByClassName("btn-tool")[0].style.left = "1050px"
    // }
  },
}
</script>

<style scoped lang="scss">
.btn-tool {
  position: absolute;
  width: 65px;
  background-size: cover;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  transform: scale(1);
  transform-origin: top left;
  top: 240px;
  left: 1050px;
  z-index: 99;
  transition: left 0.3s ease;
  font-size: 28px;
  gap: 40px; // 按钮之间的间距
}

.btn2-active {
  position: relative;
  color: #ffc460 !important;
  background-image: url('@/components/Map/MapAssets/Common/video-search-btn-active.png') !important;
  background-size: 100% 100%;
}
.btn1-active {
  position: relative;
  color: #ffc460 !important;
  background-image: url('@/components/Map/MapAssets/Common/video-search-btn-active.png') !important;
  background-size: 100% 100%;
}
.btn2 {
  width: 54px;
  height: 190px;
  background-image: url('@/components/Map/MapAssets/Common/video-search-btn.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  font-size: 32px;
  color: white;
  line-height: 42px;
  text-align: center;
  cursor: pointer;
}

.btn1 {
  width: 54px;
  height: 270px;
  background-image: url('@/components/Map/MapAssets/Common/video-search-btn.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  font-size: 32px;
  color: white;
  line-height: 42px;
  text-align: center;
  cursor: pointer;
}

.btn2 p {
  padding-top: 30px;
}

.btn2 img {
  width: 28px;
  height: 29px;
  position: relative;
}

.btn1 p {
  padding-top: 30px;
}

.btn1 img {
  width: 28px;
  height: 29px;
  position: relative;
}
</style>
