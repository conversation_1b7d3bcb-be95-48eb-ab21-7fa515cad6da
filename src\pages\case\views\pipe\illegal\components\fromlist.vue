<template>
  <div>
    <el-dialog class="m-dialog" :close-on-click-modal="false" append-to-body v-bind="$attrs" :title="title" v-on="$listeners" @open="onOpen" @close="onClose">
      <el-scrollbar v-loading="formLoading" style="height: 100%;" :element-loading-text="formLoadingText">
        <el-row :gutter="15" style="margin-right: 10px;">
          <el-form ref="elForm" :model="formData" :rules="rules" :disabled="formDisabled" size="medium" label-width="120px">
            <h3 class="title">
              <span>当事人信息</span>
              <attentionBtn :case-id="formData.transportId || 0" case-type="transport" :case-content="formData.content" />
            </h3>
            <el-col :span="8">
              <el-form-item label="当事人" prop="party">
                <el-input v-model="formData.party" placeholder="请输入当事人" clearable />
              </el-form-item>
            </el-col>
            <!-- <el-col :span="8">
              <el-form-item label="身份证" label-width="80px">
                <el-input v-model="formData.identityCard" placeholder="请输入身份证" clearable />
              </el-form-item>
            </el-col> -->
            <el-col :span="8">
              <el-form-item label="联系电话" label-width="80px">
                <el-input v-model="formData.phone" placeholder="请输入联系电话" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="车辆车牌" prop="carNo">
                <el-input v-model="formData.carNo" placeholder="请输入车辆车牌" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="车辆品牌" prop="models">
                <el-input v-model="formData.models" placeholder="请输入车辆品牌" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="营运公司" prop="operationCompany">
                <el-input v-model="formData.operationCompany" placeholder="请输入营运公司" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="从业资格证号" prop="certificateNo">
                <el-input v-model="formData.certificateNo" placeholder="请输入从业资格证号" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="道路运输证号" prop="transportCertificate">
                <el-input v-model="formData.transportCertificate" placeholder="请输入道路运输证号" clearable />
              </el-form-item>
            </el-col>
            <!-- 违规信息 -->
            <h3 class="title">违规信息</h3>
            <!-- <el-col :span="24">
              <el-form-item label="标题" prop="title">
                <el-input v-model="formData.title" placeholder="请输入标题" clearable />
              </el-form-item>
            </el-col> -->
            <el-col :span="24">
              <el-form-item label="违规内容" prop="content">
                <el-input v-model="formData.content" type="textarea" placeholder="请输入违规内容" :maxlength="150" show-word-limit :autosize="{minRows: 4, maxRows: 4}" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="上报人员" prop="userName">
                <el-input v-model="formData.userName" placeholder="请输入违规人员" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="上报时间" prop="inspectionTime">
                <el-date-picker v-model="formData.inspectionTime" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="请选择违规时间" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="车辆类型" prop="carType">
                <!-- <el-select v-model="formData.carType" placeholder="请输入车辆类型" clearable :style="{ width: '100%' }" @change="handleCarChange" @clear="handleCarClear">
                  <el-option v-for="(item, index) in carTypeOptions" :key="index" :label="item.dictLabel" :value="item.dictValue" />
                </el-select> -->
                <el-input v-if="formData.status > 2" v-model="formData.carTypeName" readonly placeholder="请选择车辆类型" clearable />
                <el-dropdown v-else trigger="click" style="display: block;" @command="handleCarChange">
                  <div class="el-dropdown-link" style="min-width: 200px;">
                    <el-input v-model="formData.carTypeName" readonly placeholder="请选择车辆类型" clearable />
                  </div>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item v-for="(item, index) in carTypeOptions" :key="index" :command="item.dictValue">{{ item.dictLabel }}</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item v-loading="typeLoading" label="违规类型" prop="inspectionTypeName">
                <!-- <el-select v-model="formData.inspectionType" placeholder="请输入违规类型" clearable :loading="typeLoading" no-data-text="无数据，请先选择车辆类型" :style="{ width: '100%' }" @change="handleIsTypeChange" @visible-change="handleVisChange">
                  <el-option v-for="(item, index) in inspectionTypeOptions" :key="index" :label="item.dictLabel" :value="item.dictValue" />
                </el-select> -->
                <el-input v-show="!formData.carType" v-model="formData.inspectionTypeName" readonly placeholder="请选择违规类型" />
                <el-checkbox-group v-model="inspectionTypeList" @change="handleIsTypeChange">
                  <el-checkbox v-for="item in inspectionTypeOptions" :key="item.dictValue" :label="item.dictValue">{{ item.dictLabel }}</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <!-- <el-col v-if="isShowFTaxiType" :span="12">
              <el-form-item label="网络预约出租汽车驾驶员证" prop="onlineCarDriver">
                <el-radio-group v-model="formData.onlineCarDriver" size="medium">
                  <el-radio v-for="(item, index) in onlineCarDriverOptions" :key="index" :label="item.value">
                    {{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col v-if="isShowSTaxiType" :span="12">
              <el-form-item label="网络预约出租汽车运输证类型" prop="onlineCarTarnsport">
                <el-radio-group v-model="formData.onlineCarTarnsport" size="medium">
                  <el-radio v-for="(item, index) in onlineCarTarnsportOptions" :key="index" :label="item.value">
                    {{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col> -->
            <el-col :span="24">
              <el-form-item label="地址" prop="address">
                <div>
                  <el-input v-model="formData.address" placeholder="请选择违规地址" style="width: 95%;" />
                  <svg-icon icon-class="map" class="svg-icon" @click="openMap = true" />
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="经度" prop="longitude">
                <el-input v-model="formData.longitude" placeholder="请输入经度" readonly clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="纬度" prop="latitude">
                <el-input v-model="formData.latitude" placeholder="请输入纬度" readonly clearable />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="上传图片" prop="files">
                <el-upload
                  ref="upload"
                  multiple
                  :limit="4"
                  list-type="picture-card"
                  class="upload-demo"
                  accept=".jpg,.jpeg,.png,.JPG,.JPEG,.PNG"
                  action="/zqzfj/system/file/upload"
                  :auto-upload="false"
                  :headers="headers"
                  :file-list="formFiles"
                  :on-preview="handlePictureCardPreview"
                  :before-remove="handleRemove"
                  :on-success="handleSuccess"
                  :on-error="handleError"
                  :data="fileEXData"
                  name="files"
                  :on-exceed="exceed"
                >
                  <i class="el-icon-plus" />
                </el-upload>
              </el-form-item>
            </el-col>
            <!-- 额外文件 -->
            <el-col :span="24">
              <el-row v-if="formData.carType">
                <el-col v-for="(item, idx) in exFile" :key="idx" :span="24">
                  <el-form-item :label="item.label" prop="files">
                    <el-upload
                      :ref="`upload${item.data.status}`"
                      name="files"
                      multiple
                      list-type="picture-card"
                      class="upload-demo"
                      accept=".jpg,.jpeg,.png,.JPG,.JPEG,.PNG"
                      action="/zqzfj/system/file/upload"
                      :auto-upload="false"
                      :headers="headers"
                      :data="item.data"
                      :file-list="item.fileList"
                      :on-preview="handlePictureCardPreview"
                      :before-remove="handleRemove"
                      :on-success="handleSuccess"
                      :on-error="handleError"
                    >
                      <i class="el-icon-plus" />
                    </el-upload>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
          </el-form>
        </el-row>
      </el-scrollbar>
      <div v-if="isBoard" slot="footer">
        <el-button @click="close">取消</el-button>
      </div>
      <div v-else slot="footer">
        <AddClassic :before-processing="formData.content" after-processing="已处理" />
        <el-button v-if="!formDisabled" @click="close">取消</el-button>
        <el-button v-if="!formDisabled" type="primary" @click="handelConfirm(2)">保存</el-button>
        <el-button v-if="!formDisabled" type="primary" @click="handelOver">办结</el-button>
      </div>
    </el-dialog>
    <!-- 图片预览 -->
    <el-dialog :close-on-click-modal="false" :visible.sync="dialogVisible" append-to-body>
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
    <!-- 地址选择 -->
    <tdtMap ref="bindmap" :visible.sync="openMap" append-to-body :map-search="true" :dw="formData" @onlnglat="onlnglat" />
  </div>
</template>
<script>
import { getTransport, addTransport, editTransport } from '@/api/case/pipe/dayCheck'
import tdtMap from '@/components/tdtMap/tdtMap.vue'
import { getToken } from '@/utils/auth'
import { getFiles, removeFiles} from '@/api/supervise/swit'
import attentionBtn from '@/components/attentioneBtn/index.vue'
import AddClassic from '@/components/AddClassic/index.vue'

export default {
  components: {tdtMap, attentionBtn, AddClassic},
  inheritAttrs: false,
  props: {
    title: String,
    detailId: Number,
    formDisabled: Boolean,
    carTypeOptions: Array,
    isBoard: Boolean
  },
  data() {
    return {
      openMap: false,
      formLoading: false,
      formLoadingText: '数据上传中',
      dialogVisible: false,
      dialogImageUrl: '',
      formData: {
        title: undefined,
        content: undefined,
        party: undefined,
        identityCard: '',
        phone: undefined,
        carNo: undefined,
        models: undefined,
        certificateNo: undefined,
        transportCertificate: undefined,
        userName: undefined,
        inspectionTime: undefined,
        carType: undefined,
        inspectionType: undefined,
        onlineCarDriver: undefined,
        onlineCarTarnsport: undefined,
        longitude: undefined,
        latitude: undefined,
        address: undefined
      },
      rules: {
        title: [{
          required: true,
          message: '请输入标题',
          trigger: 'blur'
        }],
        content: [{
          required: true,
          message: '请输入违规内容',
          trigger: 'blur'
        }],
        party: [{
          required: true,
          message: '请输入当事人',
          trigger: 'blur'
        }],
        carNo: [{
          required: true,
          message: '请输入车辆车牌',
          trigger: 'blur'
        }],
        models: [],
        certificateNo: [{
          required: true,
          message: '请输入从业资格证号',
          trigger: 'blur'
        }],
        transportCertificate: [{
          required: true,
          message: '请输入道路运输证号',
          trigger: 'blur'
        }],
        userName: [{
          required: true,
          message: '请输入违规人员',
          trigger: 'blur'
        }],
        inspectionTime: [{
          required: true,
          message: '请输入违规时间',
          trigger: 'change'
        }],
        carType: [{
          required: true,
          message: '请输入车辆类型',
          trigger: 'change'
        }],
        inspectionTypeName: [{
          required: true,
          message: '请输入违规类型',
          trigger: 'change'
        }],
        longitude: [{
          required: true,
          message: '请输入经度',
          trigger: 'change'
        }],
        latitude: [{
          required: true,
          message: '请输入纬度',
          trigger: 'change'
        }],
        address: [{
          required: true,
          message: '请输入地址',
          trigger: 'change'
        }]
      },
      carTypeDict: '',
      inspectionTypeOptions: [],
      typeLoading: false,
      isShowFTaxiType: false,
      isShowSTaxiType: false,
      onlineCarDriverOptions: [{
        'label': '有',
        'value': '1'
      }, {
        'label': '无',
        'value': '0'
      }],
      onlineCarTarnsportOptions: [{
        'label': '有',
        'value': '1'
      }, {
        'label': '无',
        'value': '0'
      }],
      formFiles: [],
      fileEXData: {
        tableName: 'case_transport',
        status: 1
      },
      exFile: [],
      businessId: null,
      upStatus: 1,
      inspectionTypeList: []
    }
  },
  computed: {
    headers() {
      return {Authorization: 'Bearer ' + getToken()}
    }
  },
  watch: {
    '$attrs.visible'(nVal) {
      if (nVal && this.detailId) {
        this.fetchData()
      }
    }
  },
  mounted() {},
  methods: {
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleRemove(file) {
      return new Promise((resolve, reject) => {
        this.$confirm('是否确认删除？ 删除后将无法恢复', '提示', { type: 'warning' }).then(() => {
          if (file.fileId) {
            removeFiles(file.fileId).then(resolve).catch(reject)
          } else {
            resolve()
          }
        }).catch(() => {
          reject()
        })
      })
    },
    handleSuccess() {
      let isPass = false
      const isFinish = this.$refs[`upload${this.upStatus}`][0].uploadFiles.some(item => (typeof item.percentage == 'number' && item.percentage !== 100 && item.status == 'success'))
      if (!isFinish) isPass = this.allFileUpload(this.businessId)
      if (isPass) {
        this.formLoading = false
        this.$message.success('操作成功')
        this.$emit('reLoad')
        this.close()
      }
    },
    handleError() {
      this.formLoading = false
      this.$message.error('文件上传失败，请重新上传')
    },
    exceed(files, filesList) {
      if (filesList.length >= 4) this.msgError('已超出最大上传数量')
    },
    onlnglat(lenlat) {
      let {lng: longitude, lat: latitude, address} = lenlat
      this.formData = {...this.formData, longitude, latitude, address}
      this.openMap = false
    },
    fetchData() {
      this.formLoading = true
      Promise.all([
        getTransport(this.detailId),
        getFiles({businessId: this.detailId, tableName: 'case_transport'})
      ])
        .then(resAry => {
          if (!this.isBoard) {
            const carTypeDict = this.carTypeOptions.find(item => item.dictValue == resAry[0].data.carType)
            if (carTypeDict) {
              this.typeLoading = true
              this.getDicts(carTypeDict.remark).then(dictRes => {
                this.inspectionTypeOptions = dictRes.data
                this.typeLoading = false
              }).catch(() => {
                this.typeLoading = false
              })
            }
          }

          this.formData = resAry[0].data
          this.inspectionTypeList = this.formData.inspectionType ? this.formData.inspectionType.split(',') : []
          // 文件类型
          this.setFileType(this.formData.carType)
          // 文件部分
          resAry[1].rows.map(item => {
            const obj = { name: item.displayName, url: `/zqzfj${item.filePath}`, ...item }
            if (item.status == 1) {
              this.formFiles.push(obj)
            } else {
              this.exFile.forEach(file => {
                if (file.data.status == item.status) {
                  file.fileList.push(obj)
                }
              })
            }
          })
          this.formLoading = false
        }).catch(() => {
          this.formLoading = false
        })
    },
    setFileType(type) {
      if (type == 4) {
        /* 网约车，修改上传文件类型 */
        this.exFile = [
          { key: 2, label: '现场笔录', data: { tableName: 'case_transport', status: 2 }, fileList: [] },
          { key: 3, label: '乘客询问笔录', data: { tableName: 'case_transport', status: 3 }, fileList: [] },
          { key: 4, label: '驾驶员询问笔录', data: { tableName: 'case_transport', status: 4 }, fileList: [] },
          { key: 5, label: '调查通知书', data: { tableName: 'case_transport', status: 5 }, fileList: [] },
          { key: 8, label: '查封通知书', data: { tableName: 'case_transport', status: 8 }, fileList: [] },
          { key: 7, label: '图片证据', data: { tableName: 'case_transport', status: 7 }, fileList: [] }
        ]
      } else if (type == 5) {
        /* 黑车，修改上传文件类型 */
        this.exFile = [
          { key: 2, label: '现场笔录', data: { tableName: 'case_transport', status: 2 }, fileList: [] },
          { key: 3, label: '乘客询问笔录', data: { tableName: 'case_transport', status: 3 }, fileList: [] },
          { key: 4, label: '驾驶员询问笔录', data: { tableName: 'case_transport', status: 4 }, fileList: [] },
          { key: 5, label: '调查通知书', data: { tableName: 'case_transport', status: 5 }, fileList: [] },
          { key: 9, label: '行政强制措施现场笔录', data: { tableName: 'case_transport', status: 9 }, fileList: [] },
          { key: 10, label: '扣押决定书', data: { tableName: 'case_transport', status: 10 }, fileList: [] },
          { key: 7, label: '图片证据', data: { tableName: 'case_transport', status: 7 }, fileList: [] }
        ]
      } else {
        /* 其余为默认 */
        this.exFile = [
          { key: 2, label: '现场笔录', data: { tableName: 'case_transport', status: 2 }, fileList: [] },
          { key: 3, label: '乘客询问笔录', data: { tableName: 'case_transport', status: 3 }, fileList: [] },
          { key: 4, label: '驾驶员询问笔录', data: { tableName: 'case_transport', status: 4 }, fileList: [] },
          { key: 5, label: '调查通知书', data: { tableName: 'case_transport', status: 5 }, fileList: [] },
          { key: 6, label: '先行登记保存证据通知书', data: { tableName: 'case_transport', status: 6 }, fileList: [] },
          { key: 7, label: '图片证据', data: { tableName: 'case_transport', status: 7 }, fileList: [] }
        ]
      }
    },
    insAllFile() {
      return new Promise((resolve, reject) => {
        let isHasFile = false
        for (let i = 0; i < this.exFile.length; i++) {
          let lists = this.$refs[`upload${this.exFile[i].data.status}`] ? this.$refs[`upload${this.exFile[i].data.status}`][0].uploadFiles : []
          if (lists && lists.length) {
            isHasFile = true
            break
          }
        }
        if (isHasFile) {
          this.$confirm('检测到已上传相关文件，切换车辆类型会清空已上传文件，是否切换？', '提示', { type: 'warning' }).then(() => {
            this.exFile.forEach(item => {
              let fileDom = this.$refs[`upload${item.data.status}`][0]
              if (fileDom) fileDom.clearFiles()
            })
            resolve()
          }).catch(() => {
            reject()
          })
        } else {
          resolve()
        }
      })
    },
    handleCarChange(val) {
      this.insAllFile().then(() => {
        const selctObj = this.carTypeOptions.find(item => item.dictValue == val)
        if (selctObj && this.formData.carType != val) {
          this.carTypeDict = selctObj.remark
          this.formData = { ...this.formData, carType: selctObj.dictValue, carTypeName: selctObj.dictLabel, inspectionType: '', inspectionTypeName: '' }
          this.inspectionTypeList = []
          this.handleVisChange()
        }
        // if (val != 4) {
        //   this.isShowFTaxiType = false
        //   this.isShowSTaxiType = false
        // }
        this.setFileType(val)
      }).catch(e => {
        console.log(e)
      })
    },
    /* handleCarClear() {
      this.inspectionTypeOptions = []
      this.formData.inspectionType = null
    }, */
    handleVisChange() {
      this.typeLoading = true
      this.getDicts(this.carTypeDict).then(res => {
        this.inspectionTypeOptions = res.data
        this.typeLoading = false
      }).catch(() => {
        this.typeLoading = false
      })
    },
    handleIsTypeChange(selectValue) {
      // const res = this.inspectionTypeOptions.find(item => item.dictValue == val)
      // const inspectionTypeName = res ? res.dictLabel : ''
      // if (this.formData.carType == 4 && val == 1) {
      //   this.isShowFTaxiType = true
      //   this.isShowSTaxiType = false
      //   this.formData = { ...this.formData, onlineCarDriver: '0', onlineCarTarnsport: '', inspectionTypeName }
      // } else if (this.formData.carType == 4 && val == '2') {
      //   this.isShowFTaxiType = false
      //   this.isShowSTaxiType = true
      //   this.formData = { ...this.formData, onlineCarDriver: '', onlineCarTarnsport: '0', inspectionTypeName }
      // } else {
      //   this.formData = { ...this.formData, inspectionType: val, inspectionTypeName }
      // }
      console.log(selectValue)
      let inspectionTypeNameAry = this.inspectionTypeOptions.filter(item => selectValue.includes(item.dictValue)).map(item => item.dictLabel)
      this.formData = { ...this.formData, inspectionType: selectValue.join(','), inspectionTypeName: inspectionTypeNameAry.join(',') }
    },
    onOpen() {
      const { name: userName, uid: userId } =  this.$store.getters
      this.formData = { userName, userId, inspectionTime: this.parseTime(new Date()) }
    },
    reset() {
      this.formData = {
        title: undefined,
        content: undefined,
        party: undefined,
        identityCard: '',
        phone: undefined,
        carNo: undefined,
        models: undefined,
        certificateNo: undefined,
        transportCertificate: undefined,
        userName: undefined,
        inspectionTime: undefined,
        carType: undefined,
        inspectionType: undefined,
        onlineCarDriver: undefined,
        onlineCarTarnsport: undefined,
        longitude: undefined,
        latitude: undefined,
        address: undefined
      }
      this.resetForm('elForm')
    },
    onClose() {
      this.reset()
      if (this.$refs.upload.clearFiles) this.$refs.upload.clearFiles()
      this.formFiles = []
      this.exFile.forEach(item => {
        if (this.$refs[`upload${item.data.status}`][0].clearFiles) this.$refs[`upload${item.data.status}`][0].clearFiles()
        item.fileList = []
      })
      this.formLoadingText = '数据上传中'
      this.exFile = []
      this.inspectionTypeOptions = []
    },
    close() {
      this.$emit('update:visible', false)
    },
    handelOver() {
      this.$confirm('提示', '是否确认办结？', { type: 'warning' }).then(() => {
        this.handelConfirm(9)
      }).catch(() => {})
    },
    validateFn(isValid) {
      // 根据传入的状态来确定是否要进行验证，用以区别保存和办结
      return new Promise((resolve, reject) => {
        if (isValid) {
          this.$refs['elForm'].validate(valid => {
            if (valid) {
              // 图片验证，没有图片不通过验证
              if (!this.$refs.upload.uploadFiles.length) {
                this.$message.error('请上传违规图片')
                reject()
              } else {
                for (let i = 0; i < this.exFile.length; i++) {
                  let exFile = this.exFile[i]
                  if (!this.$refs[`upload${exFile.data.status}`][0].uploadFiles.length) {
                    this.$message.error(`请上传${this.exFile[i].label}`)
                    reject()
                    break
                  } else if (i == this.exFile.length - 1) {
                    resolve()
                  }
                }
              }
            } else {
              reject()
            }
          })
        } else {
          resolve()
        }
      })
    },
    allFileUpload(businessId) {
      // 先检测违规图片，没有需要上传文件后再检测其余类型文件
      let isPass = false // 为true的话则代表没有需要上传的文件了
      const uploadFile = this.$refs.upload.uploadFiles.some(item => (typeof item.percentage == 'number' && item.percentage !== 100))
      if (uploadFile) {
        this.fileEXData.businessId = businessId
        this.formLoading = true
        this.formLoadingText = '违规图片上传中'
        this.$refs.upload.submit()
      } else {
        // 循环所有额外文件上传对象，检测是否还有文件没有上传
        for (let i = 0; i < this.exFile.length; i++) {
          let exfile = this.exFile[i]
          const exUploadFile = this.$refs[`upload${exfile.data.status}`][0].uploadFiles.some(item => (typeof item.percentage == 'number' && item.percentage !== 100))
          if (exUploadFile) {
            // 存在为上传文件
            console.log(exfile.data.status, this.$refs[`upload${exfile.data.status}`][0].uploadFiles)
            this.formLoading = true
            this.formLoadingText = `${exfile.label}上传中`
            this.exFile[i].data.businessId = businessId
            this.$refs[`upload${exfile.data.status}`][0].submit()
            this.upStatus = exfile.data.status
            break
          } else if (i == this.exFile.length - 1) {
            isPass = true
          }
        }
      }
      return isPass
    },
    handelConfirm(status) {
      this.validateFn(status == 9).then(() => {
        const methodFn = this.formData.transportId ? editTransport : addTransport
        this.formLoading = true
        const params = { ...this.formData, status, type: 1 }
        methodFn(params).then(res => {
          this.businessId = this.formData.transportId ? this.formData.transportId : res.data.transportId
          const isPass = this.allFileUpload(this.businessId)
          console.log('12')
          if (isPass) {
            this.formLoading = false
            this.$message.success('操作成功')
            this.$emit('reLoad')
            this.close()
          }
        }).catch(() => {
          this.formLoading = false
          this.close()
        })
      }).catch(() => {})
    }
  }
}

</script>
