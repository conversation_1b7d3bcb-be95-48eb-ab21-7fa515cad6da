import {request} from '@/utils/request'
let tout = {
  // 获取黄牛信息查询
  toutList(params) {
    return request({
      url: '/business/tout/list',
      method: 'get',
      params
    })
  },
  // 新增黄牛信息
  toutAdd(data) {
    return request({
      url: '/business/tout/add',
      method: 'post',
      data
    })
  },
  // 修改黄牛信息
  toutEdit(data) {
    return request({
      url: '/business/tout/edit',
      method: 'post',
      data
    })
  },
  // 删除黄牛信息
  toutRemove(data) {
    return request({
      url: '/business/tout/remove/' + data,
      method: 'post'
    })
  }
}
export default tout
