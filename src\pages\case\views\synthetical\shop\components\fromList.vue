<template>
  <div>
    <el-dialog class="m-dialog" :close-on-click-modal="false" v-bind="$attrs" width="1000px" :title="title" v-on="$listeners" @close="onClose">
      <el-scrollbar v-loading="formLoading" style="height: 100%;" :element-loading-text="formLoadingText">
        <div style="margin-right: 10px;">
          <!--:gutter="15"-->
          <el-form ref="form" :model="form" :rules="rules" :disabled="formDisabled" size="medium" label-width="120px">
            <h3 class="title">联系人信息</h3>
            <el-row>
              <el-col :span="12">
                <el-form-item label="法人" prop="corporation">
                  <el-input v-model="form.corporation" placeholder="请输入法人" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系人" prop="contactsName">
                  <el-input v-model="form.contactsName" placeholder="请输入联系人" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系电话" prop="contactsTelephone">
                  <el-input v-model="form.contactsTelephone" placeholder="请输入联系电话" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系人手机2" prop="contactsTelephonetwo">
                  <el-input v-model="form.contactsTelephonetwo" placeholder="请输入联系人手机2" clearable />
                </el-form-item>
              </el-col>
              <!-- <el-col :span="24">
                <el-form-item label="联系人身份证号" prop="contactsIdcard">
                  <el-input v-model="form.contactsIdcard" placeholder="请输入联系人身份证号" clearable />
                </el-form-item>
              </el-col> -->
              <el-col :span="24">
                <el-form-item label="住址(地址)" prop="businessAddress">
                  <div>
                    <el-input v-model="form.businessAddress" type="textarea" placeholder="请选择住址(地址)">
                      <svg-icon slot="suffix" icon-class="map" class="svg-icon" @click="openMap2 = true" />
                    </el-input>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <!-- <h3 class="title">管辖中队</h3>
            <el-row>
              <el-col :span="12">
                <el-form-item label="管辖中队名称" prop="jurisdiction">
                  <el-tooltip :disabled="!form.jurisdiction" class="item" effect="dark" :content="form.jurisdiction" placement="top-start">
                    <el-input v-model="form.jurisdiction" readonly placeholder="请选择管辖中队名称">
                      <el-button slot="append" type="primary" @click="showJurisdiction = true">选择</el-button>
                    </el-input>
                  </el-tooltip>
                  <userSelect id="jurisdiction" v-model="showJurisdiction" :name="'bumen'" :select-user-keys="[parseInt(form.jurisdictionId)]" :multiple="false" :default-expanded-keys="[form.jurisdictionId+'']" @confirm="userConfirm" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="管辖中队小组" prop="team">
                  <el-tooltip :disabled="!form.team" class="item" effect="dark" :content="form.team" placement="top-start">
                    <el-input v-model="form.team" readonly placeholder="请选择管辖中队小组">
                      <el-button slot="append" type="primary" @click="showTeam = true">选择</el-button>
                    </el-input>
                  </el-tooltip>
                  <userSelect id="team" v-model="showTeam" :name="'bumen'" :select-user-keys="[parseInt(form.teamId)]" :multiple="false" :default-expanded-keys="[form.teamId+'']" @confirm="teamConfirm" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="扫码信息" prop="scanCode">
                  <el-input v-model="form.scanCode" placeholder="请输入扫码信息" clearable />
                </el-form-item>
              </el-col>
            </el-row> -->
            <h3 class="title">店铺信息</h3>
            <el-row>
              <el-col :span="12">
                <el-form-item label="店铺名称" prop="shopName">
                  <el-input v-model="form.shopName" placeholder="请输入店铺名称" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="店铺类型" prop="businessTypeName">
                  <el-select v-model="form.businessTypeName" placeholder="请输入店铺类型" clearable :style="{ width: '100%' }" @change="handleCarChange" @clear="handleCarClear">
                    <el-option v-for="(item, index) in shopTypeData" :key="index" :label="item.dictLabel" :value="item.dictValue" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="网格小组">
                  <el-select v-model="form.deptName" placeholder="请选择网格小组" clearable size="small" :style="{ width: '100%' }">
                    <el-option
                      v-for="(deptItem, deptIndex) in deptOptions"
                      :key="deptIndex"
                      :label="deptItem"
                      :value="deptItem"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="注册号" prop="registrationNumber">
                  <el-input v-model="form.registrationNumber" placeholder="请输入注册号" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="营业执照号码" prop="businessNo">
                  <el-input v-model="form.businessNo" placeholder="请输入营业执照号码" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="执照开始时间" prop="businessStartTime">
                  <el-date-picker v-model="form.businessStartTime" type="date" value-format="yyyy-MM-dd" placeholder="请选择营业执照开始时间" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="周期" prop="type">
                  <!-- <el-tooltip :disabled="!form.businessEndTime" class="item" effect="dark" :content="form.businessEndTime" placement="top">
                    <el-date-picker v-model="form.businessEndTime" type="date" value-format="yyyy-MM-dd" placeholder="请选择营业执照结束时间" clearable />
                  </el-tooltip> -->
                  <el-select v-model="form.type" style="width: 100%;">
                    <el-option v-for="item in typeList" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="营业执照类型" prop="certificateType">
                  <el-input v-model="form.certificateType" placeholder="请输入营业执照类型" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="企业类型" prop="enterpriseType">
                  <el-input v-model="form.enterpriseType" placeholder="请输入企业类型" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="登记机关" prop="registrar">
                  <el-input v-model="form.registrar" placeholder="请输入登记机关" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="管辖单位" prop="jurisdictionUnit">
                  <el-input v-model="form.jurisdictionUnit" placeholder="请输入管辖单位" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="注册资金" prop="registeredCapital">
                  <el-input v-model="form.registeredCapital" placeholder="请输入注册资金" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="编码" prop="shopKey">
                  <el-input v-model="form.shopKey" placeholder="请输入编码" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="二维码串号" prop="bindCode">
                  <el-input v-model="form.bindCode" placeholder="请输入绑定的二维码串号" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="省级" prop="province">
                  <el-input v-model="form.province" placeholder="请输入省级" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="地级" prop="city">
                  <el-input v-model="form.city" placeholder="请输入地级" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="县级" prop="area">
                  <el-input v-model="form.area" placeholder="请输入县级" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="乡级" prop="street">
                  <el-input v-model="form.street" placeholder="请输入乡级" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="村级" prop="village">
                  <el-input v-model="form.village" placeholder="请输入村级" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="电子围栏" prop="remark">
                  <el-input v-model="form.remark" placeholder="请选择电子围栏">
                    <el-button slot="append" type="primary" @click="drawVisible = true">选择</el-button>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="经度" prop="longitude">
                  <el-input v-model="form.longitude" readonly placeholder="请选择详细地址" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="维度" prop="latitude">
                  <el-input v-model="form.latitude" readonly placeholder="请选择详细地址" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="详细地址" prop="address">
                  <div>
                    <el-input v-model="form.address" placeholder="请选择详细地址">
                      <el-button slot="append" type="primary" @click="openMap = true">选择</el-button>
                    </el-input>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="经营范围" prop="businessScope">
                  <el-input v-model="form.businessScope" type="textarea" placeholder="请输入经营范围" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="黑名单">
                  <el-radio-group v-model="form.blacklist">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="0">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="主体附件信息" prop="files">
                  <el-row :gutter="20" style="text-align: center;">
                    <el-col :span="1.5">
                      <MFileUpload ref="file0" :file-list="formFiles[0]" :ex-data="formData" :limit="1" style="height: 100px;" @uploadSucces="uploadFileSucesss" @error="fileError" />
                      <span>营业执照</span>
                    </el-col>
                    <el-col :span="1.5">
                      <MFileUpload ref="file1" :file-list="formFiles[1]" :ex-data="formData" :limit="1" style="height: 100px;" @uploadSucces="uploadFileSucesss" @error="fileError" />
                      <span>身份证正面</span>
                    </el-col>
                    <el-col :span="1.5">
                      <MFileUpload ref="file2" :file-list="formFiles[2]" :ex-data="formData" :limit="1" style="height: 100px;" @uploadSucces="uploadFileSucesss" @error="fileError" />
                      <span>身份证反面</span>
                    </el-col>
                  </el-row>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="店面附件信息" prop="files">
                  <el-row :gutter="20" style="text-align: center;">
                    <el-col :span="1.5">
                      <MFileUpload ref="file3" :file-list="formFiles[3]" :ex-data="formData" :limit="1" style="height: 100px;" @uploadSucces="uploadFileSucesss" @error="fileError" />
                      <span>门店图片</span>
                    </el-col>
                    <el-col :span="1.5">
                      <MFileUpload ref="file4" :file-list="formFiles[4]" :ex-data="formData" :limit="1" style="height: 100px;" @uploadSucces="uploadFileSucesss" @error="fileError" />
                      <span>附件</span>
                    </el-col>
                    <el-col :span="1.5">
                      <MFileUpload ref="file5" :file-list="formFiles[5]" :ex-data="formData" :limit="1" style="height: 100px;" @uploadSucces="uploadFileSucesss" @error="fileError" />
                      <span>附件</span>
                    </el-col>
                  </el-row>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </el-scrollbar>
      <tdtMap ref="bindmap" :visible.sync="openMap" append-to-body :map-search="true" :dw="form" @onlnglat="onlnglat" />
      <tdtMap :visible.sync="openMap2" append-to-body :name="'zhuzhi'" :map-search="true" :dw="form" @onlnglat="onlnglat" />
      <TMapDraw :visible.sync="drawVisible" :area="form.remark" @confirm="handleConfirmDraw" />
      <!-- <div v-if="openMap" class="map">
        <tdtMap ref="bindmap" :map-search="true" :dw="form" @onlnglat="onlnglat" />
      </div>
      <div v-if="openMap2" class="map">
        <tdtMap ref="bindmap" :map-search="true" :name="'zhuzhi'" :dw="form" @onlnglat="onlnglat" />
      </div> -->
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handelOver">保 存</el-button>
        <el-button @click="close">取 消 </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'
import shop from '@/api/case/synthetical/shop'
import tdtMap from '@/components/tdtMap/tdtMap'
import { getFiles, removeFiles} from '@/api/supervise/swit'
import {listData} from '@/api/system/dict/type'
// import userSelect from '@/components/userselect/index'
import MFileUpload from '@/components/MFileUpload/index.vue'
import TMapDraw from '@/components/tMapDraw/index.vue'
import { userList as deptList} from '@/api/system/dict/type'

export default {
  name: 'FromList',
  components: {tdtMap, MFileUpload, TMapDraw},
  props: {
    title: String,
    detailId: Number,
    formDisabled: Boolean,
    typeData: Array
  },
  data() {
    return {
      drawVisible: false,
      headers: {Authorization: 'Bearer ' + getToken()},
      formData: {businessId: null, tableName: 'case_shop'},
      formLoadingText: '数据上传中',
      formLoading: false,
      openMap: false,
      openMap2: false,
      showJurisdiction: false,
      showTeam: false,
      formFiles: { 0: [], 1: [], 2: [], 3: [], 4: [], 5: [] },
      shopTypeData: [],
      form: {},
      rules: {
        shopName: [{ required: true, message: '请输入店铺名称', trigger: 'blur' }],
        // shopKey: [{ required: true, message: '请输入编码', trigger: 'blur' }],
        // registrationNumber: [{ required: true, message: '请输入注册号', trigger: 'blur' }],
        businessNo: [{ required: true, message: '请输入营业执照号码', trigger: 'blur' }],
        businessStartTime: [{ required: true, message: '请输入营业执照开始时间', trigger: 'change' }],
        type: [{ required: true, message: '请输入选择营业执照周期', trigger: 'change' }],
        corporation: [{ required: true, message: '请输入法人', trigger: 'blur' }],
        address: [{ required: true, message: '请输入详细地址', trigger: 'change' }],
        jurisdictionName: [{ required: true, message: '请输入管辖中队', trigger: 'change' }],
        teamName: [{ required: true, message: '请输入管辖中队小组', trigger: 'change' }],
        jurisdiction: [{ required: true, message: '请输入管辖中队名称', trigger: 'change' }],
        contactsName: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
        contactsTelephone: [
          { required: true, message: '请输入正确的联系电话', trigger: 'change' }],
        // contactsIdcard: [{ required: true, message: '请输入联系人身份证号', trigger: 'blur' }],
        businessAddress: [{ required: true, message: '请输入住址(地址)', trigger: 'change' }],
        businessType: [{ required: true, message: '请输入店铺类型', trigger: 'change' }],
        bindCode: [{ required: true, message: '请输入绑定的二维码串号', trigger: 'blur' }]
      },
      fileIdx: 0,
      typeList: [],
      deptOptions: []
    }
  },
  watch: {
    '$attrs.visible'(nVal) {
      if (nVal && this.detailId) {
        this.getData()
      }
    }
  },
  async mounted() {
    await this.getData()
    this.getDeptNameOptions()

  },
  methods: {
    // 网格小组选项
    getDeptNameOptions() {
      deptList({ typeList: 1 }).then(res => {
        this.deptOptions = res.data.map(item => item.label)
      })
    },
    handleConfirmDraw(polygon) {
      this.form = { ...this.form, remark: polygon }
    },
    uploadFileSucesss() {
      // 图片上传后回调
      if (this.fileIdx == 5) {
        this.msgSuccess('保存成功')
        this.formLoading = false
        this.$emit('reLoad')
        this.close()
      } else {
        this.fileIdx++
        this.formData.status = this.fileIdx
        this.$refs[`file${this.fileIdx}`].submitFile()
      }
    },
    fileError() {
      this.formLoading = false
      this.form.shopId = this.formData.businessId
    },
    getData() {
      this.formLoading = true
      this.formLoadingText = '加载中'
      Promise.all([
        shop.oneList(this.detailId),
        getFiles({businessId: this.detailId, tableName: 'case_shop'}),
        listData({dictType: 'shop_type'}),
        this.getDicts('shop_cycle')
      ]).then(res => {
        this.form = {...res[0].data}
        // 文件部分
        this.formFiles = { 0: [], 1: [], 2: [], 3: [], 4: [], 5: [] }
        res[1].rows.map(item => {
          const obj = { name: item.displayName, url: `/zqzfj${item.filePath}`, ...item }
          this.formFiles[item.status].push(obj)
        })
        this.typeList = res[3].data
        this.shopTypeData = res[2].rows
        this.shopTypeData.forEach(v => {
          if (v.dictValue == this.form.businessType) this.form.businessTypeName = v.dictLabel
        })
        this.formLoading = false
      }).catch(err => { console.log(err), this.formLoading = false, this.msgError('加载失败') })
    },
    handelOver() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          let params = {...this.form}
          this.formLoading = true
          this.formLoadingText = '数据上传中'
          let api = params.shopId ? shop.edit(params) : shop.add(params)
          api.then(res => {
            this.formData.status = this.fileIdx
            this.formData.businessId = this.form.shopId || res.data.shopId
            if (res.data && res.data.shopId) this.form.shopId = res.data.shopId
            this.$refs[`file${this.fileIdx}`].submitFile()
          }).catch(err => { console.log(err); this.formLoading = false, this.msgError('上传失败') })
        } else {
          return false
        }
      })

    },
    handleCarChange(val) {
      const selctObj = this.shopTypeData.find(item => item.dictValue == val)
      if (selctObj) this.form = {...this.form, businessType: selctObj.dictSort, businessTypeName: selctObj.dictLabel}
    },
    handleCarClear() {
      this.form.businessType = ''
    },
    userConfirm(e) {    // 管辖中队名称
      this.form = {...this.form, jurisdiction: e.name, jurisdictionId: e.id}
    },
    teamConfirm(e) {    // 管辖中队名称
      this.form = {...this.form, team: e.name, teamId: e.id}
    },
    // 上传函数
    beforeRemove(file) {
      return new Promise((resolve, reject) => {
        this.$confirm('是否确认删除？ 删除后将无法恢复', '提示', { type: 'warning' }).then(() => {
          if (file.fileId) {
            removeFiles(file.fileId).then(resolve).catch(reject)
          } else {
            resolve()
          }
        }).catch(() => {
          reject()
        })
      })
    },
    handleChange(file, fileList) {   // 删除图片
      this.fileList = fileList
    },
    exceed(files, filesList) {
      if (filesList.length >= 4) this.msgError('已超出最大上传数量')
    },
    handleSuccess(files) {    // 上传成功
      this.$emit('reLoad')
      this.close()
      this.formLoading = false
      if (files.code == 200) {
        this.msgSuccess('上传成功')
      } else {
        this.msgError('上传失败,请修改后重试')
      }
    },
    handleError() { // 上传失败
      this.msgError('上传失败,请修改后重试')
      this.formLoading = false
    },
    // 上传
    upload(data) {
      console.log(data)
      this.formLoading = true
      this.formLoadingText = '图片上传中'
      this.formData.businessId = data.id
      this.$refs.upload.submit()
    },
    reset() {
      this.form = {}
      this.formData = {businessId: null, tableName: 'case_shop'}
      this.formFiles = { 0: [], 1: [], 2: [], 3: [], 4: [], 5: [] }
      this.fileIdx = 0
      this.resetForm('form')
    },
    onClose() {
      this.reset()
      this.formFiles = []
      this.formLoadingText = '数据上传中'
      this.$refs.form.clearValidate()
    },
    close() {
      this.$emit('update:visible', false)
      this.$refs.form.clearValidate()
    },
    onlnglat(lenlat, qq) {      // 经纬度  详细地址
      let {lng: longitude, lat: latitude, address} = lenlat
      this.openMap = false
      this.openMap2 = false
      if (qq == 'zhuzhi') return this.form = {...this.form, businessAddress: address}
      this.form = {...this.form, longitude, latitude, address}
    }
  }
}
</script>

<style scoped>
.fileUploadBtn {
  width: 100px;
  height: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.fileUploadBtn i {
  margin-top: 30px;
  margin-bottom: 10px;
}
.fileUploadBtn span {
  line-height: 30px;
  color: rgb(175, 174, 174);
}
</style>
