import {request} from '@/utils/request'

// 查询法律法规列表
export function listLaw(query) {
  return request({
    url: '/business/law/list',
    method: 'get',
    params: query
  })
}

// 查询法律法规详细
export function getLaw(id) {
  return request({
    url: '/business/law/' + id,
    method: 'get'
  })
}

// 新增法律法规
export function addLaw(data) {
  return request({
    url: '/business/law/add',
    method: 'post',
    data: data
  })
}

// 修改法律法规
export function updateLaw(data) {
  return request({
    url: '/business/law/edit',
    method: 'post',
    data: data
  })
}

// 删除法律法规
export function delLaw(id) {
  return request({
    url: '/business/law/remove/' + id,
    method: 'post'
  })
}

// 导出法律法规
export function exportLaw(query) {
  return request({
    url: '/business/law/export',
    method: 'get',
    params: query
  })
}
