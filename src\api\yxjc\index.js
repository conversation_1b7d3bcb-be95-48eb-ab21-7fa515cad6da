import { request } from '@/utils/request'

//公共设施运行监测
export function getPublicList(params) {
  return request({
    url: `/screen/yxjc/public`,
    method: 'get',
    params
  })
}

//折线图
export function getIndexData(params) {
  return request({
    url: `/indexPort`,
    method: 'get',
    params
  })
}

//事件统计
export function getwarningCount(params) {
  return request({
    url: `/screen/yxjc/warningCount`,
    method: 'get',
    params
  })
}

//发生事件趋势
export function geteventTrend(params) {
  return request({
    url: `/screen/yxjc/eventTrend`,
    method: 'get',
    params
  })
}

//事件列表
export function geteventList(params) {
  return request({
    url: `/screen/yxjc/eventList`,
    method: 'get',
    params
  })
}

//获取报告
export function getreportList(params) {
  return request({
    url: `/screen/yxjc/reportList`,
    method: 'get',
    params
  })
}

//报告详情-运行监测
export function getPublicList2(params) {
  return request({
    url: `/screen/yxjc/publicV2`,
    method: 'get',
    params
  })
}

//监测预警事件
export function getJianCe(params) {
  return request({
    url: `/screen/yxjc/eventDeviceLayer`,
    method: 'get',
    params
  })
}

//人员密集预警
export function getRenYuan(params) {
  return request({
    url: `/screen/yxjc/eventPersonLayer`,
    method: 'get',
    params
  })
}

//监测点
export function getMonitorPoint(params) {
  return request({
    url: `/MonitorPoint/MonitorPoint/listAll`,
    method: 'get',
    params
  })
}