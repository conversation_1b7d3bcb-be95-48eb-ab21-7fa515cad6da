<template>
    <div class='pageContainer'>
      <left></left>
      <right></right>
    </div>
  </template>
  
  <script>
  import left from '@/pages/zqyzt/left/index.vue'
  import right from '@/pages/zqyzt/right/index.vue'
  export default {
    name: 'index',
    components: {
        left,
        right,
    },
    data() {
      return {
      }
    },
    computed: {},
    mounted() {
  
    },
    methods: {},
    watch: {}
  }
  </script>
  
  <style scoped>
  
  </style>