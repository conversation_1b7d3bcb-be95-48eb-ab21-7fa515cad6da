import Map from "@arcgis/core/Map.js";
import SceneView from "@arcgis/core/views/SceneView.js";
import Basemap from "@arcgis/core/Basemap.js";
import { layerCreate } from "./core.js";
import {
  getIRSLayer,
  getCustomBasemp,
} from "./basemap.js";
// import layerCreatAsync from "./layerCreatAsync.js";
import { getLayerConfigById } from "./layerConfig.js";
import esriConfig from "@arcgis/core/config.js";
import MapClickEventHandle from "./MapClickEventHandle.js";
import MapPopupWidget from "./MapPopupWidget.js";
// import { addDEMToMap, removeDEMFromMap, addPoiLayers } from "./exchangeMap.js";

const defaultCamera = {
  position: {
    spatialReference: {
      wkid: 4490,
    },
    x: 120.02164073413151,
    y: 28.922141859406103,
    z: 455201.1748902945,
  },
  heading: 353.0620839814934,
  tilt: 0.8979354024150412,
};
const defaultGlobalCamera = {
  position: {
    spatialReference: {
      latestWkid: 4490,
      wkid: 4490,
    },
    x: 120.12656231499913,
    y: 28.154407308672614,
    z: 25512548.000000015,
  },
  heading: 354.9940451770809,
  tilt: 0.09998686595828937,
};
/**
 * 加载SceneView
 * @param {*} urlTemplate
 * @param {*} divId
 */
function initSceneView({
  divId = "viewDiv",
  camera = defaultCamera,
  basemap = "TDT_vct",
  viewingModeExtend = "global",
  isDefaultGoToFromGlobal = false,
  isDefaultAddDem = true,
  alphaCompositingEnabled = false,
  environment,
  ground = {
    opacity: 1,
    surfaceColor: "#08294a",
  },
}) {
  const baseMapConfig = {
    image: getIRSLayer,
  };
  const type = typeof basemap;
  let catchBaseMap = null;
  if (type === "object") {
    if (!basemap.title || !basemap.layerConfigs) {
      throw Error("底图title和layerConfigs必传");
    }

    catchBaseMap = getCustomBasemp(basemap);
  } else if (type === "string") {
    catchBaseMap = baseMapConfig[basemap]();
  } else {
    throw Error("暂不支持该底图类型");
  }



  const map = new Map({
    basemap: catchBaseMap,
    ground,
    minZoom: 10,
  });
  const viewProps = {
    container: divId,
    alphaCompositingEnabled,
    map: map,
    camera: isDefaultGoToFromGlobal ? defaultGlobalCamera : camera,
    constraints: {
      altitude: {
        min: 10,
        max: 100000,
      },
    },
  };
  environment && (viewProps.environment = environment);
  if (viewingModeExtend === "global" || viewingModeExtend === "local") {
    viewProps.viewingMode = viewingModeExtend;
  } else if (typeof viewingModeExtend == "object") {
    viewProps.camera = viewingModeExtend;
  }
  const view = new SceneView(viewProps);
  view.qualityProfile = "low";
  // view.qualitySettings.memoryLimit = 1024;
  view.popup.autoOpenEnabled = false;
  view.ui.remove('zoom')//清除放大缩小按钮
  view.ui.remove('attribution')//清楚底部powered by ESRI
  view.when(() => {

    if (isDefaultGoToFromGlobal) {
      setTimeout(() => {
        view.goTo(camera, {
          speedFactor: 0.8,
        });
      }, 4000);
      view.watch("zoom", (e) => {
        //zoom大于9级时清除
        if (e > 9) {
          if (
            view.map.basemap.baseLayers.items.some(
              (layer) => layer.id === "initBaseLayer"
            )
          ) {
            baseMap.destroy();
            view.map.basemap = catchBaseMap;
          }
        }
      });
    }

  });

  window.view = view;
  // isDefaultAddDem && addDEMToMap(view);
  // goHome方法
  view.goHome = () => {
    view.goTo(camera);
  };

  //去掉水波纹
  // if (basemap != "image") {
  //   addWaterWaveEffect({
  //     color: waterColor,
  //     zoom: 15.422761891002374,
  //   });
  // }

  // 可以访问wmts服务
  const regExp = new RegExp("/geoserver/JHRS/");
  esriConfig.request.interceptors.push({
    urls: regExp,
    before: function (param) {
      let urlSplit = param.url.split("/geoserver/JHRS/");
      const newUrl = `${window.location.protocol}//${window.location.host}/geoserver/JHRS/${urlSplit[1]}`;
      param.url = newUrl;
    },
  });

  esriConfig.request.interceptors.push({
    urls: "https://csdnwlgz.dsjj.jinhua.gov.cn:82/map/rest/services",
    before: function (params) {
      if (params.requestOptions.query) {
        params.requestOptions.query.gctk =
          token || "727d5ddcc1ae4d1f9f5aaccab7100a54";
      } else {
        params.requestOptions.query = {
          gctk: token || "727d5ddcc1ae4d1f9f5aaccab7100a54",
        };
      }
    },
  });

  let mapClickEventHandle = new MapClickEventHandle(view);
  // if (ArcGisUtils) {
  //   ArcGisUtils.mapClickEventHandle = mapClickEventHandle;
  //   ArcGisUtils.mapPopupWidget = new MapPopupWidget({
  //     view,
  //   });
  // }

  return view;
}

export default initSceneView;
