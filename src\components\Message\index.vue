<template>
  <div>
    <el-popover
      placement="bottom"
      trigger="hover"
    >
      <el-table :data="gridData">
        <el-table-column property="noticeTitle" width="200" show-overflow-tooltip label="公告标题" />
        <el-table-column property="createTime" width="160" label="发布时间" />
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button type="text" @click="handleClick(scope.row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div slot="reference" style="cursor: pointer;">
        <el-badge :value="gridData.length" :hidden="!gridData.length" class="item">
          <i class="el-icon-message-solid" @click="handleOpenList" />
        </el-badge>
      </div>
    </el-popover>
    <el-dialog :close-on-click-modal="false" :visible.sync="dialogVisible" :title="title" append-to-body @onClose="handleClose">
      <div v-html="content" />
    </el-dialog>
    <!-- 消息列表 -->
    <el-dialog :close-on-click-modal="false" :visible.sync="visibleList" append-to-body title="历史公告">
      <el-table v-loading="hisLoading" :data="HisGridData">
        <el-table-column property="noticeTitle" show-overflow-tooltip label="公告标题" />
        <el-table-column property="createTime" label="发布时间" />
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button type="text" @click="handleClick(scope.row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="pageNum"
        :limit.sync="pageSize"
        @pagination="fetchHisData"
      />
    </el-dialog>
  </div>
</template>

<script>
import { createSocket } from '@/utils/websocket'
import { listNoticeDetail, getNotice, editNoticeDetail } from '@/api/manage/oa/notice/notice'

export default {
  data() {
    return {
      gridData: [],
      dialogVisible: false,
      title: '',
      content: '',
      visibleList: false,
      HisGridData: [],
      hisLoading: false,
      total: 0,
      pageNum: 1,
      pageSize: 10
    }
  },
  mounted() {
    this.fetchData()
    createSocket({ uId: `${this.$store.getters.uid}-pc` })
    window.addEventListener('onmessageWS', this.getsocketData)
  },
  destroyed() {
    window.removeEventListener('onmessageWS', this.getsocketData)
  },
  methods: {
    getsocketData(e) {
      const data = e && e.detail.data
      try {
        const msgData = JSON.parse(data)
        console.log(msgData)
      } catch (error) {
        this.$message.error('监听数据格式错误，请联系管理员')
      }
    },
    handleClick(row) {
      getNotice(row.noticeId).then(res => {
        this.dialogVisible = true
        this.title = res.data.noticeTitle
        this.content = res.data.noticeContent
        this.readMsg(row.noticeDetailId)
      })
    },
    readMsg(noticeDetailId) {
      editNoticeDetail({ noticeDetailId, isRead: 1 }).then(() => {
        this.gridData = this.gridData.filter(item => item.noticeDetailId != noticeDetailId)
      })
    },
    handleClose() {
      this.title = ''
      this.content = ''
    },
    fetchData() {
      listNoticeDetail({ userId: this.$store.getters.uid, isRead: 0 }).then(res => {
        this.gridData = res.rows
      })
    },
    fetchHisData() {
      this.hisLoading = true
      listNoticeDetail({ userId: this.$store.getters.uid, pageNum: this.pageNum, pageSize: this.pageSize }).then(res => {
        this.HisGridData = res.rows
        this.total = res.total
        this.hisLoading = false
      }).catch(() => {
        this.hisLoading = false
      })
    },
    handleOpenList() {
      this.visibleList = true
      this.fetchHisData()
    }
  }
}
</script>

<style>
.el-badge__content.is-fixed {
  top: 15px;
}
</style>
