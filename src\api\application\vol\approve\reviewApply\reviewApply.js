import {request} from '@/utils/request'

// 查询审批记录列表
export function listApprove(query) {
  return request({
    url: '/business/approve/list',
    method: 'get',
    params: query
  })
}

// 查询审批记录详细
export function getApprove(id) {
  return request({
    url: '/business/approve/' + id,
    method: 'get'
  })
}

// 审批
export function addReviewApply(data) {
  return request({
    url: '/business/approve/reviewApply',
    method: 'post',
    data: data
  })
}

// 新增审批记录
export function addApprove(data) {
  return request({
    url: '/business/approve/add',
    method: 'post',
    data: data
  })
}

// 修改审批记录
export function updateApprove(data) {
  return request({
    url: '/business/approve/edit',
    method: 'post',
    data: data
  })
}

// 删除审批记录
export function delApprove(id) {
  return request({
    url: '/business/approve/remove/' + id,
    method: 'post'
  })
}

// 导出审批记录
export function exportApprove(query) {
  return request({
    url: '/business/approve/export',
    method: 'get',
    params: query
  })
}

// 查询补卡申请详情
export function getSign(id) {
  return request({
    url: '/business/vol/sign/' + id,
    method: 'get'
  })
}

// 查询志愿者任务（报名）详情
export function getTaskUser(id) {
  return request({
    url: '/business/vol/taskUser/detail/' + id,
    method: 'get'
  })
}

// 查询志愿者申请详情
export function getApplyUser(userId) {
  return request({
    url: '/business/vol/apply/' + userId,
    method: 'get'
  })
}
