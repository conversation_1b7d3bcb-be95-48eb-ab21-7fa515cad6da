/* eslint-disable */
import store from '@/store/index'
import { Message, Notification } from 'element-ui'
import { getZf } from '@/utils/auth'
let m_IdtApi, m_CallId, terminalNo
function onRecvMsgHook(link, msg) {
  // console.log(`收到消息:link: ${link}, msg: ${msg}`)
}

function onSendMsgHook(link, msg) {
  // console.log(`发送消息:link: ${link}, msg: ${msg}`)
}

// 查询所有用户
function fn_UQueryAll() {
  var strGNum = '330711' // 站前组
  var iGroup  = 0
  var iUser   = 1
  var iPage   = 0

  //      pucGNum:        组号码
  //      ucGroup:        是否查询下属组,0不查询,1查询
  //      ucUser:         是否查询下属用户,0不查询,1查询
  //      dwPage:         第几页,从0开始.默认每页1024个用户,如果不到1024个用户,说明查询结束

  var query = {
    GNum: strGNum,
    QueryExt: {
      All: 1,
      Group: iGroup,
      User: iUser,
      Order: 0,
      Page: iPage,
      Count: 1024,
      TotalCount: 0
    }
  }

  m_IdtApi.GQueryU(query, fn_OamCallBack_UQueryAll)
}

// 查询所有组
function fn_GQueryAll() {
  var strGNum = '0'
  var iGroup  = 1
  var iUser   = 0
  var iPage   = 0

  //      pucGNum:        组号码
  //      ucGroup:        是否查询下属组,0不查询,1查询
  //      ucUser:         是否查询下属用户,0不查询,1查询
  //      dwPage:         第几页,从0开始.默认每页1024个用户,如果不到1024个用户,说明查询结束

  var query = {
    GNum: strGNum,
    QueryExt: {
      All: 1,
      Group: iGroup,
      User: iUser,
      Order: 0,
      Page: iPage,
      Count: 1024,
      TotalCount: 0
    }
  }

  m_IdtApi.GQueryU(query, fn_OamCallBack)
}

function fn_OamCallBack(bRes, cause, strCause, MsgBody, UserSn) {
  // var xx = 0
  // console.log(bRes, cause, strCause, MsgBody, UserSn)
  //    switch (MsgBody.OpCode)
  //    {
  //    case IDT.OPT_R_QUERY://路由查询
  //        //结束了
  //        if (1 == MsgBody.CommQuery.EndFlag)
  //        {
  //            break;
  //        }
  //        break;
  //    default:
  //        break;
  //    }

  return 0
}

function fn_OamCallBack_UQueryAll(bRes, cause, strCause, MsgBody) {
  if (true == window.PUtility.isEmpty(MsgBody)) {
    return 0
  }
  var i
  for (i = 0; i < MsgBody.GNumU; i++) {
    m_IdtApi.GpsSubs(MsgBody.GMember[i].Num, window.IDT.GU_STATUSSUBS_DETAIL1)
    // m_IdtApi.GpsSubs(MsgBody.GMember[i].Num, IDT.GU_STATUSSUBS_QUERY_ONETIME);
  }

  if (MsgBody.GNumU < 1024)// 查询完成
    return 0
    // if (MsgBody.QueryExt.Page * MsgBody.QueryExt.Count )

  var query = {
    GNum: MsgBody.GNum,
    QueryExt: {
      All: 1,
      Group: 0,
      User: 1,
      Order: 0,
      Page: MsgBody.QueryExt.Page + 1,
      Count: 1024,
      TotalCount: 0
    }
  }

  m_IdtApi.GQueryU(query, fn_OamCallBack_UQueryAll)
  return 0
}

function onStatusInd(status, usCause) {
  console.log(`当前登录状态：${status}, usCause: ${usCause}`)
  if (status) {
    m_IdtApi.StatusSubs(window.IDT.GU_STATUSSUBS_STR_ALL, window.IDT.GU_STATUSSUBS_DETAIL1)

    // 加载组织下所有用户和组
    fn_UQueryAll()
    fn_GQueryAll()
  }
  return 0
}

function onGInfoInd(gInfo) {
  // console.log(gInfo)
}

function onIMRecv(pucSn, dwType, pcFrom, pcFromName, pcTo, pcOriTo, pcTxt, pcFileName, pcSourceFileName, pcTime) {
  // console.log(pucSn, dwType, pcFrom, pcFromName, pcTo, pcOriTo, pcTxt, pcFileName, pcSourceFileName, pcTime)
}

function onIMStatusInd(dwSn, pucSn, dwType, ucStatus) {
  // console.log(dwSn, pucSn, dwType, ucStatus)
}

function onGUOamInd(dwOptCode, pucGNum, pucGName, pucUNum, pucUName, ucUAttr) {
  // console.log(dwOptCode, pucGNum, pucGName, pucUNum, pucUName, ucUAttr)
}

function onGUStatusInd(GMemberStatus) {
  // console.log(GMemberStatus)
}

function onGpsRecInd(GpsRecStr) {
  // console.log(GpsRecStr)
}

function onGpsHisQueryInd(UsrNum, sn, EndFlag, GpsRecStr) {
  // console.log(UsrNum, sn, EndFlag, GpsRecStr)
}

function onNsQueryInd(NsQueryExt, EndFlag, NsQueryRec) {
  // console.log(NsQueryExt, EndFlag, NsQueryRec)
}

// 执法记录仪呼入弹窗显示
/* function callInNf(name) {
  Notification({
    title: `${name}呼入`,
    type: 'info',
    duration: 0,
    showClose: false,
    dangerouslyUseHTMLString: true,
    message: <notifyMsg />,
    onClick: function(e) {
      console.log(e)
    }
  })
} */

function onCallInd(event) {
  console.log(`类型时间：${event}`)
  // console.log(arguments)
  switch (event) {
    case window.IDT.CALL_EVENT_RecvInfo:
      // 向执法记录仪呼叫，进入该回调
      console.log('SET_VIDEO_TITLE')
      store.commit('board/SET_VIDEO_TITLE', `正在呼叫${terminalNo}中...`)
      break
    case window.IDT.CALL_EVENT_PeerAnswer:
      // 执法记录仪应答
      store.commit('board/SET_VIDEO_TITLE', `正在与${terminalNo}通话中...`)
      break
    case window.IDT.CALL_EVENT_In:
      // 执法记录仪向指挥中心呼叫
      m_CallId = arguments[1]
      // callInNf()
      store.commit('board/SET_CALL_TYPE', { type: 'callIn', name: arguments[3], ARx: arguments[6], ATx: arguments[7], VRx: arguments[8], VTx: arguments[9] }) // 修改为呼入状态
      // store.commit('board/SET_VIDEO_VISIBLE', true)
      break
    case window.IDT.CALL_EVENT_Rel:
      // 记录仪挂断后回调
      store.commit('board/SET_VIDEO_VISIBLE', false)
      store.commit('board/SET_VIDEO_TITLE', '视频连接中')
      Message.info(`${window.IDT.GetCloseStr(arguments[3])} ${ arguments[4] ? window.IDT.GetCauseStr(arguments[4]) : ''}`)
      Notification.closeAll() // 再调用一次关闭，防止没有接听之类的导致没有关闭
      m_CallId = -1
      break
    default:
      break
  }
}

export function fn_start() {
  if (null == m_IdtApi) {
    m_IdtApi = new window.CIdtApi()
  }
  m_IdtApi.RUN_MODE = 0
  var pwd = getZf()
  var strSrvUrl = 'ws://202.96.125.90:10004'
  var strGpsSrvUrl = 'ws://202.96.125.90:10005'
  var strNsUrl = 'ws://202.96.125.90:10007'
  var strUserId = '1005'
  var strPwd = pwd
  var CallBack =
    {
      onRecvMsgHook: onRecvMsgHook,          // 收到消息的钩子函数,只用来调试打印,如果修改消息内容,会出问题
      onSendMsgHook: onSendMsgHook,          // 发送消息的钩子函数,只用来调试打印,如果修改消息内容,会出问题
      onStatusInd: onStatusInd,              // 登录状态指示
      onGInfoInd: onGInfoInd,                // 组信息指示,指示用户在哪些组里面
      onIMRecv: onIMRecv,                    // 短信接收指示
      onIMStatusInd: onIMStatusInd,          // 短信状态指示
      onGUOamInd: onGUOamInd,                // 用户/组OAM操作指示
      onGUStatusInd: onGUStatusInd,          // 用户/组状态指示
      onGpsRecInd: onGpsRecInd,              // GPS数据指示
      onGpsHisQueryInd: onGpsHisQueryInd,    // GPS历史数据查询响应
      onCallInd: onCallInd,                  // 呼叫指示
      onNsQueryInd: onNsQueryInd             // GPS查询响应
    }
  m_IdtApi.Start(strSrvUrl, strGpsSrvUrl, strUserId, strPwd, 32, 32, 1, 4096, CallBack, false, strNsUrl)
}

export function fn_Exit() {
  m_IdtApi.Exit()
  m_IdtApi = null
}

// 视频呼叫
export function callMakeOut(num, videoPeer = null, type = {}) {
  terminalNo = num
  m_CallId = m_IdtApi.CallMakeOut(100, null, videoPeer, type.ARx, type.ATx, type.VRx, type.VTx, num, window.IDT.SRV_TYPE_BASIC_CALL,  '', 1, 0)
}

// 挂断
export function fn_rel() {
  if (m_CallId == -1) return
  m_IdtApi.CallRel(m_CallId, 100, 0)
  store.commit('board/SET_CALL_TYPE', { type: '' })
  store.commit('board/SET_VIDEO_VISIBLE', false)
  store.commit('board/SET_VIDEO_TITLE', '连接中')
  m_CallId = -1
}

// 全屏显示
export function fn_fullscreen(videoPeer) {
  window.PUtility.RequestFullScreen(videoPeer)
}

// 应答
export function fn_answer(videoMy, videoPeer = null, type = {}) {
  m_IdtApi.CallAnswer(m_CallId, 100, videoMy, videoPeer, type.ARx, type.ATx, type.VRx, type.VTx)
  store.commit('board/SET_CALL_TYPE', { type: '' })
}
