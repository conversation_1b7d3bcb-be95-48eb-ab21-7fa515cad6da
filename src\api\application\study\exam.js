import {request} from '@/utils/request'

// 查询试卷列表
export function listExam(query) {
  return request({
    url: '/business/exam/list',
    method: 'get',
    params: query
  })
}

// 查询试卷详细
export function getExam(params) {
  return request({
    url: '/business/question/info',
    method: 'get',
    params
  })
}

// 新增试卷
export function addExam(data) {
  return request({
    url: '/business/exam/add',
    method: 'post',
    data: data
  })
}

// 修改试卷
export function updateExam(data) {
  return request({
    url: '/business/exam/edit',
    method: 'post',
    data: data
  })
}

// 删除试卷
export function delExam(id) {
  return request({
    url: '/business/exam/remove/' + id,
    method: 'post'
  })
}

// 导出试卷
export function exportExam(query) {
  return request({
    url: '/business/exam/export',
    method: 'get',
    params: query
  })
}
