let THREE = window.THREE_r123;

class FireInit {
  constructor(options) {
    this.opts = {
      camera: null,
      scene: null,
      Mover: null,
      position: null,
    };
    this.opts.camera = options.camera;
    this.opts.scene = options.scene;
    this.opts.Mover = options.Mover;
    this.opts.position = options.position;
    this.movers_num = options.movers_num || 10000;
    this.movers = [];
    this.geometry = null;
    this.material = null;
    this.obj = null;
    this.texture = null;
    this.antigravity = new THREE.Vector3(0, 0, -0.02);
    this.target = [0, 0, 1];
  }
  /**
   * 初始化创建粒子
   * @memberof FireInit
   * @method update
   */
  init() {
    let position = this.opts.position;
    this.createTexture();
    this.geometry = new THREE.Geometry();
    this.material = new THREE.PointsMaterial({
      color: "#fd3109",
      size: 200,
      transparent: true,
      opacity: 0.5,
      map: this.texture,
      depthTest: false,
      blending: THREE.AdditiveBlending,
    });
    for (var i = 0; i < this.movers_num; i++) {
      var mover = new this.opts.Mover();

      mover.init(new THREE.Vector3(position[0], position[1], position[2]));
      this.movers.push(mover);
      this.geometry.vertices.push(mover.position);
    }
    const length = Math.sqrt(
      position[0] * position[0] +
        position[1] * position[1] +
        position[2] * position[2]
    );

    // Global场景使用
    this.target = [
      position[0] / length,
      position[1] / length,
      position[2] / length,
    ];
    // Local场景使用
    // this.target = [0, 0, 1]

    this.t = [
      this.target[1] * 1 - this.target[2] * 0,
      this.target[2] * 0 - this.target[0] * 1,
      this.target[0] * 0 - this.target[1] * 0,
    ];
    this.b = [
      this.target[1] * this.t[2] - this.target[2] * this.t[1],
      this.target[2] * this.t[0] - this.target[0] * this.t[2],
      this.target[0] * this.t[1] - this.target[1] * this.t[0],
    ];
    this.antigravity = new THREE.Vector3(
      this.target[0] / -20,
      this.target[1] / -20,
      this.target[2] / -20
    );
    this.obj = new THREE.Points(this.geometry, this.material);
  }
  /**
   * 更新粒子位置
   * @memberof FireInit
   * @method update
   */
  update() {
    var points_vertices = [];

    for (var i = 0; i < this.movers.length; i++) {
      var mover = this.movers[i];
      if (mover.is_active) {
        mover.applyForce(this.antigravity);
        mover.updateVelocity();
        mover.updatePosition();
        if (mover.time > 200) {
          mover.inactivate();
        }
      }
      points_vertices.push(mover.position);
    }
    this.obj.geometry.vertices = points_vertices;
    this.obj.geometry.verticesNeedUpdate = true;
  }
  /**
   * 激活粒子运动
   * @memberof FireInit
   * @method activateMover
   */
  activateMover() {
    let position = this.opts.position;
    var count = 0;

    for (var i = 0; i < this.movers.length; i++) {
      var mover = this.movers[i];

      if (mover.is_active) continue;
      var rad1 = this.getRadian(
        (Math.log(this.getRandomInt(200, 256)) / Math.log(256)) * 300
      );
      var rad2 = this.getRadian(this.getRandomInt(0, 360));
      var force = this.getSpherical(rad1, rad2, 5);
      mover.activate();
      mover.init(new THREE.Vector3(position[0], position[1], position[2]));
      mover.applyForce(force);
      count++;
      if (count > 20) break;
    }
  }
  /**
   * 创建粒子贴图
   * @memberof FireInit
   * @method createTexture
   */
  createTexture() {
    var canvas = document.createElement("canvas");
    var ctx = canvas.getContext("2d");
    var grad = null;

    canvas.width = 200;
    canvas.height = 200;
    grad = ctx.createRadialGradient(100, 100, 20, 100, 100, 100);
    grad.addColorStop(0.2, "rgba(255, 255, 255, 1)");
    grad.addColorStop(0.5, "rgba(255, 255, 255, 0.5)");
    grad.addColorStop(1.0, "rgba(255, 255, 255, 0)");
    ctx.fillStyle = grad;
    ctx.arc(100, 100, 100, 0, Math.PI / 180, true);
    ctx.fill();
    this.texture = new THREE.Texture(canvas);
    this.texture.minFilter = THREE.NearestFilter;
    this.texture.needsUpdate = true;
  }
  /**
   * 在数值范围内获取随机整数
   * @memberof FireInit
   * @param {Number} min 最小值
   * @param {Number} max 最大值
   * @method getRandomInt
   */
  getRandomInt(min, max) {
    return Math.floor(Math.random() * (max - min)) + min;
  }
  /**
   * 计算度数
   * @memberof FireInit
   * @param {Number} radian 弧度
   * @method getDegree
   */
  getDegree(radian) {
    return (radian / Math.PI) * 180;
  }
  /**
   * 计算弧度
   * @memberof FireInit
   * @param {Number} degrees 度数
   * @method getRadian
   */
  getRadian(degrees) {
    return (degrees * Math.PI) / 180;
  }
  /**
   * 计算粒子运动向量
   * @memberof FireInit
   * @param {Number} rad1 水平方向弧度
   * @param {Number} rad2 垂直方向弧度
   * @param {Number} r 半径
   * @method getSpherical
   */
  getSpherical(rad1, rad2, r) {
    var x = Math.cos(rad1) * Math.cos(rad2) * r;
    var y = Math.cos(rad1) * Math.sin(rad2) * r;
    var z = Math.sin(rad1) * r;
    return new THREE.Vector3(
      this.b[0] * x + this.t[0] * y + this.target[0] * z,
      this.b[1] * x + this.t[1] * y + this.target[1] * z,
      this.b[2] * x + this.t[2] * y + this.target[2] * z
    );
  }
}

export default FireInit;
