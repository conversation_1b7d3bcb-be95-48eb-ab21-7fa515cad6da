<template>
  <div v-if="visible" class="largeWindowInfo">
    <div class="peopleList">
      <div class="search-top">
        <el-row :gutter="20">
          <el-col :span="1.5">
            <el-input v-model="userName" class="w-150" size="mini" placeholder="按用户名搜索" @keyup.enter.native="searchList" @blur="searchList" />
          </el-col>
          <el-col :span="1.5">
            <el-input v-model="terminalNo" class="w-150" size="mini" placeholder="按终端号搜索" @keyup.enter.native="searchList" @blur="searchList" />
          </el-col>
          <el-col :span="1.5">
            <el-select v-model="online" class="w-150" size="mini" @change="searchList">
              <el-option value="" label="全部" />
              <el-option value="1" label="在线" />
              <el-option value="0" label="离线" />
            </el-select>
          </el-col>
        </el-row>
        <span class="close" @click="handleClose">×</span>
      </div>
      <!-- 列表 -->
      <el-table :data="tableList" max-height="800" height="calc(100% - 0.15625rem)">
        <el-table-column property="name" label="姓名" />
        <el-table-column property="terminalNo" label="终端号" />
        <el-table-column label="状态">
          <template slot-scope="scope">
            <span :style="{ color: scope.row.status == 1 ? '#00cc19': '#ccc' }">{{ scope.row.status == 1 ? '在线': '离线' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-tooltip class="item" :open-delay="500" effect="dark" content="查看位置" placement="top">
              <el-button type="text" style="padding: 0;" icon="el-icon-location-outline" @click="handleClick(scope.row)" />
            </el-tooltip>
            <el-tooltip class="item" :open-delay="500" effect="dark" content="查看详情" placement="top">
              <el-button type="text" style="padding: 0;" icon="el-icon-document" @click="handleOpenDetail(scope.row)" />
            </el-tooltip>
            <el-tooltip class="item" :open-delay="500" effect="dark" content="视频呼叫" placement="top">
              <el-button type="text" style="padding: 0;" icon="el-icon-video-camera" @click="handleOpenVideo(scope.row.terminalNo)" />
            </el-tooltip>
            <el-tooltip class="item" :open-delay="500" effect="dark" content="语音呼叫" placement="top">
              <el-button type="text" style="padding: 0;" icon="el-icon-phone-outline" @click="handleOpenVoice(scope.row.terminalNo)" />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    userlistData: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      userName: '',
      online: '',
      terminalNo: '',
      searchResList: []
    }
  },
  computed: {
    tableList() {
      if (this.searchResList == null) {
        return []
      } else if (this.searchResList.length) {
        return this.searchResList
      } else {
        return this.userlistData
      }
    }
  },
  methods: {
    handleClose() {
      console.log(this.userlistData)
      this.dateRange = []
      this.$emit('update:visible', false)
    },
    handleClick(item) {
      console.log(item)
      this.$emit('changeCenter', item)
    },
    handleOpenDetail(item) {
      this.$emit('openDetail', item)
    },
    searchList() {
      const userName = this.userName.trim()
      const terminalNo = this.terminalNo.trim()
      if (this.online == '' && userName == '' && terminalNo == '') {
        this.searchResList = []
        return
      }
      const resAry = this.userlistData.filter(item => {
        let nameFlag = item.name ? item.name.includes(userName) : false
        const terminalNoFlag = item.terminalNo.includes(terminalNo)
        if (terminalNoFlag && !userName) nameFlag = true
        const onlineFlag = this.online == '' ? true : item.status == this.online
        console.log(nameFlag, terminalNoFlag, onlineFlag)
        return (nameFlag && terminalNoFlag && onlineFlag)
      })
      this.searchResList = resAry.length ? resAry : null
    },
    handleOpenVideo(terminalNo) {
      this.$emit('setTerminalNo', terminalNo)
      this.$store.commit('board/SET_CALL_TYPE', { type: 'callRecv', ARx: 1, ATx: 1, VRx: 1, VTx: 0 }) // 修改为呼出状态
      this.$store.commit('board/SET_VIDEO_VISIBLE', true)
    },
    handleOpenVoice(terminalNo) {
      this.$emit('setTerminalNo', terminalNo)
      this.$store.commit('board/SET_CALL_TYPE', { type: 'callRecv', ARx: 1, ATx: 1, VRx: 0, VTx: 0 }) // 修改为呼出状态
      this.$store.commit('board/SET_VIDEO_VISIBLE', true)
    }
  }
}
</script>

<style lang="scss" scoped>
.largeWindowInfo {
  background: rgba(1, 19, 67, 0.75);
  border-radius: 8px;
  box-shadow: 0 0 20px rgba(0, 198, 255, 0.2);
  border: 1px solid rgba(0, 198, 255, 0.3);
  padding: 20px;
  margin-left: 20px;

  .peopleList {
    .search-top {
      display: flex;
      gap: 15px;
      padding: 10px 0 20px 0;
      align-items: center;
      
      ::v-deep .el-input,
      ::v-deep .el-select {
        width: 250px;
        
        .el-input__inner {
          background: rgba(16, 24, 44, 0.6);
          border: 1px solid rgba(0, 212, 255, 0.3);
          color: #00d4ff;
          font-size: 28px;
          height: 50px;
          line-height: 50px;
          border-radius: 4px;
          transition: all 0.3s;

          &:hover, &:focus {
            border-color: #00d4ff;
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.2);
          }

          &::placeholder {
            color: rgba(0, 212, 255, 0.5);
          }
        }
      }
      
      .close {
        margin-left: auto;
        transform: translateY(-30%);
        cursor: pointer;
        font-size: 40px;
        color: rgba(0, 212, 255, 0.7);
        transition: all 0.3s;
        
        &:hover {
          color: #00d4ff;
          text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        }
      }
    }

    ::v-deep .el-table {
      background-color: transparent;
      font-size: 28px;
      
      &::before {
        display: none;
      }

      // 表格行背景
      .el-table__body tr {
        background: rgba(16, 24, 44, 0.5);
        transition: all 0.3s;
        
        &:hover > td {
          background: rgba(0, 212, 255, 0.1) !important;
        }
      }
      
      th {
        background: rgba(0, 47, 167, 0.5) !important;
        font-size: 28px;
        padding: 15px 0;
        font-weight: 500;
        color: #00d4ff;
        border-bottom: 1px solid rgba(0, 212, 255, 0.3);
      }

      td {
        background: transparent !important;
        padding: 15px 0;
        border-bottom: 1px solid rgba(0, 212, 255, 0.1);
        color: rgba(0, 212, 255, 0.9);
      }

      .cell {
        line-height: 28px;
      }

      .el-button {
        font-size: 28px;
        padding: 0;
        margin: 0 8px;
        color: #00d4ff;
        transition: all 0.3s;
        
        &:hover {
          color: #fff;
          text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        }
      }

      // 美化滚动条
      .el-table__body-wrapper::-webkit-scrollbar {
        width: 10px;
        height: 10px;
      }

      .el-table__body-wrapper::-webkit-scrollbar-thumb {
        background: rgba(0, 212, 255, 0.3);
        border-radius: 5px;
      }

      .el-table__body-wrapper::-webkit-scrollbar-track {
        background: rgba(16, 24, 44, 0.5);
      }
    }
  }
}

// 下拉选项样式
::v-deep .el-select-dropdown {
  background: rgba(1, 19, 67, 0.95);
  border: 1px solid rgba(0, 212, 255, 0.3);
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.2);

  .el-select-dropdown__item {
    font-size: 28px;
    height: 50px;
    line-height: 50px;
    color: rgba(0, 212, 255, 0.9);
    transition: all 0.3s;

    &.selected {
      background: rgba(0, 212, 255, 0.1);
      color: #00d4ff;
    }

    &:hover {
      background: rgba(0, 212, 255, 0.2);
      color: #00d4ff;
    }
  }
}

// 在线离线状态样式
::v-deep .el-table {
  .online {
    color: #52c41a;
    text-shadow: 0 0 5px rgba(82, 196, 26, 0.5);
  }
  .offline {
    color: rgba(0, 212, 255, 0.5);
  }
}

// tooltip样式
::v-deep .el-tooltip__popper {
  background: rgba(1, 19, 67, 0.95) !important;
  border: 1px solid rgba(0, 212, 255, 0.3) !important;
  color: #00d4ff !important;
  font-size: 26px;
  padding: 8px 12px;
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.2);

  .popper__arrow {
    border-top-color: rgba(0, 212, 255, 0.3) !important;
    border-bottom-color: rgba(0, 212, 255, 0.3) !important;
    
    &::after {
      border-top-color: rgba(1, 19, 67, 0.95) !important;
      border-bottom-color: rgba(1, 19, 67, 0.95) !important;
    }
  }
}
</style>
