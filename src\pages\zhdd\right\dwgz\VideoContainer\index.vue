<template>
  <div class="container" id="video" style="position: relative" v-if='show'>
    <div class="panel-video" v-for='(item,i) in videoList' :key='i'>
      <DHWSvideo
        width='440px'
        height='140px'
        :ref="`videoRef${i}`"
        :visible="item.visible"
        :video-config='item'>
      </DHWSvideo>
      <div class="text">{{item.title}}</div>
    </div>
  </div>
</template>

<script>
import DHWSvideo from '@/components/Video/VenueVideo'

export default {
  name: 'index',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    list: {
      type: Array,
      default: function () {
        return []
      }
    }
  },
  components: {
    DHWSvideo
  },
  data() {
    return {
      videoList: [],
      isVideoInitialized: false
    }
  },
  mounted() {
    this.initVideoConfigs()
  },
  methods: {
    // 初始化视频配置
    initVideoConfigs() {
      if (this.list && this.list.length > 0) {
        this.videoList = this.list.map((item, i) => ({
          visible: true,
          domId: `zhddDom${i}`,
          dom: document.getElementById("zhddDom" + i),
          ctrlCode: `zhddDom${i}`,
          title: item.name || '视频监控',
          ctrlType: 'playerWin',
          ctrlProperty: {
            displayMode: 1,
            splitNum: 1,
            channelList: [
              {
                channelId: item.code
              }
            ]
          }
        }))

        console.log(this.videoList,"firstVideo")

        // 首次加载延迟执行登录
        this.$nextTick(() => {
          this.initializeVideo()
        })
      }
    },

    // 获取视频组件引用
    getVideoComponents() {
      const components = []
      // 根据索引获取每个视频组件的引用
      for (let i = 0; i < this.videoList.length; i++) {
        const refName = `videoRef${i}`
        const comp = this.$refs[refName]
        if (comp && comp.length > 0) {
          components.push(comp[0])
        }
      }
      return components
    },

    // 初始化视频（只执行一次登录）
    initializeVideo() {
      if (this.isVideoInitialized || !this.videoList.length) return

      // 获取所有视频组件实例
      this.$nextTick(() => {
        const videoComponents = this.getVideoComponents()
        if (!videoComponents.length) {
          console.error('未找到视频组件')
          return
        }

        // 只在第一个视频组件上执行登录
        const firstVideoComponent = videoComponents[0]
        if (firstVideoComponent && typeof firstVideoComponent.loginVideo === 'function') {
          console.log('初始化视频登录')
          firstVideoComponent.loginVideo()
          this.isVideoInitialized = true

          // 登录成功后，初始化其他视频组件
          this.$nextTick(() => {
            this.createAllVideos(videoComponents)
          })
        }
      })
    },

    // 创建所有视频窗口
    createAllVideos(components) {
      if (!components) {
        components = this.getVideoComponents()
      }

      // 确保所有视频组件都创建了视频窗口
      components.forEach((component, index) => {
        if (component && typeof component.createMinitor === 'function') {
          console.log(`创建视频 ${index}`)
          component.createMinitor()
        }
      })
    },

    // 更新视频源
    updateVideoSources() {
      if (!this.isVideoInitialized || !this.videoList.length) return

      this.$nextTick(() => {
        const videoComponents = this.getVideoComponents()
        if (!videoComponents.length) return

        // 更新所有视频组件的视频源
        videoComponents.forEach((component, index) => {
          if (component && typeof component.createMinitor === 'function') {
            console.log(`更新视频源 ${index}`)
            component.createMinitor()
          }
        })
      })
    },

    // 停止所有视频
    stopAllVideos() {
      this.$nextTick(() => {
        const videoComponents = this.getVideoComponents()
        if (!videoComponents.length) return

        // 隐藏所有视频
        videoComponents.forEach((component, index) => {
          if (component && typeof component.changeVisible === 'function') {
            // 设置视频为不可见
            if (this.videoList[index]) {
              this.videoList[index].visible = false
              component.changeVisible()
            }
          }
        })
      })
    }
  },
  watch: {
    list: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.videoList = newVal.map((item, i) => ({
            visible: true,
            domId: `zhddDom${i}`,
            dom: document.getElementById("zhddDom" + i),
            ctrlCode: `zhddDom${i}`,
            title: item.name || '视频监控',
            ctrlType: 'playerWin',
            ctrlProperty: {
              displayMode: 1,
              splitNum: 1,
              channelList: [
                {
                  channelId: item.code
                }
              ]
            }
          }))

          console.log(this.videoList, "videoList")

          // 如果已初始化，则只更新视频源，不再登录
          if (this.isVideoInitialized) {
            this.$nextTick(() => {
              this.updateVideoSources()
            })
          } else {
            // 首次加载，执行登录
            this.$nextTick(() => {
              this.initializeVideo()
            })
          }
        } else {
          // 列表为空时，停止所有视频
          this.stopAllVideos()
          this.videoList = []
        }
      },
      deep: true,
      immediate: true
    },

    show(newVal) {
      // 当显示状态改变时，更新视频状态
      if (newVal && this.videoList.length > 0) {
        if (this.isVideoInitialized) {
          // 已初始化则更新视频源
          this.$nextTick(() => {
            this.updateVideoSources()
          })
        } else {
          // 首次显示时初始化视频
          this.$nextTick(() => {
            this.initializeVideo()
          })
        }
      } else if (!newVal) {
        // 隐藏时停止所有视频
        this.stopAllVideos()
      }
    }
  },
  beforeDestroy() {
    // 组件销毁前停止所有视频并登出
    this.stopAllVideos()

    // 登出大华视频
    this.$nextTick(() => {
      const videoComponents = this.getVideoComponents()
      if (videoComponents.length > 0) {
        const firstVideoComponent = videoComponents[0]
        if (firstVideoComponent && typeof firstVideoComponent.logOut === 'function') {
          firstVideoComponent.logOut()
        }
      }
    })
  }
}
</script>

<style scoped lang='less'>
/* 视频 */
.videoBox {
  width: 400px;
  height: 198px;
}

.container {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.panel-video {
  width: 440px;
  height: 250px;
  position: relative;
  margin: 0 40px 0 30px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
}

.text {
  width: 100%;
  position: absolute;
  bottom: 20px;
  font-size: 30px;
  color: #fff;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-left: 30px;
  padding: 2px 0;
  background: url("@/assets/zhdd/tit_bg.png") no-repeat;
}
</style>