import {request} from '@/utils/request'
/** 监控抓拍 */
// 列表
export function captureList(params) {
  return request({
    url: '/business/capture/list',
    method: 'get',
    params
  })
}
// 我的列表
export function myCaptureList(params) {
  return request({
    url: '/business/capture/myCases',
    method: 'get',
    params
  })
}
// 查询一个
export function captureOne(params) {
  return request({
    url: '/business/capture/' + params,
    method: 'get'
  })
}
// 新增
export function addCapture(data) {
  return request({
    url: '/business/capture/add',
    method: 'post',
    data
  })
}
// 编辑
export function editCapture(data) {
  return request({
    url: '/business/capture/edit',
    method: 'post',
    data
  })
}

// 删除
export function removeCapture(data) {
  return request({
    url: '/business/capture/remove/' + data,
    method: 'post'
  })
}
// 撤销
export function revokeCapture(data) {
  return request({
    url: '/business/capture/revoke',
    method: 'post',
    data
  })
}

// 操作日志
export function getLogList(params) {
  return request({
    url: '/business/log/list',
    method: 'get',
    params
  })
}
// 获取指挥中心审核时间
export function getConfigTime(params) {
  return request({
    url: '/business/capture/config/1',
    method: 'get',
    params
  })
}
// 设置指挥中心审核时间
export function editConfigTime(params) {
  return request({
    url: '/business/capture/config/1',
    method: 'get',
    params
  })
}
// 审批确认
export function captureApprove(data) {
  return request({
    url: '/business/capture/approve',
    method: 'post',
    data
  })
}
