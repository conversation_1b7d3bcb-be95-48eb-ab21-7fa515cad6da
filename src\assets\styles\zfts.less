/* zfts模块公共样式 */

/* 通用标题样式 */
.zfts-title {
  font-size: 32px;
  font-weight: bold;
  color: #fff;
  margin-bottom: 20px;
}

/* 数值显示样式 */
.zfts-number {
  font-family: DINCondensed;
  font-size: 48px;
  font-style: italic;
  font-weight: bold;
}

.zfts-number-yellow {
  color: #eed252;
}

.zfts-number-blue {
  color: #34DFE3;
}

.zfts-number-green {
  color: #45f95e;
}

/* 单位样式 */
.zfts-unit {
  font-size: 20px;
  margin-left: 10px;
}

/* 容器样式 */
.zfts-container {
  width: 100%;
  padding: 20px;
  box-sizing: border-box;
  position: relative;
}

/* 图表容器样式 */
.zfts-chart-container {
  width: 100%;
  height: 300px;
  position: relative;
}

/* 无数据提示样式 */
.zfts-no-data {
  width: 100%;
  height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 30px;
  color: #fff;
}

/* 动画效果 */
.zfts-float-animation {
  animation: zftsFloat infinite 5s;
}

@keyframes zftsFloat {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(20px);
  }
  100% {
    transform: translateY(0px);
  }
} 