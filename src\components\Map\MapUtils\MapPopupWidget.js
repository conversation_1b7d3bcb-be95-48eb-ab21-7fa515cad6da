class MapPopupWidget {
  constructor({ view }) {
    this.view = view;

    this._x = -9999;
    this._y = -9999;
    this._display = "none";
    this._popupRef;
    this.index = 1; // 当前页
    this.isMultiple = false;
  }

  _watchPoint(view, point) {
    const that = this;
    this._watchHandle = view.watch("viewpoint", () => {
      that._calculatePosition(view, point);
    });
  }
  _initDom() {
    const mapDom = view.container;
    const mapPopupContainer = document.createElement("div");
    mapPopupContainer.classList.add("mapPopup");
    mapPopupContainer.classList.add("hide");
    mapPopupContainer.setAttribute(
      "style",
      `left:${this._x}px;top:${this._y}px;display:${this._display};width:${
        this.width ? this.width + "px" : "300px"
      }`
    );

    // header
    const headDom = document.createElement("div");
    headDom.setAttribute("class", "header");
    mapPopupContainer.appendChild(headDom);
    const titleDom = document.createElement("div");
    titleDom.setAttribute("class", "title");
    titleDom.setAttribute("id", "mapPopupTitle");
    titleDom.innerHTML = this.title;
    headDom.appendChild(titleDom);
    const closeDom = document.createElement("span");
    closeDom.setAttribute("class", "closeBtn");
    closeDom.innerHTML = "X";
    headDom.appendChild(closeDom);
    const that = this;
    closeDom.addEventListener("click", () => {
      that.close();
    });

    // body
    const bodyDom = document.createElement("div");
    bodyDom.setAttribute("class", "body");
    const contentDom = document.createElement("div");
    contentDom.setAttribute("class", "bodyContent");
    contentDom.setAttribute("id", "mapPopupBodyContent");
    bodyDom.appendChild(contentDom);
    if (this.content) {
      contentDom.appendChild(this.isMultiple ? this.content[0] : this.content);
    } else if (this.data) {
      const contentDoms = this._createDataDom(
        this.isMultiple ? this.data[0] : this.data
      );
      for (let i = 0; i < contentDoms.length; i++) {
        const item = contentDoms[i];
        contentDom.appendChild(item);
      }
    }

    // this._addPageDom(contentDom);

    mapPopupContainer.appendChild(bodyDom);

    mapDom.appendChild(mapPopupContainer);
    this._popupRef = mapPopupContainer;
  }

  _createDataDom(data) {
    if (typeof data !== "object") {
      throw new Error("参数data，必须为普通对象！");
    }
    try {
      const contentDoms = [];
      for (let i = 0; i < data.length; i++) {
        const item = data[i];
        const itemDom = document.createElement("div");
        itemDom.setAttribute("class", "item");
        const titleDom = document.createElement("span");
        titleDom.innerHTML = `${item.key}：`;
        const valueDom = document.createElement("span");
        valueDom.innerHTML = item.value;
        itemDom.appendChild(titleDom);
        itemDom.appendChild(valueDom);
        contentDoms.push(itemDom);
      }
      return contentDoms;
    } catch (error) {
      throw new Error("参数data，必须为普通对象！");
    }
  }

  _calculatePosition(view, point) {
    if (view) {
      let rect = view?.container.getBoundingClientRect();
      let headerHeight = rect.top; // 顶部header高度
      let sideMenuWidth = rect.left; // 侧边菜单宽度
      let screenPoint = view?.toScreen(point);
      this._x = screenPoint.x + sideMenuWidth;
      this._y = screenPoint.y + headerHeight;
      this._popupRef.style.left = `${this._x + this.offset[0]}px`;
      this._popupRef.style.top = `${this._y + this.offset[1]}px`;
      const that = this;
      setTimeout(() => {
        this._display = "block";
        that._popupRef.style.display = "block";
      }, 100);

      setTimeout(() => {
        that._popupRef.classList.remove("hide");
        that._popupRef.classList.add("show");
      }, 300);
    }
  }

  showAt({ point, title, onClose, data, content, width, offset = [0, 0] }) {
    point = { ...point, z: 0 };
    this.data = data;
    this.title = title;
    this.onClose = onClose;
    this.point = point;
    this.content = content;
    this.width = width;
    this.offset = offset;
    this.isMultiple = this._isMultiple();
    if (!this._popupRef) {
      this._initDom();
    } else {
      let titleDom = document.getElementById("mapPopupTitle");
      titleDom.innerHTML = title;
      let contentDom = document.getElementById("mapPopupBodyContent");
      contentDom.innerHTML = "";

      if (content) {
        contentDom.appendChild(this.isMultiple ? content[0] : content);
      } else if (data) {
        const contentDoms = this._createDataDom(
          this.isMultiple ? data[0] : data
        );
        for (let i = 0; i < contentDoms.length; i++) {
          const item = contentDoms[i];
          contentDom.appendChild(item);
        }
      }
    }

    if (
      (this.isMultiple && !this.data) ||
      (this.isMultiple && this.data.length > 1)
    ) {
      this.index = 1;
      this._addPageDom();
    }

    this._calculatePosition(view, point);
    if (this._watchHandle) {
      this._watchHandle.remove();
    }
    this._watchPoint(view, point);
    this.point = point;
  }
  close() {
    this._popupRef.classList.remove("show");
    this._popupRef.classList.add("hide");
    if (this._watchHandle) {
      this._watchHandle.remove();
    }
    setTimeout(() => {
      this._popupRef.style.display = "none";
      if (this.onClose) {
        this.onClose();
      }
    }, 300);
    this.index = 1;
  }
  destroy() {
    if (this._popupRef) this._popupRef.parentNode.removeChild(this._popupRef);
    this._popupRef = undefined;
  }

  // 判断是否需要多页切换
  _isMultiple() {
    let isMultiple = false;
    if (Array.isArray(this.content)) {
      isMultiple = true;
    }

    if (Array.isArray(this.data) && Array.isArray(this.data[0])) {
      isMultiple = true;
    }
    return isMultiple;
  }
  _addPageDom(contentDom) {
    contentDom = contentDom || document.getElementById("mapPopupBodyContent");
    const pageDom = document.createElement("div");
    pageDom.setAttribute("class", "pageContainer");
    contentDom.appendChild(pageDom);

    const preDom = document.createElement("span");
    preDom.setAttribute(
      "class",
      "esri-popup__icon esri-icon-left-triangle-arrow esri-popup__pagination-previous-icon"
    );
    pageDom.appendChild(preDom);
    preDom.addEventListener("click", () => {
      const prePage = this.index - 1;
      if (prePage > 0) {
        this.index = prePage;
        this._pageChange(this.index - 1);
      }
    });

    const pageText = document.createElement("span");
    pageText.innerText = `${this.index}/${
      this.data ? this.data.length : this.content.length
    }`;
    pageDom.appendChild(pageText);

    const nextDom = document.createElement("span");
    nextDom.setAttribute(
      "class",
      "esri-popup__icon esri-icon-right-triangle-arrow esri-popup__pagination-next-icon"
    );
    pageDom.appendChild(nextDom);
    nextDom.addEventListener("click", () => {
      const nextPage = this.index + 1;
      const contentLenght = this.data ? this.data.length : this.content.length;
      if (nextPage < contentLenght + 1) {
        this.index = nextPage;
        this._pageChange(this.index - 1);
      }
    });
  }
  _pageChange(index) {
    let contentDom = document.getElementById("mapPopupBodyContent");
    contentDom.innerHTML = "";

    if (this.content) {
      contentDom.appendChild(this.isMultiple ? this.content[index] : content);
    } else if (this.data) {
      const contentDoms = this._createDataDom(
        this.isMultiple ? this.data[index] : this.data
      );
      for (let i = 0; i < contentDoms.length; i++) {
        const item = contentDoms[i];
        contentDom.appendChild(item);
      }
    }
    this._addPageDom();
  }
}

export default MapPopupWidget;
