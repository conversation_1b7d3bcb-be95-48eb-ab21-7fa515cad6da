<template>
  <div :id="chartId" :style="{ width: width, height: height }"></div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'CirclePercentChart',
  props: {
    chartId: {
      type: String,
      required: true
    },
    percentage: {
      type: Number,
      required: true,
      validator: value => value >= 0 && value <= 100
    },
    width: {
      type: String,
      default: '220px'
    },
    height: {
      type: String,
      default: '220px'
    },
    color: {
      type: Array,
      default: () => ['#37a2da', '#32c5e9', '#67e0e3']
    }
  },

  data() {
    return {
      chart: null,
      timerId: null
    }
  },

  methods: {
    getChartOption() {
      return {
        title: {
          text: `${this.percentage}%`,
          x: 'center',
          y: 'center',
          textStyle: {
            fontSize: '32',
            color: this.color[0],
            fontFamily: 'DINAlternate-Bold'
          }
        },
        series: [
          {
            name: '最外层环',
            type: 'pie',
            clockWise: false,
            radius: ['65%', '70%'],
            itemStyle: {
              normal: {
                label: { show: false },
                labelLine: { show: false }
              }
            },
            hoverAnimation: false,
            data: [
              {
                value: this.percentage,
                name: '占比',
                itemStyle: {
                  normal: {
                    color: {
                      type: 'linear',
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        { offset: 0, color: this.color[0] },
                        { offset: 1, color: this.color[1] }
                      ]
                    }
                  }
                }
              },
              {
                value: 100 - this.percentage,
                name: '剩余',
                itemStyle: {
                  normal: { color: '#eee' }
                }
              }
            ]
          },
          {
            name: '中间环',
            type: 'pie',
            clockWise: false,
            radius: ['55%', '60%'],
            itemStyle: {
              normal: {
                label: { show: false },
                labelLine: { show: false }
              }
            },
            hoverAnimation: false,
            data: [
              {
                value: this.percentage,
                name: '占比',
                itemStyle: {
                  normal: {
                    color: {
                      type: 'linear',
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        { offset: 0, color: this.color[1] },
                        { offset: 1, color: this.color[2] }
                      ]
                    }
                  }
                }
              },
              {
                value: 100 - this.percentage,
                name: '剩余',
                itemStyle: {
                  normal: { color: '#eee' }
                }
              }
            ]
          }
        ]
      }
    },

    initChart() {
      const chartDom = document.getElementById(this.chartId)
      if (chartDom) {
        this.chart = echarts.init(chartDom)
        this.updateChart()
      }
    },

    updateChart() {
      if (this.chart) {
        this.chart.setOption(this.getChartOption())
      }
    },

    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    }
  },

  watch: {
    percentage: {
      handler() {
        this.updateChart()
      }
    }
  },

  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
    window.addEventListener('resize', this.handleResize)
  },

  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
    window.removeEventListener('resize', this.handleResize)
    if (this.timerId) {
      clearInterval(this.timerId)
    }
  }
}
</script>