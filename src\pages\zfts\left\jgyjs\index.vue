<template>
  <div>
    <CommonTitle text='监管一件事'></CommonTitle>
    <div class="tj">
      <li v-for="(item,index) in tjList" :key="index" style="flex: 0.2">
        <img :src="item.icon" alt="" class="breath-light" />
        <div class="sp">
          <div class="sp-ys">
            {{item.value}}
            <span class="s-font-28">{{item.unit}}</span>
          </div>
          <div>{{item.name}}</div>
        </div>
      </li>
      <div class="table" style="height: 380px; width: 850px">
        <div class="th">
          <div class="th_td" style="flex: 0.2">序号</div>
          <div class="th_td" style="flex: 0.2">年度</div>
          <div class="th_td" style="flex: 0.2">地区</div>
          <div class="th_td" style="flex: 0.4">重点任务选题</div>
          <div class="th_td" style="flex: 0.4">上报部门</div>
        </div>
        <div
          class="tbody"
          id="box1"
          @mouseover="mouseenterEvent1()"
          @mouseleave="mouseleaveEvent1()"
        >
          <div class="tr" v-for="(item,index) in tableData1" :key="index">
            <div class="tr_td" style="flex: 0.2">{{index + 1}}</div>
            <div class="tr_td" style="flex: 0.2">{{item.nd}}</div>
            <div class="tr_td" style="flex: 0.2">{{item.dq}}</div>
            <div class="tr_td" style="flex: 0.4" :title="item.zdrwxt">
              {{item.zdrwxt}}
            </div>
            <div
              class="tr_td"
              style="flex: 0.4"
              :title="item.cydw"
              @click="openYJS(item.xh)"
            >
              {{item.cydw}}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import {indexApi} from '@/api/indexApi'
export default {
  name: 'index',
  components: {
    CommonTitle
  },
  data() {
    return {
      year: localStorage.getItem("year"),
      city: localStorage.getItem("city"),
      dom1: null,
      time1: null,
      //监管一件事
      tjList: [
        {
          icon: require("@/assets/zfts/zfts_left.png"),
          name: "重点任务",
          value: "20",
          unit: "件",
        },
        // {
        //   icon: "/static/images/zfts/dyzs.png",
        //   name: "监管一件事",
        //   value: "71",
        //   unit: "件",
        // },
        // {
        //   icon: "/static/images/zfts/jcdw.png",
        //   name: "应用场景",
        //   value: "3",
        //   unit: "个",
        // },
      ],
      tableData1: [],
    }
  },
  computed: {},
  mounted() {
    this.$bus.$on('cityChange', (city) => {
      this.city = city;
      this.initApi(city,localStorage.getItem("year"));
    })
    this.$bus.$on('yearChange', (year) => {
      this.year = year;
      this.initApi(localStorage.getItem("city"),year);
    })
    this.initApi(localStorage.getItem("city"),localStorage.getItem("year"));
    // 表格滚动s
    this.dom1 = document.getElementById("box1");
    this.mouseleaveEvent1();
  },
  methods: {
    initApi(city,year) {
      // 监管表格
      indexApi("/csdn_yjyp23", { dq: city,nd: year }).then((res) => {
        console.log(res)
        this.tableData = res.data;
        const res1 = new Map();
        for (let i of res.data) {
          if (!res1.has(i.xh)) {
            res1.set(i.xh, i);
          }
        }
        let str = [...res1.values()];
        this.tableData1 = str;
        this.tjList[0].value = str.length;
      });
    },
    mouseenterEvent1() {
      clearInterval(this.time1);
    },
    mouseleaveEvent1() {
      this.time1 = setInterval(() => {
        this.dom1.scrollBy({
          top: 71,
          behavior: "smooth",
        });
        if (
          this.dom1.scrollTop >=
          this.dom1.scrollHeight - this.dom1.offsetHeight
        ) {
          this.dom1.scrollTop = 0;
        }
      }, 1500);
    },
    openYJS() {

    }
  },
  watch: {}
}
</script>

<style scoped lang='less'>
/* 监管一件事 */
.tj {
  width: 100%;
  height: 400px;
  display: flex;
  padding: 10px;
  box-sizing: border-box;
}

.tj li {
  width: 50%;
  height: 100%;
  list-style: none;
  position: relative;
  padding-top: 110px;
}

.tj li img {
  width: 190px;
  height: 174px;
}

.jdt {
  width: 100%;
  height: 350px;
  box-sizing: border-box;
}

.tj li:first-child .sp-ys {
  font-size: 65px;
  font-style: italic;
  color: #00fffc;
  font-family: DINCondensed;
}

.tj li:nth-child(2) .sp-ys {
  font-size: 65px;
  font-style: italic;
  color: #08a0f5 !important;
  font-family: DINCondensed;
}

.tj li:nth-child(3) .sp-ys {
  font-size: 48px;
  color: #ffb436 !important;
}

.sp {
  font-size: 36px;
  color: #fff;
  position: absolute;
  /* top: -13px;
  left: 155px;
  font-weight: bolder; */
  top: 45px;
  width: 190px;
  text-align: center;
}

/* 表格 */
.table {
  width: 100%;
  /* height: 500px; */
  padding: 10px 15px;
  box-sizing: border-box;
}

.table .th {
  width: 100%;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  font-weight: 700;
  font-size: 28px;
  line-height: 60px;
  color: #ffffff;
}

.table .th_td {
  letter-spacing: 0px;
  text-align: center;
}

.table .tbody {
  width: 100%;
  height: calc(100% - 59px);
  /* overflow-y: auto; */
  overflow: hidden;
}

.table .tbody:hover {
  overflow-y: auto;
}

.table .tbody::-webkit-scrollbar {
  width: 4px;
  /*滚动条整体样式*/
  height: 4px;
  /*高宽分别对应横竖滚动条的尺寸*/
}

.table .tbody::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: #20aeff;
  height: 8px;
}

.table .tr {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  height: 70px;
  line-height: 70px;
  font-size: 24px;
  color: #ffffff;
  cursor: pointer;
  border-top: 1px solid #959aa1;
  border-image: linear-gradient(to right, #e9f5ff3b, #f5ffffd4, #e9f5ff3b) 1;
  box-sizing: border-box;
}

.table .tr:nth-child(2n) {
  background: rgba(50, 134, 248, 0.2);
}

.table .tr:nth-child(2n + 1) {
  background: rgba(50, 134, 248, 0.12);
}

.table .tr:hover {
  background-color: #0074da75;
}

.table .tr_td {
  letter-spacing: 0px;
  text-align: center;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

</style>