// base color
$blue:#324157;
$light-blue:#3a71a8;
$red:#c03639;
$pink: #e65d6e;
$green: #30b08f;
$tiffany: #4ab7bd;
$yellow:#fec171;
$panGreen: #30b08f;

// sidebar
$menutext:#bfcbd9;
$menuactivetext:#409eff;
$submenuactivetext:#f4f4f5; // https://github.com/ElemeFE/element/issues/12951

$menubg:#304156;
$menuhover:#263445;
$sidebartitle: #fff;

$menulightbg:#fff;
$menulighthover:#f0f1f5;
$sidebarlighttitle: #001529;

$submenubg:#1f2d3d;
$submenuhover:#001528;

$sidebarwidth: 200px;
@function pxtorem($px) {
  @return round($px / 192 * 100000) / 100000 * 1rem;
}

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menutext: $menutext;
  menuactivetext: $menuactivetext;
  submenuactivetext: $submenuactivetext;
  menubg: $menubg;
  menuhover: $menuhover;
  menulightbg: $menulightbg;
  menulighthover: $menulighthover;
  submenubg: $submenubg;
  submenuhover: $submenuhover;
  sidebarwidth: $sidebarwidth;
  sidebartitle: $sidebartitle;
  sidebarlighttitle: $sidebarlighttitle;
}
