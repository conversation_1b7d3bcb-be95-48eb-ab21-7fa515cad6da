<template>
  <div>
    <CommonTitle text="累计服务人次"></CommonTitle>
    <div class="wrap-container" id="barChart"></div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import { getTotalService } from '@/api/gzfw/index.js'
export default {
  name: 'index',
  components: {
    CommonTitle,
  },
  data() {
    return {
      chartsData: [],
    }
  },
  computed: {},
  mounted() {
    getTotalService().then((res) => {
      this.chartsData = res.data.map((item) => {
        return {
          name: item.key,
          value: item.value,
        }
      })
      this.initCharts()
    })
  },
  methods: {
    initCharts() {
      let myChart = this.$echarts.init(document.getElementById('barChart'))
      let option = {
        tooltip: {
          trigger: 'axis',
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        grid: {
          left: '8%',
          right: '6%',
          top: '18%',
          bottom: '10%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            data: this.chartsData.map((item) => item.name),
            axisLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)', // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              textStyle: {
                color: '#ffffffcc',
                fontSize: 28,
                padding: [10, 0],
              },
            },
          },
        ],
        yAxis: [
          {
            name: '单位(人次)',
            type: 'value',
            nameTextStyle: {
              fontSize: 24,
              color: '#ffffffcc',
              padding: 20,
            },
            splitLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)',
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 28,
                color: '#ffffffcc',
              },
            },
          },
        ],
        series: [
          {
            name: '',
            type: 'bar',
            barWidth: '14%',
            barBorderRadius: 8,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: '#007BFF',
                  },
                  {
                    offset: 1,
                    color: 'rgba(0,192,255,0)',
                  },
                ]),
                barBorderRadius: 4,
              },
            },
            data: this.chartsData.map((item) => item.value),
          },
        ],
      }
      myChart.setOption(option)
      myChart.getZr().on('mousemove', (param) => {
        myChart.getZr().setCursorStyle('default')
      })
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
.wrap-container {
  width: 100%;
  height: 580px;
  padding: 20px 0;
  box-sizing: border-box;
}
</style>