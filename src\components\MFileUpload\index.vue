<template>
  <div class="m-uplaod" :class="{ hide: isHide }">
    <el-upload
      ref="upload"
      multiple
      :limit="limit"
      list-type="picture-card"
      :accept="accept"
      action="/zqzfj/system/file/upload"
      :auto-upload="false"
      :headers="headers"
      :file-list="formFiles"
      :before-upload="beforeUpload"
      :on-preview="handlePictureCardPreview"
      :before-remove="beforeRemove"
      :on-success="handleSuccess"
      :on-error="handleError"
      :data="exData"
      :on-change="handleChange"
      :on-remove="handleRemove"
      name="files"
      :disabled="disabled"
    >
      <i class="el-icon-plus" />
    </el-upload>
    <!-- 图片预览 -->
    <!-- <el-dialog :close-on-click-modal="false" :visible.sync="dialogVisible" append-to-body> -->
    <el-image v-show="false" ref="imgList" :src="dialogImageUrl" style="width: 100%;" :preview-src-list="srcList" />
    <!-- <img width="100%" :src="dialogImageUrl"> -->
    <!-- </el-dialog> -->
  </div>
</template>

<script>
import { removeFiles } from '@/api/supervise/swit'
import { getToken } from '@/utils/auth'
import * as imageConversion from 'image-conversion'

export default {
  props: {
    exData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    disabled: {
      type: Boolean,
      default: false
    },
    limit: {
      type: Number,
      default: 4
    },
    accept: {
      type: String,
      default: '.jpg,.jpeg,.png,.JPG,.JPEG,.PNG'
    },
    params: {
      type: Object,
      default: () => {
        return {}
      }
    },
    fileList: {
      type: Array,
      default: () => {
        return []
      }
    },
    notUploadMsg: {
      type: String,
      default: '未上传图片'
    },
    required: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      headers: {Authorization: 'Bearer ' + getToken()},
      formFiles: this.fileList,
      dialogVisible: false,
      dialogImageUrl: '',
      isHide: this.fileList.length == this.limit
    }
  },
  computed: {
    srcList() {
      return this.formFiles.map(item => item.url)
    }
  },
  watch: {
    fileList: {
      handler: function(nVal) {
        this.formFiles = [...nVal]
        this.isHide = nVal.length == this.limit
      },
      deep: true
    }
  },
  methods: {
    beforeUpload(file) {
      return new Promise((resolve, reject) => {
        let isLt2M = (file.size / 1024 / 1024) < 4 // 判定图片大小是否小于4MB
        if (isLt2M) resolve(file)
        imageConversion.compressAccurately(file, 400).then(uploadBlob => {
          const tFile = new window.File([uploadBlob], file.name, {type: file.type})
          resolve(tFile)
        }).catch(() => {
          reject(file)
        })
      })
    },
    handleChange(file, fileList) {
      this.isHide = fileList.length == this.limit
    },
    handleRemove(file, fileList) {
      setTimeout(() => {
        this.isHide = fileList.length == this.limit
      }, 1000)
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.imgList.clickHandler()
      })
    },
    beforeRemove(file) {
      return new Promise((resolve, reject) => {
        this.$confirm('是否确认删除？ 删除后将无法恢复', '提示', { type: 'warning' }).then(() => {
          if (file.fileId) {
            removeFiles(file.fileId).then(resolve).catch(reject)
          } else {
            resolve()
          }
        }).catch(() => {
          reject()
        })
      })
    },
    handleSuccess() {
      const hasFailFile = this.$refs.upload.uploadFiles.some(item => item.response && item.response.code != 200)
      if (hasFailFile) {
        this.$message.error('文件上传失败，请重新上传')
        this.$emit('error')
      } else {
        this.$emit('uploadSucces')
      }
    },
    handleError() {
      this.$message.error('文件上传失败，请重新上传')
      this.$emit('error')
    },
    isNotUploadFile() {
      if (!this.$refs.upload.uploadFiles.length && !this.formFiles.length && this.required) {
        // 判断是否有文件存在，判断必传的条件
        this.$message.error(this.notUploadMsg)
        return true
      } else {
        return false
      }
    },
    clear() {
      this.$refs.upload.clearFiles()
      this.isHide = this.fileList.length == this.limit
    },
    submitFile() {
      const uploadFile = this.$refs.upload.uploadFiles.some(item => {
        if (item.response && item.response.code != 200) {
          item.status = 'ready'
          item.percentage = 0
        }
        return (typeof item.percentage == 'number' && item.percentage !== 100)
      })
      if (uploadFile) {
        // 存在需要上传的文件，调用上传
        this.$refs.upload.submit()
      } else {
        this.$emit('uploadSucces')
      }
    }
  }
}
</script>

<style scoped lang="scss">
.m-uplaod {
  &.hide {
    ::v-deep .el-upload--picture-card {
      display: none;
    }
  }
}
</style>
