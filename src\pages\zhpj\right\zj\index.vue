<template>
  <div>
    <CommonTitle :text="title"></CommonTitle>
    <div class="wrap-container" id="chartzj"></div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
export default {
  name: 'index',
  components: {
    CommonTitle,
  },
  data() {
    return {
      title: '整洁：86分',
      chartsData: [
        {
          name: '街道环境',
          max: 100,
          value: 70,
          value1: 60,
        },
        {
          name: '市容秩序',
          max: 100,
          value: 70,
          value1: 60,
        },
        {
          name: '卫生保洁',
          max: 100,
          value: 70,
          value1: 60,
        },
        {
          name: '绿化养护',
          max: 100,
          value: 70,
          value1: 60,
        },
        {
          name: '公共设施',
          max: 100,
          value: 70,
          value1: 60,
        },
        {
          name: '建筑外观',
          max: 100,
          value: 70,
          value1: 60,
        },
      ],
    }
  },
  computed: {},
  mounted() {
    this.initRadar()
  },
  methods: {
    initRadar() {
      let myChart = this.$echarts.init(document.getElementById('chartzj'))
      let option = {
        color: ['#22E197'],
        tooltip: {
          show: true,
          trigger: 'item',
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        legend: {
          show: true,
          icon: 'circle',
          right: '10%',
          top: '10%',
          itemWidth: 16, // 图例标记的图形宽度。
          itemHeight: 16, // 图例标记的图形高度。
          itemGap: 20,
          orient: 'vertical',
          textStyle: {
            fontSize: 28,
            color: '#CFD7E5',
          },
          data: [
            {
              name: '当前水平',
              textStyle: {
                color: '#CFD7E5', // 第一个图例文字颜色
              },
              itemStyle: {
                color: '#22E197', // 第一个图例文字颜色
              },
            },
            {
              name: '上月水平',
              textStyle: {
                color: '#CFD7E5', // 第二个图例文字颜色
              },
              itemStyle: {
                color: '#00EAFF', // 第一个图例文字颜色
              },
            },
          ],
        },
        radar: {
          center: ['45%', '48%'],
          radius: '50%',
          name: {
            textStyle: {
              fontSize: 28,
              color: '#CFD7E5',
              width: 100,
              lineHeight: 40,
              padding: [0, 23, -10, 24],
            },
          },
          startAngle: 90,
          splitNumber: 3,
          shape: 'circle',
          splitArea: {
            areaStyle: {
              color: ['#0D306980', '#0E265B80', '#0D306980', '#0E265B80'],
            },
          },
          axisLabel: {
            show: false,
            fontSize: 36,
            color: '#CFD7E5',
            textStyle: {
              width: 200,
            },
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#0091FF50', //
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#0B3978', //
            },
          },
          indicator: this.chartsData.map((x) => {
            return {
              name: x.name,
              max: x.max,
            }
          }),
        },
        series: [
          {
            name: '当前水平',
            type: 'radar',
            symbol: 'circle',
            symbolSize: 0,
            lineStyle: {
              type: 'solid',
              color: '#22E197',
              width: 2,
            },
            data: [this.chartsData.map((x) => x.value)],
          },
          {
            name: '上月水平',
            type: 'radar',
            symbol: 'circle',
            symbolSize: 0,
            lineStyle: {
              type: 'solid',
              color: '#00EAFF',
              width: 2,
            },
            data: [this.chartsData.map((x) => x.value1)],
          },
        ],
      }
      myChart.setOption(option)
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
.wrap-container {
  width: 100%;
  height: 360px;
  margin-bottom: 40px;
}
</style>