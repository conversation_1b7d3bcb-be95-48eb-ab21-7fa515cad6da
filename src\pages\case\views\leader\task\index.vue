<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" :inline="true" label-width="100px">
      <el-form-item label="搜索" prop="searchValue">
        <el-input
          v-model="queryParams.searchValue"
          placeholder="请输入搜索内容"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="规定完成时间" prop="limitTime">
        <el-date-picker v-model="queryParams.limitTime" clearable
                        size="small"
                        type="date"
                        value-format="yyyy-MM-dd"
                        placeholder="选择规定完成时间"
        />
      </el-form-item>
      <el-form-item label="实际完成时间" prop="finishTime">
        <el-date-picker v-model="queryParams.finishTime" clearable
                        size="small"
                        type="date"
                        value-format="yyyy-MM-dd"
                        placeholder="选择实际完成时间"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button

          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >
          新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button

          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="taskList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="交办人" align="center" prop="userName" />
      <el-table-column label="执行人" align="center" prop="workUserName" />
      <el-table-column label="规定完成时间" align="center" prop="limitTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.limitTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="实际完成时间" align="center" prop="finishTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.finishTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >
            修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改领导交办对话框 -->
    <div>
      <el-dialog class="m-dialog" :close-on-click-modal="false" :title="title" :visible.sync="open">
        <el-scrollbar style="height: 100%;">
          <el-row :gutter="15" style="margin-right: 10px;">
            <el-form ref="form" :model="form" :rules="rules" label-width="100px">
              <el-col :span="12">
                <el-form-item label="交办人" prop="userId">
                  <el-input v-model="form.userName" placeholder="请输入交办人" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="执行人" prop="workUserName">
                  <el-input v-model="form.workUserName" placeholder="请输入执行人">
                    <el-button slot="append" type="primary" @click="showWorkUser = true">选择</el-button>
                  </el-input>
                  <userSelect v-model="showWorkUser" :multiple="false" :select-user-keys="[form.workUserId+'']" :default-expanded-keys="form.workUserId ? [form.workUserId+''] : ['100']" @confirm="handleConfirm" />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="规定完成时间" prop="limitTime">
                  <el-date-picker v-model="form.limitTime" clearable
                                  size="small"
                                  type="datetime"
                                  value-format="yyyy-MM-dd hh:mm:ss"
                                  placeholder="选择规定完成时间"
                  />
                </el-form-item>
              </el-col>
              <!-- <el-col :span="12">
                <el-form-item label="类型" prop="type">
                  <el-select v-model="form.type" placeholder="请选择类型" :style="{ width: '100%' }">
                    <el-option label="请选择字典生成" value="" />
                  </el-select>
                </el-form-item>
              </el-col> -->
              <el-col :span="24">
                <el-form-item label="交办内容">
                  <el-input v-model="form.content" type="textarea" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="备注" prop="remark">
                  <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
                </el-form-item>
              </el-col>
            </el-form>
          </el-row>
        </el-scrollbar>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { listTask, getTask, delTask, addTask, updateTask, exportTask } from '@/api/case/leader/leaderTask'
import userSelect from '@/components/userselect/index'

export default {
  name: 'Task',
  components: {
    userSelect
  },
  data() {
    return {
      showWorkUser: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 领导交办表格数据
      taskList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        workUserId: null,
        content: null,
        limitTime: null,
        finishTime: null,
        type: null,
        status: null,
        deptId: null,
        deptName: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        status: [
          { required: true, message: '状态 0-正常 1-停用不能为空', trigger: 'blur' }
        ],
        delFlag: [
          { required: true, message: '删除状态:0-下发任务 1-接受任务 2-完成任务 9-任务完结不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    handleConfirm({ id, name }) {
      this.form = { ...this.form, workUserId: id, workUserName: name }
    },
    /** 查询领导交办列表 */
    getList() {
      this.loading = true
      listTask(this.queryParams).then(response => {
        this.taskList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        userId: null,
        workUserId: null,
        userName: null,
        workUserName: null,
        content: null,
        limitTime: null,
        finishTime: null,
        type: null,
        status: '0',
        delFlag: null,
        deptId: null,
        deptName: null,
        createBy: null,
        createTime: null,
        updateTime: null,
        remark: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.form = { ...this.form, userId: this.$store.getters.uid, userName: this.$store.getters.nickName }
      this.title = '添加领导交办'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getTask(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改领导交办'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateTask(this.form).then(() => {
              this.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addTask(this.form).then(() => {
              this.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$confirm('是否确认删除领导交办编号为"' + ids + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return delTask(ids)
      }).then(() => {
        this.getList()
        this.msgSuccess('删除成功')
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams
      this.$confirm('是否确认导出所有领导交办数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return exportTask(queryParams)
      }).then(response => {
        this.download(response.msg)
      })
    }
  }
}
</script>
