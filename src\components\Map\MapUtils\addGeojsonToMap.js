import { layerCreate } from "./core.js";

/**
 * 添加JSON图层，并返回图层对象
 * @param {*} view MapView|SceneView 对象
 * @param {*} geojson 必填 geojison 数据
 * @param {*} props 选填 图层设置信息
 * @returns
 */
function addGeojsonToMap(view, geojson, props, layerid) {
  const blob = new Blob([JSON.stringify(geojson)], {
    type: "application/json",
  });
  const isGoto=props.isGoto;
  delete props.isGoto;
  const url = URL.createObjectURL(blob);
  const layer = layerCreate({
    type: "geojson",
    url,
    name: layerid,
    outFields:["*"],
    ...props,
  });
  view.map.add(layer);
  layer.when(()=>{
    isGoto&&view.goTo(layer.fullExtent);
  })
  return layer;
}

export default addGeojsonToMap;
