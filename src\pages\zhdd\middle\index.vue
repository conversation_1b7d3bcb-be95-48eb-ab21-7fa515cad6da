<template>
  <div class="zhdd_mapIcon">
    <div class="iconItem" @click="personClick" style="cursor: pointer">
      <div :class="personStatus?'active':'icon-bg'">
        <img src="@/assets/zhdd/relitu.png" title="人群热力" />
      </div>
      <div :class="personStatus?'active-title':'title'">人群热力</div>
    </div>

    <div class="iconItem" @click="giveNotice" style="cursor: pointer">
      <div :class="noticeStatus?'active':'icon-bg'">
        <img src="@/assets/zhdd/一键通知.png" title="一键通知" />
      </div>
      <div :class="noticeStatus?'active-title':'title'">一键通知</div>
    </div>

    <div @click="dispatchClick" style="cursor: pointer">
      <div :class="dispatchStatus?'active':'icon-bg'">
        <img src="@/assets/zhdd/一键调度.png" title="一键调度" />
      </div>
      <div :class="dispatchStatus?'active-title':'title'">一键调度</div>
    </div>

    <NoticeDialog :visible="showNotice" @close='noticeClose' :msgData='NoticeData'></NoticeDialog>
    <DispatchDialog v-if='showDispatch' @close='dispatchClose' :msgData='DispatchData'></DispatchDialog>
  </div>
</template>

<script>
import NoticeDialog from './NoticeDialog'
import DispatchDialog from './DispatchDialog'
import mapService from '@/components/Map/index.js'
export default {
  name: 'index',
  components: {
    NoticeDialog,
    DispatchDialog
  },
  data() {
    return {
      personStatus: false,
      discussStatus: false,
      noticeStatus: false,
      dispatchStatus: false,
      sgId: "",

      showNotice: false,
      showDispatch: false,

      NoticeData: {},
      DispatchData: {}
    }
  },
  computed: {},
  mounted() {
    this.$bus.$on('openyjtz', res => {
      if (res.type == "openyjtz") {
        this.NoticeData = res
        this.giveNotice()
      }
    })
    this.$bus.$on('openyjdd', res => {
      if (res.type == "openyjdd") {
        this.DispatchData = res
        this.dispatchClick()
      }
    })
  },
  methods: {
    noticeClose() {
      this.showNotice = false;
      this.noticeStatus = false
    },
    dispatchClose() {
      this.showDispatch = false;
      this.dispatchStatus = false
    },
    // 人群热力
    personClick() {
      this.personStatus = !this.personStatus;
      if (this.personStatus) {
        const mapData = {
          layerid: "rkztHot0",
          type: "dynamic",
        };
        mapService.loadHeatmapLayer(mapData);
      } else {
        mapService.removeLayer("rkztHot0");
      }
    },
    // 一键通知
    giveNotice() {
      this.noticeStatus = !this.noticeStatus;
      if (this.noticeStatus) {
        this.showNotice = true
      } else {
        this.showNotice = false
      }
    },
    // 一键调度
    dispatchClick() {
      this.dispatchStatus = !this.dispatchStatus;
      if (this.dispatchStatus) {
        this.showDispatch = true
      } else {
        //清除指挥调度操作
        this.$bus.$emit('yjdd_clear')
        this.showDispatch = false
      }
    },
  },
  watch: {}
}
</script>

<style scoped lang='less'>
.zhdd_mapIcon {
  position: absolute;
  left: 1680px;
  bottom: 630px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 6;
}

.mapIcon>div {
  text-align: center;
}

.iconItem {
  margin-right: 80px;
}

.icon-bg {
  width: 105px;
  height: 105px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10px;
  background: url('@/assets/zhdd/noActive.png') no-repeat;
}

.active {
  width: 105px;
  height: 105px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10px;
  background: url('@/assets/zhdd/active.png') no-repeat;
}

img {
  width: 94px;
  height: 94px;
}

.active-title {
  font-size: 25px;
  font-weight: 400;
  color: #FFFFFF;
}

.title {
  font-size: 25px;
  font-weight: 400;
  color: #FFFFFF;
  opacity: 0.6;
}

</style>