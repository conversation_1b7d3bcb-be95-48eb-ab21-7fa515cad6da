// 雷达遮罩
import * as externalRenderers from "@arcgis/core/views/3d/externalRenderers.js";
import {libRoot} from "../config.js"
var THREE = window.THREE_r116;

class RadarMask {
  constructor({ view, data = [] }) {
    this.view = view;
    this.data = data;
    this.renderObject = [];
    this.angle = 0;
  }

  setup(context) {
    this.renderer = new THREE.WebGLRenderer({
      context: context.gl, // 可用于将渲染器附加到已有的渲染环境(RenderingContext)中
      premultipliedAlpha: false, // renderer是否假设颜色有 premultiplied alpha. 默认为true
    });
    this.renderer.setPixelRatio(window.devicePixelRatio); // 设置设备像素比。通常用于避免HiDPI设备上绘图模糊
    this.renderer.setViewport(0, 0, this.view.width, this.view.height); // 视口大小设置
    // this.renderer.setSize(context.camera.fullWidth, context.camera.fullHeight);

    // Make sure it does not clear anything before rendering
    this.renderer.autoClear = false;
    this.renderer.autoClearDepth = false;
    this.renderer.autoClearColor = false;
    // this.renderer.autoClearStencil = false;

    // The ArcGIS JS API renders to custom offscreen buffers, and not to the default framebuffers.
    // We have to inject this bit of code into the three.js runtime in order for it to bind those
    // buffers instead of the default ones.
    var originalSetRenderTarget = this.renderer.setRenderTarget.bind(
      this.renderer
    );
    this.renderer.setRenderTarget = function (target) {
      originalSetRenderTarget(target);
      if (target == null) {
        context.bindRenderTarget();
      }
    };

    this.scene = new THREE.Scene();
    // setup the camera
    var cam = context.camera;
    this.camera = new THREE.PerspectiveCamera(
      cam.fovY,
      cam.aspect,
      cam.near,
      cam.far
    );

    // 添加坐标轴辅助工具

    let transform = new THREE.Matrix4(); // 变换矩阵
    let transformation = new Array(16);
    transform.fromArray(
      externalRenderers.renderCoordinateTransformAt(
        this.view,
        [
          this.data[0].position[0],
          this.data[0].position[1],
          this.data[0].position[2],
        ], // 坐标在地面上的点[x值, y值, 高度值]
        this.view.spatialReference,
        transformation
      )
    );

    const axesHelper = new THREE.AxesHelper(0);
    axesHelper.position.set(
      transform.elements[12],
      transform.elements[13],
      transform.elements[14]
    );
    this.scene.add(axesHelper);

    // setup scene lighting
    this.ambient = new THREE.AmbientLight(0xffffff, 0.5);
    this.scene.add(this.ambient);
    this.sun = new THREE.DirectionalLight(0xffffff, 0.5);
    this.sun.position.set(-600, 300, 60000);
    this.scene.add(this.sun);
    //var geometrys = this.getCoords(context);
    //var canvas = this.produceCanvas();
    this.clock = new THREE.Clock();
    for (let i = 0; i < this.data.length; i++) {
      const item = this.data[i];
      const { position, radius, height,color='azure' } = item;
      this.getGeometry(position, radius, height,color);
    }

    context.resetWebGLState();
  }
  render(context) {
    var cam = context.camera;
    //需要调整相机的视角
    this.camera.position.set(cam.eye[0], cam.eye[1], cam.eye[2]);
    this.camera.up.set(cam.up[0], cam.up[1], cam.up[2]);
    this.camera.lookAt(
      new THREE.Vector3(cam.center[0], cam.center[1], cam.center[2])
    );
    // Projection matrix can be copied directly
    this.camera.projectionMatrix.fromArray(cam.projectionMatrix);
    // update lighting
    /////////////////////////////////////////////////////////////////////////////////////////////////////
    // view.environment.lighting.date = Date.now();
    var l = context.sunLight;
    this.sun.position.set(l.direction[0], l.direction[1], l.direction[2]);
    this.sun.intensity = l.diffuse.intensity;
    this.sun.color = new THREE.Color(
      l.diffuse.color[0],
      l.diffuse.color[1],
      l.diffuse.color[2]
    );
    this.ambient.intensity = l.ambient.intensity;
    this.ambient.color = new THREE.Color(
      l.ambient.color[0],
      l.ambient.color[1],
      l.ambient.color[2]
    );
    for (let i = 0; i < this.renderObject.length; i++) {
      const itemRenderObject = this.renderObject[i];
      if (itemRenderObject) {
        itemRenderObject.rotateOnAxis(new THREE.Vector3(0, 1, 0), -Math.PI / 60 );
      }
    }

    // draw the scene
    /////////////////////////////////////////////////////////////////////////////////////////////////////
    this.renderer.state.reset();
    this.renderer.render(this.scene, this.camera);
    // as we want to smoothly animate the ISS movement, immediately request a re-render
    externalRenderers.requestRender(this.view);
    // cleanup
    context.resetWebGLState();
  }
  getGeometry(position, radius, height,color) {
    let url = `${libRoot}img/rectangle_${color}.png`;
    const textureLoader = new THREE.TextureLoader();
    var that = this;
    const map = textureLoader.load(url, function (map) {
      //var map = textureLoader.load(url);
      map.repeat.x = 2;
      var geo = new THREE.SphereGeometry(
        radius,
        200,
        200,
        0,
        Math.PI * 2,
        0,
        Math.PI / 2
      ); //半球几何
      var material = new THREE.MeshPhongMaterial({
        side: THREE.DoubleSide,
        transparent: true, // 必须设置为true,alphaMap才有效果
        depthWrite: false, // 渲染此材质是否对深度缓冲区有任何影响
        map: map,
        depthTest: false,
        opacity: 0.3,
      });
      const renderItemObject = new THREE.Mesh(geo, material);
      var cenP = [];
      externalRenderers.toRenderCoordinates(
        that.view,
        position,
        0,
        that.view.spatialReference,
        cenP,
        0,
        1
      );
      const length = Math.sqrt(
        cenP[0] * cenP[0] + cenP[1] * cenP[1] + cenP[2] * cenP[2]
      );
      var stt = new THREE.Vector3(...cenP.map((e) => e / length));
      renderItemObject.lookAt(stt);
      renderItemObject.rotateX(Math.PI / 2);
      renderItemObject.position.set(cenP[0], cenP[1], cenP[2]);
      renderItemObject.maxRadius = radius;
      that.scene.add(renderItemObject);

      const geometry = new THREE.CircleGeometry(radius, 32, 0, Math.PI / 2);
      const circleMaterial = new THREE.MeshBasicMaterial({
        color: 0xffffff,
        side: THREE.DoubleSide,
        transparent: true, 
        opacity: 0.2,
      });
      const circle = new THREE.Mesh(geometry, circleMaterial);
      circle.position.set(cenP[0], cenP[1], cenP[2]);
      circle.lookAt(stt);
      circle.rotateX(-Math.PI / 2);
      that.scene.add(circle);

      that.renderObject.push(circle);
    });
  }
}

export default RadarMask;
