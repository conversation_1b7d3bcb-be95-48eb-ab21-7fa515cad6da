<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      :inline="true"
    >
      <!-- <el-dropdown trigger="click" @command="handleCommand">
        <span class="el-dropdown-link">
          数据源
          <el-input
            v-model="queryParams.dataName"
            placeholder="请选择"
            readonly="readonly"
            clearable
            size="small"
            style="width: 100px; margin: 0 5px;"
            @keyup.enter.native="handleQuery"
          />
        </span>
        <el-dropdown-menu slot="dropdown" style="width: 100px;">
          <el-dropdown-item v-for="(v) in formData" :key="v.id" :command="{...v,type:'user'}">{{ v.value }}</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown> -->
      <el-form-item label="案件类型">
        <el-dropdown trigger="click" @command="handleCommand">
          <el-input
            v-model="queryParams.caseTypeName"
            placeholder="请选择案件类型"
            readonly="readonly"
            clearable
            size="small"
            style="width: 150px; margin: 0 5px;"
            @keyup.enter.native="handleQuery"
          />
          <el-dropdown-menu slot="dropdown" style="width: 150px;">
            <el-dropdown-item v-for="(v) in violationData" :key="v.dictSort" :command="v">{{ v.dictLabel }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px;"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="daterange"
          unlink-panels
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item label="网格小组">
        <el-select v-model="queryParams.deptName" placeholder="请选择网格小组" clearable size="small">
          <el-option
            v-for="(deptItem, deptIndex) in deptOptions"
            :key="deptIndex"
            :label="deptItem"
            :value="deptItem"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否超时">
        <el-select v-model="queryParams.isTimeOut" placeholder="请选择是否超时" clearable size="small">
          <el-option label="全部" value="" />
          <el-option label="正常" value="0" />
          <el-option label="超时" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="关键词搜索">
        <el-input v-model="queryParams.searchValue" size="small" style="width: 150px; margin: 0 5px;" placeholder="请输入内容" />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >
          新增
        </el-button>
      </el-col>
      <el-col v-if="queryParams.data == 0" :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="roleList"
      :cell-style="cellStyle"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="案件名称" align="center" prop="title" width="120" /> -->
      <!-- <el-table-column label="案件类型" prop="caseTypeName" align="center" :show-overflow-tooltip="true" width="120" /> -->
      <el-table-column label="任务发起人" prop="userName" :show-overflow-tooltip="true" />
      <el-table-column label="出警人员" prop="userNames" :show-overflow-tooltip="true" />
      <el-table-column label="发生时间" align="center" prop="happenTime" :show-overflow-tooltip="true" width="170">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.happenTime) }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="地址" prop="address" :show-overflow-tooltip="true" /> -->
      <el-table-column label="当班组长" prop="squadronUserName" align="center" :show-overflow-tooltip="true" width="150" />
      <el-table-column label="网格小组" prop="deptName" align="center" :show-overflow-tooltip="true" />
      <el-table-column label="案件内容" prop="content" :show-overflow-tooltip="true" />
      <el-table-column label="状态" prop="operaDescn" :show-overflow-tooltip="true" width="150">
        <template slot-scope="scope">
          <span :style="{color: isTimeOutColor[scope.row.isTimeOut]}">{{ scope.row.operaDescn }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审核状态" prop="approveType" align="center" :show-overflow-tooltip="true" width="100">
        <template slot-scope="scope">
          <span :style="{ color: approveTypeColor(scope.row.approveType) }">{{ scope.row.approveType | approveTypeName }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="状态" align="center" prop="status" :show-overflow-tooltip="true" width="90">
        <template slot-scope="scope">
          <span :style="{ color: circleColor[scope.row.status] }">{{ scope.row.status | statusData }}</span>
        </template>
      </el-table-column> -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="170">
        <template slot-scope="scope">
          <el-button v-if="isShowRevoke(scope.row)" size="mini" type="text" icon="el-icon-edit" @click="handleRevoke(scope.row)">撤回</el-button>
          <el-button v-if="isShowAssign(scope.row)" size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">分配</el-button>
          <el-button v-else size="mini" type="text" icon="el-icon-edit" @click="handleHis(scope.row)">详情</el-button>
          <el-button v-if="isShowApprove(scope.row)" size="mini" type="text" icon="el-icon-check" @click="handleApproveBtn(scope.row, true)">通过</el-button>
          <el-button v-if="isShowApprove(scope.row)" size="mini" type="text" icon="el-icon-close" @click="handleApproveBtn(scope.row, false)">退回</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleLogList(scope.row)">日志</el-button>
          <el-button v-if="scope.row.status == 9" size="mini" type="text" icon="el-icon-printer" style="color: #9e9e9e;" @click="handlePrint(scope.row.captureId)">打印</el-button>
          <el-button v-if="isShowDel(scope.row)" size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    /> -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改角色配置对话框 -->
    <fromlist ref="fromlist" :visible.sync="open" :form-disabled="formDisabled" :title="title" :detail-id="detailId" @onPrimary="primary" @reLoad="handleQuery" />
    <!-- 撤回 -->
    <el-dialog
      :close-on-click-modal="false"
      title="撤销"
      :visible.sync="dialogVisible"
      width="30%"
      append-to-body
      :before-close="handleClose"
    >
      <el-form ref="bhForm" :model="bhForm" :rules="bhFormrules" label-width="80px">
        <el-form-item label="撤销理由" prop="revokeReason">
          <el-input v-model="bhForm.revokeReason" type="textarea" placeholder="请输入撤销理由" :maxlength="150" show-word-limit :autosize="{minRows: 2, maxRows: 4}" />
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="handelWithdraw(0)">取 消</el-button>
        <el-button type="primary" @click="handelWithdraw(1)">确 定</el-button>
      </span>
    </el-dialog>
    <LogList v-model="logVisible" :case-id="caseId" />
    <!-- 审核弹窗 -->
    <el-dialog
      :close-on-click-modal="false"
      title="审核"
      :visible.sync="approveVisible"
      width="30%"
      append-to-body
      :before-close="handleApproveClose"
    >
      <el-form ref="approveForm" :model="approveForm" :rules="approveFormrules" label-width="80px">
        <el-form-item label="退回原因" prop="approveReason">
          <el-input v-model="approveForm.approveReason" type="textarea" placeholder="请输入退回原因" :maxlength="150" show-word-limit :autosize="{minRows: 2, maxRows: 4}" />
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="approveVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleApproveReBtn">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {captureList, removeCapture, revokeCapture, captureApprove } from '@/api/case/synthetical/capture'
import fromlist from '@/pages/case/views/synthetical/capture/components/fromlist.vue'
import {listData as listDatas} from '@/api/system/dict/type'
import LogList from '@/components/LogList/index'
import { userList as deptList} from '@/api/system/dict/type'
// import Userselect from '@/components/userselect/index.vue'

export default {
  name: 'Capture',
  filters: {
    caseTypes(type, violationData) {
      let types = violationData.find(v => v.dictSort == type)
      if (types) return types.dictLabel
    },
    statusData(type) {
      let data = {1: '待处理', 3: '下发当班组长', 4: ' 下发队员', 5: ' 出警', 6: ' 警情反馈', 9: ' 已完结'}
      if (type) return data[type]
    },
    approveTypeName(type) {
      let data = { 1: '审核通过', 0: '审核退回' }
      return data[type] || '待审核'
    }
  },
  components: {
    fromlist,
    LogList
    // Userselect
  },
  data() {
    return {
      deptVisible: false,
      $map: null,
      dialogVisible: false,
      bhForm: {},
      bhFormrules: {
        revokeReason: [{ required: true, message: '请输入任务名称', trigger: 'blur' }]
      },
      value: [],
      userOptions: [],
      circleColor: {1: '#409EFF', 3: '#FAB71C', 4: '#FAB71C', 5: '#FAB71C', 6: '#FAB71C', 9: '#bdc3bf'},
      // 0=未超时 1=有超时
      isTimeOutColor: {0: '#666', 1: '#fc9a03'},
      formData: [{id: 0, value: '我的'}, {id: 1, value: '全部'}],

      // statusData: [ {id: 1, value: 下发当班组长', statusColor: '#409EFF'}, {id: 2, value: '下发队员', statusColor: '#E6A23C'}, {id: 3, value: '出警', statusColor: '#67C23A'}, {id: 0, value: '警情反馈', statusColor: '#F56C6C'} ],
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      roleList: [],
      // 弹出层标题
      title: '新增案件',
      // 是否显示弹出层
      open: false,
      detailId: 0,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        caseTypeName: '',
        caseType: undefined,
        statusName: '',
        status: undefined,
        data: 0,
        dataName: '我的',
        // 日期范围
        dateRange: [],
        deptId: '',
        deptName: '',
        isTimeOut: ''
      },
      formDisabled: false,

      // 表单参数
      form: {},
      qd: true,
      violationData: [],
      logVisible: false,
      caseId: 0,
      deptOptions: [],
      typeList: 1,
      approveVisible: false,
      approveForm: {},
      approveFormrules: {
        approveReason: [{ required: true, message: '请输入退回原因', trigger: 'blur' }]
      }
    }
  },
  computed: {
    listData() {
      let {pageNum, pageSize} = this.queryParams
      let arr = this.roleList.slice((pageNum - 1) * pageSize, (pageNum - 1) * pageSize + pageSize)
      return arr
    }
  },
  async created() {
    await this.getList()
    listDatas({dictType: 'case_alert_type'}).then(res => {
      this.violationData = res.rows
    })
    this.getDeptNameOptions()

  },
  methods: {
    approveTypeColor(approveType) {
      let data = { 1: '#07a907', 0: '#db1616' }
      return data[approveType] || ''
    },
    handleApproveClose() {
      this.approveVisible = false
      this.approveForm = {}
    },
    handleApproveReBtn() {
      this.$refs.approveForm.validate(valid => {
        if (valid) this.handelApprove(0)
      })
    },
    handelApprove(type) {
      const params = { ...this.approveForm, approveType: type  }
      console.log(params)
      this.loading = true
      this.approveVisible = false
      captureApprove(params).then(() => {
        this.loading = false
        this.$message.success('操作成功')
        this.approveForm = {}
        this.getList()
      }).catch(() => {
        this.loading = false
      })
    },
    isShowApprove(row) {
      if (row.status == 9 && row.userId == this.$store.getters.uid && row.approveType != 1) {
        return true
      } else {
        return false
      }
    },
    handleApproveBtn(row, isCheck) {
      this.$confirm(`是否${isCheck ? '通过' : '退回'}该案件？`, '提示', { type: 'warning' }).then(() => {
        this.approveForm = { captureId: row.captureId }
        if (isCheck) {
          // 案件通过
          this.handelApprove(1)
        } else {
          // 案件驳回
          this.approveVisible = true
        }
      }).catch(() => {})
    },
    // 网格小组选项
    getDeptNameOptions() {
      deptList({ typeList: this.typeList }).then(res => {
        this.deptOptions = res.data.map(item => item.label)
      })
    },
    // 选择部门
    handleConfirmDept({id, name}) {
      this.queryParams = { ...this.queryParams, deptId: id, deptName: name }
    },
    handlePrint(id) {
      let routeUrl = this.$router.resolve({ path: '/case/print/capture', query: { id } })
      window.open(routeUrl.href, '_blank')
    },
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(() => {
          done()
          this.bhForm = {}
        })
        .catch(() => {})
    },
    handelWithdraw(state) {
      if (state) {
        this.$refs.bhForm.validate(valid => {
          if (valid) {
            this.loading = true
            this.formLoadingText = '数据上传中'
            this.dialogVisible = false
            let params = {revokeReason: this.bhForm.revokeReason, captureId: this.bhForm.captureId }
            /* if (this.$store.getters.uid ==  this.bhForm.squadronUserId) {
              params.revokeType = 2
              params.status = 3
            }
            if (this.bhForm.userId == this.$store.getters.uid) {
              params.revokeType = 1
              params.status = 1
            } */
            if (this.$store.getters.roles.includes('manage') || this.$store.getters.roles.includes('admin')) {
              params.revokeType = 1
              params.status = 1
            }
            revokeCapture(params).then(() => {
              this.dialogVisible = false
              this.bhForm = {}
              this.getList()
              this.$message.success('撤回成功')
            }).catch(() => { this.loading = false })
          }
        })
      } else {
        this.dialogVisible = false
        this.bhForm = {}
      }
    },
    handleRevoke(row) {
      this.bhForm = { captureId: row.captureId, squadronUserId: row.squadronUserId, userId: row.userId }
      this.dialogVisible = true
    },
    isShowDel(row) {
      if (this.$store.getters.roles.includes('manage') && row.status == 1) {
        return true
      } else if (this.$store.getters.roles.includes('admin') && row.status == 1) {
        return true
      } else if (this.$store.getters.admin) {
        // 如果是admin账号登录直接返回true
        return true
      } else {
        return false
      }
    },
    // 是否显示撤回按钮
    isShowRevoke(row) {
      // if (row.userId == this.$store.getters.uid && row.status >= 3 && row.status <= 4) {
      //   /* 指挥中心在状态3和4的时候显示撤回按钮 */
      //   return true
      // } else if (row.status == 4 && row.squadronUserId == this.$store.getters.uid) {
      //   /* 中队长ID等于当前登录人ID的时候显示撤回按钮 */
      //   return true
      // }
      if (this.$store.getters.roles.includes('manage') && row.status > 1 && row.status <= 4) {
        return true
      } else if (this.$store.getters.roles.includes('admin') && row.status > 1 && row.status <= 4) {
        return true
      } else {
        return false
      }
    },
    // 是否显示分配按钮
    isShowAssign(row) {
      if (row.status == 1) {
        return true
      } else if (row.status == 3 && row.squadronUserId == this.$store.getters.uid) {
        return true
      } else {
        return false
      }
    },
    // 类型选择
    handleCommand(command) {
      if (command.type == 'user') {
        this.queryParams = {...this.queryParams, data: command.id, dataName: command.value}
      } else {
        this.queryParams = {...this.queryParams, caseType: command.dictSort, caseTypeName: command.dictLabel}
      }
      this.handleQuery()
    },
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.queryParams.pageNum = 1
    },
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
    },
    // 状态颜色
    cellStyle(row) {
      if (row.column.label == '状态') return `color: ${row.row.statusColor}`
    },
    // 已下为模板
    /** 查询列表 */
    getList() {
      this.loading = true
      let { dateRange, searchValue, caseType, pageNum, pageSize, deptName, isTimeOut} = this.queryParams
      let params = { pageNum, pageSize, type: 1}
      if (Array.isArray(dateRange) && !dateRange.length == 0) { params = {...params, searchStartTime: dateRange[0], searchEndTime: dateRange[1] } }
      if (searchValue) params.searchValue = searchValue
      if (caseType) params.caseType = caseType
      if (deptName) params.deptName = deptName
      if (isTimeOut) params.isTimeOut = isTimeOut
      // let api = this.$store.getters.postKeys.includes('zhzx') ? captureList({...params}) : myCaptureList(params)
      captureList({...params}).then(res => {
        res.rows.forEach(item => {
          item.operaDescn = item.logList[item.logList.length - 1].operaDescn
        })
        this.roleList = res.rows
        this.total = res.total
        this.loading = false
      }).catch(() => this.loading = false)
    },
    // 确定按钮
    primary() {
      this.open = false
      this.getList()
    },
    // 表单重置
    reset() {},
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {dateRange: [], pageNum: 1, pageSize: 10,  searchValue: ''}
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.captureId)
      this.single = selection.length != 1
      this.multiple = !selection.length
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.detailId = 0
      this.open = true
      this.title = '新增信息'
      this.formDisabled = false
    },
    /** 详情 */
    handleHis(row) {
      this.open = true
      this.formDisabled = true
      this.detailId = row.captureId
      this.title = '案件详情'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.open = true
      this.formDisabled = false
      this.detailId = row.captureId
      this.title = '修改信息'
    },
    /* 查看操作日志 */
    handleLogList(row) {
      this.logVisible = true
      this.caseId = row.captureId
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const fourIds = row.captureId || this.ids
      this.$confirm('是否确认删除?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // return delRole(roleIds)
          this.roleList = this.roleList.filter(
            irem => row.captureId != irem.captureId
          )
          this.total--
        })
        .then(() => {
          removeCapture(fourIds).then(() => {
            this.getList()
            this.msgSuccess('删除成功')
          })
        })
    }
  }
}
</script>
