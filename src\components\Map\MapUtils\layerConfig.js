import TileInfo from "@arcgis/core/layers/support/TileInfo.js";

//下面是省里irs地图的配置


function base64UrlEncode(str) {
  var encodedSource = CryptoJS.enc.Base64.stringify(str);
  var reg = new RegExp('/', 'g');
  encodedSource = encodedSource.replace(/=+$/, '').replace(/\+/g, '-').replace(reg, '_');
  return encodedSource;
}

let header = JSON.stringify({
  "alg": "HS256",
  "typ": "JWT"
})


let accessKey = "ca348e35b0b349c1b786fe3129b8aba5";   //修改自己申请的accessKey，ak、sk不要弄反
let secretKey = "8bf9e0970c744cde920a189aa4b9bf45";   //修改自己申请的secretKey，ak、sk不要弄反

//ak、sk和生成token建议在后端，避免前端调用的时候泄露
let payload = JSON.stringify({
  "key": accessKey,
  "exp": new Date().setHours(new Date().getHours()+1)
});
let secretSalt = "user";

let before_sign = base64UrlEncode(CryptoJS.enc.Utf8.parse(header)) + '.' + base64UrlEncode(CryptoJS.enc.Utf8.parse(payload));
let signature = CryptoJS.HmacSHA256(before_sign, secretKey);
signature = base64UrlEncode(signature);
let final_sign = before_sign + '.' + signature;

console.log(final_sign)

const url1 = 'https://bcdsg.zj.gov.cn:8443/restapi/prod/IC33000020220811000009/mapserver/vmap/WMTS/1.0/zjvmap/tdt_kejiganyangshi_2017'       //atg.biz.gettile  电子地图服务调用地址
const url2 = 'https://bcdsg.zj.gov.cn:8443/restapi/prod/IC33000020220811000011/mapserver/label/WMTS/1.0/zjvmap/tdt_kejiganyangshi_2017'	  //atg.biz.gettile_lab 电子地图标注服务调用地址 


const baseUrl1 = url1+'?jwt='+final_sign+'&x-bg-auth-type=jwt_auth&SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}' // 网关地址
console.log(baseUrl1)
const baseUrl2 = url2+'?jwt='+final_sign+'&x-bg-auth-type=jwt_auth&SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}' // 网关地址

// const tileInfo = new TileInfo({
//   dpi: 90.71428571427429,
//   origin: {
//     x: -180,
//     y: 90,
//   },
//   spatialReference: {
//     wkid: 4490,
//   },
//   lods: [
//     {
//       level: 0,
//       levelValue: "1",
//       resolution: 0.703125,
//       scale: 295497593.05875003,
//     },
//     {
//       level: 1,
//       levelValue: "2",
//       resolution: 0.3515625,
//       scale: 147748796.52937502,
//     },
//     {
//       level: 2,
//       levelValue: "3",
//       resolution: 0.17578125,
//       scale: 73874398.264687508,
//     },
//     {
//       level: 3,
//       levelValue: "4",
//       resolution: 0.087890625,
//       scale: 36937199.132343754,
//     },
//     {
//       level: 4,
//       levelValue: "5",
//       resolution: 0.0439453125,
//       scale: 18468599.566171877,
//     },
//     {
//       level: 5,
//       levelValue: "6",
//       resolution: 0.02197265625,
//       scale: 9234299.7830859385,
//     },
//     {
//       level: 6,
//       levelValue: "7",
//       resolution: 0.010986328125,
//       scale: 4617149.8915429693,
//     },
//     {
//       level: 7,
//       levelValue: "8",
//       resolution: 0.0054931640625,
//       scale: 2308574.9457714846,
//     },
//     {
//       level: 8,
//       levelValue: "9",
//       resolution: 0.00274658203125,
//       scale: 1154287.4728857423,
//     },
//     {
//       level: 9,
//       levelValue: "10",
//       resolution: 0.001373291015625,
//       scale: 577143.73644287116,
//     },
//     {
//       level: 10,
//       levelValue: "11",
//       resolution: 0.0006866455078125,
//       scale: 288571.86822143558,
//     },
//     {
//       level: 11,
//       levelValue: "12",
//       resolution: 0.00034332275390625,
//       scale: 144285.93411071779,
//     },
//     {
//       level: 12,
//       levelValue: "13",
//       resolution: 0.000171661376953125,
//       scale: 72142.967055358895,
//     },
//     {
//       level: 13,
//       levelValue: "14",
//       resolution: 8.58306884765625e-5,
//       scale: 36071.483527679447,
//     },
//     {
//       level: 14,
//       levelValue: "15",
//       resolution: 4.291534423828125e-5,
//       scale: 18035.741763839724,
//     },
//     {
//       level: 15,
//       levelValue: "16",
//       resolution: 2.1457672119140625e-5,
//       scale: 9017.8708819198619,
//     },
//     {
//       level: 16,
//       levelValue: "17",
//       resolution: 1.0728836059570313e-5,
//       scale: 4508.9354409599309,
//     },
//     {
//       level: 17,
//       levelValue: "18",
//       resolution: 5.3644180297851563e-6,
//       scale: 2254.4677204799655,
//     },
//     {
//       level: 18,
//       levelValue: "19",
//       resolution: 2.68220901489257815e-6,
//       scale: 1127.23386023998275,
//     },
//     {
//       level: 19,
//       levelValue: "20",
//       resolution: 1.341104507446289075e-6,
//       scale: 563.616930119991375,
//     },
//   ],
// });
const tileInfo = new TileInfo({
  dpi: 96,
  rows: 256,
  cols: 256,
  compressionQuality: 10,
  origin: {
    x: -180,
    y: 90
  },
  spatialReference: {
    wkid: 4490
  },
  lods: [
    {
      level: 0,
      levelValue: 1,
      resolution: 0.703125,
      scale: 295497593.05875003
    },
    {
      level: 1,
      levelValue: 2,
      resolution: 0.3515625,
      scale: 147748796.52937502
    },
    {
      level: 2,
      levelValue: 3,
      resolution: 0.17578125,
      scale: 73874398.264687508
    },
    {
      level: 3,
      levelValue: 4,
      resolution: 0.087890625,
      scale: 36937199.132343754
    },
    {
      level: 4,
      levelValue: 5,
      resolution: 0.0439453125,
      scale: 18468599.566171877
    },
    {
      level: 5,
      levelValue: 6,
      resolution: 0.02197265625,
      scale: 9234299.7830859385
    },
    {
      level: 6,
      levelValue: 7,
      resolution: 0.010986328125,
      scale: 4617149.8915429693
    },
    {
      level: 7,
      levelValue: 8,
      resolution: 0.0054931640625,
      scale: 2308574.9457714846
    },
    {
      level: 8,
      levelValue: 9,
      resolution: 0.00274658203125,
      scale: 1154287.4728857423
    },
    {
      level: 9,
      levelValue: 10,
      resolution: 0.001373291015625,
      scale: 577143.73644287116
    },
    {
      level: 10,
      levelValue: 11,
      resolution: 0.0006866455078125,
      scale: 288571.86822143558
    },
    {
      level: 11,
      levelValue: 12,
      resolution: 0.00034332275390625,
      scale: 144285.93411071779
    },
    {
      level: 12,
      levelValue: 13,
      resolution: 0.000171661376953125,
      scale: 72142.967055358895
    },
    {
      level: 13,
      levelValue: 14,
      resolution: 8.58306884765625e-5,
      scale: 36071.483527679447
    },
    {
      level: 14,
      levelValue: 15,
      resolution: 4.291534423828125e-5,
      scale: 18035.741763839724
    },
    {
      level: 15,
      levelValue: 16,
      resolution: 2.1457672119140625e-5,
      scale: 9017.8708819198619
    },
    {
      level: 16,
      levelValue: 17,
      resolution: 1.0728836059570313e-5,
      scale: 4508.9354409599309
    },
    {
      level: 17,
      levelValue: 18,
      resolution: 5.3644180297851563e-6,
      scale: 2254.4677204799655
    },
    {
      level: 18,
      levelValue: 19,
      resolution: 2.68220901489257815e-6,
      scale: 1127.23386023998275
    },
    {
      level: 19,
      levelValue: 20,
      resolution: 1.341104507446289075e-6,
      scale: 563.616930119991375
    }
  ]
})


const LAYER_CONFIG = {
  // 省电子底图
  VECTOR_BASEMAP: [
    {
      type: "web-tile",
      id: "baseMap",
      title: "省电子地图",
      // urlTemplate:
      // "http://{subDomain}.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}&tk=d84cde1b04ff76cfda0f01bc3bac6557",
      urlTemplate:baseUrl1,
      subDomains: ["t0", "t1", "t2", "t3", "t4", "t5", "t6", "t7"],
      tileInfo: tileInfo,
      spatialReference: {
        wkid: 4490,
      },
      fullExtent: {
        xmin: -180,
        ymin: -90,
        xmax: 180,
        ymax: 90,
        spatialReference: {
          wkid: 4490,
        },
      },
    },
    {
      type: "web-tile",
      id: "baseMapLabel",
      title: "省电子地图标注",
      // urlTemplate:
      // "http://{subDomain}.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}&tk=d84cde1b04ff76cfda0f01bc3bac6557",
      urlTemplate:baseUrl2,
      subDomains: ["t0", "t1", "t2", "t3", "t4", "t5", "t6", "t7"],
      tileInfo: tileInfo,
      spatialReference: {
        wkid: 4490,
      },
      fullExtent: {
        xmin: -180,
        ymin: -90,
        xmax: 180,
        ymax: 90,
        spatialReference: {
          wkid: 4490,
        },
      },
    },
  ],
  IMAGE_BASEMAP2: [
    {
      type: "web-tile",
      id: "baseMap2",
      title: "金华影像底图2",
      urlTemplate:
        "https://sdi.zjzwfw.gov.cn/gqservices/wmts/imgmap/default/local?layer=imgmap&style=default&tilematrixset=default028mm&Service=WMTS&Request=GetTile&Version=1.0.0&Format=image/jpgpng&TileMatrix={level}&TileCol={col}&TileRow={row}&token=sy-6786f525-a9bd-477f-ab4a-f26d29524077&code=", //需动态传入code
      // urlTemplate:'https://csdn.dsjj.jinhua.gov.cn:8101/gqservices/wmts/imgmap/default/local?layer=imgmap&style=default&tilematrixset=default028mm&Service=WMTS&Request=GetTile&Version=1.0.0&Format=image/jpgpng&TileMatrix={level}&TileCol={col}&TileRow={row}&token=sy-6786f525-a9bd-477f-ab4a-f26d29524077&code=d7b6220a34ea4b34b862ec8596372a04ba88f801',
      tileInfo: tileInfo,
      spatialReference: {
        wkid: 4490,
      },
      fullExtent: {
        xmin: -180,
        ymin: -90,
        xmax: 180,
        ymax: 90,
        spatialReference: {
          wkid: 4490,
        },
      },
    },
  ],

  // 行政区
  shiliangqiepian: {
    id: "shiliangqiepian",
    type: "tile",
    url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/Country/MapServer",
  },

  //初始化三维场景全球底图
  initBaseLayer: {
    type: "tile",
    id: "initBaseLayer",
    url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/DOM_L9_4490/MapServer",
  },

  // 路网数据
  TRA_NET_LN: {
    id: "TRA_NET_LN",
    type: "tile",
    url:
      window.location.hostname === "127.0.0.1"
        ? `https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/TRA_NET_LN_JH/MapServer`
        : `https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/TRA_NET_LN_JH/MapServer`,
  },
};

export function getLayerConfigById(id) {
  if (id in LAYER_CONFIG) {
    return LAYER_CONFIG[id];
  } else {
    throw new Error(`无id=${id}的图层配置！`);
  }
}
