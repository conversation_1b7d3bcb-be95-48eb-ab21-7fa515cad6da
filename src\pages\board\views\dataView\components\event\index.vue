<template>
  <div class="m-table">
    <div class="m-header">
      <span>经办人</span>
      <span>部门</span>
      <span>发生时间</span>
      <span>案件内容</span>
      <span>类型</span>
    </div>
    <!-- 主体内容 -->
    <div v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.8)" class="m-body" @click="clickItem">
      <vueSeamlessScroll :data="tableList" :class-option="defaultOption">
        <div v-for="item in tableList" :key="item.id" class="m-tr" :data-type="item.caseType" :data-id="item.id">
          <span>{{ item.userNames }}</span>
          <span>{{ item.deptName }}</span>
          <span>{{ item.happenDate }}</span>
          <span>{{ item.content }}</span>
          <span>{{ item.typeName }}</span>
        </div>
        <div v-if="!tableList.length" class="m-tr">
          暂无数据
        </div>
      </vueSeamlessScroll>
    </div>

    <!-- 详情弹窗 -->
    <!-- 综合执法-日常巡查 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="inspectionOpen" width="1000px">
      <xcfxrcxcFromlist v-if="inspectionOpen" :is-board="isBoard" :detail-id="detailId" :form-disabled="formDisabled" @oncancel="cancel('inspectionOpen')" />
    </el-dialog>
    <!-- 综合执法-店铺巡查 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="shopInspectionOpen" width="1000px">
      <patrolFromlist v-if="shopInspectionOpen" :is-board="isBoard" :detail-id="detailId" :form-disabled="formDisabled" @oncancel="cancel('shopInspectionOpen')" />
    </el-dialog>
    <!-- 综合执法-简易处罚 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="punishmentOpen" width="1000px">
      <punishmentFromlist v-if="punishmentOpen" :is-board="isBoard" :detail-id="detailId" :form-disabled="formDisabled" @oncancel="cancel('punishmentOpen')" />
    </el-dialog>
    <!-- 综合执法-监控抓拍 -->
    <captureFromlist :visible.sync="captureOpen" :is-board="isBoard" :form-disabled="formDisabled" :title="title" :detail-id="detailId" />
    <!-- 综合执法-智能抓拍 -->
    <aicaptureFromlist :visible.sync="autoCaptureOpen" :is-board="isBoard" :form-disabled="formDisabled" :title="title" :detail-id="detailId" />
    <!-- 运管执法-日常巡查 -->
    <dailyCheckFromList :visible.sync="transportOpen" :is-board="isBoard" :title="title" :detail-id="detailId" :form-disabled="formDisabled" />
    <!-- 运管执法-违规处置 -->
    <illegalFromList :visible.sync="illegalTransportOpen" :is-board="isBoard" :title="title" :detail-id="detailId" :form-disabled="formDisabled" />
    <!-- 交警执法-电子抓拍 -->
    <dzzpFromList :visible.sync="trafficCountOpen" :is-board="isBoard" :title="title" :detail-id="detailId" :form-disabled="formDisabled" />
  </div>
</template>

<script>
import { getEventList } from '@/api/board/dataView/index'
import vueSeamlessScroll from 'vue-seamless-scroll'
import captureFromlist from '@/pages/case/views/synthetical/capture/components/fromlist.vue'
import aicaptureFromlist from '@/pages/case/views/synthetical/aicapture/components/fromlist.vue'
import xcfxrcxcFromlist from '@/pages/case/views/synthetical/xcfxrcxc/components/fromlist'
import patrolFromlist from '@/pages/case/views/synthetical/patrol/components/fromlist'
import punishmentFromlist from '@/pages/case/views/synthetical/punishment/components/fromlist'
import dailyCheckFromList from '@/pages/case/views/pipe/dailyCheck/components/fromlist'
import illegalFromList from '@/pages/case/views/pipe/illegal/components/fromlist.vue'
import dzzpFromList from '@/pages/case/views/jjzf/dzzp/components/fromList'

export default {
  components: {
    vueSeamlessScroll,
    xcfxrcxcFromlist,
    captureFromlist,
    aicaptureFromlist,
    patrolFromlist,
    punishmentFromlist,
    dailyCheckFromList,
    illegalFromList,
    dzzpFromList
  },
  data() {
    return {
      // 控制是从大屏进入弹窗组件
      isBoard: true,
      tableList: [],
      loading: false,
      // 是否显示弹出层
      // open: false,
      title: '',
      // 是否可编辑表单
      formDisabled: true,
      // 详情ID
      detailId: null,
      caseType: '',
      captureOpen: false,
      autoCaptureOpen: false,
      inspectionOpen: false,
      shopInspectionOpen: false,
      punishmentOpen: false,
      transportOpen: false,
      illegalTransportOpen: false,
      trafficCountOpen: false
    }
  },
  computed: {
    defaultOption() {
      return {
        step: 0.2, // 数值越大速度滚动越快
        limitMoveNum: 5, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 1000 // 单步运动停止的时间(默认值1000ms)
      }
    }
  },
  mounted() {
    this.fetchData()
  },
  methods: {
    fetchData() {
      this.loading = true
      getEventList().then(res => {
        this.tableList = res.data
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    // 点击弹窗
    clickItem(e) {
      const {id, type} = e.target.parentNode.dataset
      if (id) {
        this.caseType = type

        const flagList = {
          'inspection': { open: 'inspectionOpen', title: '日常巡查详情' },
          'shopInspection': { open: 'shopInspectionOpen', title: '店铺巡查详情' },
          'punishment': { open: 'punishmentOpen', title: '简易处罚详情' },
          'capture': { open: 'captureOpen', title: '监控抓拍详情' },
          'autoCapture': { open: 'autoCaptureOpen', title: '智能抓拍详情' },
          'transport': { open: 'transportOpen', title: '日常巡查详情' },
          'illegalTransport': { open: 'illegalTransportOpen', title: '违规处置详情' },
          'trafficCount': { open: 'trafficCountOpen', title: '电子抓拍详情' }
        }

        this.title = flagList[this.caseType].title
        this.detailId = id
        this[flagList[this.caseType].open] = true
      }
    },
    // 取消弹窗
    cancel(openName) {
      this[openName] = false
    }
  }
}
</script>

<style scoped lang="scss">
.m-table {
  height: 100%;
  padding-top: pxtorem(10);
  .m-header {
    height: pxtorem(28);
    line-height: pxtorem(28);
    background: url(@/assets/images/m-header.png) no-repeat center center / 100% 100%;
    font-size: pxtorem(14);
    display: flex;
    padding: 0 10px 0 10px;
    span {
      text-align: center;
      flex: 1;
    }
  }
  .m-body {
    height: calc(100% - 0.14583rem);
    overflow: hidden;
    .m-tr {
      overflow: hidden;
      height: pxtorem(26);
      line-height: pxtorem(26);
      font-size: pxtorem(12);
      color: #00f7ff;
      background: rgba(0, 0, 0, 0.3);
      padding: 0 10px;
      margin-top: 5px;
      text-align: center;
      cursor: pointer;
      span {
        width: 20%;
        height: pxtorem(26);
        float: left;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
  }
}
</style>
