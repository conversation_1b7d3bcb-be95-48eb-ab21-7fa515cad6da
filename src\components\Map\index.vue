<template>
  <div class="container" style="width: 100%; height: 100%">
    <div id="viewDiv" style="width: 100%; height: 100%"></div>
    <LayerControl
      v-if="currentRoute !== '/home'"
      ref="layerControlRef"
      @loadLayer="loadLayer"
      @removeLayer="removeLayer"
      @loadPointLayer="loadPoint"
      @removePointLayer="removePoint"
    />
    <LeftIndex v-if="currentRoute === '/home' || currentRoute === '/zhdd'"></LeftIndex>
    <RightIndex v-if="currentRoute === '/home' || currentRoute === '/zhdd'"></RightIndex>
    <video-box
      :visible.sync="$store.getters.videoVisible"
      :terminal-no="terminalNo"
      :modal="false"
      :close-on-click-modal="false"
    />
    <zfzsPop v-if="showZfzsPop" :data="point" @close="showZfzsPop = false"></zfzsPop>
    <fmtsPop v-if="showFmtsPop" :id="point" @close="showFmtsPop = false"></fmtsPop>
    <wgPop v-if="showWgPop" :data="point" @close="showWgPop = false"></wgPop>

    <!--视频相关-->
    <pocVideoPop
      :visible="pocDialogVisible"
      :gVideoUser="gVideoUser"
      :gUID="gUID"
      :gPWD="gPWD"
      @close="pocDialogVisible = false"
    ></pocVideoPop>

    <VideoPopNOGB
      :visible="VideoPopNOGBVisible"
      :videoCode='videoCode' :type='type' :baseURL="baseURL"
      :username="username"
      :pwd="pwd"
      @close="VideoPopNOGBVisible = false"
    ></VideoPopNOGB>
    <VideoPopISGB
      :visible='VideoPopISGBVisible'
      :videoCode='videoCode'
      :type='type'
      :baseURL='baseURL'
      :username='username'
      :pwd='pwd'
      @close='VideoPopISGBVisible = false'>
    </VideoPopISGB>
  </div>
</template>

<script>
import mapService from './index.js'
import '@arcgis/core/assets/esri/themes/light/main.css'
import LayerControl from '@/components/Map/LayerControl/index.vue'
import LeftIndex from '@/components/Map/MapControl/left-index.vue'
import RightIndex from '@/components/Map/MapControl/right-index.vue'
import videoBox from '@/pages/board/views/components/windowInfo/videoBox/index.vue'
import zfzsPop from '@/components/Map/MapPopup/zfzsPop.vue'
import fmtsPop from '@/components/Map/MapPopup/fmtsPop.vue'
import wgPop from '@/components/Map/MapPopup/wgPop.vue'
import pocVideoPop from '@/components/PocVideoPop'
import VideoPopNOGB from '@/components/VideoPopNOGB'
import VideoPopISGB from '@/components/VideoPopISGB'
import { fn_start } from '@/assets/idt/Idt'
import { indexApi } from '@/api/indexApi'
import { getDevicePoint } from '@/api/wlwsb'
import {getAJtotal} from '@/api/csgl'
import {getJianCe, getRenYuan, getMonitorPoint} from '@/api/yxjc'
import {getAJDist} from '@/api/csyxgl'
export default {
  name: 'mapComponent',
  components: { LayerControl, LeftIndex, RightIndex, videoBox, zfzsPop, fmtsPop, wgPop, pocVideoPop, VideoPopNOGB, VideoPopISGB },
  data() {
    return {
      terminalNo: '',
      pointData: [
        {
          code: 3,
          county: '磐安县',
          dept_name: '磐安县行政执法局安文中队',
          guid: '9fe73a54-b0ae-470a-9e24-d13b148b62c7',
          hostcode: '04',
          laiyuan: '-',
          lasttime: '2024-12-23 08:31:42',
          lat: 29.0807125711639,
          layerid: 'zfry_point_在线',
          lng: 119.63927334753055,
          name: '泰哥宠物医院',
          objid: 0,
          phone: '-',
          sys_depart: 'T142396',
          type: '-',
          ymjzl: 100,
          myzz: '有',
          zpffzz: '有',
          address: '浙江省金华市磐安县安文街道',
          lxdh: '13812345678',
        },
        {
          code: 3,
          county: '磐安县',
          dept_name: '磐安县行政执法局安文中队',
          guid: '8475d12a-8a11-444e-ad34-dae2b90111f2',
          hostcode: '03',
          laiyuan: '-',
          lasttime: '2024-12-19 02:24:52',
          lat: 28.9707125711639,
          layerid: 'zfry_point_在线',
          lng: 119.63827334753055,
          name: '汤工宠物医院',
          objid: 1,
          phone: '-',
          sys_depart: 'T142391',
          type: '-',
          ymjzl: 100,
          myzz: '有',
          zpffzz: '有',
          address: '浙江省金华市磐安县xx街道',
          lxdh: '13812345678',
        },
      ],
      scaleWidth: 1,
      scaleHeight: 1,
      currentRoute: '',
      showZfzsPop: false,
      showFmtsPop: false,
      showWgPop: false,
      point: '',

      //视频相关
      pocDialogVisible: false,
      gVideoUser: '',
      gUID: '',
      gPWD: '',
      VideoPopNOGBVisible: false,
      VideoPopISGBVisible: false,
      type:"",
      videoCode:"",
      baseURL:"",
      username:"",
      pwd:"",
      videoMapId: [],
      timeOut: null,
    }
  },
  mounted() {
      this.init()
      window.PopClose = this.PopClose.bind(this)
      fn_start()
      this.$bus.$on("ShowZfzsPop", res => {
        this.point = res
        console.log(res)
        this.showZfzsPop = true
      })
      this.$bus.$on("ShowWgPop", res => {
        this.point = res
        console.log(res)
        this.showWgPop = true
      })
      this.$bus.$on("ShowFmtsPop", res => {
        this.point = res
        console.log(res)
        this.showFmtsPop = true
      })

      this.$bus.$on("pocVideoPop", res => {
        this.gVideoUser = res.gVideoUser;
        this.gUID = res.gUID;
        this.gPWD = res.gPWD;
        this.pocDialogVisible = true
      })
      this.$bus.$on("noGbVideoPop", res => {
        this.type = res.type
        this.videoCode = res.videoCode
        this.baseURL = res.baseURL;
        this.username = res.username;
        this.pwd = res.pwd;
        this.VideoPopNOGBVisible = true
      })
      this.$bus.$on("isGbVideoPop", res => {
        this.type = res.type
        this.videoCode = res.videoCode
        this.baseURL = res.baseURL;
        this.username = res.username;
        this.pwd = res.pwd;
        this.VideoPopISGBVisible = true
      })
  },
  beforeDestroy() {
    mapService.layers = []
  },
  methods:{
    init() {
        this.currentRoute = this.$route.path

        // 这里进行了一些逻辑处理 主要是考虑到有时间别人是直接用的url跳转的
        window.view = mapService.initMap('viewDiv')
        window.view.when(() => {
          mapService.yanmo()
          mapService.quhua()
          if (this.currentRoute !== '/zqyzt') {
            mapService.banKuai('金华市')
          }
          if (this.currentRoute === '/gzfw') {
            let textData = [
              { pos: [119.602579, 29.070607, 400], text: '开发区\n服务人次：60' },
              { pos: [119.514748, 28.964012, 400], text: '婺城区\n服务人次：60' },
              { pos: [119.799596, 29.149391, 400], text: '金东区\n服务人次：60' },
              { pos: [119.714529, 28.768287, 400], text: '武义县\n服务人次：60' },
              { pos: [119.903937, 29.520086, 400], text: '浦江县\n服务人次：60' },
              { pos: [120.609672, 29.007893, 400], text: '磐安县\n服务人次：60' },
              { pos: [119.526736, 29.278165, 400], text: '兰溪市\n服务人次：60' },
              { pos: [120.061011, 29.300614, 400], text: '义乌市\n服务人次：60' },
              { pos: [120.364678, 29.232405, 400], text: '东阳市\n服务人次：60' },
              { pos: [120.102417, 28.934317, 400], text: '永康市\n服务人次：60' },
            ]
            mapService.loadTextLayer({
              layerid: 'fwrc', //服务人次
              data: textData,
              style: {
                size: 30,
                color: '#ffffff',
                icon: './pointAssets/billboard.svg',
                iconSize: 320,
              },
            })
            setTimeout(() => {
              window.view.goTo({
                center: [119.839596, 29.149391],
                zoom: 10.9,
              })
            }, 3000)
          }
          if (this.currentRoute === '/csgl') {
            getAJtotal().then((res) => {
              let textData = [
                { pos: [119.526736, 29.278165, 400], text: `兰溪市\n事件总数：${res.data[1]?.acount}` },
                { pos: [120.364678, 29.232405, 400], text: `东阳市\n事件总数：${res.data[2]?.acount}` },
                { pos: [120.061011, 29.300614, 400], text: `义乌市\n事件总数：${res.data[3]?.acount}` },
                { pos: [119.903937, 29.520086, 400], text: `浦江县\n事件总数：${res.data[4]?.acount}` },
                { pos: [120.102417, 28.934317, 400], text: `永康市\n事件总数：${res.data[5]?.acount}` },
                { pos: [119.799596, 29.149391, 400], text: `金东区\n事件总数：${res.data[6]?.acount}` },
                { pos: [119.514748, 28.964012, 400], text: `婺城区\n事件总数：${res.data[7]?.acount}` },
                { pos: [119.602579, 29.070607, 400], text: `开发区\n事件总数：${res.data[8]?.acount}` },
                { pos: [119.714529, 28.768287, 400], text: `武义县\n事件总数：${res.data[9]?.acount}` },
                { pos: [120.609672, 29.007893, 400], text: `磐安县\n事件总数：${res.data[0]?.acount}` },
              ]
              mapService.loadTextLayer({
                layerid: 'sjs', //服务人次
                data: textData,
                style: {
                  size: 30,
                  color: '#ffffff',
                  icon: './pointAssets/billboard.svg',
                  iconSize: 320,
                },
              })
              setTimeout(() => {
                window.view.goTo({
                  center: [119.839596, 29.149391],
                  zoom: 10.9,
                })
              }, 3000)
            })
          }
          // mapService.banKuai("金华市")
          window.view.watch('zoom', () => {
              if (window.view.zoom < 10.5) {
                if(mapService.layers.bankuai){
                  mapService.layers.bankuai.visible = true
                }
              }
              if (window.view.zoom > 10.5) {
                if(mapService.layers.bankuai){
                  mapService.layers.bankuai.visible = false
                }
              }
          })
          window.view.watch('zoom', () => {
            if (window.view.zoom < 9.5) {
              window.view.zoom = 9.5
            }
          })
        })
        if (this.currentRoute.includes('/zqyzt')) {
          window.view.camera = {
            position: {
              spatialReference: {
                latestWkid: 4490,
                wkid: 4490,
              },
              x: 119.**************,
              y: 29.***************,
              z: 2226.************,
            },
            heading: 0.*****************,
            tilt: 0.*****************,
          }
        }
        window.view.on("pointer-move",(event) => {
                // 修正移动事件的坐标
                const scaleWidth1 = 1.0 / window.scaleWidth;
                const scaleHeight1 = 1.0 / window.scaleHeight;
                event.x = event.x * scaleWidth1;
                event.y = event.y * scaleHeight1;

                if (event.screenPoint) {
                    event.screenPoint.x = event.screenPoint.x * scaleWidth1;
                    event.screenPoint.y = event.screenPoint.y * scaleHeight1;
                }

              })
        // //  监听view中鼠标动作，高亮图像
        // let graphic
        // let highlightedList = [];
        // window.view
        //     .on("pointer-move",(event) => {
        //             // 修正移动事件的坐标
        //             const scaleWidth1 = 1.0 / window.scaleWidth;
        //             const scaleHeight1 = 1.0 / window.scaleHeight;
        //             event.x = event.x * scaleWidth1;
        //             event.y = event.y * scaleHeight1;

        //             if (event.screenPoint) {
        //                 event.screenPoint.x = event.screenPoint.x * scaleWidth1;
        //                 event.screenPoint.y = event.screenPoint.y * scaleHeight1;
        //             }
        //             window.view
        //                 .hitTest(event,{exclude:[window.view.graphics]})
        //                 .then((hitTestResult) => {

        //                     if (hitTestResult.results[0]) {
        //                         if (hitTestResult.results.length>1){
        //                             if (hitTestResult.results[1].layer.name && hitTestResult.results[1].layer.name !== 'yanmo') {
        //                               graphic = hitTestResult.results[1].graphic
        //                             }
        //                         }else{
        //                           if (hitTestResult.results[0].layer.name && hitTestResult.results[0].layer.name !== 'yanmo') {
        //                               graphic = hitTestResult.results[0].graphic
        //                             }
        //                         }
        //                         // highlight the hit object
        //                         if(graphic) {
        //                           window.view.whenLayerView(graphic.layer).then((layerView) => {
        //                             highlightedList.push(layerView.highlight(graphic));
        //                           });
        //                           highlightedList.forEach((highlightObject) => {
        //                             highlightObject.remove();
        //                         });
        //                         }

        //                     } else {
        //                         highlightedList.forEach((highlightObject) => {
        //                             highlightObject.remove();
        //                         });
        //                     }
        //                 })
        //     })
    },
    loadPoint(node) {
      let this_ = this
      if (this.currentRoute === '/dog') {
        if (node.name === '宠物医院' || node.name === '动物诊所') {
          mapService.loadPointLayer({
            data: this_.pointData, //点位数据
            layerid: node.name, //图层id
            iconcfg: {
              image: `./pointAssets/${node.name}.png`,
              size: 40,
            },
            popcfg: {
              offset: [0, 0],
              show: false, //关闭按钮
            },
            cluster: false,
            onclick: this_.dogPop,
          })
        }
      }
      if (this.currentRoute === '/csyxgl') {
        if (node.name === '环境监测') {
          let id = '10-0001'
          axios({
            method: 'post',
            url: '/dtdd/iot/aep/v1/jh-device/find',
            data: {
              group_code: id.split('-')[0],
              code: id.split('-')[1],
              page_size: 40000,
              page_num: 1,
            },
          }).then(function (allRes) {
            let allPointData = allRes.data.list

            let pointData = []
            let textData = []
            let twoText = []
            allPointData.forEach((item, index) => {
              let til = item.type_name === '公交车GPS' ? ['车牌号'] : ['名称']
              item.device_zxstate = "--";
              item.device_type = item.type_name
              item.cjTime = "--";
              item.type = "环境监测"

              var keyMap = {
                latitude: 'lat',
                longitude: 'lng',
              }
              for (var key in item) {
                var newKey = keyMap[key]
                if (newKey) {
                  item[newKey] = item[key]
                  delete item[key]
                }
              }
              pointData.push(item)
            })
            mapService.loadPointLayer({
              data: pointData,
              layerid: node.name, //图层id
              iconcfg: {
                image: './pointAssets/空气质量监测.png',
                iconSize: 0.3,
                iconlist: {
                  field: 'device_state',
                  list: [
                    {
                      value: 'ONLINE',
                      size: '80',
                      src: './pointAssets/空气质量监测.png',
                    },
                    {
                      value: 'INIT',
                      size: '80',
                      src: './pointAssets/空气质量监测-lx.png',
                    },
                  ],
                },
              }, //图标
              onclick: this_.hjjcPop,
            })
          })
        }
        if (node.name === '案件分布') {
          console.log(node)
          getAJDist().then((res) => {
            res.data.forEach((item) => {
              item.lat = item.y84
              item.lng = item.x84
            })
            mapService.loadPointLayer({
              data: res.data,
              layerid: node.name,
              cluster: false,
              iconcfg: { image: `./pointAssets/监测报警事件.png`, size: 40 },
              onclick: this.ajfbPop,
            })
          })
        }
      }
      if (this.currentRoute === '/wlwsb') {
        const deviceTypeMap = {
          '液位监测': '液位监测感知设备',
          '流量监测': '流量监测感知设备',
          '液位、雨量监测': '液位、雨量监测综合感知设备',
          '空气质量监测': '空气质量监测（综合）感知设备',
          '水质监测': '水质监测（综合）',
          '水肥一体化监测': '水肥一体化监测感知设备',
          '气体监测': '气体（综合）感知设备'
        }
        const deviceTypeParam = deviceTypeMap[node.name] || ''
        getDevicePoint({deviceTypeList: deviceTypeParam}).then((res) => {
          res.data.forEach((item) => {
            item.lat = item.lat
            item.lng = item.lon
          })
          mapService.loadPointLayer({
            data: res.data,
            layerid: node.name,
            cluster: true,
            iconcfg: { image: `./pointAssets/点位/${deviceTypeParam}@1x.png`, size: 40 },
            // onclick: this.onclick,
          })
           console.log(res)
        })
      }
      if (this.currentRoute === '/yxjc') {
        if (node.name === '监测报警事件') {
          getJianCe().then((res) => {
            res.data.forEach((item) => {
              item.lat = item.lat
              item.lng = item.lon
            })
            mapService.loadPointLayer({
              data: res.data,
              layerid: node.name,
              cluster: false,
              iconcfg: { image: `./pointAssets/${node.name}@1x.png`, size: 40 },
              onclick: this.jcyjPop,
            })
          })
        }
        if (node.name === '人员密集预警') {
          getRenYuan().then((res) => {
            res.data.forEach((item) => {
              item.lat = item.lat
              item.lng = item.lon
            })
            mapService.loadPointLayer({
              data: res.data,
              layerid: node.name,
              cluster: false,
              iconcfg: { image: `./pointAssets/${node.name}@1x.png`, size: 40 },
              onclick: this.jcyjPop,
            })
          })
        }
        if (node.name === '监测点') {
          getMonitorPoint().then((res) => {
            res.data.forEach((item) => {
              item.lat = item.lat
              item.lng = item.lon
            })
            mapService.loadPointLayer({
              data: res.data,
              layerid: node.name,
              cluster: false,
              iconcfg: { image: `./pointAssets/${node.name}@1x.png`, size: 40 },
              onclick: this.monitorPointPop,
            })
          })
        }
      }
    },
    // 犬类弹窗
    dogPop(e) {
      console.log('点到了', e)
      let this_ = this
      let str = `
            <div
              style="
                position: relative;
                background: url('./pointAssets/du_bg2.png') no-repeat;
                background-size: 100% 100%;
                width: 800px;
                min-height: 500px;
                padding: 20px;
              "
            >
              <nav
                style="
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  padding: 10px 20px;
                  border-bottom: 2px solid;
                  border-image: linear-gradient(-90deg, rgba(0, 216, 247, 0) 0%, #00afed 100%) 2 2 2 2;
                "
              >
                <h2 style="margin: 0; color: #fff; font-size: 35px;">机构详情</h2>
                <span
                  style="cursor: pointer;"
                  onclick="window.PopClose()"
                >
                  <img style="vertical-align: middle; width: 24px; height: 24px;" src="./pointAssets/close.png" alt="" />
                </span>
              </nav>

              <div style="padding: 10px 40px;">
                <div>
                  <p style="margin-bottom: 0px; font-size: 30px; color: #2299e2; white-space: nowrap; text-overflow: ellipsis; overflow: hidden;">
                    机构名称：<span style="color: #fff;">${e.name || '--'}</span>
                  </p>
                  <p style="margin-bottom: 0px; font-size: 30px; color: #2299e2; white-space: nowrap; text-overflow: ellipsis; overflow: hidden;">
                    地址：<span style="color: #fff;">${e.address || '--'}</span>
                  </p>
                  <p style="margin-bottom: 0px; font-size: 30px; color: #2299e2; white-space: nowrap; text-overflow: ellipsis; overflow: hidden;">
                    联系电话：<span style="color: #fff;">${e.lxdh || '--'}</span>
                  </p>
                  <p style="margin-bottom: 0px; font-size: 30px; color: #2299e2; white-space: nowrap; text-overflow: ellipsis; overflow: hidden;">
                    疫苗接种量：<span style="color: #fff;">${e.ymjzl || '--'}</span>
                  </p>
                  <p style="margin-bottom: 0px; font-size: 30px; color: #2299e2; white-space: nowrap; text-overflow: ellipsis; overflow: hidden;">
                    免疫资质：<span style="color: #fff;">${e.myzz || '--'}</span>
                  </p>
                  <p style="margin-bottom: 0px; font-size: 30px; color: #2299e2; white-space: nowrap; text-overflow: ellipsis; overflow: hidden;">
                    犬牌发放资质：<span style="color: #fff;">${e.zpffzz || '--'}</span>
                  </p>
                </div>
              </div>
            </div>
          `
      let objData = {
        layerid: 'Pop',
        position: [e.esX, e.esY],
        offset: [45, 0], //3840*2160
        content: str,
      }
      mapService._createPopup(objData)
    },
    // 首页环境监测弹窗
    hjjcPop(e) {
      console.log('点到了', e)
      let this_ = this
      let str = `
            <div
              style="
                position: relative;
                background: url('./pointAssets/du_bg2.png') no-repeat;
                background-size: 100% 100%;
                width: 730px;
                min-height: 390px;
                padding: 20px;
              "
            >
              <nav
                style="
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  padding: 10px 20px;
                  border-bottom: 2px solid;
                  border-image: linear-gradient(-90deg, rgba(0, 216, 247, 0) 0%, #00afed 100%) 2 2 2 2;
                "
              >
                <h2 style="margin: 0; color: #fff; font-size: 35px;">点位详情</h2>
                <span
                  style="cursor: pointer;"
                  onclick="window.PopClose()"
                >
                  <img style="vertical-align: middle; width: 24px; height: 24px;" src="./pointAssets/close.png" alt="" />
                </span>
              </nav>

              <div style="padding: 10px 40px;">
                <div>
                  <p style="margin-bottom: 0px; font-size: 30px; color: #2299e2; white-space: nowrap; text-overflow: ellipsis; overflow: hidden;">
                    名称：<span style="color: #fff;">${e.device_name || '--'}</span>
                  </p>
                  <p style="margin-bottom: 0px; font-size: 30px; color: #2299e2; white-space: nowrap; text-overflow: ellipsis; overflow: hidden;">
                    分组类型：<span style="color: #fff;">${e.group_name || '--'}</span>
                  </p>
                  <p style="margin-bottom: 0px; font-size: 30px; color: #2299e2; white-space: nowrap; text-overflow: ellipsis; overflow: hidden;">
                    设备类型：<span style="color: #fff;">${e.type_name || '--'}</span>
                  </p>
                  <p style="margin-bottom: 0px; font-size: 30px; color: #2299e2; white-space: nowrap; text-overflow: ellipsis; overflow: hidden;">
                    承载单位：<span style="color: #fff;">${e.dept_name || '--'}</span>
                  </p>
                  <p style="margin-bottom: 0px; font-size: 30px; color: #2299e2; white-space: nowrap; text-overflow: ellipsis; overflow: hidden;">
                    所属区域：<span style="color: #fff;">${e.administrative_division_name || '--'}</span>
                  </p>
                  <p style="margin-bottom: 0px; font-size: 30px; color: #2299e2; white-space: nowrap; text-overflow: ellipsis; overflow: hidden;">
                    所属乡镇：<span style="color: #fff;">${e.township_division_name || '--'}</span>
                  </p>
                </div>
              </div>
            </div>
          `
      let objData = {
        layerid: 'Pop',
        position: [e.esX, e.esY],
        offset: [45, 0], //3840*2160
        content: str,
      }
      mapService._createPopup(objData)
    },
    // 首页案件分布弹窗
    ajfbPop(e) {
      console.log('点到了', e)
      let this_ = this
      let str = `
            <div
              style="
                position: relative;
                background: url('./pointAssets/du_bg2.png') no-repeat;
                background-size: 100% 100%;  /* 修改背景尺寸适配方式 */
                width: 730px;
                min-height: auto;  /* 移除固定最小高度 */
                padding: 20px 20px 40px;  /* 调整内边距 */
                display: flex;
                flex-direction: column;
              "
            >
              <nav
                style="
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  padding: 10px 20px;
                  border-bottom: 2px solid;
                  border-image: linear-gradient(-90deg, rgba(0, 216, 247, 0) 0%, #00afed 100%) 2 2 2 2;
                  flex-shrink: 0;  /* 防止导航栏压缩 */
                "
              >
                <h2 style="margin: 0; color: #fff; font-size: 35px;">案件详情</h2>
                <span
                  style="cursor: pointer;"
                  onclick="window.PopClose()"
                >
                  <img style="vertical-align: middle; width: 24px; height: 24px;" src="./pointAssets/close.png" alt="" />
                </span>
              </nav>

              <div style="padding: 10px 10px; flex-grow: 1; display: flex; flex-direction: column;">  <!-- 添加弹性布局 -->
                <div style="flex: 1;">  <!-- 内容区域自动填充剩余空间 -->
                  <p style="margin-bottom: 10px; font-size: 30px; color: #2299e2; display: flex;">
                    <span style="flex-shrink: 0;">创建时间：</span>
                    <span style="color: #fff; flex-grow: 1; overflow: hidden; text-overflow: ellipsis;">${e.createtime || '--'}</span>
                  </p>
                  <p style="margin-bottom: 10px; font-size: 30px; color: #2299e2; display: flex;">
                    <span style="flex-shrink: 0;">创建人员：</span>
                    <span style="color: #fff; flex-grow: 1; overflow: hidden; text-overflow: ellipsis;">${e.createid || '--'}</span>
                  </p>
                  <p style="margin-bottom: 10px; font-size: 30px; color: #2299e2; display: flex;">
                    <span style="flex-shrink: 0;">案件地址：</span>
                    <span style="color: #fff; flex-grow: 1; overflow: hidden; text-overflow: ellipsis;">${e.address || '--'}</span>
                  </p>
                  <p style="margin-bottom: 10px; font-size: 30px; color: #2299e2; display: flex; align-items: flex-start;">
                    <span style="flex-shrink: 0;">案件描述：</span>
                    <span style="color: #fff; flex-grow: 1; overflow: auto; max-height: 200px; word-break: break-all;">${e.eventdesc || '--'}</span>
                  </p>
                  <p style="margin-bottom: 40px; font-size: 30px; color: #2299e2; display: flex;">
                    <span style="flex-shrink: 0;">处理进度：</span>
                    <span style="color: #fff; flex-grow: 1; overflow: hidden; text-overflow: ellipsis;">${e.status_desc || '--'}</span>
                  </p>
                </div>
              </div>
            </div>
          `
      let objData = {
        layerid: 'Pop',
        position: [e.esX, e.esY],
        offset: [45, 0], //3840*2160
        content: str,
      }
      mapService._createPopup(objData)
    },
    // 运行监测一张图弹窗
    // 监测报警事件和人员密集事件弹窗
    jcyjPop(e) {
      console.log('点到了', e)
      let this_ = this
      let str = `
            <div
              style="
                position: relative;
                background: url('./pointAssets/du_bg2.png') no-repeat;
                background-size: 100% 100%;  /* 修改背景尺寸适配方式 */
                width: 730px;
                min-height: auto;  /* 移除固定最小高度 */
                padding: 20px 20px 40px;  /* 调整内边距 */
                display: flex;
                flex-direction: column;
              "
            >
              <nav
                style="
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  padding: 10px 20px;
                  border-bottom: 2px solid;
                  border-image: linear-gradient(-90deg, rgba(0, 216, 247, 0) 0%, #00afed 100%) 2 2 2 2;
                  flex-shrink: 0;  /* 防止导航栏压缩 */
                "
              >
                <h2 style="margin: 0; color: #fff; font-size: 35px;">事件详情</h2>
                <span
                  style="cursor: pointer;"
                  onclick="window.PopClose()"
                >
                  <img style="vertical-align: middle; width: 24px; height: 24px;" src="./pointAssets/close.png" alt="" />
                </span>
              </nav>

              <div style="padding: 10px 10px; flex-grow: 1; display: flex; flex-direction: column;">  <!-- 添加弹性布局 -->
                <div style="flex: 1;">  <!-- 内容区域自动填充剩余空间 -->
                  <p style="margin-bottom: 10px; font-size: 30px; color: #2299e2; display: flex;">
                    <span style="flex-shrink: 0;">事件标题：</span>
                    <span style="color: #fff; flex-grow: 1; overflow: hidden; text-overflow: ellipsis;">${e.eventTitle || '--'}</span>
                  </p>
                  <p style="margin-bottom: 10px; font-size: 30px; color: #2299e2; display: flex;">
                    <span style="flex-shrink: 0;">事件等级：</span>
                    <span style="color: #fff; flex-grow: 1; overflow: hidden; text-overflow: ellipsis;">${e.eventLevel || '--'}</span>
                  </p>
                  <p style="margin-bottom: 10px; font-size: 30px; color: #2299e2; display: flex;">
                    <span style="flex-shrink: 0;">报警时间：</span>
                    <span style="color: #fff; flex-grow: 1; overflow: hidden; text-overflow: ellipsis;">${e.eventTime || '--'}</span>
                  </p>
                  <p style="margin-bottom: 10px; font-size: 30px; color: #2299e2; display: flex; align-items: flex-start;">
                    <span style="flex-shrink: 0;">事件内容：</span>
                    <span style="color: #fff; flex-grow: 1; overflow: auto; max-height: 200px; word-break: break-all;">${e.eventContent || '--'}</span>
                  </p>
                  <p style="margin-bottom: 40px; font-size: 30px; color: #2299e2; display: flex;">
                    <span style="flex-shrink: 0;">事件地址：</span>
                    <span style="color: #fff; flex-grow: 1; overflow: hidden; text-overflow: ellipsis;">${e.eventAddress || '--'}</span>
                  </p>
                </div>
              </div>
            </div>
          `
      let objData = {
        layerid: 'Pop',
        position: [e.esX, e.esY],
        offset: [50, 0], //3840*2160
        content: str,
      }
      mapService._createPopup(objData)
    },
    // 监测点弹窗
    monitorPointPop(e) {
      console.log('点到了', e)
      let this_ = this
      let str = `
            <div
              style="
                position: relative;
                background: url('./pointAssets/du_bg2.png') no-repeat;
                background-size: 100% 100%;  /* 修改背景尺寸适配方式 */
                width: 500px;
                min-height: auto;  /* 移除固定最小高度 */
                padding: 20px 20px 40px;  /* 调整内边距 */
                display: flex;
                flex-direction: column;
              "
            >
              <nav
                style="
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  padding: 10px 20px;
                  border-bottom: 2px solid;
                  border-image: linear-gradient(-90deg, rgba(0, 216, 247, 0) 0%, #00afed 100%) 2 2 2 2;
                  flex-shrink: 0;  /* 防止导航栏压缩 */
                "
              >
                <h2 style="margin: 0; color: #fff; font-size: 35px;">点位详情</h2>
                <span
                  style="cursor: pointer;"
                  onclick="window.PopClose()"
                >
                  <img style="vertical-align: middle; width: 24px; height: 24px;" src="./pointAssets/close.png" alt="" />
                </span>
              </nav>

              <div style="padding: 10px 10px; flex-grow: 1; display: flex; flex-direction: column;">  <!-- 添加弹性布局 -->
                <div style="flex: 1;">  <!-- 内容区域自动填充剩余空间 -->
                  <p style="margin-bottom: 10px; font-size: 30px; color: #2299e2; display: flex;">
                    <span style="flex-shrink: 0;">监测点名称：</span>
                    <span style="color: #fff; flex-grow: 1; overflow: hidden; text-overflow: ellipsis;">${e.pointName || '--'}</span>
                  </p>
                  <p style="margin-bottom: 10px; font-size: 30px; color: #2299e2; display: flex;">
                    <span style="flex-shrink: 0;">监测点类型：</span>
                    <span style="color: #fff; flex-grow: 1; overflow: hidden; text-overflow: ellipsis;">${e.pointType || '--'}</span>
                  </p>
                  <p style="margin-bottom: 10px; font-size: 30px; color: #2299e2; display: flex;">
                    <span style="flex-shrink: 0;">监测点负责人：</span>
                    <span style="color: #fff; flex-grow: 1; overflow: hidden; text-overflow: ellipsis;">${e.pointUserName || '--'}</span>
                  </p>
                  <p style="margin-bottom: 40px; font-size: 30px; color: #2299e2; display: flex;">
                    <span style="flex-shrink: 0;">联系方式：</span>
                    <span style="color: #fff; flex-grow: 1; overflow: hidden; text-overflow: ellipsis;">${e.pointUserMobile || '--'}</span>
                  </p>
                </div>
              </div>
            </div>
          `
      let objData = {
        layerid: 'Pop',
        position: [e.esX, e.esY],
        offset: [50, 0], //3840*2160
        content: str,
      }
      mapService._createPopup(objData)
    },

    removePoint(node) {
      mapService.removeLayer(node.name)
    },
    // 弹窗关闭
    PopClose() {
      if (mapService.popups.layerid.includes('Pop')) {
        mapService.removeLayer('Pop')
      }
    },
    loadLayer(node) {
      let that = this
      if (node.name === '人口热力') {
        const mapData = {
          layerid: node.name,
          type: "dynamic",
        };
        mapService.loadHeatmapLayer(mapData);
      }
      if (node.name === '交通流量') {
        // 新地图添加路况
        mapService.loadTrafficLayer({
          layerid: node.name,
        })
        this.pointRoadFun()
        let time = setInterval(() => {
          that.timeOut = setTimeout(that.pointRoadFun, 0)
        }, 1000 * 120)
      }
    },
    removeLayer(node) {
      mapService.removeLayer(node.name)
      if (node.name === '交通流量') {
        // 清除路况
        let mapIdArr = [
          'hxRoad_index',
          'ydRoad_index',
          'yzydRoad_index',
          '拥堵视频',
          '拥堵点1',
          'icon_road_2',
          'camera-index-3840',
        ]
        mapService.removeAllLayers(mapIdArr)
        mapService.removeLayer('mouseente01')
        mapService.removeLayer('mouseente02')
        //  清除所有视频
        for (let i = 0; i < this.videoMapId.length; i++) {
          let id = this.videoMapId[i]
          this.rmLayer('拥堵视频' + id)
          this.rmLayer('roadText' + id)
        }
        // 清除定时器
        let end = setInterval(function () {}, 3)
        for (let i = 1; i <= end; i++) {
          clearInterval(i)
        }
        clearTimeout(this.timeOut)
      }
    },
    // 给拥堵上点
    pointRoadFun() {
      let that = this
      let str = "'婺城区','金东区','武义县','浦江县','磐安县','兰溪市','义乌市','东阳市','永康市','开发区'"

      // let mapIdArr = ['hxRoad_index', 'ydRoad_index', 'yzydRoad_index', '拥堵视频', '拥堵点1']
      // this.rmAllLayer(mapIdArr)
      // //  清除所有视频
      // for (let i = 0; i < this.videoMapId.length; i++) {
      //   let id = this.videoMapId[i]
      //   this.rmLayer(id)
      // }
      indexApi('/cstz_baiduydd', { addressName: str }).then((res) => {
        for (let i = 0; i < res.length; i++) {
          let time = new Date(res[i].insert_time)
          let h = time.getHours(),
            m = time.getMinutes()
          res[i].insert_time = h.toString().padStart(2, '0') + ':' + m.toString().padStart(2, '0')
          res[i].idx = Number(res[i].idx)
        }
        var roadPointData = []
        res.data.map((ele) => {
          let roadName = `${ele.roadName}`
          let address = ele.description ? ele.description + ele.direction : '--'
          let arr = ele.location.split(',')
          let pointArr = mapService.transTo4490(arr)
          let point = pointArr[0] + ',' + pointArr[1]
          let str = {
            data: {
              item: ele,
              title: roadName,
              linkStates: ele.linkStates,
              key: ['指数', '时速', '拥堵距离', '持续时间'],
              value: [ele.idx, ele.speed, ele.distance, ele.durationMin],
              distance: ele.distance,
            },
            address: address,
            point: point,
            lng: pointArr[0],
            lat: pointArr[1],
            type: '路况拥堵点',
            idx: ele.idx,
            location: ele.location,
            linkStates: ele.linkStates,
          }
          roadPointData.push(str)
        })
        mapService.loadPointLayer({
          data: roadPointData,
          layerid: '拥堵点1',
          iconcfg: { image: `./pointAssets/icon/spritesImage/拥堵.png`, iconSize: 40 },
          onclick: this.onclick,
        })
        clearTimeout(this.timeOut)
      })
    },
    onclick(e, list) {
      mapService.removeLayer('mouseente01')
      mapService.removeLayer('mouseente02')
      if (e.data.chn_code) {
        mapService.flyTo({
          destination: [e.data.gps_x, e.data.gps_y],
          // zoom: 15,
          offset: [0, -666],
        })
        let item = {
          obj: {
            // chn_name: e.data.chn_name,
            chn_name: e.data.video_name,
            pointList: list,
          },
          video_code: e.data.chn_code,
          csrk: true,
        }
        let iframe1 = {
          type: 'openIframe',
          name: 'video_main_code',
          src: baseURL.url + '/static/citybrain/tcgl/commont/video_main_code.html',
          width: '100%',
          height: '100%',
          left: '0',
          top: '0',
          zIndex: '1000',
          argument: item,
        }
        window.parent.lay.openIframe(iframe1)
      } else if (e.type == '路况拥堵点') {
        let coor = [e.lng, e.lat]
        let arr = {
          name: e.data.key,
          value: e.data.value,
        }
        let countStr = ''
        for (let index = 0; index < arr.name.length; index++) {
          countStr += `<div style="margin:0 10px">${arr.name[index]}：${arr.value[index]}</div>`
        }
        let str = `
                    <div
                    style="
                        position: relative;
                        background: url('./pointAssets/du_bg2.png') no-repeat;
                        background-size: 100% 100%;
                        width: max-content;
                        min-height: 320px;
                    "
                    >
                        <nav  style="display: flex; justify-content: space-between; margin: 0 20px">
                        <h2  style="
                            margin-top: 20px;
                            white-space: nowrap;
                            font-size: 38px;
                            display: flex;
                            background: linear-gradient(to bottom, #df5151, #f4f1ff, #ff4949, #e03e3e);
                            -webkit-background-clip: text;
                            color: transparent;">
                            <img src="/static/citybrain/csdn/img/dupoint.png" width="50px" alt="" />
                            ${e.data.title}${e.data.item.eventSource}（${e.data.item.insert_time}）
                            <span style="font-size: 32px !important;font-style: italic;">${e.data.item.congestAffectArea}</span>
                        </h2>
                        <span style="cursor: pointer; margin-left: 20px; font-size: 40px;width:34px;color: #fff" onclick="this.parentNode.parentNode.style.display = 'none'">x</span>
                        </nav>
                        <header style="font-size: 32px;margin: 20px 20px 10px 20px; display: flex; justify-content: space-between;padding:10px;box-sizing:border-box;">
                        <div style="width: 80%; overflow: hidden;display:flex">
                            <div>
                            <p  style="background: linear-gradient(to bottom, #ffebce, #ffffff, #ffc559, #ffffff);
                            -webkit-background-clip: text;
                            color: transparent;font-size:36px !important;
                            width: 500px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis
                            "
                                title="${e.address}">${e.address}</p>
                            <p  style="color: #fff;width: 500px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis" title="${e.data.item.congestTailDesc}">源头：${e.data.item.congestTailDesc}</p>
                            </div>
                        </div>
                        <div style="font-size: 32px;color: #fff; background: rgba(216, 81, 81, 0.897);padding: 2px 5px;border-radius: 5px;height:40px;line-height:40px;">${e.data.item.extraEventStatus}</div>
                        </header>
                        <footer style="color: #05BE94;white-space: nowrap;display: flex;justify-content: space-around;margin: 0 20px;font-size: 32px;">
                        ${countStr}
                        </footer>
                    </div> `

        let objData = {
          layerid: e.layerid,
          position: coor,
          offset: [55, -40],
          closeButton: true,
          content: str,
        }

        mapService._createPopup(objData)
        this.roadMapFun(e)
      }
    },
    roadMapFun(obj) {
      let that = this
      let arrLngLats = obj.data.linkStates
      // 畅通 缓行 拥堵 严重拥堵
      // 由于调用了地图绿色的路况了所以只要除了畅通以外的数据
      mapService.removeAllLayers(['hxRoad_index', 'ydRoad_index', 'yzydRoad_index'])
      mapService.removeAllLayers(['camera-index-3840'])
      //  清除所有视频
      for (let i = 0; i < this.videoMapId.length; i++) {
        let id = this.videoMapId[i]
        mapService.removeLayer('拥堵视频' + id)
        mapService.removeLayer('roadText' + id)
      }
      let hxData = []
      let ydData = []
      let yzydData = []
      let point = obj.location.split(',')
      let pointArr = mapService.transTo4490(point)
      this.videoMapId = []
      let arrList = []
      for (let key of Object.keys(arrLngLats)) {
        let line = arrLngLats[key]
        let arrData = line.split(';')
        let lineArr = []
        let lineStr = ''
        for (let i = 0; i < arrData.length; i++) {
          // 排序
          // let str=that.roadSort(arrData[i])
          // 不排序
          let str = arrData[i]
          let arr = str.split(/[,;]/)
          let coords = mapService.transTo4490(arr)
          let coordsStr = coords.join(',')
          lineArr.push(coordsStr)
          obj.idx >= 1.5 && obj.idx < 2
            ? hxData.push(coords)
            : obj.idx >= 2 && obj.idx < 4
            ? ydData.push(coords)
            : obj.idx >= 4
            ? yzydData.push(coords)
            : ''
        }
        lineStr = lineArr.join(';')

        let len = Math.ceil(lineArr.length / 2 - 1)
        let linePoint = lineArr[len].split(',')

        that.videoMapId.push(key)
        mapService.loadTextLayer({
          layerid: 'roadText' + key,
          data: [
            {
              pos: [linePoint[0], linePoint[1]], //上文字经纬度
              //内容
              text: obj.data.distance,
            },
          ], //数据
          style: {
            size: 40, //文字大小
            color: [252, 198, 42, 1], //文字颜色
          },
        })
        mapService.loadRoadVideo({
          layerid: '拥堵视频' + key,
          videoType: '拥堵视频' + key,
          distance: 30,
          lineStr: lineStr,
          // onclick: this.onclick,
          callback: (e) => {
            e.forEach((item) => {
              arrList.push(item)
            })
          },
        })
      }
      that.addPoint(arrList)
    },
    async addPoint(arrList) {
      // console.log('arrList=>',arrList)
      //去重
      let forData = []
      for (let i = 0; i < arrList.length; i++) {
        if (!forData.some((e) => e.id == arrList[i].id)) forData.push(arrList[i])
      }
      // console.log('forData=>',forData)
      let arr = []
      for (let i = 0; i < forData.length; i++) {
        let res = await indexApi('/xxwh_dwzlmore', {
          code: forData[i].chn_code,
        }).then((res) => {
          return {
            code: res[0].chn_code,
            pointId: 'camera-index-3840',
            data: res[0],
            point: res[0].gps_x + ',' + res[0].gps_y,
            lng: res[0].gps_x,
            lat: res[0].gps_y,
            status: res[0].is_online,
            cameraType: res[0].cameraType,
            pointType: this.getPointType(res[0].is_online, res[0].cameraType),
          }
        })
        arr.push(res)
      }
      console.log(arr)
      // this.cameraList.forEach((item) => {
      //   this.getManyPoint(this.filterData(arr, item.name), item.code)
      // })

      // 加载视频点位的先隐藏
      // this.getManyPoint(arr)
    },
  },
  watch: {
    $route(to, from) {
      console.log('fff111', to)
      this.currentRoute = to.path
      mapService.removeAllLayers()
      if (this.currentRoute === '/zqyzt') {
        window.view.camera = {
          position: {
            spatialReference: {
              latestWkid: 4490,
              wkid: 4490,
            },
            x: 119.**************,
            y: 29.109005649408036,
            z: 4226.************,
          },
          heading: 0.*****************,
          tilt: 0.*****************,
        }
      } else {
        window.view.camera = {
          position: {
            spatialReference: {
              wkid: 4490,
            },
            x: 120.02164073413151,
            y: 28.922141859406103,
            z: 455201.1748902945,
          },
          heading: 353.0620839814934,
          tilt: 0.8979354024150412,
        }
      }
      if (this.currentRoute === '/gzfw') {
        let textData = [
          { pos: [119.602579, 29.070607, 400], text: '开发区\n服务人次：60' },
          { pos: [119.514748, 28.964012, 400], text: '婺城区\n服务人次：60' },
          { pos: [119.799596, 29.149391, 400], text: '金东区\n服务人次：60' },
          { pos: [119.714529, 28.768287, 400], text: '武义县\n服务人次：60' },
          { pos: [119.903937, 29.520086, 400], text: '浦江县\n服务人次：60' },
          { pos: [120.609672, 29.007893, 400], text: '磐安县\n服务人次：60' },
          { pos: [119.526736, 29.278165, 400], text: '兰溪市\n服务人次：60' },
          { pos: [120.061011, 29.300614, 400], text: '义乌市\n服务人次：60' },
          { pos: [120.364678, 29.232405, 400], text: '东阳市\n服务人次：60' },
          { pos: [120.102417, 28.934317, 400], text: '永康市\n服务人次：60' },
        ]
        mapService.loadTextLayer({
          layerid: 'fwrc', //服务人次
          data: textData,
          style: {
            size: 30,
            color: '#ffffff',
            icon: './pointAssets/billboard.svg',
            iconSize: 320,
          },
        })
        setTimeout(() => {
          window.view.goTo({
            center: [120.039596, 29.149391],
            zoom: 10.9,
          })
        }, 3000)
      }
      if (this.currentRoute === '/csgl') {
        debugger
        let textData = [
          { pos: [119.602579, 29.070607, 400], text: '开发区\n事件总数：60' },
          { pos: [119.514748, 28.964012, 400], text: '婺城区\n事件总数：60' },
          { pos: [119.799596, 29.149391, 400], text: '金东区\n事件总数：60' },
          { pos: [119.714529, 28.768287, 400], text: '武义县\n事件总数：60' },
          { pos: [119.903937, 29.520086, 400], text: '浦江县\n事件总数：60' },
          { pos: [120.609672, 29.007893, 400], text: '磐安县\n事件总数：60' },
          { pos: [119.526736, 29.278165, 400], text: '兰溪市\n事件总数：60' },
          { pos: [120.061011, 29.300614, 400], text: '义乌市\n事件总数：60' },
          { pos: [120.364678, 29.232405, 400], text: '东阳市\n事件总数：60' },
          { pos: [120.102417, 28.934317, 400], text: '永康市\n事件总数：60' },
        ]
        mapService.loadTextLayer({
          layerid: 'sjs', //服务人次
          data: textData,
          style: {
            size: 30,
            color: '#ffffff',
            icon: './pointAssets/billboard.svg',
            iconSize: 320,
          },
        })
        setTimeout(() => {
          window.view.goTo({
            center: [120.039596, 29.149391],
            zoom: 10.9,
          })
        }, 3000)
      }
      if (this.currentRoute !== '/zqyzt') {
        mapService.banKuai('金华市')
      }
    },
    '$store.getters.callType'(nVal) {
      if (nVal == 'callIn') {
        // 当有执法记录仪呼入时
        this.$notify.info({
          title: `${this.$store.state.board.callArgs.name}呼入`,
          showClose: false,
          position: 'bottom-right',
          duration: 0,
          dangerouslyUseHTMLString: true,
          message: <notifyMsg />,
        })
      }
    },
    '$store.getters.videoId'(nVal) {
      if (nVal) {
        this.terminalNo = String(nVal)
      }
    },
    immediate: true,
  },
}
</script>
