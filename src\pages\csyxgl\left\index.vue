<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-03-10 14:47:13
 * @LastEditors: wjb
 * @LastEditTime: 2025-03-11 08:42:20
-->
<template>
  <div class="left-Map">
    <sjtr></sjtr>
    <ajlx></ajlx>
    <ycgl></ycgl>
  </div>
</template>

<script>
import sjtr from './sjtr'
import ajlx from './ajlx'
import ycgl from './ycgl'

export default {
  name: 'index',
  components: { sjtr, ajlx, ycgl },
  data() {
    return {}
  },
  computed: {},
  mounted() {},
  methods: {},
  watch: {},
}
</script>

<style scoped>
</style>