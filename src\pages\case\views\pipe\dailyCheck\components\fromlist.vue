<template>
  <div>
    <el-dialog class="m-dialog" :close-on-click-modal="false" append-to-body v-bind="$attrs" :title="title" v-on="$listeners" @open="onOpen" @close="onClose">
      <el-scrollbar style="height: 100%;">
        <el-row :gutter="15" style="margin-right: 10px;">
          <el-form ref="elForm" v-loading="formLoading" :model="formData" :rules="rules" :disabled="formDisabled" size="medium" label-width="120px">
            <h3 class="title">基本信息</h3>
            <el-col :span="12">
              <el-form-item label="巡查人员" prop="userName">
                <el-input v-model="formData.userName" placeholder="请输入巡查人员" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="辅助人员" prop="userNames">
                <el-input v-model="formData.userNames" placeholder="请选择辅助人员名称" readonly>
                  <el-button slot="append" type="primary" @click="userNamesOpen = true">选择</el-button>
                </el-input>
                <userSelect id="userNames" v-model="userNamesOpen" :select-user-keys="formData.userIds?(formData.userIds+'').split(','):[]" :default-expanded-keys="formData.userIds?(formData.userIds+'').split(','):['100']" @confirm="deptConfirm" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="网格小组" prop="deptName">
                <el-input v-model="formData.deptName" readonly placeholder="请选择网格小组" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="巡查时间" prop="inspectionTime">
                <el-date-picker v-model="formData.inspectionTime" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="请选择巡查时间" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="当事人" prop="party">
                <el-input v-model="formData.party" placeholder="请输入当事人" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="车辆车牌" prop="carNo">
                <el-input v-model="formData.carNo" placeholder="请输入车辆车牌" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="车辆品牌" prop="models">
                <el-input v-model="formData.models" placeholder="请输入车辆品牌" clearable />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="营运公司" prop="operationCompany">
                <el-input v-model="formData.operationCompany" placeholder="请输入营运公司" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="车辆类型" prop="carType">
                <el-select v-model="formData.carType" placeholder="请输入车辆类型" clearable :style="{ width: '100%' }" @change="handleCarChange" @clear="handleCarClear">
                  <el-option v-for="(item, index) in carTypeOptions" :key="index" :label="item.dictLabel" :value="item.dictValue" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="有无问题" prop="isProblem">
                <el-select v-model="formData.isProblem" placeholder="请选择有无问题" clearable :style="{ width: '100%' }">
                  <el-option label="没有问题" value="0" />
                  <el-option label="有问题" value="1" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col v-if="formData.isProblem == 1" :span="12">
              <el-form-item label="问题类型" prop="problemType">
                <el-select v-model="formData.problemType" placeholder="请选择问题类型" clearable :style="{ width: '100%' }" @change="handleProblemChange">
                  <el-option
                    v-for="(item, index) in ProblemTypeOptions"
                    :key="index"
                    :label="item.dictLabel"
                    :value="item.dictValue"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="地址" prop="address">
                <div>
                  <el-input v-model="formData.address" placeholder="请选择巡查地址" style="width: 95%;" />
                  <svg-icon icon-class="map" class="svg-icon" @click="openMap = true" />
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="巡查内容" prop="content">
                <el-input v-model="formData.content" type="textarea" placeholder="请输入巡查内容" :maxlength="150" show-word-limit :autosize="{minRows: 4, maxRows: 4}" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="上传图片" prop="files">
                <el-upload
                  ref="upload"
                  multiple
                  :limit="4"
                  list-type="picture-card"
                  class="upload-demo"
                  accept=".jpg,.jpeg,.png,.JPG,.JPEG,.PNG"
                  action="/zqzfj/system/file/upload"
                  :auto-upload="false"
                  :headers="headers"
                  :file-list="formFiles"
                  :on-preview="handlePictureCardPreview"
                  :before-remove="handleRemove"
                  :on-success="handleSuccess"
                  :on-error="handleError"
                  :data="fileEXData"
                  name="files"
                  :on-exceed="exceed"
                >
                  <i class="el-icon-plus" />
                </el-upload>
              </el-form-item>
            </el-col>

            <!-- <el-col :span="8">
              <el-form-item label="身份证" prop="identityCard" label-width="80px">
                <el-input v-model="formData.identityCard" placeholder="请输入身份证" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="联系电话" prop="phone" label-width="80px">
                <el-input v-model="formData.phone" placeholder="请输入联系电话" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="从业资格证号" prop="certificateNo">
                <el-input v-model="formData.certificateNo" placeholder="请输入从业资格证号" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="道路运输证号" prop="transportCertificate">
                <el-input v-model="formData.transportCertificate" placeholder="请输入道路运输证号" clearable />
              </el-form-item>
            </el-col>
            <h3 class="title">巡查信息</h3>
            <el-col :span="24">
              <el-form-item label="标题" prop="title">
                <el-input v-model="formData.title" placeholder="请输入标题" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="巡查类型" prop="inspectionType">
                <el-select v-model="formData.inspectionType" placeholder="请输入巡查类型" clearable :loading="typeLoading" no-data-text="无数据，请先选择车辆类型" :style="{ width: '100%' }" @change="handleIsTypeChange" @visible-change="handleVisChange">
                  <el-option v-for="(item, index) in inspectionTypeOptions" :key="index" :label="item.dictLabel" :value="item.dictValue" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col v-if="isShowFTaxiType" :span="12">
              <el-form-item label="网络预约出租汽车驾驶员证" prop="onlineCarDriver">
                <el-radio-group v-model="formData.onlineCarDriver" size="medium">
                  <el-radio v-for="(item, index) in onlineCarDriverOptions" :key="index" :label="item.value">
                    {{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col v-if="isShowSTaxiType" :span="12">
              <el-form-item label="网络预约出租汽车运输证类型" prop="onlineCarTarnsport">
                <el-radio-group v-model="formData.onlineCarTarnsport" size="medium">
                  <el-radio v-for="(item, index) in onlineCarTarnsportOptions" :key="index" :label="item.value">
                    {{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="经度" prop="longitude">
                <el-input v-model="formData.longitude" placeholder="请输入经度" readonly clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="纬度" prop="latitude">
                <el-input v-model="formData.latitude" placeholder="请输入纬度" readonly clearable />
              </el-form-item>
            </el-col> -->
          </el-form>
        </el-row>
      </el-scrollbar>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button v-if="!formDisabled" type="primary" @click="handelConfirm(2)">保存</el-button>
        <el-button v-if="!formDisabled" type="primary" @click="handelOver">办结</el-button>
      </div>
    </el-dialog>
    <!-- 图片预览 -->
    <el-dialog :close-on-click-modal="false" :visible.sync="dialogVisible" append-to-body>
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
    <!-- 地址选择 -->
    <tdtMap ref="bindmap" :visible.sync="openMap" append-to-body :map-search="true" :dw="formData" @onlnglat="onlnglat" />
  </div>
</template>
<script>
import { getTransport, addTransport, editTransport } from '@/api/case/pipe/dayCheck'
import tdtMap from '@/components/tdtMap/tdtMap.vue'
import { getToken } from '@/utils/auth'
import { getFiles, removeFiles} from '@/api/supervise/swit'
import userSelect from '@/components/userselect/index'

export default {
  components: {tdtMap, userSelect},
  inheritAttrs: false,
  props: {
    title: String,
    detailId: Number,
    formDisabled: Boolean,
    carTypeOptions: Array,
    isBoard: Boolean
  },
  data() {
    return {
      userNamesOpen: false,
      // 问题类型数据字典
      ProblemTypeOptions: [],
      openMap: false,
      formLoading: false,
      formData: {
        title: undefined,
        content: undefined,
        party: undefined,
        identityCard: '',
        phone: undefined,
        carNo: undefined,
        models: undefined,
        certificateNo: undefined,
        transportCertificate: undefined,
        userName: undefined,
        inspectionTime: undefined,
        carType: undefined,
        inspectionType: undefined,
        onlineCarDriver: undefined,
        onlineCarTarnsport: undefined,
        longitude: undefined,
        latitude: undefined,
        address: undefined
      },
      rules: {
        title: [{
          required: true,
          message: '请输入标题',
          trigger: 'blur'
        }],
        content: [{
          required: true,
          message: '请输入巡查内容',
          trigger: 'blur'
        }],
        party: [{
          required: true,
          message: '请输入当事人',
          trigger: 'blur'
        }],
        // identityCard: [{
        //   required: true,
        //   message: '请输入身份证',
        //   trigger: 'blur'
        // }],
        phone: [{
          required: true,
          message: '请输入联系电话',
          trigger: 'blur'
        }],
        carNo: [{
          required: true,
          message: '请输入车辆车牌',
          trigger: 'blur'
        }],
        models: [],
        certificateNo: [{
          required: true,
          message: '请输入从业资格证号',
          trigger: 'blur'
        }],
        transportCertificate: [{
          required: true,
          message: '请输入道路运输证号',
          trigger: 'blur'
        }],
        userName: [{
          required: true,
          message: '请输入巡查人员',
          trigger: 'blur'
        }],
        inspectionTime: [{
          required: true,
          message: '请输入巡查时间',
          trigger: 'change'
        }],
        carType: [{
          required: true,
          message: '请输入车辆类型',
          trigger: 'change'
        }],
        inspectionType: [{
          required: true,
          message: '请输入巡查类型',
          trigger: 'change'
        }],
        longitude: [{
          required: true,
          message: '请输入经度',
          trigger: 'change'
        }],
        latitude: [{
          required: true,
          message: '请输入纬度',
          trigger: 'change'
        }],
        address: [{
          required: true,
          message: '请输入地址',
          trigger: 'change'
        }]
      },
      dialogVisible: false,
      dialogImageUrl: '',
      carTypeDict: '',
      inspectionTypeOptions: [],
      typeLoading: false,
      isShowFTaxiType: false,
      isShowSTaxiType: false,
      onlineCarDriverOptions: [{
        'label': '有',
        'value': '1'
      }, {
        'label': '无',
        'value': '0'
      }],
      onlineCarTarnsportOptions: [{
        'label': '有',
        'value': '1'
      }, {
        'label': '无',
        'value': '0'
      }],
      formFiles: [],
      fileEXData: {
        tableName: 'case_transport',
        status: 1
      }
    }
  },
  computed: {
    headers() {
      return {Authorization: 'Bearer ' + getToken()}
    }
  },
  watch: {
    '$attrs.visible'(nVal) {
      if (nVal && this.detailId) {
        this.fetchData()
      }
    }
  },
  created() {
    this.getDicts('transport_problem_type').then(response => {
      this.ProblemTypeOptions = response.data
    })
  },
  mounted() {},
  methods: {
    deptConfirm(e) {    // 上报人名称
      this.formData = {...this.formData, userNames: e.name, userIds: e.id}
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleRemove(file) {
      return new Promise((resolve, reject) => {
        this.$confirm('是否确认删除？ 删除后将无法恢复', '提示', { type: 'warning' }).then(() => {
          console.log(file)
          if (file.fileId) {
            removeFiles(file.fileId).then(resolve).catch(reject)
          } else {
            resolve()
          }
        }).catch(() => {
          reject()
        })
      })
    },
    handleSuccess() {
      const hasFailFile = this.$refs.upload.uploadFiles.some(item => item.response && item.response.code != 200)
      if (hasFailFile) {
        this.$message.error('文件上传失败，请重新上传')
      } else {
        this.formLoading = false
        this.$message.success('操作成功')
        this.$emit('reLoad')
        this.close()
      }
    },
    handleError() {
      this.$message.error('文件上传失败，请重新上传')
    },
    exceed(files, filesList) {
      if (filesList.length >= 4) this.msgError('已超出最大上传数量')
    },
    onlnglat(lenlat) {
      let {lng: longitude, lat: latitude, address} = lenlat
      this.formData = {...this.formData, longitude, latitude, address}
      this.openMap = false
    },
    fetchData() {
      this.formLoading = true
      Promise.all([
        getTransport(this.detailId),
        getFiles({businessId: this.detailId, tableName: 'case_transport'})
      ])
        .then(resAry => {
          if (!this.isBoard) {
            const carTypeDict = this.carTypeOptions.find(item => item.dictValue == resAry[0].data.carType)
            if (carTypeDict) {
              this.typeLoading = true
              this.getDicts(carTypeDict.remark).then(dictRes => {
                this.inspectionTypeOptions = dictRes.data
                this.typeLoading = false
              }).catch(() => {
                this.typeLoading = false
              })
            }
          }

          this.formData = resAry[0].data
          // 文件部分
          this.formFiles = resAry[1].rows.map(item => {
            return { name: item.displayName, url: `/zqzfj${item.filePath}`, ...item }
          })
          this.formLoading = false
        }).catch(() => {
          this.formLoading = false
        })
    },
    // 问题类型选择
    handleProblemChange(val) {
      console.log(val)
      console.log(this.ProblemTypeOptions)
      const selctObj = this.ProblemTypeOptions.find(item => item.dictValue == val)
      console.log(selctObj)
      this.formData.problemTypeName = selctObj.dictLabel
      console.log(this.formData.problemTypeName)
    },
    handleCarChange(val) {
      const selctObj = this.carTypeOptions.find(item => item.dictValue == val)
      if (selctObj) {
        this.carTypeDict = selctObj.remark
        this.formData.carTypeName = selctObj.dictLabel
        this.handleCarClear()
      }
      if (val != 4) {
        this.isShowFTaxiType = false
        this.isShowSTaxiType = false
      }
    },
    handleCarClear() {
      this.inspectionTypeOptions = []
      this.formData.inspectionType = null
    },
    handleVisChange(confirm) {
      if (confirm && this.formData.carType && !this.inspectionTypeOptions.length) {
        this.typeLoading = true
        this.getDicts(this.carTypeDict).then(res => {
          this.inspectionTypeOptions = res.data
          this.typeLoading = false
        }).catch(() => {
          this.typeLoading = false
        })
      }
    },
    handleIsTypeChange(val) {
      if (this.formData.carType == 4 && val == 1) {
        this.isShowFTaxiType = true
        this.isShowSTaxiType = false
        this.formData = { ...this.formData, onlineCarDriver: '0', onlineCarTarnsport: '' }
      } else if (this.formData.carType == 4 && val == '2') {
        this.isShowFTaxiType = false
        this.isShowSTaxiType = true
        this.formData = { ...this.formData, onlineCarDriver: '', onlineCarTarnsport: '0' }
      } else {
        this.formData = { ...this.formData, inspectionType: val }
      }
    },
    onOpen() {
      const { name: userName, uid: userId } =  this.$store.getters
      this.formData = { userName, userId, inspectionTime: this.parseTime(new Date()) }
    },
    reset() {
      this.formData = {
        title: undefined,
        content: undefined,
        party: undefined,
        identityCard: '',
        phone: undefined,
        carNo: undefined,
        models: undefined,
        certificateNo: undefined,
        transportCertificate: undefined,
        userName: undefined,
        inspectionTime: undefined,
        carType: undefined,
        inspectionType: undefined,
        onlineCarDriver: undefined,
        onlineCarTarnsport: undefined,
        longitude: undefined,
        latitude: undefined,
        address: undefined
      }
      this.resetForm('elForm')
    },
    onClose() {
      this.reset()
      this.$refs.upload.clearFiles()
    },
    close() {
      this.$emit('update:visible', false)
    },
    handelOver() {
      this.$confirm('提示', '是否确认办结？', { type: 'warning' }).then(() => {
        this.handelConfirm(9)
      }).catch(() => {})
    },
    handelConfirm(status) {
      this.$refs['elForm'].validate(valid => {
        if (!valid) return
        if (!this.$refs.upload.uploadFiles.length && !this.formFiles.length) {
          this.$message.error('请上传巡查图片')
          return
        }
        if (this.formData.isProblem == 0) {
          this.formData = {...this.formData, problemType: '', problemTypeName: ''}
        }
        const methodFn = this.formData.transportId ? editTransport : addTransport
        this.formLoading = true
        const params = { ...this.formData, status }
        // debugger
        methodFn(params).then(res => {
          if (this.$refs.upload.uploadFiles.length) {
            this.fileEXData.businessId = this.formData.transportId ? this.formData.transportId : res.data.transportId
            this.$refs.upload.submit()
          }
          this.formLoading = false
          this.$message.success('操作成功')
          this.$emit('reLoad')
          this.close()

        }).catch(() => {
          this.formLoading = false
          this.close()
        })
      })
    }
  }
}

</script>
