<template>
  <div class="container">
    <el-row>
      <el-form ref="form" v-loading="loading" :model="form" label-width="100px">
        <el-col :span="24">
          <h3 class="title" style="position: relative;">
            <span>基本信息</span>
          </h3>
        </el-col>

        <el-col :span="12">
          <el-form-item label="违法人姓名" prop="lawbreakers">
            <span>{{ form.lawbreakers }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="phone">
            <span>{{ form.phone }}</span>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="12">
          <el-form-item label="身份证" prop="identityCard">
            <span>{{ form.identityCard }}</span>
          </el-form-item>
        </el-col> -->
        <el-col :span="12">
          <el-form-item label="发生时间" prop="happenDate">
            <span>{{ form.happenDate }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="案件地址" prop="address">
            <span>{{ form.address }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="完成时间" prop="completeDate">
            <span>{{ form.completeDate }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="案件内容" prop="content">
            <span>{{ form.content }}</span>
          </el-form-item>
        </el-col>

        <el-col :span="24" style="border-bottom: 1px solid #ccc;">
          <el-form-item label="处置图片" prop="files">
            <el-image v-for="(src,idx) in formFiles" :key="idx" style="width: 200px; height: 200px;" fit="cover" :src="src" @load="() => handleLoadImg(idx)" />
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
  </div>
</template>

<script>
import toutCase from '@/api/case/tout/hncz'
import { getFiles } from '@/api/supervise/swit'

export default {
  data() {
    return {
      loading: false,
      form: {},
      formFiles: [],
      fileIdx: 0
    }
  },
  mounted() {
    const params = this.$route.query
    if (params.id) {
      this.loading = true
      Promise.all([
        toutCase.getDetail(params.id),
        getFiles({businessId: params.id, tableName: 'case_tout'})
      ])
        .then(resAry => {
          const [formData, fileData] = resAry
          this.form = formData.data
          this.formFiles = fileData.rows.map(file => {
            const url =  `/zqzfj${file.filePath}`
            return url
          })
          this.loading = false
        }).catch(() => {
          this.loading = false
        })
    }
  },
  methods: {
    handleLoadImg() {
      this.fileIdx++
      if (this.fileIdx == this.formFiles.length) {
        this.pagePrint()
      }
    },
    pagePrint() {
      this.$nextTick(() => {
        window.print()
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .container {
    .title {
      padding-left: 10px;
    }
    ::v-deep {
      .el-form-item {
        margin-bottom: 0;
      }
      .el-row {
        border-right: 1px solid #ccc;
      }
      [class*=el-col-] {
        border-top: 1px solid #ccc;
        border-left: 1px solid #ccc;
      }
      .el-form-item--medium .el-form-item__label {
        padding-left: 10px;
      }
      .el-form-item--medium .el-form-item__content {
        padding-left: 10px;
        border-left: 1px solid #ccc;
      }
    }
  }
</style>
