<template>
  <div>
    <el-dialog class="m-dialog" :close-on-click-modal="false" v-bind="$attrs" append-to-body :title="title" v-on="$listeners" @close="onClose">
      <el-scrollbar v-loading="loading" style="height: 100%;" :element-loading-text="formLoadingText">
        <el-row :gutter="15" style="margin-right: 10px;">
          <el-form ref="form" :model="form" :rules="rules" :disabled="dispatch" label-width="110px">
            <h3 class="title">
              <span>基本信息</span>
              <attentionBtn :case-id="form.captureId || 0" case-type="capture" :case-content="form.content" />
            </h3>
            <el-col :span="12">
              <el-form-item label="发起人" prop="userName">
                <el-input v-model="form.userName" :disabled="jbdis" placeholder="发起人" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-tooltip :disabled="!form.happenTime" class="item" effect="dark" :content="form.happenTime" placement="top">
                <el-form-item label="发生时间" prop="happenTime">
                  <el-date-picker v-model="form.happenTime" :disabled="jbdis" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="请选择发生时间" clearable />
                </el-form-item>
              </el-tooltip>
            </el-col>
            <el-col :span="12">
              <el-form-item label="案件类型" prop="caseTypeName">
                <el-select v-model="form.caseTypeName" :disabled="jbdis" placeholder="请输入案件类型" clearable :style="{ width: '100%' }" @change="handleCarChange" @clear="handleCarClear">
                  <el-option v-for="(item, index) in violationData" :key="index" :label="item.dictLabel" :value="item.dictValue" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="地址" prop="address">
                <el-tooltip :disabled="!form.address" class="item" effect="dark" :content="form.address" placement="top">
                  <div>
                    <el-input v-model="form.address" placeholder="请选择违规地址">
                      <svg-icon slot="suffix" icon-class="map" class="svg-icon" @click="openMap = !jbdis" />
                    </el-input>
                  </div>
                </el-tooltip>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="案件内容" prop="content">
                <el-input v-model="form.content" :disabled="jbdis" type="textarea" placeholder="请输入案件内容" :maxlength="150" show-word-limit :autosize="{minRows: 2, maxRows: 4}" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="上传图片" prop="files">
                <MFileUpload ref="happenFile" :ex-data="formData" :disabled="jbdis" :file-list="formFiles" @uploadSucces="handleFileSuccess" @error="handleFileError" />
              </el-form-item>
            </el-col>
            <h3 class="title">接警信息</h3>
            <el-col :span="12">
              <el-form-item label="当班组长" prop="squadronUserName">
                <el-input v-model="form.squadronUserName" placeholder="请选择当班组长" readonly>
                  <el-button v-if="!(form.status >= 3)" slot="append" type="primary" @click="deptOpens = true">选择</el-button>
                </el-input>
                <userSelect id="squadronUserName" v-model="deptOpens" :multiple="false" :select-user-keys="[form.squadronUserId+'']" :default-expanded-keys="form.squadronUserId ? [form.squadronUserId+''] : ['100']" @confirm="launchDeptConfirms" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="网格小组" prop="deptName">
                <el-input v-model="form.deptName" readonly placeholder="请选择网格小组" />
              </el-form-item>
            </el-col>
            <el-col v-if="form.status >= 3" :span="12">
              <el-form-item label="下发组长时间" prop="allotTeamTime">
                <el-input v-model="form.allotTeamTime" placeholder="下发组长时间" readonly />
              </el-form-item>
            </el-col>
            <el-col v-if="form.status >= 3" :span="12">
              <el-form-item label="执行人员" prop="userNames">
                <el-input v-model="form.userNames" placeholder="请选择执行人员名称" readonly>
                  <el-button v-if="form.squadronUserId == $store.getters.uid && form.status == 3" slot="append" type="primary" @click="userNamesOpen = true">选择</el-button>
                </el-input>
                <userSelect id="userNames" v-model="userNamesOpen" :select-user-keys="form.userIds?(form.userIds+'').split(','):[]" :default-expanded-keys="form.userIds?(form.userIds+'').split(','):['100']" @confirm="deptConfirm" />
              </el-form-item>
            </el-col>
            <el-col v-if="form.status >= 4" :span="12">
              <el-form-item label="下发队员时间" prop="teamAllotTime">
                <el-input v-model="form.teamAllotTime" placeholder="下发队员时间" readonly />
              </el-form-item>
            </el-col>
            <div v-if="form.status >= 5">
              <h3 class="title">处警信息</h3>
              <el-col :span="12">
                <el-tooltip :disabled="!form.outTime" class="item" effect="dark" :content="form.outTime" placement="top">
                  <el-form-item label="出警时间" prop="outTime">
                    <el-date-picker v-model="form.outTime" disabled type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="请选择出警时间" clearable />
                  </el-form-item>
                </el-tooltip>
              </el-col>
              <el-col :span="12">
                <el-tooltip :disabled="!form.arriveTime" class="item" effect="dark" :content="form.arriveTime" placement="top">
                  <el-form-item label="到达时间" prop="arriveTime">
                    <el-date-picker v-model="form.arriveTime" disabled type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="请选择到达时间" clearable />
                  </el-form-item>
                </el-tooltip>
              </el-col>
              <el-col :span="24">
                <el-form-item label="现场情况" prop="doDescn">
                  <el-input v-model="form.doDescn" disabled type="textarea" placeholder="请输入出警情况" :maxlength="150" show-word-limit :autosize="{minRows: 2, maxRows: 4}" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="现场图片" prop="files">
                  <MFileUpload ref="descnFile" :ex-data="formData" :disabled="true" :file-list="descnFileList" />
                </el-form-item>
              </el-col>
              <el-col v-if="form.status >= 6" :span="12">
                <el-form-item label="办结时间" prop="fbTime">
                  <el-date-picker v-model="form.fbTime" disabled type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="请选择反馈时间" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="处警结果" prop="doResult">
                  <el-input v-model="form.doResult" disabled type="textarea" placeholder="请输入处警结果" :maxlength="150" show-word-limit :autosize="{minRows: 2, maxRows: 4}" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="处警图片" prop="files">
                  <MFileUpload ref="resultFile" :ex-data="formData" :disabled="true" :file-list="resultFileList" />
                </el-form-item>
              </el-col>
              <el-col v-if="form.approveReason" :span="24">
                <el-form-item label="退回原因" prop="approveReason">
                  <el-input v-model="form.approveReason" disabled type="textarea" placeholder="退回原因" :maxlength="150" show-word-limit :autosize="{minRows: 2, maxRows: 4}" />
                </el-form-item>
              </el-col>
            </div>
          </el-form>
        </el-row>
      </el-scrollbar>
      <div v-if="isBoard" slot="footer" class="dialog-footer">
        <el-button :loading="loading" @click="close">取 消 </el-button>
      </div>
      <div v-else slot="footer" class="dialog-footer">
        <AddClassic v-if="form.status == 9" :before-processing="form.content" :after-processing="form.doResult" />
        <el-button v-if="form.status != 9" :loading="loading" type="primary" @click="extraPrimary(1)">直接办结</el-button>
        <el-button v-if="isShowRevoke" type="danger" :loading="loading" @click="dialogVisible = true">撤 回</el-button>
        <el-button v-if="isShowAssign" type="primary" :loading="loading" @click="primary">保存</el-button>
        <el-button v-if="isShowApprove" type="primary" icon="el-icon-check" @click="handleApproveBtn(true)">通过</el-button>
        <el-button v-if="isShowApprove" type="danger" icon="el-icon-close" @click="handleApproveBtn(false)">退回</el-button>
        <el-button :loading="loading" @click="close">取 消 </el-button>
      </div>
    </el-dialog>
    <!-- 撤回 -->
    <el-dialog
      :close-on-click-modal="false"
      title="撤销"
      :visible.sync="dialogVisible"
      width="30%"
      append-to-body
      :before-close="handleClose"
    >
      <el-form ref="bhForm" :model="bhForm" :rules="bhFormrules" label-width="80px">
        <el-form-item label="撤销理由" prop="revokeReason">
          <el-input v-model="bhForm.revokeReason" type="textarea" placeholder="请输入撤销理由" :maxlength="150" show-word-limit :autosize="{minRows: 2, maxRows: 4}" />
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="handelWithdraw(0)">取 消</el-button>
        <el-button type="primary" @click="handelWithdraw(1)">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 地图 -->
    <tdtMap ref="bindmap" :visible.sync="openMap" append-to-body :map-search="true" :dw="form" @onlnglat="onlnglat" />
    <!-- 审核弹窗 -->
    <el-dialog
      :close-on-click-modal="false"
      title="审核"
      :visible.sync="approveVisible"
      width="30%"
      append-to-body
      :before-close="handleApproveClose"
    >
      <el-form ref="approveForm" :model="approveForm" :rules="approveFormrules" label-width="80px">
        <el-form-item label="退回原因" prop="approveReason">
          <el-input v-model="approveForm.approveReason" type="textarea" placeholder="请输入退回原因" :maxlength="150" show-word-limit :autosize="{minRows: 2, maxRows: 4}" />
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="approveVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleApproveReBtn">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import tdtMap from '@/components/tdtMap/tdtMap'
import userSelect from '@/components/userselect/index'
import { getFiles, removeFiles } from '@/api/supervise/swit'
import { getToken } from '@/utils/auth'
import {listData} from '@/api/system/dict/type'
import {addCapture, editCapture, captureOne, revokeCapture, captureApprove} from '@/api/case/synthetical/capture'
import MFileUpload from '@/components/MFileUpload/index.vue'
import attentionBtn from '@/components/attentioneBtn/index.vue'
import AddClassic from '@/components/AddClassic/index.vue'

export default {
  name: 'Fromlist',
  components: {tdtMap, userSelect, MFileUpload, attentionBtn, AddClassic},
  props: {
    detailId: Number,
    title: String,
    formDisabled: Boolean,
    isBoard: Boolean
  },
  data() {
    return {
      isBh: false,
      bhForm: {},
      dialogVisible: false,
      userOpen: false,
      userNamesOpen: false,
      headers: {Authorization: 'Bearer ' + getToken()},
      loading: false,
      formData: {businessId: null, status: 2, tableName: 'case_capture'},
      formData2: {businessId: null, tableName: 'case_capture'},
      dialogImageUrl: '',
      form: {userIds: '', userId: '', status: 1},
      violationData: [],
      openMap: false,
      deptOpen: false,
      deptOpens: false,
      jbdis: false,
      dispatch: false,
      formFiles: [], // 文件
      descnFileList: [],
      resultFileList: [],
      bhFormrules: {
        revokeReason: [
          { required: true, message: '请输入任务名称', trigger: 'blur' }
        ] },
      fileNum: 0,
      rules: {
        squadronUserName: [
          { required: true, message: '请选择中队长', trigger: 'change' }
        ],
        userNames: [
          { required: true, message: '请选择执行人员', trigger: 'change' }
        ],
        party: [
          { required: true, message: '请输入当事人姓名', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入任务内容', trigger: 'blur' }
        ],
        happenTime: [
          { required: true, message: '请选择发生时间', trigger: 'change' }
        ],
        caseTypeName: [
          { required: true, message: '请选择案件类型', trigger: 'change' }
        ],
        address: [
          { required: true, message: '请选择案件地址', trigger: 'change' }
        ]
      },
      formLoadingText: '数据加载中',
      approveVisible: false,
      approveForm: {},
      approveFormrules: {
        approveReason: [{ required: true, message: '请输入退回原因', trigger: 'blur' }]
      }
    }
  },
  computed: {
    // 是否显示撤回按钮
    isShowRevoke() {
      // if (this.$store.getters.postKeys.includes('zhzx') && this.form.status >= 3 && this.form.status <= 4) {
      //   /* 指挥中心在状态3和4的时候显示撤回按钮 */
      //   return true
      // } else if (this.form.status == 4 && this.form.squadronUserId == this.$store.getters.uid) {
      //   /* 中队长ID等于当前登录人ID的时候显示撤回按钮 */
      //   return true
      // } else {
      //   return false
      // }

      // 调试先注释掉
      // if (this.$store.getters.roles.includes('manage') && this.form.status > 1 && this.form.status <= 4) {
      //   return true
      // } else if (this.$store.getters.roles.includes('admin') && this.form.status > 1 && this.form.status <= 4) {
      //   return true
      // } else {
      //   return false
      // }
    },
    // 是否显示分配按钮
    isShowAssign() {
      if (this.form.status == 1) {
        return true
      } else if (this.form.status == 3 && this.form.squadronUserId == this.$store.getters.uid) {
        return true
      } else {
        return false
      }
    },
    isShowApprove() {
      if (this.form.status == 9 && this.form.userId == this.$store.getters.uid && this.form.approveType != 1) {
        return true
      } else {
        return false
      }
    }
  },
  watch: {
    '$attrs.visible'(nVal) {
      if (nVal && this.detailId) {
        this.getData()
      } else if (nVal) {
        this.reset()
      }
    }
  },
  created() {
    // this.getData()
    this.loading = true
    listData({dictType: 'case_alert_type'}).then(res => {
      this.loading = false
      this.violationData = res.rows
    }).catch(() => {
      this.loading = false
    })
  },
  methods: {
    handleApproveClose() {
      this.approveVisible = false
      this.approveForm = {}
    },
    handleApproveReBtn() {
      this.$refs.approveForm.validate(valid => {
        if (valid) this.handelApprove(0)
      })
    },
    handelApprove(type) {
      const params = { ...this.approveForm, approveType: type  }
      console.log(params)
      this.loading = true
      this.approveVisible = false
      captureApprove(params).then(() => {
        this.loading = false
        this.$message.success('操作成功')
        this.approveForm = {}
        this.$emit('reLoad')
        this.close()
      }).catch(() => {
        this.loading = false
      })
    },
    handleApproveBtn(isCheck) {
      this.$confirm(`是否${isCheck ? '通过' : '退回'}该案件？`, '提示', { type: 'warning' }).then(() => {
        this.approveForm = { captureId: this.form.captureId }
        if (isCheck) {
          // 案件通过
          this.handelApprove(1)
        } else {
          // 案件驳回
          this.approveVisible = true
        }
      }).catch(() => {})
    },
    // 直接办结
    extraPrimary(extraStatus) {
      this.$confirm('是否确认直接办结?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let params = {...this.form, status: 9, extraStatus, fbTime: this.parseTime(new Date()) }
        this.loading = true
        this.formLoadingText = '数据上传中'

        editCapture(params).then(res => {
          // 调用文件上传
          this.formData.businessId = this.form.captureId || res.data.captureId
          if (res.data && res.data.captureId) this.form.captureId = res.data.captureId
          this.$refs.happenFile.submitFile()
          // this.$emit('reLoad')
          // this.close()
          // this.loading = false
        }).catch(() => {
          this.close()
          this.loading = false
        })

      })

    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    getData() {
      if (this.detailId) {
        let params = {businessId: this.detailId, tableName: 'case_capture'}
        this.loading = true
        Promise.all([captureOne(this.detailId), getFiles(params)]).then(res => {
          this.loading = false
          this.form = {...this.form, ...res[0].data}
          if (this.form.status > 1) this.jbdis = true
          if (this.form.status >= 5) this.dispatch = true
          if (this.formDisabled) this.dispatch = true
          if (this.$store.getters.uid ==  this.form.squadronUserId && this.form.status == 4) this.isBh = true
          if (this.$store.getters.uid ==  this.form.userId && this.form.status < 5) this.isBh = true
          res[1].rows.map(v => {
            if (v.status == 2) this.formFiles.push({name: v.displayName, url: '/zqzfj' + v.filePath, ...v})
            if (v.status == 6) this.descnFileList.push({name: v.displayName, url: '/zqzfj' + v.filePath, ...v})
            if (v.status == 9) this.resultFileList.push({name: v.displayName, url: '/zqzfj' + v.filePath, ...v})
          }, [])
        }).catch(() => {
          this.loading = false
        })
      }
    },
    launchDeptConfirms(e) {    // 中队名称
      // console.log(e)
      this.form = {...this.form, squadronUserName: e.name, squadronUserId: e.id, deptId: e.parent.id, deptName: e.parent.label}
    },
    handelWithdraw(state) {
      if (state) {
        this.$refs.bhForm.validate(valid => {
          if (valid) {
            this.loading = true
            this.formLoadingText = '数据上传中'
            this.dialogVisible = false
            let params = {revokeReason: this.bhForm.revokeReason, captureId: this.form.captureId }
            /* if (this.$store.getters.uid ==  this.form.squadronUserId) {
              params.revokeType = 2
              params.status = 3
            }
            if (this.$store.getters.uid ==  this.form.userId) {
              params.revokeType = 1
              params.status = 1
            } */
            if (this.$store.getters.roles.includes('manage') || this.$store.getters.roles.includes('admin')) {
              params.revokeType = 1
              params.status = 1
            }
            console.log(params)
            revokeCapture(params).then(() => {
              this.handleFileSuccess()
            }).catch(err => { console.log(err), this.loading = false })
          }
        })
      } else { this.dialogVisible = false, this.bhForm = {} }

    },
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(() => {
          done()
          this.bhForm = {}
        })
        .catch(() => {})
    },
    handleFileSuccess() {
      this.loading = false
      this.$emit('reLoad')
      this.close()
    },
    handleFileError() {
      this.loading = false
    },
    // this.$emit('onPrimary', params)
    primary() {
      this.$refs['form'].validate(valid => {
        if (valid && !this.$refs.happenFile.isNotUploadFile()) {
          let status = 1
          if (this.form.squadronUserId) {
            this.form.allotTeamTime = this.parseTime(new Date())
            status = 3
          }
          if (this.form.userIds) {
            this.form.teamAllotTime = this.parseTime(new Date())
            status = 4
          }
          let params = {...this.form, status, type: 1 }
          if (status == 3) {
            params.userId = this.$store.getters.uid
            params.userName = this.$store.getters.nickName
          }
          this.loading = true
          this.formLoadingText = '数据上传中'
          // console.log(params)
          let api = params.captureId ? editCapture(params) : addCapture(params)
          api.then(res => {
            // 调用文件上传
            this.formData.businessId = this.form.captureId || res.data.captureId
            if (res.data && res.data.captureId) this.form.captureId = res.data.captureId
            this.$refs.happenFile.submitFile()
          }).catch(() => this.loading = false)
        } else {
          return false
        }
      })
    },
    handleCarChange(val) {
      const selctObj = this.violationData.find(item => item.dictValue == val)
      if (selctObj) this.form = {...this.form, caseType: selctObj.dictSort, caseTypeName: selctObj.dictLabel}
    },
    handleSquChange(val) { // 中队
      const selctObj = this.violationData.find(item => item.dictValue == val)
      if (selctObj) this.form = {...this.form, squadronId: selctObj.dictSort, squadronName: selctObj.dictLabel}
    },
    handleSquUserChange(val) {  // 中队长
      const selctObj = this.violationData.find(item => item.dictValue == val)
      if (selctObj) this.form = {...this.form, squadronUserId: selctObj.dictSort, squadronUserName: selctObj.dictLabel}
      console.log(this.form)
    },

    handleCarClear(id) {
      if (id == 1) {
        this.form.squadronId = ''
      } else if (id == 2) {
        this.form.squadronUserId = ''
      } else {
        this.form.caseType = ''
      }

    },
    onlnglat(lenlat) {      // 经纬度  详细地址
      let {lng: longitude, lat: latitude, address} = lenlat
      this.form = {...this.form, longitude, latitude, address}
      this.openMap = false
    },
    userConfirm(e) {    //	执行部门名称
      this.form = {...this.form, userName: e.name, userId: e.id}
    },
    deptConfirm(e) {    // 上报人名称
      this.form = {...this.form, userNames: e.name, userIds: e.id}
    },
    selectOne(e) {    // 选择案件类型
      let data = this.typeData.filter(item => { return item.id == e })
      this.form.type = data[0].id
      this.form.typeName = data[0].value
    },
    // 上传函数
    beforeRemove(file) {
      return new Promise((resolve, reject) => {
        this.$confirm('是否确认删除？ 删除后将无法恢复', '提示', { type: 'warning' }).then(() => {
          if (file.fileId) {
            removeFiles(file.fileId).then(resolve).catch(reject)
          } else {
            resolve()
          }
        }).catch(() => {
          reject()
        })
      })
    },
    exceed(files, filesList) {
      if (filesList.length >= 4) this.msgError('已超出最大上传数量')
    },
    handleSuccess(files) {    // 上传成功
      if (files.code == 200) {
        this.msgSuccess('图片上传成功')
        this.fileNum++
      } else {
        this.msgError('上传失败,请修改后重试')
        this.loading = false
      }
      let upis = this.form.userIds ? this.fileNum == this.$refs.upload.uploadFiles.length + this.$refs.upload2.uploadFiles.length - this.formFiles.length - this.formFiles2.length   :   this.fileNum == this.$refs.upload.uploadFiles.length  - this.formFiles.length
      if (upis) {
        this.loading = false
        this.$emit('reLoad')
        this.close()
      }

    },
    handleError() { // 上传失败
      this.msgError('上传失败,请修改后重试')
      this.loading = false
    },
    reset() {
      this.form = {userIds: '', userId: this.$store.getters.uid, userName: this.$store.getters.nickName, status: 1, happenTime: this.parseTime(new Date()) }
      this.jbdis = false
      this.dispatch = false
      this.fileNum = 0
      this.formFiles = []
      this.descnFileList = []
      this.resultFileList = []
      this.resetForm('form')
    },
    // 取消
    onClose() {
      this.reset()
      this.formLoadingText = '数据加载中'
    },
    close() {
      this.$emit('update:visible', false)
      this.onClose()
    }
  }
}
</script>
