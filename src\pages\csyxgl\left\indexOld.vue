<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-03-10 14:47:13
 * @LastEditors: wjb
 * @LastEditTime: 2025-03-11 08:42:20
-->
<template>
  <div class="left-Map">
    <gyss></gyss>
    <ljcll></ljcll>
    <czqk></czqk>
    <cslh></cslh>
  </div>
</template>

<script>
import gyss from './gyss'
import ljcll from './ljcll'
import czqk from './czqk'
import cslh from './cslh'
export default {
  name: 'index',
  components: { gyss, ljcll, czqk, cslh },
  data() {
    return {}
  },
  computed: {},
  mounted() {},
  methods: {},
  watch: {},
}
</script>

<style scoped>
</style>