<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      :inline="true"
    >
      <el-form-item label="关键词检索">
        <!-- label="创建时间"  -->
        <el-input v-model="queryParams.searchValue" size="small" style="width: 240px; margin: 0 5px;" placeholder="请输入内容" />
      </el-form-item>
      <el-form-item label="网格小组">
        <el-select v-model="queryParams.deptName" placeholder="请选择网格小组" clearable size="small">
          <el-option
            v-for="(deptItem, deptIndex) in deptOptions"
            :key="deptIndex"
            :label="deptItem"
            :value="deptItem"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">
          重置
        </el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >
          新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-if="$store.getters.admin"
          type="primary"
          plain
          size="mini"
          @click="showPhone = !showPhone"
        >
          {{ showPhone ? '隐藏全号' : '显示全号' }}
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>
    <el-table
      v-loading="loading"
      :data="dataList"
      @selection-change="handleSelectionChange"
    >
      <!-- :cell-style="cellStyle" -->
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="店铺名称" align="center" prop="shopName" width="200" show-overflow-tooltip />
      <el-table-column label="联系人" align="center" prop="contactsName" width="100" />
      <el-table-column label="网格小组" align="center" prop="deptName" width="100" />
      <el-table-column v-if="!showPhone" label="联系电话" align="center" prop="contactsTelephone" width="150" show-overflow-tooltip />
      <el-table-column v-if="showPhone" label="联系电话" align="center" prop="ctphone" width="150" show-overflow-tooltip />
      <el-table-column label="详细地址" align="center" prop="address" show-overflow-tooltip />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 详情弹窗 -->
    <from-list :visible.sync="open" :get-form="form" :title="title" :detail-id="detailId" :form-disabled="formDisabled" @reLoad="parQuery" />
  </div>
</template>

<script>
import shop from '@/api/case/synthetical/shop'
import { userList as deptList} from '@/api/system/dict/type'
import fromList from '@/pages/case/views/synthetical/shop/components/fromList'
export default {
  components: {
    fromList
  },
  filters: {
    statusName(status) {
      const statusObj = { 2: '处理中', 9: '已办结' }
      return statusObj[status]
    }
  },
  data() {
    return {
      formDisabled: false,
      detailId: 0,
      title: '',
      open: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      dataList: [],
      circleColor: {
        2: '#FAB71C',
        9: '#bdc3bf'
      },
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        searchValue: '',
        deptId: '',
        deptName: ''
      },
      form: {},
      deptOptions: [],
      typeList: 1,
      showPhone: false
    }
  },
  computed: {
    listData() {
      let {pageNum, pageSize} = this.queryParams
      let arr = this.roleList.slice((pageNum - 1) * pageSize, (pageNum - 1) * pageSize + pageSize)
      return arr
    }
  },
  mounted() {
    this.getDeptNameOptions()
    this.getList()
  },
  methods: {
    // 网格小组选项
    getDeptNameOptions() {
      deptList({ typeList: this.typeList }).then(res => {
        this.deptOptions = res.data.map(item => item.label)
      })
    },
    /** 查询列表 */
    getList() {
      this.loading = true
      let { pageNum, pageSize, searchValue, deptName} = this.queryParams
      let params = { pageNum, pageSize }
      if (deptName) params.deptName = deptName
      if (searchValue) params.searchValue = searchValue
      shop.list(params).then(res => {
        this.dataList = res.rows
        this.total = res.total
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    // 类型选择
    handleCommand(command) {
      this.queryParams = {...this.queryParams, type: command.dictSort, typeName: command.dictLabel}
    },
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.queryParams.pageNum = 1
      this.getList()
    },
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.getList()
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    parQuery() {
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = { pageNum: 1, pageSize: 10, searchValue: '', deptName: ''}
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.shopId)
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.open = true
      this.title = '新增店铺信息'
      this.detailId = 0
      this.formDisabled = false
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.open = true
      this.title = '修改店铺信息'
      this.detailId = row.shopId
      this.formDisabled = false
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const transportIds = row.shopId || this.ids
      if (Array.isArray(transportIds) && !transportIds.length) {
        this.$message.warning('请选择需要删除的数据')
        return
      }
      this.$confirm('是否确认删除?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        shop.remove(transportIds).then(() => {
          this.getList()
          this.msgSuccess('删除成功')
        })
      }).catch(() => {})
    }
  }
}
</script>
