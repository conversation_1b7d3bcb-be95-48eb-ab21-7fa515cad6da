<template>
  <div>
    <CommonTitle text='事件列表'>
      <div class='yearChange'>
        <el-select v-model="value" placeholder="请选择" style='width: 220px;position: relative;right: 20px;' @change='queryData' :popper-append-to-body='false'>
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
        <el-date-picker
          v-model="datas"
          type="daterange"
          value-format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="queryData"
          :append-to-body='false'
        >
        </el-date-picker>
      </div>
    </CommonTitle>
    <div class='wrap-container'>
      <TableComponent :thConfig="thConfig" :tableData="tableData" :table-height='750' @infoClick="infoClick"></TableComponent>
    </div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import TableComponent from '@/components/TableComponent'
import moment from 'moment'
import { getEventList } from '@/api/dataView'
export default {
  name: 'index',
  components: {
    CommonTitle,
    TableComponent
  },
  data() {
    return {
      datas: [
        new Date().getFullYear() + "-01-01",
        moment(new Date()).format("YYYY-MM-DD"),
      ],
      value: "",
      options: [
        {
          label:"全部",
          value:""
        },
        {
          label:"简易案件",
          value:"1"
        },
        {
          label:"四位一体",
          value:"7"
        },
        {
          label:"黄牛处置",
          value:"6"
        },
        {
          label:"监控抓拍",
          value:"2"
        },
        {
          label:"智能抓拍",
          value:"3"
        },
        {
          label:"巡查发现",
          value:"5"
        },
        {
          label:"违规处置",
          value:"4"
        }
      ],
      tableData: [
        {
          xz:"B1小组",
          fssj:"2022-01-18 12:00:00",
          sjlx:"违规养犬事件",
          zbr:"赖雨刚",
          sjnr:"新城大道601"
        },
        {
          xz:"B1小组",
          fssj:"2022-01-18 12:00:00",
          sjlx:"违规养犬事件",
          zbr:"赖雨刚",
          sjnr:"新城大道601"
        },
        {
          xz:"B1小组",
          fssj:"2022-01-18 12:00:00",
          sjlx:"违规养犬事件",
          zbr:"赖雨刚",
          sjnr:"新城大道601"
        },
        {
          xz:"B1小组",
          fssj:"2022-01-18 12:00:00",
          sjlx:"违规养犬事件",
          zbr:"赖雨刚",
          sjnr:"新城大道601"
        },
        {
          xz:"B1小组",
          fssj:"2022-01-18 12:00:00",
          sjlx:"违规养犬事件",
          zbr:"赖雨刚",
          sjnr:"新城大道601"
        },
        {
          xz:"B1小组",
          fssj:"2022-01-18 12:00:00",
          sjlx:"违规养犬事件",
          zbr:"赖雨刚",
          sjnr:"新城大道601"
        },
        {
          xz:"B1小组",
          fssj:"2022-01-18 12:00:00",
          sjlx:"违规养犬事件",
          zbr:"赖雨刚",
          sjnr:"新城大道601"
        },
        {
          xz:"B1小组",
          fssj:"2022-01-18 12:00:00",
          sjlx:"违规养犬事件",
          zbr:"赖雨刚",
          sjnr:"新城大道601"
        },
        {
          xz:"B1小组",
          fssj:"2022-01-18 12:00:00",
          sjlx:"违规养犬事件",
          zbr:"赖雨刚",
          sjnr:"新城大道601"
        },
        {
          xz:"B1小组",
          fssj:"2022-01-18 12:00:00",
          sjlx:"违规养犬事件",
          zbr:"赖雨刚",
          sjnr:"新城大道601"
        },
      ],
      thConfig: [
        {
          th: '小组',
          field: 'xz',
          width: '15%',
          hover: true,
        },
        {
          th: '主办人',
          field: 'zbr',
          width: '20%',
          hover: true,
        },
        {
          th: '事件类型',
          field: 'sjlx',
          width: '20%',
          hover: true,
        },
        {
          th: '事件内容',
          field: 'sjnr',
          width: '20%',
          hover: true,
        },
        {
          th: '发生时间',
          field: 'fssj',
          width: '25%',
          hover: true,
        }
      ],
    }
  },
  computed: {},
  mounted() {
    this.queryData()
  },
  methods: {
    infoClick() {

    },
    getTypeName(type) {
      return this.options.find(item => item.value == type).label
    },
    queryData() {
      getEventList({startTime: this.datas[0], endTime: this.datas[1], type: this.value}).then(res => {
        console.log(res,"res")
        this.tableData = res.rows.map(item => ({
          xz:item.deptName,
          fssj:item.createTime,
          sjlx:this.getTypeName(item.type),
          zbr:item.zbr,
          sjnr:item.content
        }))
      })
    }
  },
  watch: {}
}
</script>

<style scoped lang='less'>
/deep/ .yearChange {
  position: relative;
  right: 10px;
  bottom: 30px;
  .el-input__inner {
    height: 48px !important;
    background-color: #132c4e !important;
    border: 2px solid #afdcfb !important;
    color: #fff !important;
    border-radius: 15px !important;
    font-size: 24px;
  }
}

:deep(.el-select-dropdown) {
  min-width: initial !important;
  background-color: #02103c94 !important;
  border: none !important;
  width: 200px !important;
  position: absolute !important;
  top: 50px !important;
  left: 10px !important;
  .el-select-dropdown__item {
    text-align: center;
    font-size: 30px !important;
    color: #cfcfd6 !important;
    height: 48px !important;
    line-height: 48px !important;

    &.hover {
      background-color: #27508f !important;
    }
  }

  .el-select-dropdown__wrap {
    max-height: initial !important;
  }
}

:deep(.el-popper) {
  .popper__arrow,
  .popper__arrow::after {
    display: none !important;
  }
}

.wrap-container {
  width: 100%;
  height: 835px;
  margin-bottom: 40px;
}
</style>