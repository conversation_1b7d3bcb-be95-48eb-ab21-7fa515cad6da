import JSEncrypt from 'jsencrypt'
export const Encrypt = (text) => {
  if (!text) {
    return;
  }
  const PUBLIC_KEY =
    "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDQJxZk/scap/t2FyZdTFHBS58Kx6yop5Hkz79RdWZJcaPFs+aEMm6hT2UulPwlWebMQFisuGkDlz0qkd1rIaUxIQcQZ6QfTQ8lm/j2uOoA3D8jFWkTncAQW+KXFx6rykuQRtmpTK3U8CC0FrcYLq372vK/R2ekDVv/VW/1FqA3KwIDAQAB";
  // console.log(text)
  // 使用公钥加密
  var encrypt = new JSEncrypt();
  encrypt.setPublicKey(
    "-----BEGIN PUBLIC KEY-----" + PUBLIC_KEY + "-----E<PERSON> PUBLIC KEY-----"
  );

  var encrypted = encrypt.encrypt(text);
  return encrypted.toString();
};