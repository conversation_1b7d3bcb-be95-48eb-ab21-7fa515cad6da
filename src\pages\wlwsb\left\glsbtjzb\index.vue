<template>
  <div>
    <CommonTitle text="各类设备统计占比"></CommonTitle>
    <div id="pieChart" style="width: 100%; height: 500px"></div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import { getDeviceTypes } from '@/api/wlwsb/index.js'
export default {
  name: 'index',
  components: {
    CommonTitle,
  },
  data() {
    return {
      //3d饼图
      pieData: [],
      pieColors: ['#22E197', '#00EAFF', '#FF9E44', '#FF5B97'],
      graphicCunstom: {
        left: '0%',
        top: '43%',
        z: -10,
        rotation: 0, //旋转
        origin: [50, 50], //中心点
        scale: [0.99, 0.99], //缩放
      },
    }
  },
  computed: {},
  mounted() {
    this.getData()
  },
  methods: {
    getData() {
      getDeviceTypes().then((res) => {
        if (res.data) {
          this.pieData = res.data.map((item) => {
            return {
              name: item.typeName,
              value: item.count,
              percent: item.percentage,
            }
          })
          this.initChart(this.pieData)
        }
      })
    },
    initChart(resdata) {
      let that = this
      let myChart = this.$echarts.init(document.getElementById('pieChart'))
      let imgUrl = require('@/assets/ajhf/chartBg3.png')
      let option = {
        color: [
          '#00C0FF',
          '#22E8E8',
          '#FFD461',
          '#A9DB52',
          '#B76FD8',
          '#FD852E',
          '#FF4949',
          '#0594C3',
          '#009D9D',
          '#A47905',
        ],

        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(51, 51, 51, 0.7)',
          borderWidth: 0,
          axisPointer: {
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          textStyle: {
            color: 'white',
            fontSize: '24',
          },
        },
        legend: {
          orient: 'vertical',
          right: '5%',
          top: '15%',
          icon: 'circle',
          itemGap: 10,
          textStyle: {
            rich: {
              name: {
                fontSize: 25,
                color: '#ffffff',
                padding: [0, 20, 0, 15],
              },
              value: {
                fontSize: 25,
                color: '#2CC6F9',
                // padding: [10, 0, 0, 15]
              },
              percent: {
                fontSize: 25,
                color: '#2CC6F9',
                padding: [0, 0, 0, 15]
              },
            },
          },
          formatter: function (name) {
            var data = option.series[0].data //获取series中的data
            var total = 0
            var tarValue,percent
            for (var i = 0, l = data.length; i < l; i++) {
              total += Number(data[i].value)
              if (data[i].name == name) {
                tarValue = data[i].value
                percent = data[i].percent
              }
            }
            // 使用模板字符串和Number.isNaN来提高代码的可读性和准确性
            return `{name|${name}}{value|${tarValue}}{percent|${percent}%}`
          },
        },
        graphic: [
          {
            type: 'image',
            id: 'logo2',
            left: '14.4%',
            top: '2%',
            z: -10,
            bounding: 'raw',
            rotation: 0, //旋转
            origin: [50, 50], //中心点
            scale: [0.8, 0.8], //缩放
            style: {
              image: imgUrl,
              opacity: 1,
            },
          },
        ],
        series: [
          {
            name: '',
            type: 'pie',
            radius: ['30%', '45%'],
            center: ['31%', '36%'],
            roseType: '',
            itemStyle: {
              borderRadius: 0,
            },
            label: {
              show: true,
              color: '#FFFFFF',
              formatter: function (params) {
                return params.percent + '%'
              },
              fontSize: 20,
            },
            labelLine: {
              length: 15,
              length2: 30,
              maxSurfaceAngle: 80,
            },
            data: resdata,
          },
        ],
      }
      myChart.setOption(option)
      myChart.getZr().on('mousemove', (param) => {
        myChart.getZr().setCursorStyle('default')
      })
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
</style>