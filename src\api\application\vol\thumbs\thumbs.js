import {request} from '@/utils/request'

// 查询点赞记录列表
export function listThumbs(query) {
  return request({
    url: '/business/vol/thumbs/list',
    method: 'get',
    params: query
  })
}

// 查询点赞记录详细
export function getThumbs(id) {
  return request({
    url: '/business/vol/thumbs/' + id,
    method: 'get'
  })
}

// 新增点赞记录
export function addThumbs(data) {
  return request({
    url: '/business/vol/thumbs/add',
    method: 'post',
    data: data
  })
}

// 修改点赞记录
export function updateThumbs(data) {
  return request({
    url: '/business/vol/thumbs/edit',
    method: 'post',
    data: data
  })
}

// 删除点赞记录
export function delThumbs(id) {
  return request({
    url: '/business/vol/thumbs/remove/' + id,
    method: 'post'
  })
}

// 导出点赞记录
export function exportThumbs(query) {
  return request({
    url: '/business/vol/thumbs/export',
    method: 'get',
    params: query
  })
}
