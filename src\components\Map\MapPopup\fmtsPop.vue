<template>
  <div id="fmts">
    <div class="content">
      <nav
        class="nav s-flex s-m-l-20 s-m-r-20 s-row-between s-p-b-10 s-col-bottom"
      >
        <h2 style="white-space: nowrap" class="s-flex s-font-35 s-m-t-20">
          <i class="yybz_icon"></i> {{taskCode}}
        </h2>
        <span
          class="s-m-l-20 s-font-30"
          style="cursor: pointer"
          @click="close()"
        >
          <img src="@/assets/tcgl/close.png" alt="" />
        </span>
      </nav>
      <div class="tabs">
        <div
          class="tab_item"
          v-for="(item,index) in btn"
          @click="tabIndex=index"
          :class="tabIndex==index?'tab_active':''"
        >
          {{item}}
        </div>
      </div>
      <div v-show="tabIndex==0" class="com">
        <p v-for="(item,i) in btn1">
          <i class="icon_"></i>
          <span class="tit">{{item.tit}}</span>:
          <span :title="item.val">{{item.val}}</span>
        </p>
      </div>
      <div v-show="tabIndex==1" class="s-p-l-20 s-p-r-20">
        <el-carousel
          :autoplay="false"
          arrow="never"
          indicator-position="none"
          style="width: 100%; margin: 0 auto"
        >
          <el-carousel-item v-for="(el,i) in newBtn2" :key="i" class="s-flex">
            <div class="item_img" v-for="ele in newBtn2[i]">
              <el-carousel
                :autoplay="false"
                arrow="always"
                indicator-position
              >
                <el-carousel-item v-for="list in ele.imgs">
                  <el-image
                    style="margin: 0 auto"
                    :src="list"
                    :preview-src-list="ele.imgs"
                  >
                  </el-image>
                </el-carousel-item>
              </el-carousel>
              <span>{{ele.tit}}({{ele.imgs.length}})</span>
              <!-- <span>现场图片</span> -->
            </div>
          </el-carousel-item>
        </el-carousel>
      </div>
      <div v-show="tabIndex==2" class="com">
        <div
          class="time-line-item"
          v-for="(item,i) in btn3"
          :key="i"
          v-show="item.time!=null"
        >
          <div class="time-line-item-left" :class="{'time-line-item-left-active':isfinish == false}">{{item.tit}}</div>
          <div class="time-line-item-center">
            <div class="time-line-center-top"></div>
            <div class="time-line-center-bottom"></div>
          </div>
          <div class="time-line-item-right">
            <div class="time-line-item-right-name">{{item.time}}</div>
            <div class="time-line-item-right-container">
              <div class="right-text-container" v-show="item.val!=null">
                处置部门：{{item.val }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { indexApi } from '@/api/indexApi'
export default {
    name:'fmtsPop',
    props: [ "id" ],
    components: { },
    data() {
      return {
        isfinish:false,
        btn: ["详细信息", "图片信息", "处置流程"],
        tabIndex: 0,
        taskCode: "-",
        btn1: [
          { tit: "问题来源", val: "-" },
          { tit: "问题描述", val: "-" },
          { tit: "街道", val: "-" },
          { tit: "地址", val: "-" },
        ],
        btn2: [
          {
            tit: "处理前",
            imgs: [
              // "/static/images/yyjc/yyjc2_bg.png",
              // "/static/images/yyjc/yyjc_bg.png",
            ],
          },
          {
            tit: "处理后",
            imgs: [
              // "/static/images/yyjc/yyjc2_bg.png",
              // "/static/images/yyjc/yyjc_bg.png",
            ],
          },
        ],
        btn3: [
          {
            tit: "上报",
            time: null,
            val: null,
          },
          {
            tit: "立案",
            time: null,
            val: null,
          },
          {
            tit: "派遣",
            time: null,
            val: null,
          },
          {
            tit: "处置",
            time: null,
            val: null,
          },
          // {
          //   tit: "核查",
          //   time: null,
          //   val: "婺城区综合行政执法局",
          // },
          {
            tit: "结案",
            time: null,
            val: null,
          },
        ],
      };
    },
    computed: {
      newBtn1() {
        let newArr = [];
        for (let i = 0; i < this.btn1.length; i += 2) {
          newArr.push(this.btn1.slice(i, i + 2));
        }
        return newArr;
      },
      newBtn2() {
        let newArr = [];
        for (let i = 0; i < this.btn2.length; i += 2) {
          newArr.push(this.btn2.slice(i, i + 2));
        }
        return newArr;
      },
    },
    mounted() {
      this.queryData(this.id);
    },
    methods: {
      queryData(e) {
        indexApi("xzzf_yybz_details", { id: e }).then((res) => {
          this.taskCode = res[0].taskcode;
          this.btn1[0].val = res[0].source;
          this.btn1[1].val = res[0].eventdesc;
          this.btn1[2].val = res[0].streetid;
          this.btn1[3].val = res[0].address;
          this.btn2[0].imgs = res[0].eventimagepath
            .split(",")
            .map((item) => {
              return baseURL.url + "/imgPath" + item;
            });
          this.btn2[1].imgs =
            res[0].dealimagepath != undefined &&
            res[0].dealimagepath.split(",").map((item) => {
              return baseURL.url + "/imgPath" + item;
            });
        });
        indexApi("xzzf_yybz_disposal", { id: e }).then((res) => {
          if (res[0].finishtime) {
            this.isfinish = true
          } else {
            this.isfinish = false
          }
          this.btn3[0].val = res[0].dealdept || null;
          this.btn3[0].time = res[0].createtime1 || null;
          this.btn3[1].time = res[0].createtime2 || null;
          this.btn3[2].time = res[0].dispatchtime || null;
          this.btn3[3].time = res[0].dealtime || null;
          this.btn3[4].time = res[0].finishtime || null;
        });
      },
      close() {
        this.$emit('close')
      },
    },     
}
</script>

<style scoped lang="scss">
  #fmts{
    width:940px;
    position: absolute;
    box-sizing: border-box;
    left: 45.15%;
    top: 17%;
  }
  .content {
    position: relative;
    background: url("@/assets/tcgl/yybz/yybz-detail_bg.png") no-repeat;
    background-size: 100% 100%;
    width: 940px;
    min-height: 600px;
    margin-top: 100px;
    color: #fff;
    font-size: 30px;
  }
  .nav {
    border-bottom: 1px solid;
    border-bottom: 2px solid;
    border-image: linear-gradient(
        -90deg,
        rgba(0, 216, 247, 0) 0%,
        #00afed 100%
      )
      2 2 2 2;
    padding-left: 20px;
  }
  .yybz_icon {
    width: 30px;
    height: 30px;
    margin: 0 20px 0 0;
    background: url("@/assets/tcgl/yybz_icon.png") no-repeat;
    background-size: 100%;
  }
  .tabs {
    min-width: 420px;
    margin: 20px;
    display: flex;
    justify-content: space-between;
  }
  .tab_item {
    width: 243px;
    margin: 0 auto;
    text-align: center;
    height: 59px;
    line-height: 59px;
    font-size: 36px;
    color: #abceef;
  }
  .tab_active {
    color: #fff;
    font-style: italic;
    font-weight: bold;
    background: url("@/assets/xzzfj/tab_bg.png");
    background-size: 100% 100%;
  }
  .tit {
    color: #abceef;
    text-align-last: justify;
    width: 150px;
    display: inline-block;
    margin: 20px;
  }
  .time-line-item {
    width: 100%;
    height: fit-content;
    display: flex;
    align-items: flex-start;
  }
  .time-line-item-left {
    width: 150px;
    margin-left: 50px;
    background: url("@/assets/tcgl/tel-bg.png") no-repeat;
    background-size: 100% 100%;
    height: 45px;
    text-align: center;
    font-size: 32px;
    font-weight: normal;
    font-stretch: normal;
    letter-spacing: 0px;
    color: #fff;
    word-break: break-all;
  }
  .time-line-item-left-active {
      width: 150px;
      margin-left: 50px;
      background: url("@/assets/tcgl/tel-bg-active.png") no-repeat !important;
      background-size: 100% 100% !important;
      height: 45px;
      text-align: center;
      font-size: 32px;
      font-weight: normal;
      font-stretch: normal;
      letter-spacing: 0px;
      color: #fff;
      word-break: break-all;
  }
  .time-line-item-center {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-direction: column;
    flex-shrink: 0;
    height: 150px;
    margin: 0 20px 0 20px;
  }
  .time-line-center-top {
    width: 21px;
    height: 21px;
    border-radius: 100px;
    background: url("@/assets/tcgl/point2.png");
    background-size: cover;
    margin-top: 10px;
    flex-shrink: 0;
  }
  .time-line-center-bottom {
    width: 3px;
    height: 100%;
    background-color: #ffffff;
    flex-shrink: 0;
  }
  .right-text-container {
    color: #b8d3f1;
  }
  .com {
    height: 340px;
    overflow-y: scroll;
    width: 95%;
    margin: 0 20px;
  }
  .com::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 5px;
    height: 5px;
    /*高宽分别对应横竖滚动条的尺寸*/
  }
  .com::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: #00c0ff;
  }
  .item_img {
    /* width: 50%; */
    width: 100%;
    height: 340px;
    text-align: center;
  }
  ::v-deep .el-carousel__item {
    display: flex;
  }
  ::v-deep .el-carousel {
    height: 350px !important;
  }
  ::v-deep .el-carousel__container {
    height: 350px !important;
  }
  ::v-deep .item_img .el-carousel {
    height: 305px !important;
  }
  ::v-deep .item_img .el-carousel__container {
    height: 280px !important;
  }
  ::v-deep .el-carousel__arrow {
    font-size: 24px;
  }
</style>