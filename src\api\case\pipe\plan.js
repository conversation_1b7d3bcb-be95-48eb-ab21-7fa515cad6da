import {request} from '@/utils/request'

// 获取分页查询
export function getPlanList(params) {
  return request({
    url: '/business/scheduling/header/list',
    method: 'get',
    params
  })
}

// 获取查询
export function getPlanDetail(id) {
  return request({
    url: `/business/scheduling/header/${id}`,
    method: 'get'
  })
}

// 新增计划
export function addPlan(data) {
  return request({
    url: '/business/scheduling/header/add',
    method: 'post',
    data
  })
}

// 编辑计划
export function editPlan(data) {
  return request({
    url: '/business/scheduling/header/edit',
    method: 'post',
    data
  })
}

// 删除计划
export function removePlan(id) {
  return request({
    url: `/business/scheduling/header/removeAll/${id}`,
    method: 'post'
  })
}

// 新增月度排班
export function addMonPlan(data) {
  return request({
    url: '/business/scheduling/detail/addByMonth',
    method: 'post',
    data
  })
}
// 更新月度排班
export function editMonPlan(data) {
  return request({
    url: '/business/scheduling/detailVo/batchEdit',
    method: 'post',
    data
  })
}
// 获取月度排班列表
export function getMonPlanList(params) {
  return request({
    url: '/business/scheduling/detail/list',
    method: 'get',
    params
  })
}
// 获取月度排班列表
export function getMonPlanDetail(id) {
  return request({
    url: `/business/scheduling/detail/getDetail/${id}`,
    method: 'get'
  })
}

// 获取月度排班列表
export function editMonPlanDetail(data) {
  return request({
    url: '/business/scheduling/detail/editByMonth',
    method: 'post',
    data
  })
}

