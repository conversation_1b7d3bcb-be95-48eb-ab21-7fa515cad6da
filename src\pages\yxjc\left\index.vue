<template>
  <div class="left-Map">
    <csyxjc @openDialog="openDialog"></csyxjc>
    <hjjc></hjjc>
    <jtlljc></jtlljc>
  </div>
</template>

<script>
import csyxjc from './csyxjc'
import hjjc from './hjjc'
import jtlljc from './jtlljc'
export default {
  name: 'index',
  components: {
    csyxjc,
    hjjc,
    jtlljc,
  },
  data() {
    return {}
  },
  watch: {},
  computed: {},
  mounted() {},
  methods: {
    getList() {},
    openDialog() {
      this.$emit('openDialog')
    },
  },
}
</script>

<style scoped lang='less'>
</style>