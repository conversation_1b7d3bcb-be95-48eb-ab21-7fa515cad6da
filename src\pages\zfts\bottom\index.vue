<template>
  <div class='bottom'>
    <div class="bottom-container" v-cloak>
      <CommonTitle text='监管一件事'></CommonTitle>
      <div class="tj">
        <li v-for="(item,index) in tjList" :key="index" style="flex: 0.2">
          <img :src="item.icon" alt="" class="breath-light" />
          <div class="sp">
            <div class="sp-ys">
              {{item.value}}
              <span class="s-font-28">{{item.unit}}</span>
            </div>
            <div>{{item.name}}</div>
          </div>
        </li>
        <div class="table-wrapper" style="width: 1760px">
          <TableComponent
            :thConfig="thConfig"
            :tableData="formattedTableData"
            :tableHeight="310"
            :autoScroll="true"
            :scrollInterval="1500"
            @infoClick="openYJS"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import TableComponent from '@/components/TableComponent'
import {indexApi} from '@/api/indexApi'
export default {
  name: 'index',
  components: {
    CommonTitle,
    TableComponent
  },
  data() {
    return {
      year: localStorage.getItem("year"),
      city: localStorage.getItem("city"),
      //监管一件事
      tjList: [
        {
          icon: require("@/assets/zfts/zfts_left.png"),
          name: "重点任务",
          value: "20",
          unit: "件",
        },
        // {
        //   icon: "/static/images/zfts/dyzs.png",
        //   name: "监管一件事",
        //   value: "71",
        //   unit: "件",
        // },
        // {
        //   icon: "/static/images/zfts/jcdw.png",
        //   name: "应用场景",
        //   value: "3",
        //   unit: "个",
        // },
      ],
      tableData1: [],
      thConfig: [
        { th: '序号', field: 'index', width: '10%' },
        { th: '年度', field: 'nd', width: '10%' },
        { th: '地区', field: 'dq', width: '10%' },
        { th: '重点任务选题', field: 'zdrwxt', width: '35%', hover: true },
        { th: '上报部门', field: 'cydw', width: '35%', hover: true }
      ]
    }
  },
  computed: {
    formattedTableData() {
      return this.tableData1.map((item, index) => {
        return {
          ...item,
          index: index + 1,
          id: item.xh // 确保每行有唯一id
        }
      });
    }
  },
  mounted() {
    this.$bus.$on('cityChange', (city) => {
      this.city = city;
      this.initApi(city,localStorage.getItem("year"));
    })
    this.$bus.$on('yearChange', (year) => {
      this.year = year;
      this.initApi(localStorage.getItem("city"),year);
    })
    this.initApi(localStorage.getItem("city"),localStorage.getItem("year"));
  },
  methods: {
    initApi(city,year) {
      // 监管表格
      indexApi("/csdn_yjyp23", { dq: city,nd: year }).then((res) => {
        console.log(res)
        this.tableData = res.data;
        const res1 = new Map();
        for (let i of res.data) {
          if (!res1.has(i.xh)) {
            res1.set(i.xh, i);
          }
        }
        let str = [...res1.values()];
        this.tableData1 = str;
        this.tjList[0].value = str.length;
      });
    },
    openYJS(item) {
      if (item && item.xh) {
        // 可以在这里处理点击事件，如果需要其他参数可以根据情况修改
      }
    }
  },
  watch: {}
}
</script>

<style scoped lang='less'>
.bottom-container {
  width: 1760px;
  height: 525px;
  padding: 20px;
  box-sizing: border-box;
  background: url("@/assets/index/bottom-bg.png") no-repeat;
  background-size: 100% 100%;
}
/* 监管一件事 */
.tj {
  width: 100%;
  height: 400px;
  display: flex;
  padding: 10px;
  box-sizing: border-box;
}

.tj li {
  width: 50%;
  height: 100%;
  list-style: none;
  position: relative;
  padding-top: 110px;
}

.tj li img {
  width: 190px;
  height: 174px;
}

.jdt {
  width: 100%;
  height: 350px;
  box-sizing: border-box;
}

.tj li:first-child .sp-ys {
  font-size: 65px;
  //font-style: italic;
  font-weight: bold;
  background: linear-gradient(to bottom, #cbf2ff, #ffffff, #00c0ff, #80e0ff, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
  font-family: DINCondensed;
}

.tj li:nth-child(2) .sp-ys {
  font-size: 65px;
  //font-style: italic;
  font-weight: bold;
  background: linear-gradient(to bottom, #cbf2ff, #ffffff, #00c0ff, #80e0ff, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
  font-family: DINCondensed;
}

.tj li:nth-child(3) .sp-ys {
  font-size: 48px;
  color: #ffb436 !important;
}

.sp {
  font-size: 36px;
  color: #fff;
  position: absolute;
  /* top: -13px;
  left: 155px;
  font-weight: bolder; */
  top: 45px;
  width: 190px;
  text-align: center;
}

.table-wrapper {
  padding: 10px 15px;
  box-sizing: border-box;
}
</style>