<template>
  <div class="mgt48">
    <CommonTitle text="交通流量">
      <TabSwitch :tabList="list" :activeIndex="index" @tab-change="handleTabChange" />
    </CommonTitle>
    <div class="yearChange" v-if="index == 1">
      <el-date-picker
        v-model="datas"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="getData"
        :append-to-body="false"
      ></el-date-picker>
    </div>
    <div class="qy_box" v-if="index == 0">
      
      <el-select v-model="value" placeholder="请选择" style="width: 220px" @change="getData">
        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
      </el-select>
    </div>
    <div class="jtllChart" id="jtllChart"></div>
    <!-- <CommonTitle2 text="违停热度区域"></CommonTitle2>
    <div class="list_box">
      <div class="list_item" v-for="(item, index) in listData" :key="index">
        <div>NO.{{ index + 1 }}</div>
        <div>{{ item.name }}</div>
        <div>{{ item.count }}辆</div>
        <div>
          <span :class="item.isAdd ? 'up' : 'down'">{{ item.isAdd ? '上升' : '下降' }}{{ item.num }}名</span>
          <img v-if="item.isAdd" src="@/assets/csyxgl/up_icon.png" alt="" />
          <img v-else src="@/assets/csyxgl/down_icon.png" alt="" />
        </div>
      </div>
    </div> -->
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import CommonTitle2 from '@/components/CommonTitle2'
import TabSwitch from '@/components/TabSwitch'
import { indexApi } from '@/api/indexApi'
import moment from 'moment'
export default {
  name: 'index',
  components: {
    CommonTitle,
    CommonTitle2,
    TabSwitch,
  },
  data() {
    return {
      index: 1,
      list: [
        { name: '时', value: '1' },
        { name: '天', value: '2' },
      ],
      datas: [new Date().getFullYear() + '-01-01', moment(new Date()).format('YYYY-MM-DD')],
      value: '',
      options: [],
      chartsData: [
        {
          name: '婺城',
          value: 2800,
          value1: 2500,
        },
        {
          name: '金东',
          value: 3800,
          value1: 3500,
        },
        {
          name: '兰溪',
          value: 2000,
          value1: 1500,
        },
        {
          name: '东阳',
          value: 3000,
          value1: 2500,
        },
        {
          name: '义乌',
          value: 3000,
          value1: 2500,
        },
        {
          name: '永康',
          value: 3000,
          value1: 2500,
        },
        {
          name: '武义',
          value: 3000,
          value1: 2500,
        },
        {
          name: '浦江',
          value: 3000,
          value1: 2500,
        },
        {
          name: '磐安',
          value: 3000,
          value1: 2500,
        },
      ],
      // listData: [
      //   { name: '站前广场', count: 5464, isAdd: true, num: 2 },
      //   { name: '站前广场', count: 5464, isAdd: true, num: 2 },
      //   { name: '站前广场', count: 5464, isAdd: false, num: 2 },
      //   { name: '站前广场', count: 5464, isAdd: true, num: 2 },
      //   { name: '站前广场', count: 5464, isAdd: true, num: 2 },
      //   { name: '站前广场', count: 5464, isAdd: true, num: 2 },
      // ],
    }
  },
  computed: {},
  mounted() {
    this.getOption()
    this.getData()
  },
  methods: {
    handleTabChange(i) {
      this.index = i
      this.getData()
    },
    getOption() {
      indexApi('/jthl-kksfzlb').then((res) => {
        this.options = res.data.map((item) => {
          return {
            label: item.station_name,
            value: item.station_id,
          }
        })
        this.value = this.options[0].value
      })
    },
    getData() {
      if (this.index == 0) {
        indexApi('/jthl-sskksrsccl', { code: this.value }).then((res) => {
          this.chartsData = res.data.map((item) => {
            return {
              name: item.time,
              value: item.in_num,
              value1: item.out_num,
            }
          })
          this.initCharts()
        })
      } else {
        indexApi('/jthl-srsccltj', { startdate: this.datas[0].replace(/-/g, ''), enddate: this.datas[1].replace(/-/g, '') }).then((res) => {
          this.chartsData = res.data.map((item) => {
            return {
              name: item.days,
              value: item.in_num,
              value1: item.out_num,
            }
          })
          this.initCharts()
        })
      }
    },
    initCharts() {
      const chartDom = document.getElementById('jtllChart')
      const myChart = this.$echarts.init(chartDom)

      const option = {
        grid: {
          left: '7%',
          right: '7%',
          bottom: '5%',
          top: '14%',
          containLabel: true,
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(51, 51, 51, 0.7)',
          borderWidth: 0,
          axisPointer: {
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          textStyle: {
            color: 'white',
            fontSize: '24',
          },
        },
        legend: {
          orient: 'horizontal',
          icon: 'circle',
          right: '5%',
          padding: [5, 10, 10, 0],
          itemGap: 45,
          textStyle: {
            color: '#D6E7F9',
            fontSize: 28,
          },
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.chartsData.map((item) => item.name),
          axisLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.6)',
            },
          },
          axisLabel: {
            textStyle: {
              fontSize: 24,
            },
          },
        },
        yAxis: {
          name: '单位(辆)',
          nameTextStyle: {
            color: 'rgba(255,255,255,0.6)',
            padding: [20, 10, 10, 10],
            fontSize: 24,
          },
          type: 'value',
          axisLabel: {
            textStyle: {
              fontSize: 24,
            },
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.6)',
            },
          },
          splitLine: {
            lineStyle: {
              color: '#4A6C89',
            },
          },
        },
        series: [
          {
            name: '输入车辆数',
            data: this.chartsData.map((item) => item.value),
            type: 'line',
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(62, 215, 253, 0.30)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(62, 215, 253, 0)',
                  },
                ],
              },
            },
            lineStyle: {
              color: '#3ED7FD',
            },
            smooth: true,
            symbol: 'none',
          },
          {
            name: '驶出车辆数',
            data: this.chartsData.map((item) => item.value1),
            type: 'line',
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(224, 214, 34, 0.30)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(224, 214, 34, 0)',
                  },
                ],
              },
            },
            lineStyle: {
              color: '#E0D622',
            },
            smooth: true,
            symbol: 'none',
          },
        ],
      }

      myChart.setOption(option)

      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
/deep/ .yearChange {
  margin: 24px 24px;
  .el-input__inner {
    height: 48px !important;
    background-color: #132c4e !important;
    border: 2px solid #afdcfb !important;
    color: #fff !important;
    border-radius: 15px !important;
    font-size: 24px;
  }
}
.jtllChart {
  width: 100%;
  height: 480px;
}
.list_box {
  height: 300px;
  overflow-y: auto;
  margin: 20px 40px;
  .list_item {
    display: flex;
    align-content: center;
    align-items: center;
    border-bottom: 2px dashed #fff;
    > div {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 28px;
      color: #ffffff;
      line-height: 48px;
      flex: 1;
      .up {
        color: #22e097;
      }
      .down {
        color: #ee3636;
      }
      img {
        width: 22px;
        height: 26px;
      }
    }
  }
}
.qy_box {
  margin: 24px 24px;
  /deep/ .el-input__inner {
    height: 48px !important;
    background-color: #132c4e !important;
    border: 2px solid #afdcfb !important;
    color: #fff !important;
    border-radius: 15px !important;
    font-size: 24px;
  }
}
</style>