<template>
  <div v-loading="loading" class="countVol" element-loading-background="rgba(0, 0, 0, 0.8)">
    <div ref="chart1" class="chart" />
    <div ref="chart2" class="chart" />
  </div>
</template>

<script>
import { getCountVol } from '@/api/board/dataView/index'

export default {
  data() {
    return {
      myChart1: null,
      myChart2: null,
      loading: false,
      jobCountData: [],
      sexCountData: []
    }
  },
  mounted() {
    this.fetchData()
  },
  destroyed() {
    window.removeEventListener('resize', this.resizeChart)
  },
  methods: {
    fetchData() {
      this.loading = true
      getCountVol().then(res => {
        this.loading = false
        this.sexCountData = res.data.sexCountData
        this.jobCountData = res.data.jobCountData
        this.initChart()
      }).catch(() => {
        this.loading = false
      })
    },
    getOption(seriesName, data) {
      return {
        color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'],
        tooltip: {
          trigger: 'item'
        },
        series: [
          {
            name: seriesName,
            type: 'pie',
            radius: ['50%', '90%'],
            avoidLabelOverlap: false,
            labelLine: {
              show: true,
              length: 5,
              length2: 2,
              smooth: true,
              lineStyle: {
                color: '#6d6d6d'
              }
            },
            label: {
              fontSize: 12,
              color: '#00f7ff'
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            data: data
          }
        ]
      }
    },
    initChart() {
      const options1 = this.getOption('性别', this.sexCountData)
      this.myChart1 = this.$echarts.init(this.$refs.chart1)
      this.myChart1.setOption(options1)
      // 第二个
      const options2 = this.getOption('职业', this.jobCountData)
      this.myChart2 = this.$echarts.init(this.$refs.chart2)
      this.myChart2.setOption(options2)
      window.addEventListener('resize', this.resizeChart)
    },
    resizeChart() {
      setTimeout(() => {
        this.myChart1.resize()
        this.myChart2.resize()
      }, 500)
    }
  }
}
</script>

<style scoped lang="scss">
.countVol {
  height: 100%;
  overflow: hidden;
  .chart {
    width: 50%;
    height: 100%;
    float: left;
  }
}
</style>
