<?xml version="1.0" encoding="UTF-8"?>
<svg width="28px" height="28px" viewBox="0 0 28 28" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="18.6776945%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="81.5134837%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="实景三维西安示范应用基础子系统切图" transform="translate(-563.000000, -150.000000)">
            <g id="拖动条" transform="translate(563.000000, 150.000000)">
                <rect id="矩形" fill-opacity="0.01" fill="#FFFFFF" x="0" y="0" width="28" height="28"></rect>
                <g id="编组-46" transform="translate(2.000000, 2.000000)">
                    <path d="M4.5,4.5 L4.5,19.5 L19.5,19.5 L19.5,4.5 L4.5,4.5 Z" id="矩形" stroke="url(#linearGradient-1)" transform="translate(12.000000, 12.000000) rotate(-315.000000) translate(-12.000000, -12.000000) "></path>
                    <path d="M4.5,4.5 L4.5,19.5 L19.5,19.5 L19.5,4.5 L4.5,4.5 Z" id="矩形" stroke="url(#linearGradient-1)" transform="translate(12.000000, 12.000000) rotate(-225.000000) translate(-12.000000, -12.000000) "></path>
                </g>
            </g>
        </g>
    </g>
</svg>