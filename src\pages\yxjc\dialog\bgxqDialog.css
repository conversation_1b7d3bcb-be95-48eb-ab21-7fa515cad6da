/* bgxqDialog.css */
::-webkit-scrollbar {
  display: none;
}

ul,
ul li {
  list-style: none;
}

.bgxq-dialog {
  width: 2030px;
  height: 2012px;
  background: url('@/assets/yxjc/dialogBg2.png') no-repeat;
  background-size: 100% 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-direction: column;
}

.rw-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 46px 3% 0;
  width: 95%;
  height: 60px;
  line-height: 60px;
}

.close {
  background: url('@/assets/zhdd/close.png') no-repeat;
  width: 34px;
  height: 34px;
}

.ql-indent-1 img {
  width: 100%;
}

img {
  width: 100%;
}

::-webkit-scrollbar {
  width: 0;
  height: 0;
}

.scrollTab {
  width: 928px;
  height: 70px;
}

.content {
  width: 100%;
  padding: 20px 36px 60px 26px;
  box-sizing: border-box;
}

.table {
  padding: 20px 30px 30px 30px;
  box-sizing: border-box;
  width: 100%;
  height: 480px;
  overflow: scroll;
}

.td {
  padding: 20px 30px;
  box-sizing: border-box;
  flex: 1;
  text-align: left;
}

.tableHead {
  height: 80px;
  background-color: #0a6cff33;
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 500;
  font-size: 32px;
  color: #cde7ff;
  text-align: left;
}

.tableLine {
  min-height: 80px;
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 500;
  font-size: 32px;
  color: #cde7ff;
  text-align: left;
}

.tableLine_active {
  background-color: #0a6cff33;
}

.pagi {
  display: flex;
  justify-content: flex-end;
  margin-top: 40px;
}

.item-wrap {
  flex-wrap: wrap;
  align-items: flex-start;
  padding: 0 20px 0 40px;
  box-sizing: border-box;
  width: 100%;
}

.itemCon {
  width: calc((100% - 70px * 2) / 3);
  margin-right: 70px;
  margin-top: 30px;
  padding: 20px 20px 20px 20px;
  box-sizing: border-box;
  height: fit-content;
  background: url('@/assets/yxjc/itemBg.png') no-repeat;
  background-size: 100% 100%;
  cursor: pointer;
}

.itemCon:nth-child(3n + 3) {
  margin-right: 0;
}

.title {
  font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
  font-weight: 400;
  font-size: 40px;
  line-height: 52px;
  text-align: center;
  font-style: normal;
  text-transform: none;
  background: linear-gradient(180deg, #0ec5ec 0%, #effcfe 69%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.num {
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 400;
  font-size: 24px;
  line-height: 70px;
  color: #ffffff;
  text-align: center;
}

.time {
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 400;
  font-size: 24px;
  line-height: 50px;
  color: #ffffff;
  text-align: center;
}

.text-yellow {
  font-family: YouSheBiaoTiHei;
  font-weight: 600;
  font-size: 40px;
  vertical-align: bottom;
  background: linear-gradient(90deg, #ffffff 32%, #f7b23b 100%);
  background: linear-gradient(0deg, #ffffff 32%, #f7b23b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.rate {
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 400;
  font-size: 24px;
  color: #ffffff;
}

.rateValue {
  margin: 0 8px;
}

.text-red {
  color: #fd364f;
}

.text-green {
  color: #22e097;
}

.icon {
  width: 18px;
  height: 20px;
}

.flex-c {
  display: flex;
  align-items: center;
}

.flex-b {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

::v-deep .el-dialog {
  margin-top: 9vh !important;
}

/* 分页 */
:deep(.el-pagination button) {
  height: 60px;
  width: 40px;
  background: #5f7b96 !important;
}

:deep(.el-pagination button .el-icon) {
  font-size: 32px !important;
  color: #c1e2fa !important;
  font-weight: 400;
}

:deep(ul li) {
  border: 2px solid transparent;
  margin-left: 10px !important;
  padding: 10px 10px !important;
  box-sizing: border-box;
  font-size: 32px !important;
  color: #c1e2fa !important;
  background: #5f7b96 !important;
  font-weight: 500;
  line-height: 36px !important;
  border-radius: 4px;
}

:deep(li.active) {
  color: #fff !important;
  background-color: #0166a6 !important;
}

:deep(.el-pagination__total) {
  color: #fff;
  font-size: 32px !important;
  padding-top: 15px;
  padding-right: 20px;
}

:deep(.el-pagination button),
:deep(.el-pagination span:not([class*='suffix'])) {
  font-size: 32px;
  color: #fff;
}

:deep(.el-pagination__editor.el-input .el-input__inner) {
  height: 60px;
  font-size: 32px;
  background-color: transparent;
  color: #fff;
  border-color: #6f788a;
}

:deep(.el-pagination__editor.el-input) {
  width: 100px;
  height: 60px;
  margin: 0 10px;
}