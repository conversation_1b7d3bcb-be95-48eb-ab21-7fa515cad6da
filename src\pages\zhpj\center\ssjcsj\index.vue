<template>
  <div>
    <CommonTitle text="实时监测数据"></CommonTitle>
    <div class="list_box">
      <div class="list_item" v-for="(item, index) in listData" :key="index">
        <img :src="item.icon" alt="" />
        <div class="item_box">
          <div class="item_top">{{ item.name }}</div>
          <img src="@/assets/zhpj/icon_line.png" alt="" />
          <div class="item_bottom blue_linear">{{ item.value }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
export default {
  name: 'index',
  components: {
    CommonTitle,
  },
  data() {
    return {
      listData: [
        { name: '当前温度', value: '25℃', icon: require('@/assets/zhpj/icon1.png') },
        { name: '空气湿度', value: '62%', icon: require('@/assets/zhpj/icon2.png') },
        { name: 'PM2.5', value: '35', icon: require('@/assets/zhpj/icon3.png') },
        { name: '噪音分贝', value: '45db', icon: require('@/assets/zhpj/icon4.png') },
        { name: '人流量', value: '2345', icon: require('@/assets/zhpj/icon5.png') },
        { name: '车流量', value: '1234', icon: require('@/assets/zhpj/icon6.png') },
      ],
    }
  },
  computed: {},
  mounted() {},
  methods: {},
  watch: {},
}
</script>

<style scoped lang='less'>
.list_box {
  display: flex;
  flex-wrap: wrap;
  margin-top: 28px;

  .list_item {
    display: flex;
    align-content: center;
    align-items: center;
    margin-bottom: 50px;
    justify-content: center;
    width: 33%;
    img {
      width: 166px;
      height: 167px;
    }
    .item_box {
      margin-left: 14px;
      text-align: center;
      .item_top {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 400;
        font-size: 32px;
        color: #cfd7e5;
      }
      img {
        width: 216px;
        height: 24px;
      }
      .item_bottom {
        font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
        font-weight: 400;
        font-size: 64px;
        line-height: 83px;
        margin-top: 13px;
      }
    }
  }
}
</style>