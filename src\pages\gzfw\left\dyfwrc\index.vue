<template>
  <div>
    <CommonTitle text="当日服务人次"></CommonTitle>
    <div class="lineChart" id="lineChart"></div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import * as echarts from 'echarts'
import { getDailyService } from '@/api/gzfw/index.js'

export default {
  name: 'index',
  components: {
    CommonTitle,
  },
  data() {
    return {
      chartsData: [],
    }
  },
  computed: {},
  mounted() {
    getDailyService().then((res) => {
      this.chartsData = res.data.map((item) => {
        return {
          name: item.key,
          value: item.value,
        }
      })
      this.initCharts()
    })
  },
  methods: {
    initCharts() {
      const chartDom = document.getElementById('lineChart')
      const myChart = echarts.init(chartDom)

      const option = {
        grid: {
          left: '7%',
          right: '7%',
          bottom: '5%',
          top: '14%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.chartsData.map((item) => item.name),
          axisLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.6)',
            },
          },
          axisLabel: {
            textStyle: {
              fontSize: 24,
            },
          },
        },
        yAxis: {
          name: '单位(人次)',
          nameTextStyle: {
            color: 'rgba(255,255,255,0.6)',
            padding: [20, 10, 10, 10],
            fontSize: 24,
          },
          type: 'value',
          axisLabel: {
            textStyle: {
              fontSize: 24,
            },
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.6)',
            },
          },
          splitLine: {
            lineStyle: {
              color: '#4A6C89',
            },
          },
        },
        series: [
          {
            data: this.chartsData.map((item) => item.value),
            type: 'line',
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(0,255,255,0.3)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(0,255,255,0)',
                  },
                ],
              },
            },
            lineStyle: {
              color: '#00ffff',
            },
            smooth: true,
            symbol: 'none',
          },
        ],
      }

      myChart.setOption(option)

      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
  },
}
</script>

<style scoped lang='less'>
.lineChart {
  width: 100%;
  height: 480px;
  padding: 20px 0;
  box-sizing: border-box;
}
</style>