import {request} from '@/utils/request'

// 查询签到记录列表
export function listSign(query) {
  return request({
    url: '/business/vol/sign/list',
    method: 'get',
    params: query
  })
}

// 查询签到记录详细
export function getSign(id) {
  return request({
    url: '/business/vol/sign/' + id,
    method: 'get'
  })
}

// 新增签到记录
export function addSign(data) {
  return request({
    url: '/business/vol/sign/add',
    method: 'post',
    data: data
  })
}

// 修改签到记录
export function updateSign(data) {
  return request({
    url: '/business/vol/sign/edit',
    method: 'post',
    data: data
  })
}

// 删除签到记录
export function delSign(id) {
  return request({
    url: '/business/vol/sign/remove/' + id,
    method: 'post'
  })
}

// 导出签到记录
export function exportSign(query) {
  return request({
    url: '/business/vol/sign/export',
    method: 'get',
    params: query
  })
}
