import { request } from '@/utils/request'

//当日服务人次
export function getDailyService(params) {
  return request({
    url: `/screen/public/dailyService`,
    method: 'get',
    params
  })
}
//累计服务人次
export function getTotalService(params) {
  return request({
    url: `/screen/public/totalService`,
    method: 'get',
    params
  })
}
//服务类型
export function getServiceType(params) {
  return request({
    url: `/screen/public/serviceType`,
    method: 'get',
    params
  })
}
//满意度分析
export function getSatisService(params) {
  return request({
    url: `/screen/public/satisService`,
    method: 'get',
    params
  })
}
//服务评价分析
export function getAvgAnalysis(params) {
  return request({
    url: `/screen/public/avgAnalysis`,
    method: 'get',
    params
  })
}
//服务热榜
export function serviceFriendly(params) {
  return request({
    url: `/screen/public/serviceFriendly`,
    method: 'get',
    params
  })
}
//中间
export function midMap(params) {
  return request({
    url: `/screen/public/midMap`,
    method: 'get',
    params
  })
}
