import {request} from '@/utils/request'
// 获取我的分页查询
export function policeList(params) {
  return request({
    url: '/police/case/one/myCases',
    method: 'get',
    params
  })
}
// 获取我的分页查询
export function policeOne(params) {
  return request({
    url: '/police/case/one/' + params,
    method: 'get'
  })
}
// 获取全部分页查询
export function policeAll(params) {
  return request({
    url: '/police/case/one/list',
    method: 'get',
    params
  })
}
// 新增四位一体
export function addPolice(data) {
  return request({
    url: '/police/case/one/add',
    method: 'post',
    data
  })
}
// 修改四位一体
export function editPolice(data) {
  return request({
    url: '/police/case/one/edit',
    method: 'post',
    data
  })
}
// 驳回四位一体
export function revokePolice(data) {
  return request({
    url: '/police/case/one/revoke',
    method: 'post',
    data
  })
}
// 删除四位一体
export function removePolice(data) {
  return request({
    url: '/police/case/one/remove/' + data,
    method: 'post'
  })
}
// 获取详情
export function police(params) {
  return request({
    url: '/police/case/one',
    method: 'get',
    params
  })
}
// 获取文件
export function getFiles(params) {
  return request({
    url: '/system/file/list',
    method: 'get',
    params
  })
}
// 删除文件
export function removeFiles(data) {
  return request({
    url: '/system/file/remove/' + data,
    method: 'post'
  })
}
// 获取人员
export function userList() {
  return request({
    url: 'system/dept/deptUsertreeselect',
    method: 'get'
  })
}
