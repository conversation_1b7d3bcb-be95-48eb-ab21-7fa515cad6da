<template>
  <div class="left">
    <div class="left-menu">
      <div class="menu-item"
        :class="{ active: isHeatMapActive }"
        @click="loadHeatMap()">
        <span class="btn-text">案卷热力图</span>
        <span class="btn-subtext">Thermodynamic diagram</span>
      </div>
      <div class="menu-item"
        :class="{ active: showPersonnelList }"
        @click="togglePersonnelList()">
        <span class="btn-text">人员列表</span>
        <span class="btn-subtext">Personnel list</span>
      </div>
    </div>
    <!-- 人员列表弹窗 -->
    <UserList :visible.sync="showPersonnelList" :userlist-data="tableData" @setTerminalNo="no => terminalNo = no" @changeCenter="changeCenter" @openDetail="handleOpenUserDetail" />
    <!-- <recorder-window-info ref="recorderWindowInfo" :visible.sync="recorderVisible" :detail-id="recorderData" /> -->
    <!-- 执法人员弹窗 -->
    <zfryInfo :visible.sync="showZfryInfo" @close="showZfryInfo = false" :detail-id="recorderId" />
    <ajHeatMap v-if="isHeatMapActive" @close="isHeatMapActive = false"></ajHeatMap>
  </div>
</template>

<script>
import UserList from '@/pages/zqyzt/left/peopleList';
import ajHeatMap from '@/pages/zqyzt/components/ajHeatMap/index.vue'
import HeatmapOverlay from '@/pages/zqyzt/left/windowInfo/HeatmapOverlay/index.vue'
import recorderWindowInfo from '@/pages/zqyzt/left/windowInfo/recorder';
import zfryInfo from '@/pages/zqyzt/left/windowInfo/recorder/index.vue'
import { listAll, listAllCase } from '@/api/zqyzt';
import MapService from '../../../components/Map/index.js';
// import {data} from '../../../../public/test.json'

export default {
  components: {
    UserList,
    ajHeatMap,
    recorderWindowInfo,
    zfryInfo
  },
  data() {
    return {
      isHeatMapActive: false,
      showPersonnelList: false,
      recorderVisible: false,
      recorderData: {},
      searchForm: {
        userName: '',
        terminalNo: '',
        online: ''
      },
      tableData: [
      ],
      showZfryInfo: false,
      recorderId: ''
    }
  },
  mounted() {
    this.getListAll();
  },
  computed: {
    filteredPersonnel() {
      return this.personnelList.filter(person => {
        const nameMatch = person.name.includes(this.nameSearch)
        const terminalMatch = person.terminal.includes(this.terminalSearch)
        const statusMatch = this.statusFilter === 'all' ||
          (this.statusFilter === 'online' && person.status === '在线') ||
          (this.statusFilter === 'offline' && person.status === '离线')
        return nameMatch && terminalMatch && statusMatch
      })
    }
  },
  methods: {
    loadHeatMap() {
      this.isHeatMapActive = !this.isHeatMapActive
    },
    togglePersonnelList() {
      this.showPersonnelList = !this.showPersonnelList
      this.getListAll()
    },
    getListAll() {
      listAll().then(res => {
        let resData = res.data
        if (resData.recorder) {
          this.tableData = resData.recorder
        }
        console.log(res)
      })
    },
    changeCenter(item) {
      console.log(item)
      // window.view.camera = {
      //   position: {
      //     spatialReference: {
      //       latestWkid: 4490,
      //       wkid: 4490,
      //     },
      //     x: item.esX,
      //     y: item.esY,
      //     z: 500,
      //   },
      //   heading: 0.26874578434742386,
      //   tilt: 0.49999999999694683,
      // }
      MapService.flyTo({
        destination: [item.esX, item.esY],
        zoom: 22,
      })
    },
    handleOpenUserDetail(info) {
      console.log(info)
      this.showZfryInfo = true
      this.recorderId = info.id
    }
  }
}
</script>

<style lang="scss" scoped>
.left {
  .left-menu {
    margin-left: 25%;
    width: 36%;
    display: flex;
    flex-direction: column;
  }

  .menu-item {
    height: 90px;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #00d4ff;
    padding: 22px 0px 12px 0px;
    cursor: pointer;
    background: linear-gradient(90deg, rgba(0,212,255,0.2) 0%, rgba(45,98,255,0.3) 100%);
    margin-bottom: 40px;
    border-radius: 10px;
    border: 1px solid rgba(0,212,255,0.5);
    box-shadow: 0 0 15px rgba(0,212,255,0.2);
    transition: all 0.3s;

    &:hover, &.active {
      background: linear-gradient(90deg, rgba(0,212,255,0.4) 0%, rgba(45,98,255,0.5) 100%);
      border-color: #00d4ff;
      box-shadow: 0 0 20px rgba(0,212,255,0.4);
    }

    .btn-text {
    font-size: 30px;
    font-weight: bold;
    }

    .btn-subtext {
        font-size: 26px;
        opacity: 0.8;
        margin-top: 2px;
    }
  }
}
</style>
