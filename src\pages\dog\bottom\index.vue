<template>
  <div class='bottom'>
    <CommonTitle text='事件列表'></CommonTitle>
    <div class='wrap-container'>
      <TableComponent :thConfig="thConfig" :tableData="tableData" @infoClick="infoClick"></TableComponent>
    </div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import TableComponent from '@/components/TableComponent'
export default {
  name: 'index',
  components: {
    CommonTitle,
    TableComponent
  },
  data() {
    return {
      tableData: [
        {
          bh:"1",
          fssj:"2022-01-18 12:00:00",
          sjlx:"违规养犬事件",
          fsqy:"婺城区",
          xxdz:"新城大道601"
        },
        {
          bh:"2",
          fssj:"2022-01-18 12:00:00",
          sjlx:"违规养犬事件",
          fsqy:"婺城区",
          xxdz:"新城大道601"
        },
        {
          bh:"3",
          fssj:"2022-01-18 12:00:00",
          sjlx:"违规养犬事件",
          fsqy:"婺城区",
          xxdz:"新城大道601"
        },
        {
          bh:"4",
          fssj:"2022-01-18 12:00:00",
          sjlx:"违规养犬事件",
          fsqy:"婺城区",
          xxdz:"新城大道601"
        },
        {
          bh:"5",
          fssj:"2022-01-18 12:00:00",
          sjlx:"违规养犬事件",
          fsqy:"婺城区",
          xxdz:"新城大道601"
        },
        {
          bh:"6",
          fssj:"2022-01-18 12:00:00",
          sjlx:"违规养犬事件",
          fsqy:"婺城区",
          xxdz:"新城大道601"
        },
        {
          bh:"7",
          fssj:"2022-01-18 12:00:00",
          sjlx:"违规养犬事件",
          fsqy:"婺城区",
          xxdz:"新城大道601"
        },
        {
          bh:"8",
          fssj:"2022-01-18 12:00:00",
          sjlx:"违规养犬事件",
          fsqy:"婺城区",
          xxdz:"新城大道601"
        },
        {
          bh:"9",
          fssj:"2022-01-18 12:00:00",
          sjlx:"违规养犬事件",
          fsqy:"婺城区",
          xxdz:"新城大道601"
        }
      ],
      thConfig: [
        {
          th: '编号',
          field: 'bh',
          width: '20%',
          hover: false,
        },
        {
          th: '发生时间',
          field: 'fssj',
          width: '20%',
          hover: true,
        },
        {
          th: '事件类型',
          field: 'sjlx',
          width: '20%',
          hover: true,
        },
        {
          th: '发生区域',
          field: 'fsqy',
          width: '20%',
          hover: false,
        },
        {
          th: '详细地址',
          field: 'xxdz',
          width: '20%',
          hover: false,
        }
      ],
    }
  },
  computed: {},
  mounted() {

  },
  methods: {
    infoClick() {

    }
  },
  watch: {}
}
</script>

<style scoped lang='less'>
  .wrap-container {
    width: 100%;
    height: 480px;
    margin-top: 20px;
  }
</style>