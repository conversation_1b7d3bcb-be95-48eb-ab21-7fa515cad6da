<template>
    <ygfDialog :visible='visible' width='1300px' style='margin-top: 200px;'>
      <div id="zhdddialog" v-cloak>
        <div class="title">
          <span class="s-c-yellow-gradient1">一键通知</span>
          <div class="close" @click="close()">×</div>
        </div>

        <div class="content">
          <el-form ref="form" :model="form" label-width="250px">
            <el-form-item label="任务来源 :">
              <div>
                <el-input
                  style="width: 80%"
                  v-model="form.from"
                  placeholder="请输入任务来源"
                ></el-input>
              </div>
            </el-form-item>
            <el-form-item label="响应级别 :">
              <el-select v-model="form.region" placeholder="请选择级别" style="width: 80%" :popper-append-to-body='false'>
                <el-option
                  label="三级响应(一般)"
                  value="三级响应(一般)"
                ></el-option>
                <el-option
                  label="二级响应(中级)"
                  value="二级响应(中级)"
                ></el-option>
                <el-option
                  label="一级响应(最高级)"
                  value="一级响应(最高级)"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item :label="'调度方式'">
              <el-radio-group v-model="labelName">
                <el-radio :label="'1'">扁平指挥</el-radio>
                <el-radio :label="'2'" v-show="adminCity == '金华市'">层级指挥</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item>
              <el-cascader
                v-if="labelName == '1'"
                style="width: 80%;margin-top: 15px"
                class="team_input"
                v-model="form.list"
                :props="props"
                :show-all-levels="false"
                @change="changeList"
              >
                <template slot-scope="{ node, data }">
                  <span :title="data.label">{{ data.label }}</span>
                </template>
              </el-cascader>
              <el-select v-model="chooseDepart" :popper-append-to-body='false' placeholder="请选择" style="width: 80%;margin-top: 15px" v-else>
                <el-option
                  v-for="item in departList"
                  :key="item.deptName"
                  :label="item.deptName"
                  :value="item.deptName">
                </el-option>
              </el-select>
            </el-form-item>
            <!-- <el-form-item label="详细地址 :">
              <div>
                <el-input
                  style="width: 70%"
                  v-model="form.name"
                  placeholder="请输入详情地址"
                ></el-input>
                <span style="width: 40%">
                  <el-button circle icon="el-icon-search"></el-button>
                  <el-button circle icon="el-icon-house"></el-button>
                  <el-button circle icon="el-icon-location"></el-button>
                </span>
              </div>
            </el-form-item> -->
            <el-form-item label="指令内容 :">
              <el-input
                style="width: 80%"
                :rows="5"
                placeholder="请输入指令内容"
                type="textarea"
                v-model="form.desc"
              ></el-input>
            </el-form-item>
          </el-form>
          <div class="btns-box s-m-t-25">
            <!-- <div class="fs-35" @click="close()">取消</div> -->
            <div
              style="text-align: center; width: 300px;font-size: 35px;color: white;"
              @click="submitForm()"
            >
              下发指令
            </div>
          </div>
        </div>
      </div>
    </ygfDialog>
</template>

<script>
import ygfDialog from '@/components/ygfDialog'
import { getDepart, getDepartMentList, getWarning, WorkNotifi, yqReport } from '@/api/zhdd'
import { indexApi } from '@/api/indexApi'
import { getUserList, xzzfjWorkNotification } from '@/api/admApi'
export default {
  name: 'index',
  props: ['visible','msgData'],
  components: {
    ygfDialog
  },
  data() {
    let this_ = this;
    return {
      deptName:"",
      form: {
        from: "",
        name: "",
        region: "三级响应(一般)",
        desc: "",
        list: [],
        dataType: 1
      },
      staffList: [],
      selStaff: [], // 选择的人员列表的全部信息
      adminCity: localStorage.getItem('adminCity'),
      props: {
        multiple: false,
        lazy: true,
        lazyLoad(node, resolve) {
          this_.lazyLoads2(node, resolve);
        },
      },
      gjxx: [],
      labelName:"1",
      departList: [],
      chooseDepart:"",
      flag: false //判断科室列是否有值
    }
  },
  computed: {},
  mounted() {

  },
  methods: {
    init() {
      this.getDepartList();
      this.getDepartment()
    },
    getDepartList() {
      getDepartMentList({area: localStorage.getItem("city")}).then(res => {
        if (res.code == 200) {
          this.departList = res.data
        }
      })
    },
    getDepartment() {
      getDepart().then(res => {
        if (res.code == 200) {
          this.deptName = res.data.deptName
        }
      })
    },
    lazyLoads(node, resolve) {
      const that = this;
      console.log("懒加载", node);
      if (node.level == 0) {
        indexApi("/zfzfry001",{area: localStorage.getItem("city") == '金华市'?"":localStorage.getItem("city")}).then((res) => {
          let data0 = [];
          res.data.map((a) => {
            data0.push({
              label: [
                "婺城区",
                "金东区",
                "兰溪市",
                "东阳市",
                "义乌市",
                "永康市",
                "浦江县",
                "武义县",
                "磐安县",
                "开发区",
              ].includes(a.dept_name.slice(0, 3))
                ? a.dept_name.slice(0, 3) + "综合行政执法办"
                : a.dept_name,
              value: a.parent_id,
              parent_id: a.parent_id,
              dept_id: a.dept_id,
            });
          });
          resolve(data0);
        });
      } else if (node.level == 1) {
        indexApi("/zfzfry001", { deptId: node.data.dept_id }).then((res) => {
          let data1 = [];
          res.data.map((a) => {
            data1.push({
              label: a.dept_name,
              value: a.dept_id,
              parent_id: a.parent_id,
              dept_id: a.dept_id,
            });
          });
          resolve(data1);
        });
      } else if (node.level == 2) {
        indexApi("/zfzfry001", { deptId: node.data.dept_id }).then((res) => {
          if (res.data.length > 0) {
            that.flag = true
            let data2 = [];
            res.data.map((a) => {
              data2.push({
                label: a.dept_name,
                value: a.dept_id,
                parent_id: a.parent_id,
                dept_id: a.dept_id,
              });
            });
            resolve(data2);
          } else {
            that.flag = false
            indexApi("/zhzfry002", { deptId: node.data.dept_id }).then((res) => {
              let data2 = [];
              res.data.map((a) => {
                const objs = {
                  label: a.nick_name,
                  value: a.employee_code,
                  dept_id: a.dept_id,
                  user_id: a.user_id,
                  user_name: a.user_name,
                  phone: a.phonenumber,
                  leaf: true,
                  hasChildren: false,
                };
                data2.push(objs);
                this.staffList.push(objs);
              });
              // this.staffList.concat(data2);
              console.log(data2);
              resolve(data2);
            });
          }
        });
      } else if (node.level == 3 && that.flag) {
        indexApi("/zhzfry002", { deptId: node.data.dept_id }).then((res) => {
          let data2 = [];
          res.data.map((a) => {
            const objs = {
              label: a.nick_name,
              value: a.employee_code,
              dept_id: a.dept_id,
              user_id: a.user_id,
              user_name: a.user_name,
              phone: a.phonenumber,
              leaf: true,
              hasChildren: false,
            };
            data2.push(objs);
            this.staffList.push(objs);
          });
          // this.staffList.concat(data2);
          console.log(data2);
          resolve(data2);
        });
      } else {
        resolve([]);
      }
    },
    lazyLoads2(node, resolve) {
      if (node.level === 0) {
        let list = [];
        if (localStorage.getItem("adminCity") == '金华市') {
          // list = [{
          //   label: "金华市", dept_id: 467
          // }];
          list = [{
            label: "金华市", dept_id: 0
          }];
        } else {
          indexApi("zfzfry002",{ area: localStorage.getItem("adminCity"),
            pageSize: 500 }).then(res => {
            let data = [];
            let result = res.data;
            result.forEach((a) => {
              let obj = {
                label: a.deptName,
                value: a.deptId,
                parent_id: a.parentId,
                dept_id: a.deptId,
              };
              data.push(obj);
            });
            list = [data.find(item => item.label == localStorage.getItem("adminCity"))]
            resolve(list);
          })
        }
        resolve(list);
      }
      if (node.level === 1) {
        indexApi("zfzfry002",{parentId:node.data.dept_id,
          pageSize: 500 }).then(res => {
          let data = [];
          let result = res.data.filter((item) => {
            return (
              item.deptName.indexOf("数字金华技术运营有限公司") == -1
            );
          });
          result.forEach((a) => {
            let obj = {
              label: a.deptName,
              value: a.deptId,
              parent_id: a.parentId,
              dept_id: a.deptId,
            };
            data.push(obj);
          });
          console.log(data);
          data = localStorage.getItem('adminCity') == '金华市'?data:data.filter((item) => {return (item.label.indexOf("政府") != -1 || item.label.indexOf("街道") != -1)})
          resolve(data);
        })
      }
      if (node.level === 2) {
        indexApi("zfzfry002",{parentId:node.data.dept_id,
          pageSize: 500 }).then(res => {
          let data = [];
          let result = res.data;
          result.forEach((a) => {
            let obj = {
              label: a.deptName,
              value: a.deptId,
              parent_id: a.parentId,
              dept_id: a.deptId,
            };
            data.push(obj);
          });
          resolve(data);
        })
      }
      if (node.level === 3) {
        indexApi("zfzfry002",{parentId:node.data.dept_id,
          pageSize: 500 }).then(res => {
          let data = [];
          let county = [
            "婺城区",
            "金东区",
            "兰溪市",
            "东阳市",
            "义乌市",
            "永康市",
            "浦江县",
            "武义县",
            "磐安县",
          ];
          let result = null;
          if (county.indexOf(node.data.label) != -1) {
            result = res.data.filter((item) => {
              return (
                item.deptName.indexOf("政府") != -1  && item.deptId != 634 ||
                item.deptName.indexOf("街道") != -1
              );
            });
          } else {
            result = res.data;
          }
          result.forEach((a) => {
            let obj = {
              label: a.deptName,
              value: a.deptId,
              parent_id: a.parentId,
              dept_id: a.deptId,
            };
            data.push(obj);
          });
          resolve(data);
        })
      }
      if (node.level > 3) {
        indexApi("zfzfry002",{parentId:node.data.dept_id,
          pageSize: 500 }).then(res => {
          if (res && res[0]) {
            let data = [];
            let result = res.data;
            result.forEach((a) => {
              let obj = {
                label: a.deptName,
                value: a.deptId,
                parent_id: a.parentId,
                dept_id: a.deptId,
                pageSize: 500
              };
              data.push(obj);
            });
            resolve(data);
          } else  {
            getUserList({ deptId: node.data.dept_id,
              pageSize: 500 }).then((res) => {
              if (res.rows && res.rows[0]) {
                let data = [];
                res.rows.map((a) => {
                  const objs = {
                    label: a.nickName,
                    value: a.employeeCode,
                    dept_id: a.deptId,
                    user_id: a.userId,
                    user_name: a.userName,
                    phone: a.phonenumber,
                    leaf: true,
                    hasChildren: false
                  };
                  data.push(objs);
                  this.staffList.push(objs);
                });
                resolve(data);
              } else {
                resolve([]);
              }
            });
          }
        })
      }
    },
    changeList(node) {
      console.log(this.staffList, "this.staffList");
      console.log(node, "选中");

      const arr = [];
      (node.filter(nitem => nitem != undefined)).forEach((item) => {
        this.staffList.forEach((a) => {
          if (item === a.value) {
            arr.push(a);
          }
        });
      });
      console.log(this.staffList, arr, "arr-选中的人员信息");
      this.selStaff = arr;
    },
    submitForm() {
      let that = this;
      console.log(this.form);
      if (!this.form.region) {
        that.$message({
          message: "请选择级别",
          type: "warning",
        });
        return;
      }
      if (this.form.desc == "") {
        that.$message({
          message: "指令内容不能为空",
          type: "warning",
        });
        return;
      }
      if (this.chooseDepart == "" && this.labelName == "2") {
        that.$message({
          message: "请至少选择一个队伍",
          type: "warning",
        });
        return;
      }
      // let employeeCodes = [];
      if (that.form.list.length == 0 && this.labelName == "1") {
        that.$message({
          message: "请至少选择一个人员",
          type: "warning",
        });
        return;
      }
      // that.form.list.map((item) => {
      //   employeeCodes.push(item[2]);
      // });
      // console.log(this.form, this.form.list, "this.form.list");
      // const p = {
      //   codeAndPhone: this.selStaff.value,
      //   name:
      // };
      console.log(this.selStaff, "this.selStaff");
      const parr = this.selStaff.map((item) => {
        return {
          codeAndPhone: item.value,
          name: item.label,
        };
      });
      let params = {
        source: "jinhuacsdn",
        list: parr,
        area: localStorage.getItem("adminCity") || "",
        level: that.form.region,
        type: 2,
        msg: that.form.desc,
        taskSource: that.form.from,
        dataType: that.form.dataType,
        detailsId: that.gjxx.id || null,
        url: "http://10.45.13.116/xzzfzx/#/", //正式
        // url: "http://10.45.13.116:8000/xzzfzx/#/",  //测试
        deptName: that.deptName
      };
      let params2 = {
        source: "jinhuacsdn",
        targetDeptName: that.chooseDepart,
        area: localStorage.getItem("adminCity") || "",
        level: that.form.region,
        type: 2,
        msg: that.form.desc,
        taskSource: that.form.from,
        dataType: that.form.dataType,
        detailsId: that.gjxx.id || null,
        url: "http://10.45.13.116/xzzfzx/#/", //正式
        // url: "http://10.45.13.116:8000/xzzfzx/#/",  //测试
        deptName: that.deptName
      };
      // let params = {
      //   source: "jinhuacsdn",
      //   employeeCodes: employeeCodes,
      //   msg: that.form.desc,
      // };
      if (this.labelName == "1") {
        xzzfjWorkNotification(params).then(function (response) {
          if (response.code == 200) {
            that.$message({
              message: "指令下达成功！",
              type: "success",
            });
            this.refreshData()
          }
        });
      } else {
        WorkNotifi(params2).then(response => {
          if (response.code == 200) {
            that.$message({
              message: "指令下达成功！",
              type: "success",
            });
            this.refreshData()
          }
        })
      }

    },
    refreshData() {
      const this_ = this;
      //更新数据
      if (this_.gjxx.yqmsg) {
        this_.getYq(this_.gjxx);
      } else {
        this_.getSOS(this_.gjxx);
      }

      //更新告警信息数据
      this.$bus.$emit('updateWarningList')

      //更新任务跟踪数据
      this.$bus.$emit('updateTaskList')
    },
    getYq(item) {
      let this_ = this;
      yqReport({ id: item.id, status: 0 }).then(res => {
        if (res.data.data == 1) {
          this_.$message({
            message: "舆情上报成功",
            type: "success",
          });
          this.close()
          this.$bus.$emit('updateYqList')
        }
      });
    },
    // sos接收告警
    getSOS(item) {
      let this_ = this;
      getWarning({ id: item.id, county: item.city }).then(res => {
        if (res.data.data == 1) {
          this_.$message({
            message: "接受告警成功",
            type: "success",
          });
          // this_.zfryPopClose();
        }
      });
    },
    close() {
      this.$emit('close')
    },
    getCheckedNodes(a, b) {
      console.log(a);
      console.log(b);
    },
  },
  watch: {
    visible(val) {
      if (val) {
        if (this.msgData) {
          const this_ = this;
          if (this.msgData.type == "openyjtz") {
            this_.form.dataType = 3;
            this_.gjxx = this.msgData.data;
            if (this.msgData.data.id && this.msgData.data.yqmsg) {
              this_.form.from = "舆情中心";
              this_.form.desc = this_.gjxx.yqmsg;
            } else {
              this_.form.from =
                item.unitname.includes(item.city)? this.msgData.data.unitname + " SOS报警":this.msgData.data.city + "" + this.msgData.data.unitname + " SOS报警";
            }
          }
        }
        this.init()
      }
    }
  }
}
</script>

<style scoped lang='less'>
[v-cloak] {
  display: none;
}

body {
  margin: 0;
  padding: 0;
}

#zhdddialog {
  width: 1300px;
  height: 800px;
  position: relative;
  background-image: url("@/assets/zhdd/bg_panel.png");
  background-size: 100% 100%;
}

.title {
  font-size: 36px;
  padding: 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.close {
  width: 46px;
  height: 78px;
  font-size: 60px;
  color: #fff;
  font-weight: 600;
  cursor: pointer;
}

.content {
  width: 100%;
}

/*elementui-tree*/
/deep/ .el-tree {
  background: unset;
}

/deep/ .el-tree-node__content:hover {
  background-color: #1c4d65;
}

/deep/ .el-tree-node__label {
  font-size: 26px;
}

/deep/ .el-tree-node.is-current > .el-tree-node__content {
  background-color: #1c4d65 !important;
}

/deep/ .el-tree-node:focus > .el-tree-node__content {
  background-color: #4a9de7 !important;
  color: #fff !important;
}

/* 下面按钮的样式 */
.btns-box {
  width: 100%;
  display: flex;
  justify-content: center;
}

.btns-box > div {
  display: inline-block;
  padding: 5px 50px;
  background: linear-gradient(0deg, #387af0, #003ca6);
  border: 1px solid;
  border-image: linear-gradient(-32deg, #359cf8, #afdcfb) 1 1;
  border-radius: 10px;
  margin: 0 20px;
}

/* el-form里面的样式 */
/deep/ .el-form {
  padding: 0 50px;
}

/deep/ .el-form-item__label {
  font-size: 28px;
  color: #fff;
  text-align: left;
}

/deep/ .el-select {
  width: 600px;
}

/deep/ .el-input {
  font-size: 26px;
}

/deep/ .el-input__inner {
  height: 55px;
  line-height: 55px;
  background-color: #052347;
  border-color: #314662 !important;
  border-radius: 5px;
  color: #fff;
}

/deep/ .el-input--suffix .el-input__inner {
  padding-right: 50px;
}

/deep/ .el-select .el-input .el-select__caret {
  color: #fff;
  font-size: 26px;
  margin-right: 10px;
}

/deep/ .el-select-dropdown {
  position: absolute !important;
  left: 0px !important;
  top: 54px !important;
  background-color: #052347;
  border-color: #314662 !important;
}

/deep/ .el-select-dropdown__item.hover, .el-select-dropdown__item:hover {
  border-color: #409eff;
  background-color: #052347;
  color: #409eff !important;
}

/deep/ .el-select-dropdown__item.selected {
  color: #409eff;
  font-weight: normal;
}

/deep/ .el-select-dropdown__item {
  font-size: 26px;
  color: #fff;
  height: 50px;
  line-height: 50px;
}

.popper__arrow {
  display: none !important;
}

/deep/ .el-textarea__inner {
  border-color: #314662;
  background-color: #052347;
  font-size: 26px;
  color: #fff;
}

/deep/ .el-textarea__inner:hover {
  border-color: #052347 !important;
}

/deep/ .el-textarea__inner:focus {
  outline: none !important;
  border-color: #052347 !important;
}

/deep/ .el-radio__label {
  color: #ffffff;
}

/deep/ .el-scrollbar__wrap {
  overflow-y: scroll;
}

/deep/ .el-scrollbar__wrap::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 3px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
}

/deep/ .el-scrollbar__wrap::-webkit-scrollbar-thumb {
  border-radius: 6px;
  background: #314662;
  height: 8px;
}

/deep/ .el-tag .el-icon-close {
  font-size: 26px;
  height: 25px;
  width: 26px;
  line-height: 26px;
}

/deep/ .el-input__icon {
  height: 25px;
  top: 33%;
  right: 0px;
  position: absolute;
}

/deep/ .el-tag--small {
  height: 35px;
  line-height: 35px;
}

/deep/ .el-tag {
  font-size: 25px;
}

.team_input {
  height: 55px !important;
  line-height: 55px !important;
}

.team_input {
  /deep/ .el-input__inner {
    height: 55px !important;
    line-height: 55px !important;
  }
}

/deep/ .el-button {
  font-size: 35px;
  border: 1px solid #2450b6;
  color: #c6bdbd;
  background: transparent;
  line-height: 0;
  padding: 4px !important;
}

/deep/ .el-avatar,
/deep/ .el-radio,
/deep/ .el-radio--medium.is-bordered .el-radio__label,
/deep/ .el-radio__label {
  font-size: 23px !important;
}

/deep/ .el-checkbox__inner {
  width: 20px;
  height: 20px;
}

/deep/ .el-checkbox__inner::after {
  height: 13px;
  left: 7px;
}

/deep/ .el-textarea__inner {
  font-family: "YouSheBiaoTiHei";
}
</style>