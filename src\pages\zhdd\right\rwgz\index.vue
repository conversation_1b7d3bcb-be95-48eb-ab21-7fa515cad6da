<template>
  <div style='margin-bottom: 40px;'>
    <CommonTitle text='任务跟踪'>
      <div class='yearChange'>
        <el-date-picker
          v-model="datas2"
          type="daterange"
          value-format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="initApi(tabActive)"
          :append-to-body='false'
        >
        </el-date-picker>
      </div>
    </CommonTitle>
    <div class="zfrw_box">
      <div class="zfrw_right s-flex-1">
        <div class="tabs">
          <div
            class="tab"
            v-for="(item,i) in tabs"
            :class="{tabConActive:tabActive==i}"
            @click="initApi(i)"
          >
            {{item.name + "(" + item.value + ")"}}
          </div>
        </div>
        <div class="table">
          <div class="th">
            <div class="th_td" style="flex: 0.11; text-align: center">
              序号
            </div>
            <div
              class="th_td"
              style="flex: 0.21; text-align: center; white-space: nowrap"
            >
              任务来源
            </div>
            <div class="th_td" style="flex: 0.2; text-align: center">
              任务名称
            </div>
            <div class="th_td" style="flex: 0.18; text-align: center">
              接收对象
            </div>
            <div class="th_td" style="flex: 0.2; text-align: center">
              任务下达时间
            </div>
            <div class="th_td" style="flex: 0.16; text-align: center">
              任务详情
            </div>
          </div>
          <div class="tr" v-show="tableData.length==0">暂无数据</div>
          <div
            class="tbody"
            id="box2"
            @mouseenter="mouseenterEvent2"
            @mouseleave="mouseleaveEvent2"
          >
            <div
              class="tr"
              v-for="(item,index) in tableData"
              :key="index"
              :class="item.isLight==1?'tr_light':'tr_noLight'"
            >
              <div
                class="tr_td"
                style="flex: 0.11; text-align: center"
                :style="{color:item.taskSource=='舆情中心'?'yellow':item.detailsId?'red':''}"
                @click="linghtSos(item)"
              >
                {{index + 1}}
              </div>
              <div
                class="tr_td"
                style="flex: 0.21; text-align: center"
                :title="item.taskSource"
              >
                {{getStatusText(item.taskSource)}}
              </div>
              <div
                class="tr_td"
                style="flex: 0.2; text-align: center"
                :title="item.msg"
              >
                {{getStatusText(item.msg)}}
              </div>
              <div
                class="tr_td"
                style="flex: 0.18; text-align: center"
                :title="item.receiveObject"
              >
                {{getStatusText(item.receiveObject)}}
              </div>
              <div
                class="tr_td"
                style="flex: 0.2; text-align: center"
                :title="item.startTime"
              >
                {{item.startTime}}
              </div>
              <div
                class="tr_td"
                style="flex: 0.16; text-align: center;cursor: pointer"
                @click="showTaskDetail(item)"
              >
                查看详情
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <rwgzDialog :params='params' :visible='visible' @close='visible = false'></rwgzDialog>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import moment from 'moment'
import { indexApi } from '@/api/indexApi'
import rwgzDialog from './rwgzDialog'
export default {
  name: 'index',
  components: {
    CommonTitle,
    rwgzDialog
  },
  data() {
    return {
      datas2: [
        moment().add("month", -2).format("YYYY-MM-DD"),
        moment(new Date()).format("YYYY-MM-DD"),
      ],
      tabActive: 0,
      tabs: [
        {
          name: "全部",
          value: 0,
          status: 0,
        },
        {
          name: "待反馈",
          value: 0,
          status: 1,
        },
        {
          name: "待重做",
          value: 0,
          status: 2,
        },
        {
          name: "待结案",
          value: 0,
          status: 3,
        },
        {
          name: "已结案",
          value: 0,
          status: 4,
        },
      ],
      tableData: [],
      dom2: null,
      time2: null,

      params: {},
      visible: false
    }
  },
  computed: {},
  mounted() {
    this.$bus.$on('cityChange', (city) => {
      this.city = city;
      this.initApi(this.tabActive);
    })
    this.$bus.$on('yearChange', (year) => {
      this.year = year;
      this.initApi(this.tabActive);
    })
    this.$bus.$on('updateTaskList', (e) => {
      this.initApi(this.tabActive);
    })
    this.initApi(this.tabActive);
    // 表格滚动
    this.dom2 = document.getElementById("box2");
    this.mouseleaveEvent2();
  },
  methods: {
    initApi(i) {
      this.tabActive = i;
      this.getTaskStatistics()
      this.queryTableData();
    },
    //任务统计
    getTaskStatistics() {
      indexApi("/xzzfj_zhtx_rwzztj", {
        queryStartTime: (this.datas2 && this.datas2[0]) || "",
        queryEndTime: (this.datas2 && this.datas2[1]) || "",
        area:
          localStorage.getItem("city") == "金华市"
            ? ""
            : localStorage.getItem("city"),
      }).then((res) => {
        this.tabs[1].value = res.data[0].waitDispose;
        this.tabs[2].value = res.data[0].reDisposal;
        this.tabs[3].value = res.data[0].waitDisposal;
        this.tabs[4].value = res.data[0].endDisposal;
        this.tabs[0].value = 0;
        this.tabs.slice(1, this.tabs.length).forEach((item) => {
          this.tabs[0].value += item.value;
        });
      });
    },
    //任务跟踪列表
    queryTableData(i) {
      indexApi("/xzzfj_zhdd_rwzzlb", {
        queryStartTime: (this.datas2 && this.datas2[0]) || "",
        queryEndTime: (this.datas2 && this.datas2[1]) || "",
        area:
          localStorage.getItem("city") == "金华市"
            ? ""
            : localStorage.getItem("city"),
        status: this.tabs[this.tabActive].status,
      }).then((res) => {
        this.tableData = res.data;
        this.tableData.forEach((item, index) => {
          item["isLight"] = 0;
        });
      });
    },
    getStatusText(item) {
      return item && item.length > 4
        ? item.slice(0, 4) + "..."
        : item == null
          ? ""
          : item;
    },
    mouseenterEvent2() {
      clearInterval(this.time2);
    },
    mouseleaveEvent2() {
      let dom2 = document.getElementById("box2");
      this.time2 = setInterval(() => {
        dom2.scrollBy({
          top: 90,
          behavior: "smooth",
        });
        if (
          dom2.scrollTop >=
          dom2.scrollHeight - dom2.offsetHeight
        ) {
          dom2.scrollTop = 0;
        }
      }, 1500);
    },
    //打开任务详情弹窗
    showTaskDetail(obj) {
      this.params = {
        id: obj.id,
        idType: obj.idType
      };
      this.visible = true;
    },
    linghtSos() {

    }
  },
  watch: {}
}
</script>

<style scoped lang='less'>
.zfrw_box {
  width: 100%;
  display: flex;
}
/deep/ .yearChange {
  position: relative;
  bottom: 35px;
  left: 20px;
  .el-input__inner {
    height: 48px !important;
    background-color: #132c4e !important;
    border: 2px solid #afdcfb !important;
    color: #fff !important;
    border-radius: 15px !important;
  }
  .el-picker-panel {
    left: -170px !important;
  }
}

.tabs {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.tab {
  font-size: 32px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  //font-style: italic;
  color: rgba(171, 206, 239, 0.7);
  cursor: pointer;
}

.tabConActive {
  background: url("@/assets/zhdd/tab-active.png") no-repeat;
  background-size: 110% 100%;
  font-size: 32px;
  font-family: Source Han Sans CN;
  font-weight: bold;
  //font-style: italic;
  color: #ffffff;
}
/* 表格 */
.table {
  width: 100%;
  height: 650px;
  padding: 10px;
  box-sizing: border-box;
}
.table .th {
  width: 100%;
  height: 85px;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  font-weight: bold;
  font-size: 31px;
  line-height: 60px;
  color: #CDE7FF;
  background: #082B62;
}

.table .th_td {
  letter-spacing: 0px;
  text-align: left;
}

.table .tbody {
  width: 100%;
  height: calc(100% - 85px);
  /* overflow-y: auto; */
  overflow: hidden;
}

.table .tbody:hover {
  overflow-y: auto;
}

.table .tbody::-webkit-scrollbar {
  width: 4px;
  /*滚动条整体样式*/
  height: 4px;
  /*高宽分别对应横竖滚动条的尺寸*/
}

.table .tbody::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: #20aeff;
  height: 8px;
}

.table .tr {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  height: 85px;
  line-height: 70px;
  font-size: 28px;
  color: #CDE7FF;
  border-top: 1px solid #959aa1;
  border-image: linear-gradient(to right, #e9f5ff3b, #f5ffffd4, #e9f5ff3b)
  1;
  box-sizing: border-box;
}
.tr_light {
  background-color: #0074da75 !important;
  border: 1px solid yellow !important;
  box-sizing: border-box;
}

.table .tr:nth-child(2n) {
  background: #072249;
}

.table .tr:nth-child(2n + 1) {
  background: #081B3C;
}

.table .tr:hover {
  background-color: #0074da75;
}

.table .tr_td {
  letter-spacing: 0px;
  text-align: left;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.table .tr_td > img {
  position: relative;
  top: 25px;
}

</style>