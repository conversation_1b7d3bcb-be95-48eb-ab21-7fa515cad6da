<template>
  <!-- 地图选择区域弹窗 -->
  <el-dialog :close-on-click-modal="false" v-bind="$attrs" append-to-body :before-close="mapBeforeClose" width="70%" @open="handleOpen" v-on="$listeners">
    <div class="map-box">
      <div id="map" ref="map" />
      <el-input v-model="mapSearch" placeholder="请输入关键字进行搜索" type="text" clearable class="map-search-box" @keyup.enter.native="handleMapSearch">
        <el-button slot="append" type="primary" @click="handleMapSearch">选择</el-button>
      </el-input>
      <div class="btns">
        <el-button size="mini" type="primary" @click="handleDraw">{{ status | btnName }}</el-button>
        <el-button size="mini" type="primary" @click="handleClear">清空全部</el-button>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="mapBeforeClose">取 消</el-button>
      <el-button type="primary" @click="handleMapSubmit">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { loadMap } from '@/utils/amapLoad'

export default {
  name: 'MapDraw',
  filters: {
    btnName(status) {
      const names = { 0: '新增区域', 1: '确认已绘制区域', 2: '开始编辑', 3: '确认编辑' }
      return names[status]
    }
  },
  inheritAttrs: false,
  props: {
    area: {
      type: String
    }
  },
  data() {
    return {
      AMap: null,
      $map: null,
      mapSearch: '',
      placeSearch: null,
      mapPolygon: [],
      status: 0,
      mouseTool: null,
      polyEditor: null,
      contextMenu: null,
      rightClickPolygon: null
    }
  },
  methods: {
    getAMap() {
      return new Promise((resolve, reject) => {
        if (this.AMap) {
          resolve(this.AMap)
        } else {
          loadMap(['AMap.PlaceSearch', 'AMap.Polygon', 'AMap.PolygonEditor', 'AMap.MouseTool', 'AMap.ContextMenu']).then(
            AMap => resolve(AMap),
            () => reject()
          )
        }
      })
    },
    handleOpen() {
      this.getAMap().then(AMap => {
        this.$nextTick(() => {
          this.$map = new AMap.Map(this.$refs.map, {
            center: [119.635857, 29.110764],
            resizeEnable: true,
            zoom: 16
          })
          this.cerateContextMenu(AMap)
          this.createPolygon(this.area, AMap)
          this.initMapSearch(AMap)
        })
      })
    },
    createPolygon(area, AMap) {
      if (!area) return
      this.mapPolygon = area.split('/').map((item, idx) => {
        const path = item.split(';').map(lnglatStr => {
          const lnglat = lnglatStr.split(',')
          return [parseFloat(lnglat[0]), parseFloat(lnglat[1])]
        })
        let ponlygon = new AMap.Polygon({
          map: this.$map,
          path: path,
          strokeColor: '#FF33FF',
          strokeWeight: 6,
          strokeOpacity: 0.2,
          fillOpacity: 0.4,
          fillColor: '#1791fc',
          zIndex: 50
        })
        ponlygon.mapKey = idx
        ponlygon.on('rightclick', event => {
          this.rightClickPolygon = event.target
          this.contextMenu.open(this.$map, event.lnglat)
        })
        return ponlygon
      })
      this.$map.setFitView()
      this.status = 2
    },
    initMapSearch(AMap) {
      this.placeSearch = new AMap.PlaceSearch({
        city: '金华',
        map: this.$map,
        autoFitView: true
      })
    },
    cerateContextMenu(AMap) {
      this.contextMenu = new AMap.ContextMenu()
      // 编辑
      this.contextMenu.addItem('编辑', () => {
        if (!this.polyEditor) {
          this.polyEditor = new AMap.PolygonEditor(this.$map, this.rightClickPolygon)
        } else {
          // 如果本身存在编辑对象先关闭再赋值
          this.polyEditor.close()
          this.polyEditor = new AMap.PolygonEditor(this.$map, this.rightClickPolygon)
        }
        this.status = 3
        this.polyEditor.open()
        this.contextMenu.close()
      })
      // 删除
      this.contextMenu.addItem('删除', () => {
        this.$confirm('是否确认删除？', '提示', { type: 'warning' }).then(() => {
          if (this.polyEditor) {
            this.status = 0
            this.polyEditor.close()
          }
          this.$map.remove(this.rightClickPolygon)
          this.mapPolygon = this.mapPolygon.filter(item => item.mapKey != this.rightClickPolygon.mapKey)
          this.rightClickPolygon = null
        }).catch(() => {})
        this.contextMenu.close()
      })
    },
    mapBeforeClose() {
      this.status = 0
      this.mapPolygon = []
      this.$map = null
      this.polyEditor = null
      this.contextMenu = null
      this.$emit('update:visible', false)
    },
    handleMapSearch() {
      if (this.mapSearch.trim()) {
        this.placeSearch.search(this.mapSearch)
      } else {
        this.$message.warning('请输入搜索内容')
      }
    },
    handleMapSubmit() {
      if (this.mapPolygon.length) {
        let lnglatStr = []
        this.mapPolygon.forEach(item => {
          const lnglat = item.getPath()
          const lnglatAry = lnglat.map(item => {
            return `${item.lng},${item.lat}`
          })
          if (lnglat.length) {
            lnglatStr.push(lnglatAry.join(';'))
          }
        })
        this.$emit('confirm', lnglatStr.join('/'))
      } else {
        this.$emit('confirm', '')
      }
      this.mapBeforeClose()
    },
    handleDraw() {
      if (!this.mouseTool || !this.polyEditor) {
        // 创建鼠标绘制对象并添加绘制完成事件
        this.mouseTool = new AMap.MouseTool(this.$map)
        this.mouseTool.on('draw', e => {
          console.log(e.obj)
          e.obj.on('rightclick', event => {
            this.rightClickPolygon = event.target
            this.contextMenu.open(this.$map, event.lnglat)
          })
          // 添加唯一标识，
          e.obj.mapKey = new Date().getTime()
          this.mapPolygon.push(e.obj)
          this.mouseTool.close()
          this.status = 2
        })
      }
      if (this.status === 0) {
        // 开始绘制
        this.mouseTool.polygon({
          strokeColor: '#FF33FF',
          strokeWeight: 6,
          strokeOpacity: 0.2,
          fillOpacity: 0.4,
          fillColor: '#1791fc',
          zIndex: 50,
          bubble: true
        })
        // 变更为待确认绘制状态
        this.status = 1
      } else if (this.status === 1) {
        // 确认绘制,关闭绘制状态
        this.status = 0
        this.mouseTool.close()
      } else if (this.status === 2) {
        if (this.mapPolygon[0]) {
          this.polyEditor = new AMap.PolygonEditor(this.$map, this.mapPolygon[0])
          this.polyEditor.open()
          this.status = 3
        }
      } else if (this.status == 3) {
        this.status == 2
        this.polyEditor.close()
      } else {
        this.status = 0
        this.polyEditor.close()
      }
    },
    handleClear() {
      if (this.polyEditor) {
        this.polyEditor.close()
        this.polyEditor = null
      }
      if (this.$map) {
        this.$map.clearMap()
        this.status = 0
      }
      this.mapPolygon = []
    }
  }
}
</script>

<style scoped lang="scss">
.map-box {
  position: relative;
  height: 60vh;
  overflow: hidden;
  #map {
    width: 100%;
    height: 620px;
  }
}
.map-search-box {
  width: 300px;
  position: absolute;
  right: 10px;
  top: 10px;
  z-index: 1;
  box-shadow: 0 0 5px #bbb;
}
.btns {
  padding: 10px;
  background: #fff;
  position: absolute;
  right: 10px;
  bottom: 10px;
  box-shadow: 0 0 5px #bbb;
  border-radius: 5px;
}
</style>
