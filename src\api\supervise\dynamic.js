import {request} from '@/utils/request'

// 查询队员工作动态列表
export function listDynamic(query) {
  return request({
    url: '/business/case/dynamic/list',
    method: 'get',
    params: query
  })
}

// 查询队员工作动态详细
export function getDynamic(id) {
  return request({
    url: '/business/case/dynamic/' + id,
    method: 'get'
  })
}

// 新增队员工作动态
export function addDynamic(data) {
  return request({
    url: '/business/case/dynamic/add',
    method: 'post',
    data: data
  })
}

// 修改队员工作动态
export function updateDynamic(data) {
  return request({
    url: '/business/case/dynamic/edit',
    method: 'post',
    data: data
  })
}

// 删除队员工作动态
export function delDynamic(id) {
  return request({
    url: '/business/case/dynamic/remove/' + id,
    method: 'post'
  })
}

// 导出队员工作动态
export function exportDynamic(query) {
  return request({
    url: '/business/case/dynamic/export',
    method: 'get',
    params: query
  })
}
