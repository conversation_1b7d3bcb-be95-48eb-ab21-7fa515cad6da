<template>
  <div>
    <el-dialog class="m-dialog" :close-on-click-modal="false" append-to-body v-bind="$attrs" :title="title" v-on="$listeners" @close="cancel">
      <el-scrollbar style="height: 100%;">
        <el-row :gutter="15" style="margin-right: 10px;">
          <el-form ref="form" v-loading="formLoading" :disabled="formDisabled" :model="form" :rules="rules" label-width="100px">
            <el-col :span="12">
              <el-form-item label="上报人" prop="userName">
                <el-input v-model="form.userName" disabled placeholder="请输入上报人" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="所属部门" prop="deptName">
                <el-input v-model="form.deptName" disabled placeholder="请输入所属部门" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="上报时间" prop="happenTime">
                <el-date-picker
                  v-model="form.happenTime" clearable
                  size="small"
                  type="datetime"
                  value-format="yyyy-MM-dd hh:mm:ss"
                  placeholder="选择上报时间"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="上报地址" prop="address">
                <div>
                  <el-input v-model="form.address" placeholder="请选择上报地址">
                    <svg-icon slot="suffix" icon-class="map" class="svg-icon" @click="openMap = true" />
                  </el-input>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="工作内容" prop="content">
                <el-input v-model="form.content" type="textarea" rows="10" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="动态图片" prop="files">
                <MFileUpload ref="mainFile" :limit="4" :ex-data="exData" :file-list="mainFileList" not-upload-msg="请上传动态图片" />
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
      </el-scrollbar>
      <div slot="footer" class="dialog-footer">
        <!-- <el-button type="primary" :loading="formLoading" @click="submitForm(9)">提 交</el-button> -->
        <el-button :loading="formLoading" @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 地图选择 -->
    <!-- <tdtMap ref="bindmap" :visible.sync="openMap" append-to-body :map-search="true" :dw="form" @onlnglat="onlnglat" /> -->
  </div>
</template>
<script>
import {  getDynamic } from '@/api/supervise/dynamic'
import MFileUpload from '@/components/MFileUpload/index.vue'
import { getFiles } from '@/api/supervise/swit'
// import tdtMap from '@/components/tdtMap/tdtMap'
export default {
  components: {
    MFileUpload
    // tdtMap
  },
  inheritAttrs: false,
  props: {
    title: String,
    detailId: Number,
    formDisabled: Boolean
  },
  data() {
    return {
      openMap: false,
      formLoading: false,
      exData: {
        tableName: 'case_work_dynamic',
        status: '1'
      },
      mainFileList: [],

      // 遮罩层
      loading: true,

      // 表单参数
      form: {},
      // 表单校验
      rules: {
        userName: [
          { required: true, message: '上报人不能为空', trigger: 'change' }
        ],
        deptName: [
          { required: true, message: '所属部门不能为空', trigger: 'change' }
        ],
        happenTime: [
          { required: true, message: '上报时间不能为空', trigger: 'change' }
        ],
        address: [
          { required: true, message: '上报地址不能为空', trigger: 'change' }
        ],
        content: [
          { required: true, message: '工作内容不能为空', trigger: 'change' }
        ]
      }
    }
  },
  computed: {

  },
  watch: {
    '$attrs.visible'(nVal) {
      if (nVal && this.detailId) {
        this.fetchData()
      }
    }
  },
  created() {

  },
  mounted() {},
  methods: {
    // onOpen() {
    //   const { name: userName, uid: userId } =  this.$store.getters
    //   this.formData = { userName, userId, inspectionTime: this.parseTime(new Date()) }
    // },
    // 取消按钮
    cancel() {
      // this.open = false
      // this.mainFileList = []
      // this.reset()
      this.$emit('update:visible', false)

    },
    // 表单重置
    reset() {
      this.form = {
        // id: null,
        content: null,
        userId: this.$store.getters.uid,
        userName: this.$store.getters.nickName,
        type: 1,
        happenTime: null,
        address: null,
        longitude: null,
        latitude: null,
        status: '0',
        // delFlag: null,
        deptId: this.$store.getters.deptId,
        deptName: this.$store.getters.deptName
        // createBy: null,
        // createTime: null,
        // updateTime: null,
        // remark: null
      }
      this.resetForm('form')
    },
    /** 打开弹窗操作 */
    fetchData() {
      this.reset()
      this.formLoading = true
      Promise.all([
        getDynamic(this.detailId),
        getFiles({ businessId: this.detailId, tableName: 'case_work_dynamic' })
      ]).then(response => {
        const [formData, fileData] = response
        this.form = formData.data
        this.formLoading = false

        // 获取文件
        this.mainFileList = fileData.rows.map(item => {
          return { name: item.displayName, url: `/zqzfj${item.filePath}`, ...item }
        })

      }).catch(() => this.formLoading = false)
    }

  }
}

</script>
