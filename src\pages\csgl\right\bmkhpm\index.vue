<template>
  <div>
    <CommonTitle text="部门考核排名">
      <TabSwitch :tabList="list" :activeIndex="index" @tab-change="handleTabChange" />
    </CommonTitle>
    <!-- <div class="yearChange">
      <el-date-picker
        v-model="datas"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="queryData"
        :append-to-body='false'
      ></el-date-picker>
    </div> -->
    <div class="chartbmkh" id="chartbmkh"></div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import TabSwitch from '@/components/TabSwitch'
import { getDeptAnalysis } from '@/api/csgl/index.js'
// import moment from 'moment'
export default {
  name: 'index',
  components: {
    CommonTitle,
    TabSwitch,
  },
  data() {
    return {
      index: 0,
      list: [
        { name: '本周', value: '2' },
        { name: '本月', value: '3' },
        { name: '本年', value: '4' },
      ],
      // datas: [new Date().getFullYear() + '-01-01', moment(new Date()).format('YYYY-MM-DD')],
      chartsData: [],
    }
  },
  computed: {},
  mounted() {
    this.getData()
  },
  methods: {
    handleTabChange(i) {
      this.index = i
      this.getData()
    },
    queryData() {},
    getData() {
      getDeptAnalysis({ type: this.list[this.index].value }).then((res) => {
        this.chartsData = res.data.map((item) => {
          return {
            name: item.key,
            value: item.value,
          }
        })
        this.initCharts()
      })
    },
    initCharts() {
      let myChart = this.$echarts.init(document.getElementById('chartbmkh'))
      let option = {
        tooltip: {
          trigger: 'axis',
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          formatter: function (params) {
            return (
              params.find((item) => item.seriesName != '背景').axisValue +
              ': ' +
              params.find((item) => item.seriesName != '背景').value +
              '分'
            )
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '32',
          },
        },
        grid: {
          top: '3%',
          left: '5%',
          right: '5%',
          bottom: '6%',
          containLabel: true,
        },
        xAxis: {
          type: 'value',
          show: false,
        },
        yAxis: {
          name: '',
          type: 'category',
          triggerEvent: false,
          inverse: true,
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
            length: 20,
          },
          axisLabel: {
            show: true,
            inside: true, // 将标签放置在坐标轴内部
            verticalAlign: 'bottom', // 垂直对齐方式
            padding: [0, 0, 30, 0], // 上右下左的内边距，调整标签位置
            textStyle: {
              color: '#FFFFFF',
              fontSize: 32,
            },
          },
          data: this.chartsData.map((item) => item.name),
        },
        series: [
          {
            type: 'bar',
            name: '',
            showBackground: true,
            backgroundStyle: {
              color: 'transparent',
            },
            itemStyle: {
              normal: {
                barBorderRadius: 30,
                color: new this.$echarts.graphic.LinearGradient(0, 0, 1, 1, [
                  {
                    offset: 0,
                    color: 'rgba(63, 217, 255, 0)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(63, 217, 255, 1)',
                  },
                ]),
              },
            },
            label: {
              show: true,
              position: [850, -46],
              color: '#fff',
              formatter: function (params) {
                return params.value + '分'
              },
              fontSize: 32,
              textStyle: {
                color: '#78D2FF',
                fontWeight: 'normal',
                fontFamily: 'Source Han Sans CN',
              },
            },
            barWidth: 20,
            color: '#539FF7',
            data: this.chartsData.map((item) => item.value),
          },
          {
            name: '',
            type: 'bar',
            barWidth: 20,
            barGap: '-100%',
            data: this.chartsData.map((item) => 100),
            itemStyle: {
              normal: {
                barBorderRadius: 30,
                color: 'rgba(255, 255, 255, 0.20)',
              },
            },
            z: 0,
          },
        ],
      }
      myChart.setOption(option)
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
/deep/ .yearChange {
  margin: 20px 40px;
  .el-input__inner {
    height: 48px !important;
    background-color: #132c4e !important;
    border: 2px solid #afdcfb !important;
    color: #fff !important;
    border-radius: 15px !important;
  }
}
.chartbmkh {
  width: 100%;
  height: 1060px;
}
</style>