<template>
  <div class="right-Map">
    <gj></gj>
    <zj></zj>
    <yx></yx>
    <qzmyd></qzmyd>
  </div>
</template>

<script>
import gj from './gj'
import zj from './zj'
import yx from './yx'
import qzmyd from './qzmyd'
export default {
  name: 'index',
  components: { gj, zj, yx,qzmyd },
  data() {
    return {}
  },
  computed: {},
  mounted() {},
  methods: {},
  watch: {},
}
</script>

<style scoped>
</style>