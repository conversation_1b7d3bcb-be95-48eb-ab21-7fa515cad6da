<template>
  <div>
    <CommonTitle text='犬类占比'>
      <TabSwitch
        :tabList="list"
        :activeIndex="index"
        @tab-change="handleTabChange"
      />
    </CommonTitle>
    <div class='wrap-container' ref="chart"></div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import TabSwitch from '@/components/TabSwitch'
import * as echarts from 'echarts'

export default {
  name: 'index',
  components: {
    CommonTitle,
    TabSwitch
  },
  data() {
    return {
      chart: null,
      index: 0,
      list: [{name:"本月",value:"1"},{name:"本季",value:"2"},{name:"本年",value:"3"}]
    }
  },
  computed: {},
  mounted() {
    setTimeout(() => {
      this.initChart()
    }, 300)
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chart)

      const option = {
        backgroundColor: 'transparent',
        graphic: {
          type: 'image',
          style: {
            image: require('@/assets/dog/chartBg.png'),
            width: 360,
            height: 360
          },
          left: '7.5%',  // 调整背景图位置，与环形图对齐
          top: 'center'
        },
        title: [{
          text: '5548',
          left: '18.5%',
          top: '48%',
          textStyle: {
            color: '#E3F4FF',
            fontSize: 52,
            fontWeight: 'normal',
            fontFamily: 'DIN',
            lineHeight: 72
          },
          z: 10  // 确保文字在背景图之上
        }, {
          text: '犬只总数',
          left: '19%',
          top: '38%',
          textStyle: {
            color: '#E3F4FF',
            fontSize: 28,
            fontWeight: 'normal',
            lineHeight: 28
          },
          z: 10  // 确保文字在背景图之上
        }],
        legend: {
          orient: 'vertical',
          left: '45%',
          y: 'center',
          itemWidth: 16,
          itemHeight: 16,
          itemGap: 28,
          icon: 'circle',
          formatter: name => {
            const value = 779;
            return `{name|${name}}{value|${value}}`
          },
          textStyle: {
            rich: {
              name: {
                fontSize: 26,
                color: '#fff',
                padding: [0, 30, 0, 0],
                width: 140,
              },
              value: {
                fontSize: 26,
                color: '#fff',
                padding: [0, 0, 0, 0]
              }
            }
          }
        },
        series: [{
          type: 'pie',
          radius: ['55%', '65%'],
          center: ['25%', '50%'],
          startAngle: 90,
          itemStyle: {
            borderRadius: 0,
            borderColor: 'rgba(2,47,115,0.5)',
            borderWidth: 2,
          },
          data: [
            {
              value: 779,
              name: '泰迪',
            },
            {
              value: 779,
              name: '阿拉斯加',
            },
            {
              value: 779,
              name: '萨摩耶',
            },
            {
              value: 779,
              name: '柯基',
            },
            {
              value: 779,
              name: '贵宾',
            },
            {
              value: 779,
              name: '中华田园犬',
            }
          ],
          label: {
            show: false
          },
          labelLine: {
            show: false
          },
          emphasis: {
            scale: false
          }
        }]
      }

      this.chart.setOption(option)
    },

    handleTabChange(i) {
      this.index = i
      // 这里可以根据tab切换更新图表数据
    }
  },
  watch: {},
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
  }
}
</script>

<style scoped lang='less'>
  .wrap-container {
    width: 100%;
    height: 460px;
    margin-bottom: 40px;
    position: relative;
  }
</style>