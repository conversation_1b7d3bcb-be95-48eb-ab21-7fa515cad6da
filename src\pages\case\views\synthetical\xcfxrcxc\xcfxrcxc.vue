<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      :inline="true"
    >
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px;"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="daterange"
          unlink-panels
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item label="网格小组">
        <el-select v-model="queryParams.deptName" placeholder="请选择网格小组" clearable size="small">
          <el-option
            v-for="(deptItem, deptIndex) in deptOptions"
            :key="deptIndex"
            :label="deptItem"
            :value="deptItem"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="关键词检索">
        <el-input v-model="queryParams.searchValue" size="small" clearable placeholder="请输入内容" />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col v-if="queryParams.data != 1" :span="1.5">
        <el-button
          v-hasPermi="['system:role:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="listData"
      :cell-style="cellStyle"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="巡查人" prop="userName" align="center" :show-overflow-tooltip="true" width="100" />
      <el-table-column label="网格小组" prop="deptName" align="center" :show-overflow-tooltip="true" />
      <el-table-column label="巡查时间" align="center" prop="happenDate">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.happenDate) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="店铺名称" prop="businessName" :show-overflow-tooltip="true" />
      <el-table-column label="地址" prop="address" :show-overflow-tooltip="true" />
      <el-table-column label="内容" prop="inspectionContent" :show-overflow-tooltip="true" width="200" />
      <el-table-column label="状态" align="center" prop="statusName" :show-overflow-tooltip="true" width="150" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
        <template slot-scope="scope">
          <el-button v-if="scope.row.status == 9 || queryParams.data==1" size="mini" type="text" icon="el-icon-edit" style="color: #e6a23c;" @click="handleUpdate(scope.row)">详情</el-button>
          <el-button v-else v-hasPermi="['system:role:edit']" size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button v-if="scope.row.status == 9" size="mini" type="text" icon="el-icon-printer" style="color: #9e9e9e;" @click="handlePrint(scope.row.inspectionId)">打印</el-button>
          <el-button v-if="queryParams.data != 1" v-hasPermi="['system:role:remove']" size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改角色配置对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="1000px">
      <fromlist v-if="open" ref="fromlist" :detail-id="detailId" :form-disabled="formDisabled" :user-options="userOptions" :qd="qd" @onPrimary="primary" @oncancel="cancel" />
    </el-dialog>
  </div>
</template>

<script>// editInspection
import {inspectionList, removeInspection, editInspection} from '@/api/case/synthetical/patrol'
import {userList} from '@/api/supervise/swit'
// import Userselect from '@/components/userselect/index.vue'
import { userList as deptList} from '@/api/system/dict/type'

import fromlist from '@/pages/case/views/synthetical/xcfxrcxc/components/fromlist'
export default {
  name: 'Xcfxrcxc',
  filters: {
  },
  components: {
    fromlist
    // Userselect
  },
  data() {
    return {
      deptVisible: false,
      $map: null,
      value: [],
      detailId: '',
      userOptions: [],
      formData: [{id: 0, value: '我的'}, {id: 1, value: '全部'}],
      caseData: [ {id: 1, value: '待处理', statusColor: '#409EFF'}, {id: 2, value: '处理中', statusColor: '#E6A23C'}, {id: 9, value: '已办结', statusColor: '#bdc3bf'}],
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [],
      // 弹出层标题
      title: '新增案件',
      // 是否显示弹出层
      open: false,
      // 状态数据字典
      statusOptions: [],
      // 菜单列表
      menuOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        typeName: '',
        type: 0,
        statusName: '',
        status: undefined,
        // 日期范围
        dateRange: [],
        data: 0,
        dataName: '我的',
        deptId: '',
        deptName: ''
      },
      // 表单参数
      form: {},
      qd: true,
      formDisabled: false,
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      listData: [],
      deptOptions: [],
      typeList: 1
    }
  },
  computed: {
    // listData() {
    //   let {pageNum, pageSize} = this.queryParams
    //   let arr = this.roleList.slice((pageNum - 1) * pageSize, (pageNum - 1) * pageSize + pageSize)
    //   return arr
    // }
  },
  created() {
    this.getDeptNameOptions()

    if (this.$route.query.data) {
      let data = JSON.parse(this.$route.query.data)
      this.open = true
      this.detailId = data.id
    }
    this.getList()
    this.getuserList()
    this.getDicts('sys_normal_disable').then(response => {
      this.statusOptions = response.data
    })
  },
  methods: {
    // 网格小组选项
    getDeptNameOptions() {
      deptList({ typeList: this.typeList }).then(res => {
        this.deptOptions = res.data.map(item => item.label)
      })
    },
    // 选择部门
    handleConfirmDept({id, name}) {
      this.queryParams = { ...this.queryParams, deptId: id, deptName: name }
    },
    handlePrint(id) {
      let routeUrl = this.$router.resolve({ path: '/case/print/patrol', query: { id } })
      window.open(routeUrl.href, '_blank')
    },
    // 类型选择
    handleCommand(command) {
      if (command.type == 'user') {
        this.queryParams = {...this.queryParams, data: command.id, dataName: command.value, status: '', statusName: ''}
      } else {
        this.queryParams = {...this.queryParams, status: command.id, statusName: command.value}
      }
      this.handleQuery()
    },
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.queryParams.pageNum = 1
      this.getList()
    },
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.getList()
    },
    // 状态颜色
    cellStyle(row) {
      if (row.column.label == '状态') return `color: ${row.row.statusColor}`
    },
    // 已下为模板
    /** 查询列表 */
    getList() {
      this.loading = true
      let {searchValue, dateRange, pageNum, pageSize, type, deptName } = this.queryParams
      let params = { pageNum, pageSize, status: 9, type }
      if (searchValue) params.searchValue = searchValue
      if (deptName) params.deptName = deptName
      if (Array.isArray(dateRange) && !dateRange.length == 0) { params = {...params, searchStartTime: dateRange[0], searchEndTime: dateRange[1] } }
      // let api =  data == 0 ? myInspectionList(params) : inspectionList({...params})
      inspectionList(params).then(res => {
        res.rows.forEach((v, i) => {
          // this.typeData.forEach(tv => {
          //   if (v.type == tv.id) this.roleList[i].typeName = tv.value
          // })
          this.caseData.forEach(tv => {
            if (v.status == tv.id) {
              res.rows[i].statusName = tv.value
              res.rows[i].statusColor = tv.statusColor
            }
          })
        })
        this.listData = res.rows
        this.total = res.total
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    getuserList() {
      userList().then(res => {
        this.userOptions = res.data
      })
    },
    // 确定按钮
    primary(params) {
      console.log(params)
      this.qd = false
      editInspection(params).then(() => {
        this.$refs.fromlist.upload({id: params.inspectionId, status: params.status})
        this.qd = true
        this.msgSuccess('修改成功')
        this.timer = setTimeout(() => {
          this.open = false
          this.getList()
        }, 1000)
      }).catch(err => { console.log(err); this.qd = true })
    },
    // 取消
    cancel() {
      this.open = false
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {dateRange: [], data: 0, type: 0, dataName: '我的', pageNum: 1, pageSize: 10, inspectionTypeName: '', inspectionType: undefined, statusName: '', status: undefined, searchValue: ''}
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      console.log(selection)
      this.ids = selection.map(item => item.inspectionId)
      this.single = selection.length != 1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.open = true
      this.formDisabled = false
      if (this.queryParams.data == 1 || row.status == 9) this.formDisabled = true
      this.detailId = row.inspectionId
      this.title = '详情信息'
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const inspectionId = row.inspectionId || this.ids
      console.log(inspectionId)
      this.$confirm('是否确认删除?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // return delRole(roleIds)
          this.roleList = this.roleList.filter(
            irem => row.inspectionId != irem.inspectionId
          )
          this.total--
        })
        .then(() => {
          removeInspection(inspectionId).then(() => {
            this.getList()
            this.msgSuccess('删除成功')
          })
        })
    }
  }
}
</script>
