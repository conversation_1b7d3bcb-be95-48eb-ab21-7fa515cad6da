<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" :inline="true">
      <el-form-item label="车牌" prop="plateNo">
        <el-input
          v-model="queryParams.plateNo"
          placeholder="请输入车牌"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="车牌颜色" prop="plateColor">
        <el-select v-model="queryParams.plateColor" style="width: 100%;">
          <el-option label="全部" />
          <el-option :value="1" label="黄色" />
          <el-option :value="2" label="蓝色" />
          <el-option :value="3" label="黑色" />
          <el-option :value="4" label="白色" />
          <el-option :value="9" label="其它" />
        </el-select>
      </el-form-item>
      <el-form-item label="车主姓名" prop="ownerName">
        <el-input
          v-model="queryParams.ownerName"
          placeholder="请输入车主姓名"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="驾驶员姓名" prop="driverName">
        <el-input
          v-model="queryParams.driverName"
          placeholder="请输入驾驶员姓名"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="驾驶员电话" prop="tel">
        <el-input
          v-model="queryParams.tel"
          placeholder="请输入驾驶员电话"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="taxiList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="车牌" align="center" prop="plateNo" />
      <el-table-column label="车牌颜色" align="center" prop="plateColor">
        <template slot-scope="scope">{{ scope.row.plateColor | cardColor }}</template>
      </el-table-column>
      <el-table-column label="车辆颜色" align="center" prop="vehicleColor">
        <template slot-scope="scope">{{ scope.row.vehicleColor | carColor }}</template>
      </el-table-column>
      <el-table-column label="车主姓名" align="center" prop="ownerName" />
      <el-table-column label="驾驶员姓名" align="center" prop="driverName" />
      <el-table-column label="驾驶员电话" align="center" prop="tel" />
      <el-table-column label="最新定位时间" align="center" prop="time" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.time, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="车辆状态" align="center" prop="status" />
      <el-table-column label="是否在区域内" align="center" prop="inArea">
        <template slot-scope="scope">{{ scope.row.inArea == 1 ? '是' : '否' }}</template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['exam:taxi:edit']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改出租车对话框 -->
    <div>
      <el-dialog class="m-dialog" :close-on-click-modal="false" :title="title" :visible.sync="open">
        <el-scrollbar style="height: 100%;">
          <el-row :gutter="15" style="margin-right: 10px;">
            <el-form ref="form" :model="form" :rules="rules" label-width="120px">
              <el-col :span="12">
                <el-form-item label="车辆编号" prop="vehicleIndexCode">
                  <el-input v-model="form.vehicleIndexCode" placeholder="请输入车辆编号" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="车辆自编号" prop="selfNo">
                  <el-input v-model="form.selfNo" placeholder="请输入车辆自编号" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="车辆名称" prop="taxiName">
                  <el-input v-model="form.taxiName" placeholder="请输入车辆名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="车牌" prop="plateNo">
                  <el-input v-model="form.plateNo" placeholder="请输入车牌" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="车牌颜色" prop="plateColor">
                  <!-- <el-input v-model="form.plateColor" placeholder="请输入车牌颜色" /> -->
                  <el-select v-model="form.plateColor" style="width: 100%;">
                    <el-option :value="1" label="黄色" />
                    <el-option :value="2" label="蓝色" />
                    <el-option :value="3" label="黑色" />
                    <el-option :value="4" label="白色" />
                    <el-option :value="9" label="其它" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="运输类型" prop="transportType">
                  <el-select v-model="form.transportType" placeholder="请选择运输类型" :style="{ width: '100%' }">
                    <el-option label="请选择字典生成" value="" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="车型" prop="vehicleType">
                  <el-select v-model="form.vehicleType" placeholder="请选择车型" :style="{ width: '100%' }">
                    <el-option label="请选择字典生成" value="" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="区域编号" prop="regionIndexCode">
                  <el-input v-model="form.regionIndexCode" placeholder="请输入区域编号" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="车辆颜色" prop="vehicleColor">
                  <!-- <el-input v-model="form.vehicleColor" placeholder="请输入车辆颜色" /> -->
                  <el-select v-model="form.vehicleColor" style="width: 100%;">
                    <el-option :value="1" label="白色" />
                    <el-option :value="10" label="棕色" />
                    <el-option :value="11" label="粉色" />
                    <el-option :value="12" label="紫色" />
                    <el-option :value="2" label="银色" />
                    <el-option :value="3" label="灰色" />
                    <el-option :value="4" label="黑色" />
                    <el-option :value="5" label="红色" />
                    <el-option :value="6" label="深蓝" />
                    <el-option :value="7" label="蓝色" />
                    <el-option :value="8" label="黄色" />
                    <el-option :value="9" label="绿色" />
                    <el-option :value="0" label="其它" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="燃油类型" prop="fuelType">
                  <el-select v-model="form.fuelType" placeholder="请选择燃油类型" :style="{ width: '100%' }">
                    <el-option label="请选择字典生成" value="" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="车主姓名" prop="ownerName">
                  <el-input v-model="form.ownerName" placeholder="请输入车主姓名" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="车主编号" prop="ownerCode">
                  <el-input v-model="form.ownerCode" placeholder="请输入车主编号" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="驾驶员姓名" prop="driverName">
                  <el-input v-model="form.driverName" placeholder="请输入驾驶员姓名" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="驾驶员资格证号" prop="driverNo">
                  <el-input v-model="form.driverNo" placeholder="请输入驾驶员资格证号" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="驾驶员电话" prop="tel">
                  <el-input v-model="form.tel" placeholder="请输入驾驶员电话" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="驾驶员编号" prop="driverIndexCode">
                  <el-input v-model="form.driverIndexCode" placeholder="请输入驾驶员编号" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="设备编号" prop="deviceIndexCode">
                  <el-input v-model="form.deviceIndexCode" placeholder="请输入设备编号" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="速度" prop="speed">
                  <el-input v-model="form.speed" placeholder="请输入速度" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="行驶记录仪速度" prop="recordSpeed">
                  <el-input v-model="form.recordSpeed" placeholder="请输入行驶记录仪速度" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="经度" prop="longitude">
                  <el-input v-model="form.longitude" placeholder="请输入经度" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="纬度" prop="latitude">
                  <el-input v-model="form.latitude" placeholder="请输入纬度" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="方向" prop="direction">
                  <el-input v-model="form.direction" placeholder="请输入方向" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="gps上传时间" prop="time">
                  <el-date-picker v-model="form.time" clearable
                                  size="small"
                                  type="date"
                                  value-format="yyyy-MM-dd"
                                  placeholder="选择gps上传时间"
                  />
                </el-form-item>
              </el-col>
              <!-- <el-col :span="12">
                <el-form-item label="汽车状态">
                  <el-radio-group v-model="form.carStatus">
                    <el-radio label="1">请选择字典生成</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="车辆状态">
                  <el-radio-group v-model="form.status">
                    <el-radio label="1">请选择字典生成</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col> -->
            </el-form>
          </el-row>
        </el-scrollbar>
        <div slot="footer" class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { listTaxi, getTaxi, delTaxi, addTaxi, updateTaxi, exportTaxi } from '@/api/case/pipe/taxi'

export default {
  name: 'Taxi',
  components: {
  },
  filters: {
    carColor(num) {
      const taxiColor = {0: '其它', 1: '白色', 10: '棕色', 11: '粉色', 12: '紫色', 2: '银色', 3: '灰色', 4: '黑色', 5: '红色', 6: '深蓝', 7: '蓝色', 8: '黄色', 9: '绿色'}
      return taxiColor[num]
    },
    cardColor(num) {
      const cardColor = {1: '黄色', 2: '蓝色', 3: '黑色', 4: '白色', 9: '其它'}
      return cardColor[num]
    }
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 出租车表格数据
      taxiList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        vehicleIndexCode: null,
        selfNo: null,
        taxiName: null,
        plateNo: null,
        plateColor: null,
        transportType: null,
        vehicleType: null,
        regionIndexCode: null,
        vehicleColor: null,
        fuelType: null,
        ownerName: null,
        ownerCode: null,
        driverName: null,
        driverNo: null,
        tel: null,
        driverIndexCode: null,
        deviceIndexCode: null,
        speed: null,
        recordSpeed: null,
        longitude: null,
        latitude: null,
        direction: null,
        time: null,
        carStatus: null,
        type: null,
        status: null,
        deptId: null,
        deptName: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询出租车列表 */
    getList() {
      this.loading = true
      listTaxi(this.queryParams).then(response => {
        this.taxiList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        vehicleIndexCode: null,
        selfNo: null,
        taxiName: null,
        plateNo: null,
        plateColor: null,
        transportType: null,
        vehicleType: null,
        regionIndexCode: null,
        vehicleColor: null,
        fuelType: null,
        ownerName: null,
        ownerCode: null,
        driverName: null,
        driverNo: null,
        tel: null,
        driverIndexCode: null,
        deviceIndexCode: null,
        speed: null,
        recordSpeed: null,
        longitude: null,
        latitude: null,
        direction: null,
        time: null,
        carStatus: '0',
        type: null,
        status: '0',
        delFlag: null,
        deptId: null,
        deptName: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加出租车'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getTaxi(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改出租车'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateTaxi(this.form).then(() => {
              this.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addTaxi(this.form).then(() => {
              this.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const taxiIds = row.id || this.ids
      this.$confirm('是否确认删除出租车编号为"' + taxiIds + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return delTaxi(taxiIds)
      }).then(() => {
        this.getList()
        this.msgSuccess('删除成功')
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams
      this.$confirm('是否确认导出所有出租车数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return exportTaxi(queryParams)
      }).then(response => {
        this.download(response.msg)
      })
    }
  }
}
</script>
