<template>
  <div class='wrap-bg'>
    <CommonTitle text='今日考勤' style='margin-top: 40px;'></CommonTitle>
    <div class='wrap-container'>
      <div class='wrap-container-box'>
        <div class='wrap-container-box-item' v-for='(item,i) in indexList' :key='i'>
          <div class='wrap-container-box-item-number'>{{item.value}}</div>
          <div class='wrap-container-box-item-text'>{{item.name}}</div>
        </div>
      </div>
      <div class='wrap-container-chart' ref="chartTop"></div>
    </div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import * as echarts from 'echarts'
export default {
  name: 'index',
  components: {
    CommonTitle
  },
  data() {
    return {
      indexList: [
        {
          name: "考勤总人数",
          value: 93
        },
        {
          name: "未到岗人数",
          value: 1
        },
        {
          name: "迟到人数",
          value: 5
        },
        {
          name: "请假人数",
          value: 1
        },
        {
          name: "出勤率",
          value: '93%'
        }
      ]
    }
  },
  computed: {},
  mounted() {
    this.initChart()
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chartTop)

      const option = {
        backgroundColor: 'transparent',
        graphic: {
          type: 'image',
          style: {
            image: require('@/assets/zqyzt/pie-bg.png'),
            width: 360,
            height: 360
          },
          left: '24.8%',  // 调整背景图位置，与环形图对齐
          top: 'center'
        },
        title: [{
          text: '',
          left: '18.5%',
          top: '48%',
          textStyle: {
            color: '#E3F4FF',
            fontSize: 52,
            fontWeight: 'normal',
            fontFamily: 'DIN',
            lineHeight: 72
          },
          z: 10  // 确保文字在背景图之上
        }, {
          text: '',
          left: '19%',
          top: '38%',
          textStyle: {
            color: '#E3F4FF',
            fontSize: 28,
            fontWeight: 'normal',
            lineHeight: 28
          },
          z: 10  // 确保文字在背景图之上
        }],
        legend: {
          orient: 'vertical',
          left: '55%',
          y: 'center',
          itemWidth: 16,
          itemHeight: 16,
          itemGap: 28,
          icon: 'circle',
          formatter: name => {
            const value = 779;
            return `{name|${name}}{value|${value}}`
          },
          textStyle: {
            rich: {
              name: {
                fontSize: 26,
                color: '#fff',
                padding: [0, 30, 0, 0],
                width: 140,
              },
              value: {
                fontSize: 26,
                color: '#fff',
                padding: [0, 0, 0, 0]
              }
            }
          }
        },
        series: [{
          type: 'pie',
          radius: ['55%', '65%'],
          center: ['35%', '50%'],
          startAngle: 90,
          itemStyle: {
            borderRadius: 0,
            borderColor: 'rgba(2,47,115,0.5)',
            borderWidth: 2,
          },
          data: [
            {
              value: 779,
              name: '考勤总人数',
            },
            {
              value: 779,
              name: '未到岗人数',
            },
            {
              value: 779,
              name: '迟到人数',
            },
            {
              value: 779,
              name: '请假人数',
            }
          ],
          label: {
            show: false
          },
          labelLine: {
            show: false
          },
          emphasis: {
            scale: false
          }
        }]
      }

      this.chart.setOption(option)
    },
  },
  watch: {}
}
</script>

<style scoped lang='less'>
.wrap-container {
  width: 100%;
  height: 854px;
  margin-bottom: 40px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  .wrap-container-box {
    width: 1651px;
    height: 168px;
    background: url('@/assets/zqyzt/ajlx-box.png') no-repeat;
    background-size: 100% 100%;
    margin-top: 51px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    .wrap-container-box-item {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      .wrap-container-box-item-number {
        font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
        font-weight: 400;
        font-size: 64px;
        text-align: center;
        font-style: normal;
        text-transform: none;
        background: linear-gradient(180deg, #FFFFFF 32%, #52C3F7 100%);
        -webkit-background-clip: text;
        color: transparent;
      }
      .wrap-container-box-item-text {
        font-family: Alimama ShuHeiTi, Alimama ShuHeiTi;
        font-weight: 700;
        font-size: 28px;
        color: #FFFFFF;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
    }
  }
  .wrap-container-chart {
    width: 100%;
    height: 804px;
  }
}
</style>