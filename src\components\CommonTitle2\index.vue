<template>
  <div class="hearder_h2">
    <span>{{text}}</span>
    <img
      v-if="manageUrl != ''"
      src="@/assets/common/edit.png"
      alt=""
      style="margin-left: 10px; cursor: pointer"
      @click="openManage()"
    />
  </div>
</template>

<script>
export default {
  name: 'index',
  props: {
    text: {
      type: String,
      default: ''
    },
    manageUrl: {
      type: String,
      default: ''
    }
  },
  data() {
    return {}
  },
  computed: {},
  mounted() {

  },
  methods: {
    openManage() {
      window.open(this.manageUrl)
    }
  },
  watch: {}
}
</script>

<style scoped>

</style>