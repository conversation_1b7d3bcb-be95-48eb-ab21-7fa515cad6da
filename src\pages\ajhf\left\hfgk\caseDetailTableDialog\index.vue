<template>
  <ygfDialog :visible='visible' width='1415px'>
    <div id="ndjhs" class="rwgz-tc">
      <div class="rw-title flex-end">
        <div class="close cursor" @click="close" style="margin-right: 20px;"></div>
      </div>
      <div class="content">
        <el-form ref="form" :model="form">
          <el-form-item label="基本信息" style="margin-bottom: 8px">
          </el-form-item>
          <el-descriptions class="margin-top" :column="3" border>
            <el-descriptions-item label="案件编号" span="2">
              {{ form.ajbh }}
            </el-descriptions-item>
            <el-descriptions-item label="处罚行为" span="2">
              {{ form.cfxw }}
            </el-descriptions-item>
            <el-descriptions-item label="重大案件" span="2">
              {{ form.zdaj }}
            </el-descriptions-item>
            <el-descriptions-item label="实施机构">
              {{ form.ssjg }}
            </el-descriptions-item>
            <el-descriptions-item label="决定送达日期" span="2">
              {{ form.jdsdrq }}
            </el-descriptions-item>
            <el-descriptions-item label="行政相对人">
              {{ form.xzxdr }}
            </el-descriptions-item>
            <el-descriptions-item label="相对人电话" span="2">
              {{ form.xdrdh }}
            </el-descriptions-item>
            <el-descriptions-item label="执法人名称">
              {{ form.zfrmc }}
            </el-descriptions-item>
            <el-descriptions-item label="国标行政区划" span="2">
              {{ form.gbxzqh }}
            </el-descriptions-item>
            <el-descriptions-item label="处罚金额">
              {{ form.cfje }}
            </el-descriptions-item>
          </el-descriptions>



          <el-divider></el-divider>



          <el-form-item label="回访信息" style="margin-bottom: 8px">
          </el-form-item>

          <el-descriptions class="margin-top" :column="3" border>
            <el-descriptions-item label="回访类型">
              {{ labelGet(followTypeList,form.hflx) }}
            </el-descriptions-item>
            <el-descriptions-item label="任务日期">
              {{ form.rwrq }}
            </el-descriptions-item>
            <el-descriptions-item label="回访日期">
              {{ form.hfrq }}
            </el-descriptions-item>
            <!--            <el-descriptions-item label="回访人">-->
            <!--              {{ form.hfr }}-->
            <!--            </el-descriptions-item>-->
            <!--            <el-descriptions-item label="回访部门">-->
            <!--              {{ form.hfbm }}-->
            <!--            </el-descriptions-item>-->
            <el-descriptions-item label="回访人姓名">
              {{ form.hfryxm }}
            </el-descriptions-item>
            <el-descriptions-item label="回访单位">
              {{ form.hfdw }}
            </el-descriptions-item>
            <el-descriptions-item label="回访结果">
              {{ form.myd }}
            </el-descriptions-item>
          </el-descriptions>
        </el-form>
      </div>
    </div>
  </ygfDialog>
</template>

<script>
import ygfDialog from '@/components/ygfDialog'
import { getAjDetail } from '@/api/ajhf'
export default {
  name: 'index',
  props: ['caseNo', 'visible'],
  components: {
    ygfDialog
  },
  data() {
    return {
      form: {},
      //回访类型列表
      followTypeList: [
        {
          label:"-",
          value:0
        },
        {
          label:"无需回访",
          value:1
        },
        {
          label:"自行回访",
          value:2
        },
        {
          label:"短信回访",
          value:3
        }
      ],
    }
  },
  computed: {},
  mounted() {

  },
  methods: {
    //获取数据
    getList() {
      getAjDetail({caseNo: this.caseNo}).then(res => {
        const data = res.data
        let hfryxm = null;
        let hfdw = null;
        if(data.followType == 3) {
          //短信回访
          hfryxm = data.followPeople?data.followPeople:''
          hfdw = data.followDept?data.followDept:''
        } else if (data.followType == 2) {
          //自行回访
          hfryxm = data.hfQuestionnaire?data.hfQuestionnaire.hfryxm:null
          hfdw = data.hfQuestionnaire?data.hfQuestionnaire.hfdw:null
        }
        this.form = {
          ajbh: data.caseNo,
          cfxw: data.punishActionName,
          zdaj: data.majorCasesind,
          ssjg: data.implementinistName,
          jdsdrq: data.deciDeliveryDate,
          xzxdr: data.admstraCpName,
          xdrdh: data.admstraCpContactNumber,
          zfrmc: data.lawEnforcementOfficersname,
          gbxzqh: data.nationRegionName,
          cfje: data.fineAmt,
          hflx: data.followType,
          rwrq: data.taskDate,
          hfr: data.followPeople,
          hfbm: data.followDept,
          hfrq: data.followTime,
          myd: data.myd !== null?data.myd == "0"?"满意":"不满意":"",
          hfryxm: hfryxm,
          hfdw: hfdw
        };
      })
    },
    close() {
      this.$emit('close')
    },
    // 字典翻译
    labelGet(list, value) {
      if (!Array.isArray(list)) {
        console.error("Expected an array for the 'list' parameter.")
        return ''
      }

      const foundItem = list.find((item) => item.value == value)

      // 确保找到对应的item后再返回其label属性，否则返回空字符串
      return value === null || value === undefined || value === '' || !foundItem
        ? ''
        : foundItem.label
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.getList();
      }
    }
  }
}
</script>
<style scoped lang='less'>

.search-box {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

::-webkit-scrollbar {
  display: none;
}


ul,
ul li {
  list-style: none;
}

.rwgz-tc {
  width: 1515px;
  height: 1186px;
  background: url("@/assets/zhdd/dialogBg2.png") no-repeat;
  background-size: 100% 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-direction: column;
}

.rw-title {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 86px 3% 0;
  width: 95%;
  height: 60px;
  line-height: 60px;
  position: absolute;
  z-index: 999;
}

.close {
  background: url("@/assets/zhdd/close.png") no-repeat;
  width: 34px;
  height: 34px;
}

.ql-indent-1 img {
  width: 100%;
}

img {
  width: 100%;
}

.content {
  width: 100%;
  height: 100%;
  margin-top: 100px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-direction: column;
}

/deep/ .el-form {
  width: 90%;
}

::-webkit-scrollbar {
  width: 0;
  height: 0;
}

.scrollTab {
  width:928px;
  height:70px;
}

.table-container {
  height: 880px;
  overflow-y: scroll;
}

.tableContainer2 {
  height: 400px;
  overflow-y: scroll;
}

.table-line {
  width: 1429px;
  height: 80px;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  background: rgba(50,134,248,0.15);
}

.title-line {
  background: transparent !important;
}

.table-title {
  font-size: 32px;
  font-family: Source Han Sans CN;
  font-weight: bold;
  color: #FFFFFF;
  text-align: left;
}

.table-column {
  font-size: 32px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
}
.activeTableLine {
  background: rgba(50,134,248,0.25);
}

/deep/ .el-input {
  /*position: absolute;*/
  /*top: 229px;*/
  /*left: 1630px;*/
  width: 537px;
  height: 72px;
}

/deep/ .el-input__icon {
  height: 100%;
  width: 25px;
  text-align: center;
  -webkit-transition: all .3s;
  transition: all .3s;
  line-height: 75px;
}

/deep/ .el-scrollbar {
  overflow: hidden;
  /*position: relative;*/
  height: 500px;
  background: #020b28;
  border-radius: 10px;
}

/deep/ .el-autocomplete-suggestion__wrap {
  max-height: unset !important;
  padding: 10px 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

/deep/ .el-input--suffix /deep/ .el-input__inner {
  border: 1px solid #359CF8;
  border-radius: 8px;
  padding-right: 30px;
  height: 70px;
  font-family: MicrosoftYaHei;
  font-size: 28px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 1px;
  color: #bbe5fd !important;
  background: #020b28;
}

/deep/ .el-input.is-active /deep/ .el-input__inner,
/deep/ .el-input__inner:focus {
  border: 1px solid #bbe5fd;
  outline: 0;
}

/deep/ .el-input__suffix-inner {
  pointer-events: all;
  font-size: 28px;
  margin: 15px 20px 0 0;
  color: #bbe5fd !important;
}

/deep/ .el-autocomplete-suggestion li {
  padding: 0 20px;
  line-height: 34px;
  cursor: pointer;
  color: #bbe5fd;
  font-size: 28px;
  list-style: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin: 10px 0 25px 0;
}

/deep/ .el-autocomplete-suggestion li:hover {
  background: unset !important;
}

.search {
  width: 100px;
  height: 65px;
  line-height: 65px;
  text-align: center;
  background: #0A619E;
  border: 1px solid #359CF8;
  border-radius: 8px;
  font-size: 28px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FEFEFE;
  margin-left: 10px;
  cursor: pointer;
}

.tab-con-active {
  background: url('@/assets/zfts/tab-active.png') no-repeat;
  background-size: 110% 100%;
}

.toolBar {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tab-con {
  width: 351px;
  height: 70px;
  line-height: 70px;
  color: #fff;
  font-size: 32px;
}

.s-flex {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-evenly;
}

.tab-item {
  font-size: 36px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  font-style: italic;
  color: rgba(171, 206, 239, 0.7);
  line-height: 59px;
}

.tabConActive {
  background: url('@/assets/zfts/tab-active.png') no-repeat;
  background-size: 110% 100%;
  font-family: Source Han Sans CN;
  font-weight: bold;
  font-size: 36px;
  color: #ffffff;
}

/deep/ .el-form-item__label {
  text-align: right;
  vertical-align: middle;
  float: left;
  font-size: 40px;
  color: white;
  line-height: 40px;
  box-sizing: border-box;
  padding: 0px 12px 20px 0px;

  background-image: linear-gradient(180deg, #ffb637, #ffb637, #fff, #ffb637, #ffb637);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/deep/ .el-descriptions__body {
  color: #606266;
  background-color: unset !important;
}

/deep/ .el-descriptions-item__label {
  white-space: nowrap;
  font-size: 26px;
  background: rgba(50, 134, 248, 0.1) !important;
  color: #C8D5E0 !important;
}

/deep/ .el-descriptions-item__content {
  font-size: 26px;
  color: #C8D5E0 !important;
}
</style>