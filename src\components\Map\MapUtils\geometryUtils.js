
import * as geometryEngineAsync from "@arcgis/core/geometry/geometryEngineAsync.js";
import * as geodesicUtils from "@arcgis/core/geometry/support/geodesicUtils.js";
import Point from "@arcgis/core/geometry/Point.js";
import * as webMercatorUtils from "@arcgis/core/geometry/support/webMercatorUtils.js";

// instance
const point = {
    type: "point", // autocasts as new Point()
    x: -0.178,
    y: 51.48791,
    z: 1010,
    spatialReference: { wkid: 4326 }
};

const polyline = {
    type: "polyline", // autocasts as new Polyline()
    paths: [
        [-0.178, 51.48791, 0],
        [-0.178, 51.48791, 1000]
    ],
    spatialReference: { wkid: 4326 }
};

const polygon = {
    type: "polygon", // autocasts as new Polygon()
    rings: [
        [-0.184, 51.48391, 400],
        [-0.184, 51.49091, 500],
        [-0.172, 51.49091, 500],
        [-0.172, 51.48391, 400],
        [-0.184, 51.48391, 400]
    ],
    spatialReference: { wkid: 4326 }
};

const fromPoint = new Point({
    x: 12938664.414901968,
    y: 4799319.776185318,
    spatialReference: { wkid: 102100, latestWkid: 3857 },
});

export function geographicToWebMercator(geometry) {
    return webMercatorUtils.geographicToWebMercator(geometry);
}

export function webMercatorToGeographic(geometry) {
    return webMercatorUtils.webMercatorToGeographic(geometry);
}
;
// 求两点之间的距离  投影坐标系
export async function webMercatorDistance(start, end, unit = "meters") {
    const distance = await geometryEngineAsync.distance(start, end, unit);
    return distance
}

// 求两点之间的距离  地理坐标系 4326 
export function geodesicDistance(start, end, unit = "meters") {
    const join = geodesicUtils.geodesicDistance(start, end, unit);
    const { distance, azimuth } = join;
    return distance
}

// 求多个polygon的面积 4326
export function geodesicAreas(polygons, unit = "square-kilometers") {
    const areas = geodesicUtils.geodesicAreas(polygons, unit);
    return areas;
}

// 计算多个4490要素的面积
export async function geodesic4490Areas(features, unit = "square-kilometers") {
    let areas = 0;
    for (let i = 0; i < features.length; i++) {
        const webGeometry = await geoToWebMercator(features[i].geometry);
        const area = await planarArea(webGeometry, unit);
        areas += area;
    }
    return areas;
}


// planarArea():投影坐标系     使用投影坐标执行此计算并且不考虑地球的曲率,如果输入几何图形的投影坐标系不是 Web Mercator，请改用 planarArea()。
export async function planarArea(polygon, unit = "square-kilometers") {
    const area = geometryEngineAsync.planarArea(polygon, unit);
    return area;
}

// geographicToWebMercator
export async function geoToWebMercator(geometry) {
    const webGeometry = webMercatorUtils.geographicToWebMercator(geometry);
    return webGeometry;
}


// 在polyline线上距离处取点 传入的点坐标是投影坐标
export async function getPointFromDistance(
    points,
    distance = 0,
    startIndex = 0,
) {
    let travalled = 0;
    if (startIndex >= 0 && startIndex < points.length) {
        if (distance === 0) {
            return points[0]
        } else if (distance > 0) {
            for (let i = 1; i < points.length; i += 1) {
                const segLength = await webMercatorDistance(points[i - 1], points[i]);
                travalled += segLength;

                if (travalled === distance) {
                    return points[i];
                } else if (travalled > distance) {
                    const diffDistance = segLength - (travalled - distance);
                    const angle = Math.atan2(
                        points[i].y - points[i - 1].y,
                        points[i].x - points[i - 1].x,
                    );
                    const diffX = diffDistance * Math.cos(angle);
                    const diffY = diffDistance * Math.sin(angle);

                    return new Point({
                        x: points[i - 1].x + diffX,
                        y: points[i - 1].y + diffY,
                        spatialReference: {
                            wkid: 102100
                        },
                    });
                }
            }
        }
    }

    return null;
}

// 将line的path转为投影坐标

export async function converPathToPoints(paths) {

    const path = paths.flat();

    const points = [];

    path.forEach(item => {
        const point = {
            type: "point", // autocasts as new Point()
            x: item[0],
            y: item[1],
            spatialReference: { wkid: 4490 }
        };
        points.push(geographicToWebMercator(point));
    });


    let resultPoins = []
    for (let i = 0; i < points.length - 1; i++) {
        const results = await insertPoins(points[i], points[i + 1], 20);
        resultPoins = [...resultPoins, ...results]
    }

    const resultPaths = [

    ]

    for (let i = 0; i < resultPoins.length - 1; i = i + 2) {
        const from = webMercatorToGeographic(resultPoins[i]);
        const to = webMercatorToGeographic(resultPoins[i + 1]);
        resultPaths.push(
            [
                [from.x, from.y, 0],
                [to.x, to.y, 0],
            ],
        )
    }

    return resultPaths;
}

// 两点之间均匀距离插入点
// 两点之间均匀距离插入点
export async function insertPoins(start, end, distance) {
    const result = [];
    const segLength = await webMercatorDistance(start, end);
    const interval = parseInt(segLength / distance);
    const angle = Math.atan2(
        end.y - start.y,
        end.x - start.x,
    );
    result.push(start);
    let nums = 0;
    if (interval % 2 == 0) {
        nums = interval - 2
    } else {
        nums = interval - 1
    }
    for (let i = 0; i < nums; i++) {
        const diffX = distance * (i + 1) * Math.cos(angle);
        const diffY = distance * (i + 1) * Math.sin(angle);
        result.push(
            {
                type: "point", // autocasts as new Point()
                x: start.x + diffX,
                y: start.y + diffY,
                spatialReference: { wkid: 102100 }
            }
        )
    }
    result.push(end);

    return result;


}