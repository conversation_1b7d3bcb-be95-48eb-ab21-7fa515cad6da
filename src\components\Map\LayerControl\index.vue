<template>
    <div class="panel-Container">
      <div class="panel-Container-inner">
        <div class="tree-container">
          <el-tree
            ref="treeRef"
            class="filter-tree"
            :data="options"
            :props="defaultProps"
            show-checkbox
            :render-after-expand="false"
            node-key="id"
            :filter-node-method="filterNode"
            :check-on-click-node="true"
            :check-strictly="true"
            @check-change="clickChange"
            :default-expand-all="true"
          >
            <template #default="{ node, data }">
              <div
                class="filter-tree"
                style="
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  width: 100%;
                  height: 100%;
                  padding: 0 20px 0 20px;
                "
                :class="{ levelContainer: data.nodeType === 'parent' }"
              >
                <div style="display: flex; align-items: center">
                  <div v-if="data.nodeType === 'parent'" class="layerIcon" />
                  <div
                    class="s-m-l-10 text-[1.875rem] leading-[3.125rem] w-[268px] overflow-hidden truncate"
                    :class="[node.disabled ? 'point-no' : '']"
                    :title="node.label"
                  >
                    {{ node.label }}
                  </div>
                </div>
                <div
                  v-if="data.nodeType === 'parent' || data.nodeType === 'child'"
                  class="icon s-m-l-10"
                  :class="[node.expanded ? 'open-file' : 'close-file']"
                  aria-hidden="true"
                />
              </div>
            </template>
          </el-tree>
        </div>
      </div>
    </div>
  </template>
  
  <script>
  import AllLayerList from './layerControlConfig'
  
  export default {
    name: 'LayerControl',
    
    props: {
      isFullscreen: {
        type: Boolean,
        default: false,
      },
    },
    
    data() {
      return {
        warningEventLayer: [],
        treeRef: null,
        defaultProps: {
          children: 'children',
          label: 'name',
        }
      }
    },
    
    computed: {
      options() {
        const currentTitle = this.$route.meta.title
        console.log(currentTitle)
        if (currentTitle == '物联网设备一张图') {
            // 异步执行，确保树渲染完成后再操作
          this.$nextTick(() => {
            // 查找ID为1-1的节点并勾选
            const node = this.$refs.treeRef.getNode('1-1');
            if (node) {
              this.$refs.treeRef.setChecked(node.key, true, false);
            }
          });
        }
        const matchedList = AllLayerList.find(item => item.name === currentTitle)
        return matchedList ? matchedList.list : []
      },
      
    },
    
    methods: {
      resetChecked() {
        this.$refs.treeRef && this.$refs.treeRef.setCheckedKeys([], false)
      },
      
      filterNode(value, data) {
        if (!value) return true
        return data.name.includes(value)
      },
      
      LoadOrRemoveLayer(nodes, str) {
        if (str === 'loadLayer') {
          this.$emit('loadLayer', nodes)
        }
        else if (str === 'removeLayer') {
          this.$emit('removeLayer', nodes)
        }
      },
      
      LoadOrRemovePointLayer(nodes, str) {
        if (str === 'loadPoint') {
          this.$emit('loadPointLayer', nodes)
        }
        else if (str === 'removePoint') {
          this.$emit('removePointLayer', nodes)
        }
      },
      
      clickChange(data, checked) {
        console.log('clickChange')
        const nodeWithParents = this.getNodeWithParents(data)
        nodeWithParents.checked = checked
          
        if (nodeWithParents.type) {
          if (checked) {
            nodeWithParents.type === 'poi'
              ? this.LoadOrRemovePointLayer(nodeWithParents, 'loadPoint')
              : this.LoadOrRemoveLayer(nodeWithParents, 'loadLayer')
          }
          else {
            nodeWithParents.type === 'poi'
              ? this.LoadOrRemovePointLayer(nodeWithParents, 'removePoint')
              : this.LoadOrRemoveLayer(nodeWithParents, 'removeLayer')
          }
        }
      },
      
      
      getNodeWithParents(data) {
        const result = { ...data }
        result.parents = []
      
        let currentNode = this.$refs.treeRef.getNode(data.id)
        while (currentNode && currentNode.parent && currentNode.parent.level > 0) {
          currentNode = currentNode.parent
          result.parents.unshift({ ...currentNode.data })
        }
      
        return result
      },
      
      cancelAll() {
        if (this.$refs.treeRef) {
          // 清空所有选中的节点
          this.$refs.treeRef.setCheckedKeys([], false)
      
          // 获取所有被选中的节点
          const checkedNodes = this.$refs.treeRef.getCheckedNodes()
      
          // 对每个之前被选中的节点触发 clickChange 事件
          checkedNodes.forEach((node) => {
            this.clickChange(node, false)
          })
        }
      },
      
      updateNodeName(nodeId, newName) {
        const node = this.$refs.treeRef.getNode(nodeId)
        if (node) {
          // 更新 data 中的 name 属性而不是 node.label
          node.data.name = newName
          // 强制更新视图
          this.$nextTick(() => {
            this.$refs.treeRef.$forceUpdate()
          })
        }
      },
      
      isNodeChecked(nodeName) {
        if (this.warningEventLayer[0] && this.warningEventLayer[0].checked) {
          return true;
        } else {
          return false;
        }
      }
    },
    
    beforeRouteUpdate(to, from, next) {
      this.resetChecked()
      next()
    },
    
    mounted() {
      // 如果有需要在mounted中执行的代码
    },
    // watch: {
    //   $route(to, from) {
    //     console.log('fff111', to)
    //     this.currentRoute = to.path
    //     if (this.currentRoute === '/wlwsb') {
    //       treeRef.value.setChecked('1-1', true)
    //     } 
    //   },
    //   immediate: true,
    // },
  }
  </script>
  
  <style scoped lang="scss">
.panel-Container {
  position: absolute;
  width: 320px;
  max-height: 1600px;
  background-size: cover;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  transform: scale(1);
  transform-origin: top left;
  top: 160px;
  left: 1050px;
  z-index: 99;
  transition: left 0.3s ease;
  font-size: 28px;
  .layerIcon {
    width: 32px;
    height: 32px;
    background-image: url('@/components/Map/MapAssets/Dog/layericon.png');
    background-size: cover;
    margin-right: 1.7rem;
  }
  .icon {
    width: 38px;
    height: 31px;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
  }
  .point-no {
    color: #c1c1c1f2;
    /* pointer-events: none;
            cursor: default; */
    cursor: not-allowed !important;
  }
  .open-file {
    background-image: url('@/components/Map/MapAssets/Dog/openfile.png');
    background-size: 100% 100%;
  }
  .close-file {
    background-image: url('@/components/Map/MapAssets/Dog/closefile.png') !important;
    background-size: 100% 100%;
  }
  .s-m-l-10 {
    //margin-left: 10px !important;
  }
  .panel-Container-inner {
    width: 100%;
    max-height: 1500px;
    margin-top: 50px;
    overflow-y: auto;
    position: relative;
    .panel-Container-text {
      width: 350px;
      height: fit-content;
      box-sizing: border-box;
      font-size: 40px;
      color: rgb(255, 255, 255);
      display: flex;
      margin-top: 10px;
    }
    .tree-container {
      // height: 1500px;
      :deep(.el-tree) {
        background-color: transparent;
        color: white;
        //margin-top: 30px;
        height: auto;
        overflow-y: scroll;
        .levelContainer {
          background: linear-gradient(271deg, rgba(19, 43, 106, 0.9) 0%, #026ef1 100%);
          border-bottom: 1px solid rgba(19, 43, 106, 0.9);
        }
        .is-expanded {
          background: rgba(19, 43, 106, 0.9) !important;
        }
        .el-tree-node__children {
          margin-left: 25px;
        }
        .el-tree-node__content:hover,
        .el-tree-node:focus > .el-tree-node__content {
          background: linear-gradient(0deg, #00f6ff 0%, #00a2ff 100%) !important;
          -webkit-background-clip: text !important;
        }
        .el-tree-node__content > label.el-checkbox {
          margin-right: 0px;
        }
        .el-tree-node__content {
          height: 80px;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          .el-tree-node__label {
            font-size: 35px;
            margin-left: 10px;
          }
        }
        .el-tree-node__expand-icon {
          display: none;
          font-size: 1.375rem;
        }
        .el-tree-node {
          .is-leaf + .el-checkbox .el-checkbox__inner {
            display: inline-block;
            width: 30px;
            height: 30px;
            margin-top: 5px;
            background: transparent;
            border: 3px solid #3ed7fe;
            border-radius: 5px;
          }
          .el-checkbox .el-checkbox__inner {
            display: none;
          }
          .el-checkbox .el-checkbox__inner::after {
            width: 30px;
            height: 30px;
            background: url('@/components/Map/MapAssets/Dog/checked.png') no-repeat;
            background-size: cover;
            transition: none !important;
          }
          .el-checkbox__input.is-checked .el-checkbox__inner:after {
            border-color: var(--el-checkbox-checked-icon-color);
            transform: rotate(0deg) scaleY(1);
            transition: none !important;
            left: 1px;
            top: 1px;
            width: 23px !important;
            height: 23px !important;
            background-size: 100% 100% !important;
          }
        }
        .el-tree__empty-text {
          font-size: 24px;
          color: rgba(255, 255, 255, 0.614);
          display: none;
        }
      }
    }
  }
}

::-webkit-scrollbar {
  width: 0;
}

.button-container {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  position: absolute;
  top: 0px;

}

.cancel-all-btn {
  background: rgba(19, 43, 106, 0.6);
  color: white;
  border: 2px solid rgba(19, 43, 106, 0.8);
  padding: 7px 15px;
  font-size: 22px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);

  &:hover {
    background-color: rgba(19, 43, 106, 0.8);
    border-color: #5edbfe;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }

  &:active {
    background-color: rgba(19, 43, 106, 1);
    border-color: #2ed0fe;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    transform: translateY(1px);
  }
}
</style>

<style>
/*去除节点hover,focus效果*/
.panel-Container .panel-Container-inner .tree-container[data-v-cd770ae9] .el-tree .el-tree-node__content:hover,
.panel-Container
  .panel-Container-inner
  .tree-container[data-v-cd770ae9]
  .el-tree
  .el-tree-node:focus
  > .el-tree-node__content {
  background: unset !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: unset !important;
}
</style>
  