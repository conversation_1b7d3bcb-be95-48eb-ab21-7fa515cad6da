<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      :inline="true"
    >
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px;"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="daterange"
          unlink-panels
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item label="关键词检索">
        <el-input v-model="queryParams.searchValue" size="small" clearable placeholder="请输入内容" />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
        >
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col v-if="queryParams.data != 1" :span="1.5">
        <el-button
          v-hasPermi="['system:role:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
        >
          删除
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="listData"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="车牌号" prop="carnumber" align="center" :show-overflow-tooltip="true" width="100" />
      <el-table-column label="进站时间" align="inTime" prop="happenDate" />
      <el-table-column label="出站时间" prop="outTime" :show-overflow-tooltip="true" />
      <el-table-column label="等候时长" prop="waitTime" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ scope.row.waitTime }}分钟</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
        <template slot-scope="scope">
          <el-button v-if="scope.row.status == 9 || queryParams.data==1" size="mini" type="text" icon="el-icon-edit" style="color: #e6a23c;">详情</el-button>
          <el-button v-else v-hasPermi="['system:role:edit']" size="mini" type="text" icon="el-icon-edit">修改</el-button>
          <el-button v-if="queryParams.data != 1" v-hasPermi="['system:role:remove']" size="mini" type="text" icon="el-icon-delete">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
    />

    <!-- 添加或修改角色配置对话框 -->
    <!-- <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="1000px">
      <fromlist v-if="open" ref="fromlist" :detail-id="detailId" :form-disabled="formDisabled" :user-options="userOptions" :qd="qd" @onPrimary="primary" @oncancel="cancel" />
    </el-dialog> -->
  </div>
</template>

<script>
export default {
  name: 'Patrol',
  filters: {
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 导出遮罩层
      exportLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [],
      // 弹出层标题
      title: '新增案件',
      // 是否显示弹出层
      open: false,
      // 状态数据字典
      statusOptions: [],
      // 菜单列表
      menuOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      // 表单参数
      form: {},
      listData: [
        {carnumber: '浙GE3211', inTime: '2020-05-05 12:12:12', outTime: '2020-05-05 13:13:22', waitTime: 61},
        {carnumber: '浙GE3211', inTime: '2020-05-05 12:12:12', outTime: '2020-05-05 13:13:22', waitTime: 61},
        {carnumber: '浙GE3211', inTime: '2020-05-05 12:12:12', outTime: '2020-05-05 13:13:22', waitTime: 61},
        {carnumber: '浙GE3211', inTime: '2020-05-05 12:12:12', outTime: '2020-05-05 13:13:22', waitTime: 61},
        {carnumber: '浙GE3211', inTime: '2020-05-05 12:12:12', outTime: '2020-05-05 13:13:22', waitTime: 61},
        {carnumber: '浙GE3211', inTime: '2020-05-05 12:12:12', outTime: '2020-05-05 13:13:22', waitTime: 61}
      ]
    }
  },
  computed: {
    // listData() {
    //   let {pageNum, pageSize} = this.queryParams
    //   let arr = this.roleList.slice((pageNum - 1) * pageSize, (pageNum - 1) * pageSize + pageSize)
    //   return arr
    // }
  },
  methods: {
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10
      }
    }
  }
}
</script>
