const state = {
  $map: null,
  videoTitle: '连接中',
  videoVisible: false,
  callType: '', // callIn: 呼入 callRecv: 呼出
  callArgs: {},
  overLayStatus: {
    'monitor': true,
    'lawCar': true,
    'taxi': true,
    'recorder': true,
    'shop': true,
    'vol': true,
    'event': true,
    'tout': true,
    'area': true
  },
  overLayNum: {
    'recorder': 0,
    'taxi': 0,
    'lawCar': 0,
    'shop': 0,
    'monitor': 0,
    'vol': 0,
    'area': 0,

    'four': 0,
    'capture': 0,
    'inspection': 0,
    'punish': 0,
    'tout': 0,
    'autoCapture': 0,
    'transport': 0
  },
  videoId:""
}

const mutations = {
  SET_MAP: (state, map) => {
    state.$map = map
  },
  SET_VIDEO_TITLE: (state, title) => {
    state.videoTitle = title
  },
  SET_VIDEO_VISIBLE: (state, flag) => {
    state.videoVisible = flag
  },
  SET_CALL_TYPE: (state, obj) => {
    state.callType = obj.type
    state.callArgs = obj
  },
  SET_OVERLAY_STATUS: (state, obj) => {
    state.overLayStatus = obj
  },
  SET_OVERLAY_NUM: (state, obj) => {
    state.overLayNum = obj
  },
  SET_VIDEO_ID: (state, obj) => {
    state.videoId = obj
  },
}

const actions = {

}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
