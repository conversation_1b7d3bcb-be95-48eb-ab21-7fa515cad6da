<template>
  <div class="right-Map">
    <fwmydfx></fwmydfx>
    <fwpjfx></fwpjfx>
    <fwrb></fwrb>
  </div>
</template>

<script>
import fwmydfx from './fwmydfx'
import fwpjfx from './fwpjfx'
import fwrb from './fwrb'
export default {
  name: 'index',

  components: { fwmydfx, fwpjfx, fwrb },
  data() {
    return {}
  },
  watch: {},
  computed: {},
  mounted() {},
  methods: {
    getList() {},
  },
}
</script>

<style scoped lang='less'>
</style>