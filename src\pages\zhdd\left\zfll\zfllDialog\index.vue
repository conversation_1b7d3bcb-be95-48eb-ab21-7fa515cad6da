<template>
  <ygfDialog :visible='visible' width='1415px'>
    <div id="zfll" class="rwgz-tc">
    <div class="rw-title flex-between">
      <div class="fs-44 text-mid-yellow dialogTitle" id="rwTitle" style="margin-left: 20px;">{{type}}</div>
      <div class="close cursor" @click="close" style="margin-right: 20px;"></div>
    </div>
    <div class="content">
      <div class="scrollTab" v-show="type == '乡镇执法' && city == '金华市' || type == '专业领域' && city == '金华市'">
        <div class="zoningTab">
          <div class="scrollBarWrapper" :style="scrollBarWrapperStyle">
            <div
              class="scrollBarContent directionX"
              ref="scrollBarContent"
            >
              <div class="zoning-item" v-for="(item,i) in type == '乡镇执法'?zoningList:zoningList2" :key="i" :class="{activeZoning:i == zoningActive}" @click="zoningActive = i">{{item.name}}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="table">
        <div class="table-line title-line">
          <div class="table-column table-title" style="flex: 1;margin-left: 30px">序号</div>
          <div class="table-column table-title" style="flex: 3">{{["综合执法","其他"].indexOf(type) != -1?"部门":type == "乡镇执法"?"乡镇街道":"领域"}}</div>
          <div class="table-column table-title" style="flex: 1.5;margin-left: 30px">数量</div>
          <div class="table-column table-title" style="flex: 1.5;margin-left: 30px">执法人员</div>
          <div class="table-column table-title" style="flex: 2;margin-left: 30px">执法辅助人员</div>
        </div>
        <div class="table-container" :class="{tableContainer2:type == '乡镇执法' && city == '金华市' || type == '专业领域' && city == '金华市'}">
          <div class="table-line" v-for="(item,i) in tableData" :key="i" :class="{activeTableLine:i % 2 == 0}">
            <div class="table-column" style="flex: 1;margin-left: 30px">{{i + 1}}</div>
            <div class="table-column" style="flex: 3;white-space: nowrap;overflow: hidden;text-overflow: ellipsis" :title="item.name">{{item.name}}</div>
            <div class="table-column" style="flex: 1.5;margin-left: 30px">{{item.value}}</div>
            <div class="table-column" style="flex: 1.5;margin-left: 30px">{{item.zfry}}</div>
            <div class="table-column" style="flex: 2;margin-left: 30px">{{item.zffzry}}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
  </ygfDialog>
</template>

<script>
import ygfDialog from '@/components/ygfDialog'
import { indexApi } from '@/api/indexApi'
export default {
  name: 'index',
  props: ['type','visible'],
  components: {
    ygfDialog
  },
  data() {
    return {
      tableData: [],
      zoningActive:0,
      zoningList:[
        {
          name:"婺城区",
          value:"婺城区"
        },
        {
          name:"金东区",
          value:"金东区"
        },
        {
          name:"兰溪市",
          value:"兰溪市"
        },
        {
          name:"义乌市",
          value:"义乌市"
        },
        {
          name:"东阳市",
          value:"东阳市"
        },
        {
          name:"永康市",
          value:"永康市"
        },
        {
          name:"武义县",
          value:"武义县"
        },
        {
          name:"浦江县",
          value:"浦江县"
        },
        {
          name:"磐安县",
          value:"磐安县"
        }
      ],
      zoningList2:[
        {
          name:"市本级",
          value:"市本级"
        },
        {
          name:"婺城区",
          value:"婺城区"
        },
        {
          name:"金东区",
          value:"金东区"
        },
        {
          name:"兰溪市",
          value:"兰溪市"
        },
        {
          name:"义乌市",
          value:"义乌市"
        },
        {
          name:"东阳市",
          value:"东阳市"
        },
        {
          name:"永康市",
          value:"永康市"
        },
        {
          name:"武义县",
          value:"武义县"
        },
        {
          name:"浦江县",
          value:"浦江县"
        },
        {
          name:"磐安县",
          value:"磐安县"
        }
      ],
      direction: "x",
      city:localStorage.getItem("city")
    }
  },
  computed: {
    scrollBarWrapperStyle() {
      return this.direction === "y"
        ? {
          height: "100%"
        }
        : {
          width: "100%"
        };
    }
  },
  mounted() {

  },
  methods: {
    //获取数据
    getDetail(type) {
      this.city = localStorage.getItem("city")
      switch (type) {
        case "综合执法":
          this.getzhzfList();
          break;
        case "专业领域":
          this.getzyjdList();
          break;
        case "乡镇执法":
          this.getxzzfList();
          break;
        case "其他":
          this.getotherList();
          break;
      }
      // let that = this;
      // axios({
      //   method: "get",
      //   url:
      //     baseURL.url +
      //     "/adm-api/pub/provincial/CommandXzzfj/getWorkNoticeDetails",
      //   params: {
      //     id: id,
      //   },
      // }).then((res) => {
      //   console.log(res);
      //   this.info.rwnr = res.data.data.msg;
      //   this.info.rwjb = res.data.data.level;
      //   this.info.zfjb = res.data.data.personList;
      //   this.info.persons = res.data.data.timelineList;
      //   this.info.status = res.data.data.status;
      //   this.info.startTime = res.data.data.startTime;
      //   this.info.taskSource = res.data.data.taskSource;
      // });
    },
    getzhzfList() {
      indexApi("/csdn_yjyp25",{qxwd:localStorage.getItem("city") == '金华市'?'市本级':localStorage.getItem("city")}).then((res) => {
        this.tableData = res.data.map(item => {return {
          name:item.ywwd1,
          value:(item.tjz.split(",")[0]?Number(item.tjz.split(",")[0].split(":")[1]):0) + (item.tjz.split(",")[1]?Number(item.tjz.split(",")[1].split(":")[1]):0),
          zfry:item.tjz.split(",")[0]?Number(item.tjz.split(",")[0].split(":")[1]):0,
          zffzry:item.tjz.split(",")[1]?Number(item.tjz.split(",")[1].split(":")[1]):0,
          orderid:item.order_id
        }})
        this.tableData.sort((a,b) => {return a.orderid - b.orderid})
      });
    },
    getzyjdList() {
      indexApi("/csdn_yjyp28",{qxwd:localStorage.getItem("city") == "金华市"?this.zoningList2[this.zoningActive].value:localStorage.getItem("city")}).then((res) => {
        this.tableData = res.data.map(item => {return {
          name:item.ywwd1,
          value:(item.tjz.split(",")[0]?Number(item.tjz.split(",")[0].split(":")[1]):0) + (item.tjz.split(",")[1]?Number(item.tjz.split(",")[1].split(":")[1]):0),
          zfry:item.tjz.split(",")[0]?Number(item.tjz.split(",")[0].split(":")[1]):0,
          zffzry:item.tjz.split(",")[1]?Number(item.tjz.split(",")[1].split(":")[1]):0,
        }})
      });
    },
    getxzzfList() {
      indexApi("/csdn_yjyp27",{qxwd:localStorage.getItem("city") == "金华市"?this.zoningList[this.zoningActive].value:localStorage.getItem("city")}).then((res) => {
        this.tableData = res.data.map(item => {return {
          name:item.ywwd1,
          value:(item.tjz.split(",")[0]?Number(item.tjz.split(",")[0].split(":")[1]):0) + (item.tjz.split(",")[1]?Number(item.tjz.split(",")[1].split(":")[1]):0),
          zfry:item.tjz.split(",")[0]?Number(item.tjz.split(",")[0].split(":")[1]):0,
          zffzry:item.tjz.split(",")[1]?Number(item.tjz.split(",")[1].split(":")[1]):0,
        }})
      });
    },
    getotherList() {
      indexApi("/csdn_yjyp25",{qxwd:localStorage.getItem("city"),zbmc:"4"}).then((res) => {
        this.tableData = res.data.map(item => {return {
          name:item.inist_name,
          value:item.zs
        }})
      });
    },
    close() {
      this.$emit('close')
    },


    initItemDisplay() {
      const content = this.$refs.scrollBarContent;
      const contentItem = content.children;
      [].forEach.call(contentItem, item => {
        if (this.direction === "y") {
          item.style.display = "block";
        } else {
          item.style.display = "inline-block";
        }
      });
    },
    handleChange() {
      this.$nextTick(() => {
        const content = this.$refs.scrollBarContent; // 发生滑动的元素
        const activeItem = content.children[this.zoningActive]; // 当前选中的元素
        if(!activeItem) return false;

        const scrollOption = {
          top: 0,
          left: 0,
          behavior: "smooth"
        };

        if (this.direction === "y") {
          const contentHeight = content.offsetHeight;
          const activeItemHeight = activeItem.offsetHeight;
          const activeItemTop = activeItem.offsetTop;
          const offset = activeItemTop - (contentHeight - activeItemHeight) / 2; // 需要移动的位置
          scrollOption.top = offset;
        } else {
          const contentWidth = content.offsetWidth; // 发生滑动元素的宽
          const activeItemWidth = activeItem.offsetWidth; // 当前元素的宽
          const activeItemLeft = activeItem.offsetLeft; // 当前元素的到他父盒子左侧的距离
          const offset = activeItemLeft - (contentWidth - activeItemWidth) / 2; // 需要移动的位置
          scrollOption.left = offset;
        }

        content.scrollTo(scrollOption);
      });
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.$nextTick(() => {
          this.initItemDisplay();
          this.handleChange();
          this.getDetail(this.type);
        })
      }
    },
    zoningActive(newVal, oldVal) {
      this.handleChange();
      this.type == "乡镇执法"?this.getxzzfList():this.getzyjdList();
    }
  }
}
</script>

<style scoped lang='less'>


.scrollBarWrapper {
  position: relative;
  overflow: hidden;
  user-select: none;
  vertical-align: middle;
}

.scrollBarContent {
  width: 100%;
  white-space: nowrap;
  word-break: keep-all;
  -webkit-overflow-scrolling: touch;
}

.directionX {
  overflow-x: scroll;
  overflow-y: hidden;
}

.directionY {
  overflow-x: hidden;
  overflow-y: scroll;
  height: 100%;
}

::-webkit-scrollbar {
  display: none;
}


ul,
ul li {
  list-style: none;
}

.rwgz-tc {
  width: 1215px;
  height: 726px;
  background: url("@/assets/zhdd/dialogBg.png") no-repeat;
  background-size: 100% 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-direction: column;
}

.rw-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 46px 3% 0;
  width: 95%;
  height: 60px;
  line-height: 60px;
}

.close {
  background: url("@/assets/zhdd/close.png") no-repeat;
  width: 34px;
  height: 34px;
}

.ql-indent-1 img {
  width: 100%;
}

img {
  width: 100%;
}

.content {
  width: 1129px;
  height: 850px;
  margin-top: 10px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-direction: column;
}

.zoningTab {
  width: 100%;
  height: fit-content;
  background: linear-gradient(90deg, rgba(0,74,166,0.2) 0%, #004AA6 50%, rgba(0,74,166,0.2) 100%);
  display: flex;
  justify-content: flex-start;
  align-items: center;
  overflow-x: scroll;
}

.zoning-item {
  flex-shrink: 0;
  width: calc(928px / 5);
  height: 70px;
  line-height: 70px;
  text-align: center;
  font-size: 32px;
  font-family: Source Han Sans CN-Regular, Source Han Sans CN;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  display: inline-block;
}

.activeZoning {
  font-size: 34px;
  font-family: Source Han Sans CN-Bold, Source Han Sans CN;
  font-weight: 700;
  color: #FFFFFF;
}

::-webkit-scrollbar {
  width: 0;
  height: 0;
}

.scrollTab {
  width:1129px;
  height:70px;
}

.table-container {
  height: 482px;
  overflow-y: scroll;
}

.tableContainer2 {
  height: 400px;
  overflow-y: scroll;
}

.table-line {
  width: 1129px;
  height: 80px;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  background: rgba(50,134,248,0.15);
}

.title-line {
  background: transparent !important;
}

.table-title {
  font-size: 32px;
  font-family: Source Han Sans CN;
  font-weight: bold;
  color: #FFFFFF;
  text-align: left;
}

.table-column {
  font-size: 32px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  overflow: hidden;
  text-overflow: ellipsis;
}
.activeTableLine {
  background: rgba(50,134,248,0.25);
}
</style>