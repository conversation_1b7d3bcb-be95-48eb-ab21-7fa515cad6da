<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" :inline="true">
      <el-form-item label="日期范围">
        <el-date-picker

          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px;"
          value-format="yyyy-MM-dd hh:mm:ss"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="getList"
        />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <!-- 刷新工具 -->
    <el-row :gutter="10" class="mb8">
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>
    <!-- 主体内容 -->
    <div v-loading="loading" class="container" :class="{ hideSearch: !showSearch }">
      <el-table
        :data="tableData"
        border
        style="width: 100%;"
        height="calc(100vh - 225px)"
      >
        <el-table-column prop="name" label="任务描述" show-overflow-tooltip />
        <el-table-column prop="amount1" label="综合执法" />
        <el-table-column prop="amount2" label="运管执法" />
        <el-table-column prop="amount3" label="交警执法" />
        <el-table-column prop="amount4" label="督查执法" />
      </el-table>
    </div>
  </div>
</template>

<script>
import { getUnionType } from '@/api/case/union/unionType'

export default {
  data() {
    return {
      tableData: [],
      loading: false,
      showSearch: true,
      queryParams: {
        dateRange: [],
        type: ''
      }
    }
  },
  watch: {
    /* 隐藏搜索框重置图表大小 */
    showSearch() {
      this.resizeChart()
    }
  },
  mounted() {
    this.getList()
  },
  destroyed() {

  },
  methods: {
    /* 获取数据 */
    getList() {
      this.loading = true
      const { dateRange, type } = this.queryParams
      let params = {}
      if (dateRange && dateRange.length) params = { ...params, startTime: dateRange[0], endTime: dateRange[1] }
      if (type) params = { ...params, type }
      getUnionType(params).then(res => {
        this.tableData = res.data
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    /* 重置数据 */
    resetQuery() {
      this.queryParams = {
        dateRange: [],
        type: ''
      }
      this.getList()
    },
    /* 页面大小变更，图标自适应 */
    resizeChart() {
      setTimeout(() => {
        this.myChart.resize()
      }, 500)
    }
  }
}
</script>
<style scoped lang="scss">
.app-container {
  .container {
    width: 100%;
    &.hideSearch {
      height: calc(100vh - 170px);
    }
  }
}
</style>
