<template>
  <div>
    <CommonTitle text='行政检查'></CommonTitle>
    <div class='centerContainer'>
      <div class='jgsxs'>
        <div class='zhcyc_item_right' style='align-items: center'>
          <div class='zhcyc_item_top'>{{jgsxs}}  <span class='unit'>个</span></div>
          <div class='zhcyc_item_bottom'>监管事项数</div>
        </div>
      </div>
      <div class="zhcyc">
        <div class='zhcyc_item' v-for='(item,i) in xzjcData' :key='i' :class='{mTop: i > 1}'>
          <div class='zhcyc_item_left' :style="{background: 'url(' + item.icon + ') no-repeat'}"></div>
          <div class='zhcyc_item_right'>
            <div class='zhcyc_item_top'>{{item.value}}  <span class='unit'>{{item.unit}}</span></div>
            <div class='zhcyc_item_bottom'>{{item.name}}</div>
          </div>
        </div>
      </div>
    </div>
    <CommonTitle text='检查计划统筹'></CommonTitle>
    <div class="ptzy_box">
      <div class="ptzy_item" v-for="(item,index) in jcjhtcList">
        <div class='ptzy_item_img' :style="{background: 'url(' + item.icon + ') no-repeat'}" />
        <div class="item_name">{{item.name}}</div>
        <div class="item_bottom">
          <span
            class="s-font-45 xt_font s-yellow"
          >{{item.value}}</span>
          <span
            style='margin-left: 10px;'
            class="s-font-25 s-yellow"
          >{{item.unit}}</span>
        </div>
      </div>
    </div>
    <CommonTitle text='检查任务统筹'></CommonTitle>
    <div class="ptzy_box">
      <div class="ptzy_item" v-for="(item,index) in jcrwtcList">
        <div class='ptzy_item_img' :style="{background: 'url(' + item.icon + ') no-repeat'}" />
        <div class="item_name">{{item.name}}</div>
        <div class="item_bottom">
          <span
            class="s-font-45 xt_font s-yellow"
          >{{item.value}}</span>
          <span
            style='margin-left: 10px;'
            class="s-font-25 s-yellow"
          >{{item.unit}}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import CommonTitle2 from '@/components/CommonTitle2'
import {indexApi} from '@/api/indexApi'
export default {
  name: 'index',
  components: {
    CommonTitle,
    CommonTitle2
  },
  data() {
    return {
      year: localStorage.getItem("year"),
      city: localStorage.getItem("city"),
      //行政检查
      xzjcData: [],
      name:"",
      dialogShow: false,
      jgsxs: '',
      jcjhtcList: [
        {
          icon: require("@/assets/zfts/年度计划数icon.png"),
          name: "年度计划数",
          value: 276,
          unit: "个",
        },
        {
          icon: require("@/assets/zfts/跨部门计划数icon.png"),
          name: "跨部门计划数",
          value: 160,
          unit: "个",
        },
        {
          icon: require("@/assets/zfts/计划完成数icon.png"),
          name: "计划完成数",
          value: 66,
          unit: "个",
        }
      ],
      jcrwtcList: [
        {
          icon: require("@/assets/zfts/总任务数icon.png"),
          name: "总任务数",
          value: 1075,
          unit: "个",
        },
        {
          icon: require("@/assets/zfts/综合查一次icon.png"),
          name: "“综合查一次”任务数",
          value: 168,
          unit: "个",
        },
        {
          icon: require("@/assets/zfts/任务完成数icon.png"),
          name: "任务完成数",
          value: 935,
          unit: "个",
        }
      ],
    }
  },
  computed: {},
  mounted() {
    this.$bus.$on('cityChange', (city) => {
      this.city = city;
      this.initApi(city,localStorage.getItem("year"));
    })
    this.$bus.$on('yearChange', (year) => {
      this.year = year;
      this.initApi(localStorage.getItem("city"),year);
    })
    this.initApi(localStorage.getItem("city"),localStorage.getItem("year"));
  },
  methods: {
    initApi(city,year) {
      //行政检查
      // indexApi("/csdn_yjyp1", { area_code: city,sjwd2: year }).then((res) => {
      //   this.xzjcData = res.data.map((a,i) => ({
      //     name: a.label.split("-")[1],
      //     value: a.num,
      //     unit: a.unit,
      //   }))
      // });

      //监管事项数
      this.jgsxs = 3465;
      this.xzjcData = [
        {
          name: "现场检查户次",
          value: 20397,
          unit: "次",
          icon:require("@/assets/zfts/xcjchc.png")
        },
        {
          name: "“综合查一次”实施率",
          value: 15.62,
          unit: "%",
          icon:require("@/assets/zfts/zhcyc.png")
        },
        {
          name: "应用信用规则率",
          value: 100,
          unit: "%",
          icon:require("@/assets/zfts/yyxygzl.png")
        },
        {
          name: "亮码检查率",
          value: 99.82,
          unit: "%",
          icon:require("@/assets/zfts/lmjcl.png")
        }
      ]
    },
    showjcdxDialog(item) {
      this.name = item.name;
      this.dialogShow = true
    }
  },
  watch: {}
}
</script>

<style scoped lang='less'>
.centerContainer {
  display: flex;
  justify-content: center;
  align-items: center;
}
.jgsxs {
  width: 539px;
  height: 474px;
  background: url("@/assets/zfts/jgsxs.png") no-repeat;
  background-size: 100% 100%;
  position: absolute;
  left: 200px;
  top: 140px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.mTop {
  position: relative;
  top: 110px;
}
/* 行政检查 */
.zhcyc {
  width: 100%;
  height: 530px;
  padding: 20px 0 10px 60px;
  box-sizing: border-box;
  position: relative;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
.zhcyc_item {
  width: 447px;
  height: 96px;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
}
.zhcyc_item_left {
  width: 96px;
  height: 96px;
  background-size: 100% 100%;
}
.zhcyc_item_right {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  margin-left: 20px;
  .zhcyc_item_top {
    font-weight: 700;
    font-size: 48px;
    line-height: 45px;
    text-align: center;
    font-style: normal;
    text-transform: none;
    background: linear-gradient(to bottom, #ffeccb, #ffffff, #ffc460, #ffe2b0, #ffffff);
    -webkit-background-clip: text;
    color: transparent;
    .unit {
      font-size: 26px !important;
      margin-left: -10px;
    }
  }
  .zhcyc_item_bottom {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 32px;
    color: #DCEFFF;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }
}
.quan {
  cursor: pointer;
}

.quan0 {
  position: absolute;
  left: 339px;
  top: -10px;
  width: 308px;
  height: 260px;
  background: url("@/assets/zfts/zrw.png") no-repeat;
  background-size: 100% 100%;
  font-size: 36px;
  color: #fff;
  text-align: center;
  line-height: 60px;
  padding-top: 50px;
  box-sizing: border-box;
}

.quan1 {white-space: nowrap;
  position: absolute;
  left: 100px;
  top: 30px;
  width: 155px;
  height: 155px;
  background: url("@/assets/zfts/rwzb.png") no-repeat;
  background-size: 100% 100%;
  font-size: 28px;
  color: #fff;
  text-align: center;
  line-height: 40px;
  padding-top: 40px;
  box-sizing: border-box;
}

.quan2 {white-space: nowrap;
  position: absolute;
  right: 170px;
  top: 30px;
  width: 155px;
  height: 155px;
  background: url("@/assets/zfts/cybm.png") no-repeat;
  background-size: 100% 100%;
  font-size: 28px;
  color: #fff;
  text-align: center;
  line-height: 40px;
  padding-top: 40px;
  box-sizing: border-box;
}

.quan3 {
  position: absolute;
  left: 150px;
  top: 200px;
  width: 186px;
  height: 186px;
  background: url("@/assets/zfts/jchc.png") no-repeat;
  background-size: 100% 100%;
  font-size: 28px;
  color: #fff;
  text-align: center;
  line-height: 40px;
  padding-top: 50px;
  box-sizing: border-box;
}

.quan4 {
  position: absolute;
  right: 210px;
  top: 195px;
  width: 179px;
  height: 179px;
  background: url("@/assets/zfts/jsqygr.png") no-repeat;
  background-size: 100% 100%;
  font-size: 28px;
  color: #fff;
  text-align: center;
  line-height: 40px;padding: 50px 0px;
  box-sizing: border-box;
}

.quan5 {
  position: absolute;
  left: 400px;
  top: 254px;
  width: 197px;
  height: 197px;
  background: url("@/assets/zfts/rwaswc.png") no-repeat;
  background-size: 100% 100%;
  font-size: 28px;
  color: #fff;
  text-align: center;
  line-height: 40px;
  padding: 40px;
  box-sizing: border-box;
}

.txt0 {
  color: #d958de;
  font-size: 30px;
  font-weight: bold;
}

.txt1 {
  color: #08a0f5;
  font-size: 30px;
  font-weight: bold;
}

.txt2 {
  color: #eed252;
  font-size: 40px;
  font-weight: bold;
}

.txt3 {
  color: #00fffc;
  font-size: 30px;
  font-weight: bold;
}

.txt4 {
  color: #45f95e;
  font-size: 30px;
  font-weight: bold;
}

.txt5 {
  color: #ffb436;
  font-size: 30px;
  font-weight: bold;
}

.quan0 {
  animation: move infinite 5s;
}
.quan1 {
  animation: move infinite 5s 0.5s;
}
.quan2 {
  animation: move infinite 5s 1.2s;
}
.quan3 {
  animation: move infinite 5s 1s;
}
.quan4 {
  animation: move infinite 5s 0.5s;
}
@keyframes move {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(20px);
  }
  100% {
    transform: translateY(0px);
  }
}

.ptzy_box {
  width: 100%;
  height: auto;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  padding: 10px 0 30px 0;
}
.ptzy_item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.ptzy_item_img {
  width: 185px;
  height: 135px;
  background-size: 100% 100%;
}
.item_name {
  width: 200px;
  height: auto;
  font-size: 28px;
  color: #d1d6df;
  text-align: center;
}
.xt_font {
  font-family: DINCondensed;
  //font-style: italic;
}
.s-yellow {
  background: linear-gradient(to bottom, #ffeccb, #ffffff, #ffc460, #ffe2b0, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
}
.s-blue {
  color: #34dfe3;
}
</style>