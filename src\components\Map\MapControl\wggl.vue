<template>
    <div class="wggl">
        <!-- 头 -->
        <div class="tcgl-title">
            <div class="arrow-css" style="margin-right: 10px">
                <img src="@/assets/tcgl/arrow.png" width="22px" height="24px" alt="" />
            </div>
            全科网格(
            <count-to :start-val="0" :end-val="10" :duration="1000"></count-to>
            )
        </div>
        <!-- 网格选择 -->
        <!-- @node-click="getChildren" lazy :load="loadNode" default-expand-all-->
        <div class="sjzx_middle_left_container">
            <el-input placeholder="输入关键字进行过滤" v-model="filterText"> </el-input>
            <el-tree
                :data="treeData"
                show-checkbox
                ref="tree"
                highlight-current
                :props="defaultProps"
                node-key="idName"
                :default-checked-keys="checkedKeys"
                lazy
                :load="loadNode"
                @check="checkChange1"
                :check-strictly="true"
                class="auth-tree"
                :render-after-expand="false"
                icon-class="el-icon-caret-left"
                :filter-node-method="filterNode"
                :default-expanded-keys="['qkwg_jhs']"
            >
                <div style="display: flex; align-items: center" slot-scope="{ node, data }">
                    <div
                        style="
                            line-height: 3.125rem;
                            font-size: 30px;
                            font-weight: bold;
                            color: #c0d6ed;
                            line-height: 58px;
                        "
                    >
                        {{ node.label }}
                        <!-- <span v-if="data.children">({{data.children.length}})</span> -->
                    </div>
                    <!-- <div v-else style="
            line-height: 3.125rem;
            font-size: 30px;
            font-family: PangMenZhengDao;
            font-weight: bold;
            line-height: 58px;
        " class="s-c-blue-gradient1">
        {{ node.label }}
        <span v-if="data.children">({{data.children.length}})</span> -->
                </div>
            </el-tree>
        </div>
    </div>
</template>

<script>
import mapService from '@/components/Map/index.js'
import { indexApi } from '@/api/indexApi'
import countTo from 'vue-count-to'
export default{
    name:'mapComponent',
    components:{ countTo },
    data() {
        return {
            filterText: "",
            treeData: [
                {
                    name: "金华市",
                    url: "",
                    idName: "qkwg_jhs",
                    lat: 29.14978834164311,
                    lng: 119.9387785285524,
                    leaf: false,
                    cj: 0,
                    children: [],
                },
            ],
            firstData: [
                {
                    qx: "金华市",
                    name: "婺城区",
                    url: "",
                    idName: "qkwg_wc",
                    lat: 28.93380203290269,
                    lng: 119.49635566172856,
                    children: [],
                    leaf: false,
                    flag: false,
                    cj: 1,
                },
                {
                    qx: "金华市",
                    name: "金义新区",
                    url: "",
                    idName: "qkwg_jy",
                    lat: 29.16049804730686,
                    lng: 119.79435122905141,
                    children: [],
                    leaf: false,
                    flag: false,
                    cj: 1,
                },
                {
                    qx: "金华市",
                    name: "兰溪市",
                    url: "",
                    idName: "qkwg_lx",
                    lat: 29.279330478334103,
                    lng: 119.43054068980388,
                    children: [],
                    leaf: false,
                    flag: false,
                    cj: 1,
                },
                {
                    qx: "金华市",
                    name: "东阳市",
                    url: "",
                    idName: "qkwg_dy",
                    lat: 29.248251186036732,
                    lng: 120.5201441138766,
                    children: [],
                    leaf: false,
                    flag: false,
                    cj: 1,
                },
                {
                    qx: "金华市",
                    name: "义乌市",
                    url: "",
                    idName: "qkwg_yw",
                    lat: 29.094683075383088,
                    lng: 120.01739085612695,
                    children: [],
                    leaf: false,
                    flag: false,
                    cj: 1,
                },
                {
                    qx: "金华市",
                    name: "永康市",
                    url: "",
                    idName: "qkwg_yk",
                    lat: 28.956095962721335,
                    lng: 120.03588432177509,
                    children: [],
                    leaf: false,
                    flag: false,
                    cj: 1,
                },
                {
                    qx: "金华市",
                    name: "浦江县",
                    url: "",
                    idName: "qkwg_pj",
                    lat: 29.58151310562738,
                    lng: 119.8954545240473,
                    children: [],
                    leaf: false,
                    flag: false,
                    cj: 1,
                },
                {
                    qx: "金华市",
                    name: "武义县",
                    url: "",
                    idName: "qkwg_wy",
                    lat: 28.740013504460414,
                    lng: 119.73219264445578,
                    children: [],
                    leaf: false,
                    flag: false,
                    cj: 1,
                },
                {
                    qx: "金华市",
                    name: "磐安县",
                    url: "",
                    idName: "qkwg_pa",
                    lat: 29.046470243324755,
                    lng: 120.62932165476633,
                    children: [],
                    leaf: false,
                    flag: false,
                    cj: 1,
                },
                {
                    qx: "金华市",
                    name: "开发区",
                    url: "",
                    idName: "qkwg_kf",
                    lat: 29.04554809805949,
                    lng: 119.35362523492347,
                    children: [],
                    leaf: false,
                    flag: false,
                    cj: 1,
                },
            ],
            defaultProps: {
                children: "children",
                label: "name",
                isLeaf: "leaf",
            },
            wgDataUrlEnum2: {
                婺城区: "/grid/wcq.json",
                金义新区: "/grid/jdq.json",
                东阳市: "/grid/dys.json",
                义乌市: "/grid/yws.json",
                永康市: "/grid/yks.json",
                兰溪市: "/grid/lxs.json",
                浦江县: "/grid/pjx.json",
                武义县: "/grid/wyx.json",
                磐安县: "/grid/pax.json",
                开发区: "/grid/kfq.json",
            },
            flag: false,
            // 分页参数
            checkedKeys: ["szwh-whfw-tsg", "szwh-whfw-whg"],
            arrObj: [],
            mapLayers: [],
            qhCheck: [],
            arrs: [],
            datas: [],
        }
    },
    mounted() {
    
    },
    beforeDestroy() {
    },
    methods: {
        filterNode(value, data) {
            if (!value) return true;
            return data.name.indexOf(value) !== -1;
        },
        initFun() {
            let that = this;
        },
        loadNode(node, resolve) {
            if (node.level === 0) {
                return resolve(this.treeData);
            } else if (node.level > 4) return resolve([]);
            else if (node.level === 1) {
                // 二级节点
                return resolve(this.firstData);
            } else if (node.level <= 4) {
                let maptype =
                    node.level == 2 ? "street" : node.level == 3 ? "shequ" : node.level == 4 ? "sqgrid" : "";

                this.getNextGrid(node.data, maptype, resolve);
            }

            // if (node.level === 2) {
            //   // 二级节点
            //   this.getChildrenNode(node, resolve);
            // }
            // if (node.level === 3) {
            //   // 三级节点
            //   this.getThirdChildrenNode(node, resolve);
            // }
            // if (node.level === 4) {
            //   // 四级节点
            //   this.getFourChildrenNode(node, resolve);
            // }
        },
        // 地图查询网格 nodeData节点对象=node.data    type查询网格层级
        async getNextGrid(nodeData, type, resolve) {
            let _this = this;
            if (type == "") resolve([]);
            mapService.findTask({
                type: type,
                key: nodeData.name == "金义新区" ? "金东区" : nodeData.name,
                callback: (res) => {
                    let newCJ = nodeData.cj + 1;
                    _this.datas = res.features.map((item) => {
                        return {
                            name:
                                newCJ == 2
                                    ? item.properties.SZZ
                                    : newCJ == 3
                                    ? item.properties.SZSQ
                                    : item.properties.NAME,
                            children: [],
                            qx: item.properties.SZQX,
                            geometry: item,
                            isLeaf: false,
                            cj: newCJ,
                            flag: false,
                        };
                    });
                },
            });
            setTimeout(() => {
                resolve(this.datas);
            }, 2000);
        },
        // 三级节点--街镇
        getChildrenNode(node, resolve) {
            if (node.data.children.length == 0) {
                axios.get(this.wgDataUrlEnum2[node.data.name]).then((res) => {
                    this.arrObj = res.data.features;
                    let leveldata2 = [];
                    const map = new Map();
                    const newArr = this.arrObj.filter(
                        (v) => !map.has(v.properties.SZZ) && map.set(v.properties.SZZ, 1)
                    );
                    newArr.map((item) => {
                        leveldata2.push({
                            name: item.properties.SZZ,
                            qx: item.properties.SZQX,
                            lat: item.properties.CENTER_Y,
                            lng: item.properties.CENTER_X,
                            children: [],
                            isLeaf: false,
                            cj: 2,
                            flag: false,
                        });
                    });
                    console.log(leveldata2);
                    resolve(leveldata2);
                });
            }
        },
        //四级节点----村社
        getThirdChildrenNode(node, resolve) {
            axios.get(this.wgDataUrlEnum2[node.data.qx]).then((res) => {
                let leveldata3 = [];
                let arr = res.data.features.filter((item) => {
                    return item.properties.SZZ == node.data.name;
                });
                arr.forEach((item) => {
                    leveldata3.push({
                        name: item.properties.SZSQ,
                        children: [],
                        qx: item.properties.SZQX,
                        isLeaf: false,
                        cj: 3,
                        flag: false,
                    });
                });
                resolve(leveldata3);
            });
        },
        //五级节点----网格
        getFourChildrenNode(node, resolve) {
            axios.get(this.wgDataUrlEnum2[node.data.qx]).then((res) => {
                let leveldata4 = [];
                let arr = res.data.features.filter((item) => {
                    return item.properties.SZSQ == node.data.name;
                });
                arr.forEach((item) => {
                    leveldata4.push({
                        name: item.properties.NAME,
                        qx: item.properties.SZQX,
                        cj: 4,
                        flag: false,
                        // children: [], isLeaf: true
                    });
                });
                resolve(leveldata4);
            });
        },
        checkChange(item, flag, node) {
            console.log(item, flag, node);
            if (flag) {
                this.mapLayers.push(item.name);
                this.setShape(item);
            } else {
                this.removeShape(item);
            }
        },
        checkChange1(item, node) {
            console.log(item, node);
            item.flag = !item.flag;
            if (item.name == "金华市" && item.flag == true) {
                sessionStorage.setItem("qhCheck", JSON.stringify(this.firstData.map((item) => item.name)));
            } else if (item.name == "金华市" && item.flag == false) {
                sessionStorage.setItem("qhCheck", JSON.stringify([]));
            } else {
                this.saveQhCheck(JSON.parse(JSON.stringify(item)));
            }
            if (item.flag) {
                this.arrs = this.$refs.tree.getCheckedNodes();
                this.setShape(item);
            } else {
                this.removeShape(item);
            }
        },
        //保存区县勾选项
        saveQhCheck(item) {
            if (sessionStorage.getItem("qhCheck")) {
                this.qhCheck = JSON.parse(sessionStorage.getItem("qhCheck"));
            }
            console.log(item, "click");
            if (item.flag == true && item.cj == 1) {
                this.qhCheck.push({name: item.name, type:"区划"});
            } else if (item.flag == false && item.cj == 1) {
                this.qhCheck.forEach((item2, i) => {
                    if (item2.name == item.name) {
                        this.qhCheck.splice(i, 1);
                    }
                });
            }

            if (item.flag == true && item.cj == 2) {
                this.qhCheck.push({name: item.qx+item.name, type:"乡镇街道"});
            } else if (item.flag == false && item.cj == 2) {
                this.qhCheck.forEach((item2, i) => {
                    if (item2.name.indexOf(item.name) != -1) {
                        this.qhCheck.splice(i, 1);
                    }
                });
            }

            if (item.flag == true && item.cj == 3) {
                this.qhCheck.push({name: item.name, type:"村社"});
            } else if (item.flag == false && item.cj == 3) {
                this.qhCheck.forEach((item2, i) => {
                    if (item2.name == item.name) {
                        this.qhCheck.splice(i, 1);
                    }
                });
            }

            if (item.flag == true && item.cj == 4) {
                this.qhCheck.push({name: item.name, type:"网格"});
            } else if (item.flag == false && item.cj == 4) {
                this.qhCheck.forEach((item2, i) => {
                    if (item2.name == item.name) {
                        this.qhCheck.splice(i, 1);
                    }
                });
            }
            this.qhCheck = this.unique(this.qhCheck)
            sessionStorage.setItem("qhCheck", JSON.stringify(this.qhCheck));
        },
        popDetail(e) {
            this.$bus.$emit("ShowWgPop", e.data)
        },
        setShape(newItem) {
            const that = this;
            if (newItem.cj == 0) {
                let qxArr = [
                    "婺城区",
                    "金义新区",
                    "兰溪市",
                    "东阳市",
                    "义乌市",
                    "永康市",
                    "浦江县",
                    "武义县",
                    "磐安县",
                    "开发区",
                ];
                qxArr.forEach((item) => {
                    axios.get(this.wgDataUrlEnum2[item]).then((res) => {
                        const params = {
                            layerid: item,
                            data: res.data,
                            style: {
                                fillColor: [34, 232, 232, 0.2],
                                strokeColor: [34, 232, 232, 0.9],
                            },
                            onclick: function (e) {
                                console.log(that)
                                that.popDetail(e);
                            },
                        };
                        that.mapLayers.push(item);
                        mapService.wangge(params);
                    });
                });
            }
            if (newItem.cj == 1) {
                axios.get(this.wgDataUrlEnum2[newItem.name]).then((res) => {
                    const params = {
                        layerid: newItem.qx + "-" + newItem.name,
                        data: res.data,
                        style: {
                            fillColor: [34, 232, 232, 0.2],
                            strokeColor: [34, 232, 232, 0.9],
                        },
                        onclick: function (e) {
                            console.log(that)
                            that.popDetail(e);
                        },
                    };
                    that.mapLayers.push(newItem.qx + "-" + newItem.name);
                    mapService.wangge(params);
                    //接下来是把协同数大于0的网格突出显示
                    var i = 0;
                    var feizerolayer = [];
                    res.data.features.forEach((item) => {
                        indexApi("csdn_yjyp_wgsjs_xz",{
                            area_name: "'" + item.properties.SZQX + "'",
                            xz: "'" + item.properties.SZZ + "'",
                            cs: "'" + item.properties.SZSQ + "'",
                            wg: "'" + item.properties.NAME + "'" || "'" + ""  + "'",
                        }).then((res1) => {
                            if (res1.data[0] && res1.data[0].xts > 0) {
                                feizerolayer.push(item)
                            }
                            i++;
                            if (i == res.data.features.length - 1) {
                                that.mapLayers.push(newItem.qx + "-" + newItem.name + 'feizero');
                                mapService.wangge({
                                    layerid: newItem.qx + "-" + newItem.name + 'feizero',
                                    data: {type: "FeatureCollection", features: feizerolayer},
                                    style: {
                                        fillColor: [0, 255, 0, 0.2],
                                        strokeColor: [252, 170, 30, 0.9],
                                    },
                                    zoomToLayer:false,
                                    onclick: function (e) {
                                        that.popDetail(e);
                                    },
                                });
                            }
                        })
                    });
                });
            } else if (newItem.cj == 2) {
                mapService.findTask({
                    type: "grid",
                    key: newItem.name, //这里传街道的名称
                    callback: (res) => {
                        let filterGeo = res.features.filter((a) => a.properties.SZQX == newItem.qx);
                        that.mapLayers.push(newItem.qx + "-" + newItem.name);
                        mapService.wangge({
                            layerid: newItem.qx + "-" + newItem.name,
                            data: {type: "FeatureCollection", features: filterGeo},
                            style: {
                                fillColor: [252, 170, 30, 0.2],
                                strokeColor: [252, 170, 30, 0.9],
                            },
                            onclick: function (e) {
                                that.popDetail(e);
                            },
                        });
                        //下面是把协同数大于0的突出显示
                        var i = 0;
                        var feizerolayer = [];
                        filterGeo.forEach((item, i) => {
                            indexApi("csdn_yjyp_wgsjs_xz",{
                                area_name: "'" + item.properties.SZQX + "'",
                                xz: "'" + item.properties.SZZ + "'",
                                cs: "'" + item.properties.SZSQ + "'",
                                wg: "'" + item.properties.NAME + "'" || "'" + ""  + "'",
                            }).then((res) => {
                                if (res.data[0].xts > 0) {
                                    feizerolayer.push(item)
                                }
                                i++;
                                if (i == filterGeo.length - 1) {
                                    that.mapLayers.push(newItem.qx + "-" + newItem.name + 'feizero');
                                    mapService.wangge({
                                        layerid: newItem.qx + "-" + newItem.name + 'feizero',
                                        data: {type: "FeatureCollection", features: feizerolayer},
                                        style: {
                                            fillColor: [0, 255, 0, 0.2],
                                            strokeColor: [252, 170, 30, 0.9],
                                        },
                                        zoomToLayer:false,
                                        onclick: function (e) {
                                            that.popDetail(e);
                                        },
                                    });
                                }
                            })
                        });
                    },
                });
            } else if (newItem.cj == 3) {
                mapService.findTask({
                    cj:newItem.cj,
                    pre: newItem.qx,
                    type: "sqgrid",
                    key: newItem.name, //这里传街道的名称
                    callback: (res) => {
                        that.mapLayers.push(newItem.qx + "-" + newItem.name);
                        mapService.wangge({
                            layerid: newItem.qx + "-" + newItem.name,
                            data: res,
                            style: {
                                fillColor: [34, 232, 232, 0.2],
                                strokeColor: [34, 232, 232, 0.9],
                            },
                            onclick: function (e) {
                                that.popDetail(e);
                            },
                        });
                        //下面是把协同数大于0的突出显示
                        var i = 0;
                        var feizerolayer = [];
                        res.features.forEach((item, i) => {
                            indexApi("csdn_yjyp_wgsjs_xz",{
                                area_name: "'" + item.properties.SZQX + "'",
                                xz: "'" + item.properties.SZZ + "'",
                                cs: "'" + item.properties.SZSQ + "'",
                                wg: "'" + item.properties.NAME + "'" || "'" + ""  + "'",
                            }).then((res1) => {
                                if (res1.data[0].xts > 0) {
                                    feizerolayer.push(item)
                                }
                                i++;
                                if (i == res.features.length - 1) {
                                    that.mapLayers.push(newItem.qx + "-" + newItem.name + 'feizero');
                                    mapService.wangge({
                                        layerid: newItem.qx + "-" + newItem.name + 'feizero',
                                        data: {type: "FeatureCollection", features: feizerolayer},
                                        style: {
                                            fillColor: [0, 255, 0, 0.2],
                                            strokeColor: [252, 170, 30, 0.9],
                                        },
                                        zoomToLayer:false,
                                        onclick: function (e) {
                                            that.popDetail(e);
                                        },
                                    });
                                }
                            })
                        });
                    },
                });
            } else if (newItem.cj == 4) {
                //   mapService.findTask({
                //     type: "sqgrid",
                //     key: newItem.name, //这里传街道的名称
                //     callback: (res) => {
                if (newItem.geometry) {
                    indexApi("csdn_yjyp_wgsjs_xz", {
                        area_name: "'" + newItem.geometry.properties.SZQX + "'",
                        xz: "'" + newItem.geometry.properties.SZZ + "'",
                        cs: "'" + newItem.geometry.properties.SZSQ + "'",
                        wg: "'" + newItem.geometry.properties.NAME + "'" || "'" + "" + "'",
                    }).then((res1) => {
                        if (res1.data[0].xts > 0) {
                            //下面是把协同数大于0的突出显示
                            that.mapLayers.push(newItem.qx + "-" + newItem.name+ 'feizero');
                            mapService.wangge({
                                layerid: newItem.qx + "-" + newItem.name+ 'feizero',
                                data: { type: "FeatureCollection", features: [newItem.geometry] },
                                style: {
                                    fillColor: [0, 255, 0, 0.2],
                                    strokeColor: "green",
                                },
                                onclick: function (e) {
                                    that.popDetail(e);
                                },
                            });
                        } else {
                            that.mapLayers.push(newItem.qx + "-" + newItem.name);
                            mapService.wangge({
                                layerid: newItem.qx + "-" + newItem.name,
                                data: { type: "FeatureCollection", features: [newItem.geometry] },
                                style: {
                                    fillColor: [30, 231, 204, 0],
                                    strokeColor: "green",
                                },
                                onclick: function (e) {
                                    that.popDetail(e);
                                },
                            });
                        }
                    })
                }
            }
        },
        removeShape(item) {
            console.log(item);

            if (item.name == "金华市") {
                mapService.removeAllLayers([
                    "婺城区",
                    "金义新区",
                    "兰溪市",
                    "东阳市",
                    "义乌市",
                    "永康市",
                    "浦江县",
                    "武义县",
                    "磐安县",
                    "开发区",
                ]);
                return;
            }
            mapService.removeLayer(item.qx + "-" + item.name);
            mapService.removeLayer(item.qx + "-" + item.name+'feizero');
        },
        //去重
        unique(arr) {
            var newArr = arr.filter(function(item,index){
                return arr.indexOf(item) === index;  // 因为indexOf 只能查找到第一个
            });
            return newArr
        }
    },
    watch: {
        filterText(val) {
            this.$refs.tree.filter(val);
        }
    },
}
</script>

<style scoped lang='less'>
* {
    margin: 0;
    padding: 0;
}

.wggl {
    position: absolute;
    width: 600px;
    height: 830px;
    background-image: url("@/assets/tcgl/bg.png");
    background-size: 100% 100%;
    overflow: hidden;
    padding: 20px 30px 30px 30px;
    box-sizing: border-box;
    left: 60px;
    top: 0px;
}

.tcgl-title {
    width: 270px;
    box-sizing: border-box;
    font-size: 32px;
    color: #fff;
    display: flex;
    margin-top: 10px;
}

.sjzx_middle_left_container {
    overflow-y: scroll;
    padding: 20px 20px 0 22px;
    height: 720px;
}

.sjzx_middle_left_container::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 6px;
    /*高宽分别对应横竖滚动条的尺寸*/
    height: 1px;
}

.sjzx_middle_left_container::-webkit-scrollbar-thumb {
    border-radius: 6px;
    background: #20aeff;
    height: 8px;
}

::v-deep .el-checkbox__input {
    float: right;
    margin-right: 30px;
}

::v-deep .el-tree-node__content {
    height: 50px !important;
    margin-bottom: 10px;
}

::v-deep .el-tree-node__label {
    font-size: 30px;
    font-family: PangMenZhengDao;
    font-weight: bold;

    color: #c0d6ed;
    line-height: 58px;
}

::v-deep .el-input__inner {
    height: 50px;
    background-color: #1e3c5c;
    border-radius: 4px;
    border: 1px solid #0a82e0;
    color: #fff;
    font-size: 26px;
}

::v-deep .el-tree {
    background-color: unset;
    margin-top: 10px;
}

::v-deep .el-tree-node__content {
    height: 50px !important;
    margin-bottom: 10px;
    padding: 0 !important;
}

.is-focusable {
    background-color: unset;
}

.is-focusable {
    background-color: unset;
}

.shijian .contain::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 6px;
    /*高宽分别对应横竖滚动条的尺寸*/
    height: 1px;
}

.shijian .contain::-webkit-scrollbar-thumb {
    border-radius: 6px;
    background: #20aeff;
    height: 8px;
}

::v-deep .el-icon-caret-left:before {
    font-size: 20px;
}

::v-deep .el-icon-caret-left:before {
    font-size: 30px;
}

::v-deep .el-tree-node__expand-icon {
    position: absolute;
    right: 0;
}

::v-deep .el-tree-node__label {
    padding-left: 15px;
}

/* .el-tree-node__expand-icon.expanded {
-webkit-transform: rotate(2700deg);
transform: rotate(270deg);
} */
::v-deep .el-tree-node__expand-icon.expanded {
    -webkit-transform: rotate(-90deg);
    transform: rotate(-90deg);
}

::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
    color: #c0d6ed;
}

/* .el-tree-node__content>label.el-checkbox {
position: absolute;
right: 0;
} */

::v-deep .el-checkbox__input.is-checked .el-checkbox__inner,
::v-deep .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #252316;
    border-color: #ffc561;
}

::v-deep .el-checkbox__inner {
    width: 33px;
    height: 33px;
    margin-top: 15px;
    background-color: #344d67;
}

::v-deep .el-tree-node.is-current > .el-tree-node__content,
::v-deep .el-tree-node__content:hover {
    background: linear-gradient(94deg, rgba(3, 97, 156, 0) 0%, #03619c 100%) !important;
    border-radius: 0px 30px 30px 0px;
}

::v-deep .auth-tree .el-checkbox__inner {
    width: 33px;
    height: 33px;
    margin-top: 5px;
    background-color: #344d67;
    border-radius: 10px;
}

::v-deep .el-checkbox__inner::after {
    width: 7px;
    height: 18px;
    left: 37%;
    color: #ffc561 !important;
    /* top: 3px; */
    top: 10%;
}

::v-deep .el-tree-node > .el-tree-node__children {
    margin-left: 30px;
}

</style>