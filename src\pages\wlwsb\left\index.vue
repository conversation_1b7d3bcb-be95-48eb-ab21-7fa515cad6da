<template>
  <div class="left-Map">
    <wlzl></wlzl>
    <glsbtjzb></glsbtjzb>
    <zxsblb></zxsblb>
  </div>
</template>

<script>
import wlzl from './wlzl'
import glsbtjzb from './glsbtjzb'
import zxsblb from './zxsblb'
export default {
  name: 'index',
  components: {
    wlzl,
    glsbtjzb,
    zxsblb,
  },
  data() {
    return {}
  },
  watch: {},
  computed: {},
  mounted() {},
  methods: {
    getList() {},
  },
}
</script>

<style scoped lang='less'>
</style>