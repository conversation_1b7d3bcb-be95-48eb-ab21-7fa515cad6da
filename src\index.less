html {
  font-size: 14px;
  height: 100%;
}

body {
  margin: 0;
  height: 100%;
  font-family: 'Microsoft YaHei' !important;
  // filter: grayscale(100%);
}
//滚动条样式
::-webkit-scrollbar {
  width: 0;
}
//小滚动条
/*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
.min_scroll::-webkit-scrollbar {
  width: 4px;
  height: 4px;
  background-color: #0c1431;
}

/*定义滚动条轨道 内阴影+圆角*/
.min_scroll::-webkit-scrollbar-track {
  /*-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);*/
  border-radius: 4px;
  background-color: #0c1431;
}

/*定义滑块 内阴影+圆角*/
.min_scroll::-webkit-scrollbar-thumb {
  border-radius: 4px;
  /*-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);*/
  background-color: #539ff7;
}

.min_scroll::-webkit-scrollbar-thumb:hover {
  border-radius: 4px;
  /*-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);*/
  background-color: #539ff7;
}

@pageWidth: 1920px;
@pageHeight: 1080px;

// @font-face {
//   font-family: 'pangmen';
//   src: url('@/assets/font/PangMenZhengDaoBiaoTiTi-1.ttf');
// }
// @font-face {
//   font-family: 'DIN-Bold';
//   src: url('@/assets/font/DIN-Bold.otf');
// }
// @font-face {
//   font-family: 'DIN-Black';
//   src: url('@/assets/font/DIN-Black.otf');
// }
// @font-face {
//   font-family: 'DIN-Medium';
//   src: url('@/assets/font/DIN-Medium.otf');
// }
// @font-face {
//   font-family: 'YouSheBiaoTiHei';
//   src: url('@/assets/font/优设标题黑.ttf');
// }

//黄白渐变
.yellow_linear {
  background: linear-gradient(0deg, #fff120 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

//蓝白渐变
.blue_linear {
  background: linear-gradient(0deg, #52c2f7 0%, #fff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
//蓝色渐变
.blue_only_linear {
  background: linear-gradient(0deg, #52c3f7 0%, #a8e0fb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
//绿色渐变
.green_linear {
  background: linear-gradient(0deg, #51f67d 0%, #fff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
//红色渐变
.red_linear {
  background: linear-gradient(0deg, #ff4f34 0%, #fff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
//橙色渐变
.orign_linear {
  background: linear-gradient(0deg, #ff7434 0%, #fff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
//省略号
.cut_text {
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: keep-all;
  white-space: nowrap;
  outline: none;
}
//自定义弹框样式
.custom-class-dialog {
  margin-top: 240px !important;
}
//可点击
.cursor {
  cursor: pointer;
}

@font-face {
  font-family: 'YouSheBiaoTiHei';
  src: url('@/assets/fonts/YouSheBiaoTiHei-2.ttf');
}

/* 关闭winh弹窗样式 */
.top-close {
  width: 120px;
  height: 120px;
  background-image: url('@/assets/common/components/close-1.png');
  background-size: 100%;
}

#zindex_bg {
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(3, 24, 39, 0.6);
  z-index: 0;
  display: block;
}

* {
  margin: 0;
  padding: 0;
}

.c-blue {
  color: #3cfdff;
}

#app {
  overflow: hidden;
  width: 100%;
  height: 100%;
  position: relative;
}

.map_center {
  width: 100%;
  height: 100%;
  top: 0;
  z-index: 5;
  position: absolute;
  background: url('@/assets/index/bg-main.png') no-repeat;
  background-size: 100% 100%;
}

.nowDate i {
  font-size: 40px !important;
  color: #fff;
  margin-right: 30px;
}

/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 0;
}
/* 滚动槽 */
::-webkit-scrollbar-track {
  border-radius: 5px;
}
/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: rgba(35, 144, 207, 0.4);
}
::-webkit-scrollbar-thumb:window-inactive {
  background: rgba(27, 146, 215, 0.8);
}

/* 展开和收起iframe */
.close_left_right_iframe_btn {
  width: 74px;
  height: 74px;
  background-image: url('@/assets/index/oepn_iframe.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  position: absolute;
  z-index: 778;
  right: 20px;
  top: 130px;
  cursor: pointer;
}

.open_left_iframe_btn {
  display: inline-block;
  width: 15px;
  height: 1900px;
  background-image: url('@/assets/index/open_left_btn_bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: absolute;
  left: 0;
  top: 220px;
  z-index: 99;
}

.open_right_iframe_btn {
  width: 15px;
  height: 1900px;
  background-image: url('@/assets/index/open_right_btn_bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: absolute;
  right: 0;
  top: 220px;
  z-index: 99;
}

.open_left_iframe_btn > div {
  width: 58px;
  height: 297px;
  background-image: url('@/assets/index/open_left_btn.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: absolute;
  left: 15px;
  top: calc(50% - 148px);
  z-index: 99;
}

.open_right_iframe_btn > div {
  width: 58px;
  height: 297px;
  background-image: url('@/assets/index/open_right_btn.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: absolute;
  right: 15px;
  top: calc(50% - 148px);
  z-index: 99;
}

/* 移动位置 */
.map_mapIcon_move {
  left: 3700px !important;
  top: 244px !important;
}

.btns_move {
  left: 30px !important;
}

.back_home {
  width: 78px;
  height: 48px;
  /* background: url('/static/citybrain/') no-repeat; */
  z-index: 888;
  position: absolute;
  z-index: 778;
  right: 105px;
  top: 142px;
  display: none;
  cursor: pointer;
}

/* 地图弹窗样式 */
.mapPopup {
  position: fixed;
  z-index: 1;
  min-width: 300px;
  background: none;
  box-shadow: none;
  transform-origin: 50% 100%;
  transition: transform 0.3s ease-out;
}

.show {
  transform: translate(-50%, calc(-100% - 16px)) scale(1);
}

.hide {
  transform: translate(-50%, calc(-100% - 16px)) scale(0);
}

.esri-view-surface {
  height: 2160px !important;
}
.mapPopup .header {
  display: none;
}

.mapPopup .body {
  position: relative;
}

.mapPopup .body::before {
  display: none;
}

.mapPopup .bodyContent {
  z-index: 1;
  padding: 0px;
}

.mapPopup .container {
  height: 100%;
  width: 100%;
}
.bounce {
  animation: 1s rowUp linear infinite alternate;
  -webkit-animation: 1s rowUp linear infinite alternate;
}
@keyframes rowUp {
  0% {
    -webkit-transform: translateY(20px);
    transform: translateY(20px);
  }

  100% {
    transform: translateY(0px);
  }
}

.left {
  padding: 50px 0;
  box-sizing: border-box;
  width: 1030px;
  height: 1960px;
  background: url('@/assets/index/left-bg.png') no-repeat;
  background-size: 100% 100%;
  position: absolute;
  top: 210px;
  z-index: 3;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-content: center;
}

.left-noMap {
  padding: 50px 0;
  box-sizing: border-box;
  width: 1030px;
  height: 1960px;
  background: url('@/assets/index/left-bg.png') no-repeat;
  background-size: 100% 100%;
  z-index: 2;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-content: center;
  margin-top: 220px;
}

.right {
  padding: 50px 0;
  box-sizing: border-box;
  width: 1030px;
  height: 1960px;
  background: url('@/assets/index/right-bg.png') no-repeat;
  background-size: 100% 100%;
  position: absolute;
  top: 210px;
  right: 0;
  z-index: 2;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-content: center;
}

.right-noMap {
  padding: 50px 0;
  box-sizing: border-box;
  width: 1030px;
  height: 1960px;
  background: url('@/assets/index/right-bg.png') no-repeat;
  background-size: 100% 100%;
  z-index: 2;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-content: center;
  margin-top: 220px;
}

.center-noMap {
  width: 1760px;
  height: 1960px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-content: center;
  margin-top: 220px;
}

.bottom {
  width: 1760px;
  height: fit-content;
  background: url('@/assets/index/bottom-bg.png') no-repeat;
  background-size: 100% 100%;
  position: absolute;
  left: 1040px;
  bottom: 1px;
  z-index: 2;
}

.wrap-bg {
  width: 100%;
  height: fit-content;
  background: url('@/assets/index/wrap-bg.png') no-repeat;
  background-size: 100% 100%;
}

.pageContainer {
  position: relative;
  width: 100%;
  height: 100%;
}

.pageContainer-noMap {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-around;
  align-items: center;
  background: #0c1431;
  position: fixed;
  z-index: 2;
}

.pageContainer-Map {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-around;
  align-items: flex-end;
  // position: fixed;
  // z-index: 2;
  .left-Map {
    padding: 50px 0;
    box-sizing: border-box;
    width: 1030px;
    height: 1960px;
    background: #091932;
    z-index: 2;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-content: center;
    margin-top: 220px;
    position: relative;
    &::after {
      content: '';
      width: 1019px;
      height: 115px;
      position: absolute;
      top: -50px;
      background: url('@/assets/index/line-top-bg.png') no-repeat;
      background-size: 100% 100%;
    }
  }

  .right-Map {
    padding: 50px 0;
    box-sizing: border-box;
    width: 1030px;
    height: 1960px;
    background: #091932;
    z-index: 2;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-content: center;
    margin-top: 220px;
    position: relative;
    &::after {
      content: '';
      width: 1019px;
      height: 115px;
      position: absolute;
      top: -50px;
      background: url('@/assets/index/line-top-bg.png') no-repeat;
      background-size: 100% 100%;
    }
  }

  .center-Map {
    width: 1760px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-content: center;
    background: #091932;
    position: relative;
    bottom: 0;
    z-index: 2;
    &::after {
      content: '';
      width: 1019px;
      height: 115px;
      position: absolute;
      top: -50px;
      left: 50%;
      transform: translateX(-50%);
      background: url('@/assets/index/line-top-bg.png') no-repeat;
      background-size: 100% 100%;
    }
  }
}

/* 一级标题 */
.hearder_h1 {
  height: 75px;
  line-height: 75px;
  font-size: 78px;
  font-weight: 500;
  color: #ffffff;
  margin-left: 20px;
  background: url('@/assets/index/header.png') no-repeat;
  background-size: 100% 100%;
}

.titleText {
  margin-left: 70px;
  margin-bottom: 40px;
  background: linear-gradient(to bottom, #7cb2ff, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
  font-family: YouSheBiaoTiHei;
  font-size: 72px;
}

/* 二级标题 */
.hearder_h2 {
  font-size: 36px;
  background: url('@/assets/common/header/header2.png') no-repeat;
  background-size: 100%;
  background-position: bottom;
  // background-position: bottom;
  height: 64px;
  margin-left: 29px;
}

.hearder_h2 > span {
  margin-left: 45px;
  background: linear-gradient(0deg, #acddff 0%, #ffffff 100%);
  -webkit-background-clip: text;
  color: transparent;
}

.el-date-editor .el-range-input {
  color: #eee !important;
  width: 100%;
  background: transparent;
  font-size: 24px;
}
.el-date-range-picker__time-header .el-input__inner {
  background-color: transparent !important;
  border: none !important;
}
.el-picker-panel {
  transform: scale(1.2);
  color: #fff;
  background-color: #132c4e;
  position: relative !important;
  top: 200px !important;
  left: 0;
}
.popper__arrow {
  display: none !important;
}
.el-date-editor .el-range-separator {
  color: #fff;
  line-height: 38px;
  font-size: 20px;
}
.el-month-table td.in-range div,
.el-month-table td.in-range div:hover {
  background-color: #39537a;
}
.el-date-range-picker__content .el-date-range-picker__header div {
  font-size: 23px;
}
.el-month-table {
  font-size: 22px;
  white-space: nowrap;
}
.el-date-editor .el-range__icon {
  font-size: 20px;
  line-height: 39px;
}
.el-date-editor .el-range__close-icon {
  font-size: 20px;
  line-height: 40px;
}

.el-date-table td.in-range div,
.el-date-table td.in-range div:hover,
.el-date-table.is-week-mode .el-date-table__row.current div,
.el-date-table.is-week-mode .el-date-table__row:hover div {
  background-color: #315a97;
}

.el-picker-panel__icon-btn {
  font-size: 12px;
  color: #ffffff;
  border: 0;
  background: 0 0;
  cursor: pointer;
  outline: 0;
  margin-top: 8px;
}

.iconPhone {
  cursor: pointer;
  background: #51c422;
  padding: 5px 7px 0px 7px;
  border-radius: 4px;
  height: 40px;
  width: 34px;
  line-height: 50px;
}
.iconPhone::after {
  content: '';
  display: inline-block;
  width: 32px;
  height: 32px;
  background: url('@/assets/common/icon_call.png') no-repeat;
  background-size: cover;
}
.iconVideo {
  cursor: pointer;
  background: #22a0c4;
  padding: 5px 7px 0px 7px;
  border-radius: 4px;
  height: 40px;
  width: 34px;
  line-height: 50px;
  margin-left: 10px;
}
.iconVideo::after {
  content: '';
  display: inline-block;
  width: 32px;
  height: 32px;
  background: url('@/assets/common/video.png') no-repeat;
  background-size: cover;
}

.iconPhone2 {
  cursor: pointer;
  background: #51c422;
  padding: 8px 15px;
  border-radius: 4px;
  height: 40px;
  width: 34px;
  line-height: 50px;
}
.iconPhone2::after {
  content: '';
  display: inline-block;
  width: 32px;
  height: 32px;
  background: url('@/assets/zhdd/icon_call2.png') no-repeat;
  background-size: cover;
}
.iconVideo2 {
  cursor: pointer;
  background: #22a0c4;
  padding: 8px 15px;
  line-height: 45px;
  border-radius: 4px;
  height: 40px;
  width: 34px;
  line-height: 50px;
}
.iconVideo2::after {
  content: '';
  display: inline-block;
  width: 32px;
  height: 32px;
  background: url('@/assets/zhdd/video2.png') no-repeat;
  background-size: cover;
}
@keyframes breath {
  from {
    opacity: 0.3;
  }
  50% {
    opacity: 1;
  }
  to {
    opacity: 0.3;
  }
}

@-webkit-keyframes breath {
  from {
    opacity: 0.3;
  }
  50% {
    opacity: 1;
  }
  to {
    opacity: 0.3;
  }
}

/* 呼吸灯 */

.breath-light {
  /* IE10、Firefox and Opera，IE9以及更早的版本不支持 */
  animation-name: breath; /* 动画名称 */
  animation-duration: 3s; /* 动画时长3秒 */
  animation-timing-function: ease-in-out; /* 动画速度曲线：以低速开始和结束 */
  animation-iteration-count: infinite; /* 播放次数：无限 */

  /* Safari and Chrome */
  -webkit-animation-name: breath; /* 动画名称 */
  -webkit-animation-duration: 3s; /* 动画时长3秒 */
  -webkit-animation-timing-function: ease-in-out; /* 动画速度曲线：以低速开始和结束 */
  -webkit-animation-iteration-count: infinite; /* 播放次数：无限 */
}

.dialogTitle {
  font-size: 44px;
  background-image: linear-gradient(180deg, #ffb637, #ffb637, #fff, #ffb637, #ffb637);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.el-cascader-menu {
  min-width: 200px !important;
  background-color: #052347 !important;
  color: #fff !important;
  border: 1px solid #314662 !important;
  padding: 5px 10px !important;
}

.el-cascader-menus {
  width: 600px;
  display: flex;
  flex-wrap: wrap;
}

.el-cascader-menu__item {
  font-size: 26px;
}

.el-cascader__tags {
  width: 100%;
  max-height: 81px;
  overflow: hidden;
  overflow-y: scroll;
  top: 44%;
}

.el-cascader__tags::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 6px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
}

.el-cascader__tags::-webkit-scrollbar-thumb {
  border-radius: 6px;
  background: #314662;
  height: 8px;
}

.el-cascader-node {
  font-size: 30px;
  margin-bottom: 10px;
  padding: 0 30px 0 0 !important;
}
.el-cascader-node:not(.is-disabled):focus, .el-cascader-node:not(.is-disabled):hover {
  background: #094194ab;
}

.el-cascader-panel .el-cascader-menu:nth-child(2) {
  /*min-width: 350px !important;*/
}

.el-cascader-panel .el-cascader-menu:nth-child(1) li .el-checkbox {
  display: none !important;
}

.el-cascader-panel .el-cascader-menu:nth-child(3) li .el-cascader-node__postfix {
  /*display: none !important;*/
}

.el-cascader__dropdown {
  /*top: 320 !important;*/
  transform: scale(0.5);
  left: 310px !important;
  background: transparent !important;
  border: 0;
}

.el-cascader-panel {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  width: 1300px;
  height: fit-content;
  position: relative;
  /*right: 50px;*/
}

.el-cascader-menu__wrap {
  height: 270px;
}

.el-cascader-node__label {
  width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.el-cascader .el-input .el-icon-arrow-down {
  font-size: 26px;
  color: #fff;
  line-height: 26px;
}

.el-cascader {
  width: 600px;
  height: 50px;
  line-height: 50px;
}

.el-cascader .el-input__suffix {
  margin-right: 20px;
  margin-top: 2px;
}

.el-cascader-panel {
  font-size: 23px !important;
}

.mgt48{
  margin-top: 48px;
}