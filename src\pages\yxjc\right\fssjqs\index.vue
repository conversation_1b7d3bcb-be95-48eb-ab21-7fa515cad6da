<template>
  <div>
    <CommonTitle2 text="发生事件趋势"></CommonTitle2>
    <div id="fssjqs" style="width: 100%; height: 430px; margin-bottom: 30px"></div>
  </div>
</template>

<script>
import CommonTitle2 from '@/components/CommonTitle2'
import { geteventTrend } from '@/api/yxjc/index.js'

export default {
  name: 'index',
  components: {
    CommonTitle2,
  },
  data() {
    return {
      year: '',
      lineChartData: [],
    }
  },
  computed: {},
  mounted() {
    this.year = localStorage.getItem('year')
    this.init()
    this.$bus.$on('yearChange', (res) => {
      if (res !== this.year) {
        this.year = res
        this.init()
      }
    })
  },
  methods: {
    init() {
      geteventTrend({ year: this.year }).then((res) => {
        this.lineChartData = res.data.map((item) => {
          return {
            name: item.month,
            value: item.totalCount,
          }
        })
        this.initCharts(this.lineChartData)
      })
    },
    initCharts(data) {
      const chartDom = document.getElementById('fssjqs')
      const myChart = this.$echarts.init(chartDom)

      const option = {
        color: ['#E0D722'],
        grid: {
          left: '7%',
          right: '7%',
          bottom: '5%',
          top: '20%',
          containLabel: true,
        },
        legend: {
          icon: 'circle',
          right: '60',
          top: '20',
          itemGap: 40,
          textStyle: {
            color: '#D6E7F9',
            fontSize: 28,
          },
        },
        tooltip: {
          trigger: 'axis',
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        xAxis: {
          type: 'category',
          data: data.map((item) => item.name),
          axisLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.6)',
            },
          },
          axisLabel: {
            textStyle: {
              fontSize: 24,
            },
          },
        },
        yAxis: {
          name: '单位:件',
          nameTextStyle: {
            color: 'rgba(255,255,255,0.6)',
            padding: [20, 10, 10, 10],
            fontSize: 24,
          },
          type: 'value',
          axisLabel: {
            textStyle: {
              fontSize: 24,
            },
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.6)',
            },
          },
          splitLine: {
            lineStyle: {
              color: '#4A6C89',
            },
          },
        },
        series: [
          {
            data: data.map((item) => item.value),
            type: 'line',
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(224,215,34,0.30)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(224,215,34, 0)',
                  },
                ],
              },
            },
            lineStyle: {
              color: '#E0D722',
            },
            smooth: true,
            symbol: 'none',
          },
        ],
      }

      myChart.setOption(option)

      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
</style>