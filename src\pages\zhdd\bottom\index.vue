<template>
  <div class='bottom' style='height: 600px'>
    <CommonTitle text='舆情中心'>
      <div class='zhddTitleBar'>
        <div class="buttons" @click="openDdsj">调度事件</div>
      </div>
    </CommonTitle>
    <div class="table_box">
        <TableComponent
        :thConfig="thConfig"
        :tableData="tableData"
        :tableHeight="425"
        :autoScroll="true"
        :scrollInterval="1500"
        @infoClick="showYqzxDetail"
      />
    </div>

    <bottomDialog :visible='visible' :yqMsg='yqMsg' @close='visible = false' @updateList='initApi(city)'></bottomDialog>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import TableComponent from '@/components/TableComponent'
import { indexApi } from '@/api/indexApi'
import bottomDialog from './bottomDialog'
import { getAdminToken } from '@/api/zhdd'
export default {
  name: 'index',
  components: {
    CommonTitle,
    bottomDialog,
    TableComponent
  },
  data() {
    return {
      year: localStorage.getItem("year"),
      city: localStorage.getItem("city"),
      zfdt: [],
      dom1: null,
      time1: null,
      thConfig: [
        { th: '标题', field: 'text', width: '50%', hover: true },
        { th: '来源', field: 'source', width: '15%', hover: true },
        { th: '地区', field: 'dept_name_format', width: '15%', hover: true },
        { th: '时间', field: 'time', width: '20%', hover: true }
      ],
      tableData: [],
      yqMsg: {},
      visible: false
    }
  },
  computed: {},
  mounted() {
    this.$bus.$on('cityChange', (city) => {
      this.city = city;
      this.initApi(city,localStorage.getItem("year"));
    })
    this.$bus.$on('yearChange', (year) => {
      this.year = year;
      this.initApi(localStorage.getItem("city"),year);
    })
    this.$bus.$on('glnr', (e) => {
      if (e.data && e.data.glid) {
        this.glChange(e.data.glid);
      }
    })
    this.$bus.$on('updateYqList', (e) => {
      this.initApi(localStorage.getItem("city"));
    })
    this.initApi(localStorage.getItem("city"),localStorage.getItem("year"));
  },
  methods: {
    initApi(city) {
      let this_ = this;
      indexApi("/xzzf_qylb", { qx: city == "金华市" ? "" : city }).then(
        (res) => {
          this_.zfdt = res.data;
          this_.formatTableData();
        }
      );
    },
    formatTableData() {
      this.tableData = this.zfdt.map(item => {
        return {
          ...item,
          source: item.source || '腾讯网',
          dept_name_format: item.dept_name.split(',')[0] || '-',
          color: item.state == 0 ? 'yellow' : ''
        }
      });
    },
    // 关联任务
    glChange(id) {
      let findI = this.zfdt.findIndex((a) => a.id == id);
      if (findI != -1) {
        if (this.glid != id) {
          this.glid = id;
          this.zfdt.forEach((item) => {
            if (item.id == id) {
              item.isLight = 1;
            } else {
              item.isLight = 0;
            }
          });
          this.zfdt.sort(function (a, b) {
            return b.isLight - a.isLight;
          });
          this.formatTableData();
        } else {
          this.glid = null;
          this.zfdt.forEach((item) => {
            item.isLight = 0;
          });
          this.$forceUpdate();
          this.formatTableData();
        }
      } else {
        this.$message({
          message: "该任务未关联舆情信息",
          type: "warning",
        });
      }
    },
    openDdsj() {
      getAdminToken({}).then(res => {
        let token = res.data.msg;
        window.cookieStore.set("YGF-Token", token);
        window.open(
          "https://csdn.dsjj.jinhua.gov.cn:8303/direct/direction"
        );
      });
    },
    showYqzxDetail(item) {
      this.$bus.$emit('glid',item.id)
      this.yqMsg = item;
      this.visible = true;
    }
  },
  watch: {}
}
</script>

<style scoped lang='less'>
  .zhddTitleBar {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    .buttons {
      width: 236px;
      height: 80px;
      background: url("@/assets/zhdd/btn.png");
      background-size: 100% 100%;
      color: #9fc9e9;
      cursor: pointer;
      font-size: 28px;
      text-align: center;
    }
  }

  /* 表格样式================================== */
  .table_box {
    width: 100%;
    padding: 0 20px;
    box-sizing: border-box;
    height: 600px;
    overflow: hidden;
  }
</style>