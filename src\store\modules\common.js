const state = {
  active_cityName: localStorage.getItem('city') || localStorage.getItem('adminCity') || ""
}

const getters = {
  activeCityName: state => state.active_cityName
}

const mutations = {
  setActiveCityName(state, cityName) {
    state.active_cityName = cityName;
    localStorage.setItem('city', cityName);
  }
}

const actions = {
  updateActiveCityName({ commit }, cityName) {
    commit('setActiveCityName', cityName);
  },
  initActiveCityName({ commit, state }) {
    // 如果没有设置active_cityName，则使用adminCity
    if (!state.active_cityName) {
      const adminCity = localStorage.getItem('adminCity');
      if (adminCity) {
        commit('setActiveCityName', adminCity);
      }
    } else {
      commit('setActiveCityName', state.active_cityName);
    }
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}