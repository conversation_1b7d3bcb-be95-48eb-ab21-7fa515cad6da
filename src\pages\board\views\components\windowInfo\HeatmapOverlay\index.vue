<template>
  <div v-if="visible" class="largeWindowInfo">
    <!-- 右侧部分 -->
    <div v-loading="rightLoading" class="right">
      <div class="search-top">
        <el-date-picker v-model="dateRange" value-format="yyyy-MM-dd 00:00:00" unlink-panels style="width: 400px;" size="mini" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="handleChange" />
        <span class="close" @click="handleClose">×</span>
      </div>
      <!-- 地图 -->
      <div id="window-map" ref="map" />
    </div>
  </div>
</template>

<script>
import { listAllCase } from '@/api/board/map/index'
export default {
  filters: {
    sexName(sex) {
      const sexName = { 1: '女', 0: '男', 3: '未知' }
      return sexName[sex || 3]
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    detailId: {
      type: Number
    }
  },
  data() {
    return {
      dataDetail: {},
      dateRange: [],
      $wMap: null,
      rightLoading: false
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.mapInit()
      }
    }
  },
  methods: {
    mapInit() {
      this.$nextTick(() => {
        this.$wMap = new window.T.Map(this.$refs.map)
        this.$wMap.centerAndZoom(new window.T.LngLat(119.63126, 29.11181), 18)
        this.setDefaultDate()
        this.handleChange()
      })
    },
    handleChange() {
      this.rightLoading = true
      let params = {}
      if (this.dateRange[0]) params.searchStartTime = this.dateRange[0]
      if (this.dateRange[1]) params.searchEndTime = this.dateRange[1]
      listAllCase(params).then(res => {
        const points = res.data.event.map(item => {
          return {
            lat: item.latitude,
            lng: item.longitude,
            name: item.name,
            count: 150
          }
        })
        if (this.$wMap) this.$wMap.clearOverLays()
        this.HeatmapOverlay = new window.T.HeatmapOverlay({'radius': 30})
        this.$wMap.addOverLay(this.HeatmapOverlay)
        this.HeatmapOverlay.setDataSet({data: points, max: 300})
        this.rightLoading = false
      }).catch(() => {
        this.rightLoading = false
      })
    },
    setDefaultDate() {
      const nowDate = new Date()
      const ym = this.parseTime(nowDate, '{y}-{m}')
      const endofMon = new Date(nowDate.getFullYear(), nowDate.getMonth() + 1, 0).getDate()
      this.dateRange = [`${ym}-01 00:00:00`, `${ym}-${endofMon} 00:00:00`]
    },
    handleClose() {
      this.dateRange = []
      this.dataDetail = {}
      this.$wMap = null
      this.HeatmapOverlay = null
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style scoped lang="scss">
.largeWindowInfo {
  width: pxtorem(1400);
  height: pxtorem(790);
  position: absolute;
  top: pxtorem(100);
  left: pxtorem(230);
  background: #fff;
  z-index: 1000;
  box-shadow: 0 4px 14px rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  .right {
    position: absolute;
    left: pxtorem(20);
    top: pxtorem(20);
    bottom: pxtorem(20);
    right: pxtorem(20);
    .search-top {
      height: pxtorem(30);
      .el-date-editor.el-input {
        width: 220px;
        ::v-deep .el-input__inner {
          background: #eee;
        }
      }
      .close {
        position: absolute;
        right: 0;
        font-size: 20px;
        color: #c3c3c3;
        cursor: pointer;
        &:hover {
          color: #999;
        }
      }
    }
    #window-map {
      position: absolute;
      top: pxtorem(40);
      left: 0;
      right: 0;
      bottom: 0;
      border: 1px solid #e9e9e9;
    }
    .ctrl-btn {
      position: absolute;
      top: pxtorem(60);
      right: pxtorem(20);
      background: #fff;
      box-shadow: 0 2px 6px 0 rgb(114 124 245 / 50%);
      z-index: 500;
      padding: 10px;
      border-radius: 5px;
    }
  }
}
</style>
