<template>
  <div class="vol-statistics">
    <!-- 统计数字 -->
    <div class="top">
      <div v-for="(item,idx) in itemList" :key="idx" class="item" @click="handleOpenVolList">
        <span class="num">{{ numData[item.key] || 0 }}</span>
        <span>{{ item.name }}</span>
      </div>
    </div>
    <!-- 动态列表 -->
    <div class="m-table">
      <div class="m-header">
        <span>发布人</span>
        <span>标题</span>
        <span>服务时间</span>
      </div>
      <!-- 主体内容 -->
      <div v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.8)" class="m-body" @click="clickItem">
        <vueSeamlessScroll :data="tableList" :class-option="defaultOption">
          <div v-for="item in tableList" :key="item.id" class="m-tr" :data-id="item.id">
            <span>{{ item.createBy | decryptVal }}</span>
            <span>{{ item.title }}</span>
            <span>{{ item.happenTime }}</span>
          </div>
          <div v-if="!tableList.length" class="m-tr">
            暂无数据
          </div>
        </vueSeamlessScroll>
      </div>
    </div>
    <!-- 志愿者弹窗 -->
    <el-dialog title="志愿者列表" :visible.sync="visible" width="80%">
      <!-- 主体 -->
      <vol-list ref="volList" />
      <div slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 动态详情弹窗 -->
    <el-dialog title="服务记录" :visible.sync="serveVisible" width="80%" @open="handleOpenNews">
      <!-- 主体 -->
      <news-detail ref="newsDetail" />
      <div slot="footer" class="dialog-footer">
        <el-button @click="serveVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { countVolUser, getNewsList } from '@/api/board/dataView/index.js'
import { AESDecrypt } from '@/utils/aesutil'
import vueSeamlessScroll from 'vue-seamless-scroll'
import VolList from './components/volList/index.vue'
import NewsDetail from './components/newsDetail/index.vue'

export default {
  name: 'VolStatistics',
  components: {
    vueSeamlessScroll,
    VolList,
    NewsDetail
  },
  filters: {
    decryptVal(value) {
      if (!value) return
      let res =  AESDecrypt(value, 'ivqpsFQwQqxYUr7f')
      return res || value
    }
  },
  data() {
    return {
      numData: {},
      itemList: [
        { key: 'totalVolUserNum', name: '志愿者总人数' },
        { key: 'hoursOfService', name: '服务总时长(小时)' },
        { key: 'serviceTimes', name: '总服务次数' },
        { key: 'onDutyVolUserNum', name: '在岗志愿者' }
      ],
      loading: false,
      tableList: [],
      visible: false,
      serveVisible: false,
      newsId: ''
    }
  },
  computed: {
    defaultOption() {
      return {
        step: 0.2, // 数值越大速度滚动越快
        limitMoveNum: 3, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 1000 // 单步运动停止的时间(默认值1000ms)
      }
    }
  },
  mounted() {
    this.fetchData()
  },
  methods: {
    handleOpenVolList() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.volList.reLoadList()
      })
    },
    handleOpenNews() {
      this.$nextTick(() => {
        this.$refs.newsDetail.fetchData(this.newsId)
      })
    },
    fetchData() {
      Promise.all([
        countVolUser(),
        getNewsList({ type: 1, approveType: 0, happenTime: this.parseTime(new Date()) })
      ]).then(resAry => {
        this.numData = resAry[0].data
        this.tableList = resAry[1].rows
      }).catch(() => {})
    },
    clickItem(e) {
      const id = e.target.parentNode.dataset.id
      if (id) {
        this.newsId = id
        this.serveVisible = true
      }
    }
  }
}
</script>

<style scoped lang="scss">
.vol-statistics {
  width: 100%;
  height: 100%;
  .top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: pxtorem(70);
    .item {
      display: flex;
      flex-direction: column;
      align-items: center;
      font-size: pxtorem(14);
      cursor: pointer;
      .num {
        color: #00f7ff;
        font-size: pxtorem(28);
        font-weight: 700;
      }
    }
  }
  .m-table {
    height: calc(100% - 0.36458rem);
    .m-header {
      height: pxtorem(28);
      line-height: pxtorem(28);
      background: url(@/assets/images/m-header.png) no-repeat center center / 100% 100%;
      font-size: pxtorem(14);
      display: flex;
      padding: 0 10px 0 10px;
      span {
        text-align: center;
        flex: 1;
      }
    }
    .m-body {
      height: calc(100% - 0.14583rem);
      overflow: hidden;
      .m-tr {
        overflow: hidden;
        height: pxtorem(26);
        line-height: pxtorem(26);
        font-size: pxtorem(12);
        color: #00f7ff;
        background: rgba(0, 0, 0, 0.3);
        padding: 0 10px;
        margin-top: 5px;
        text-align: center;
        cursor: pointer;
        span {
          width: 33.3%;
          height: pxtorem(26);
          float: left;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
      }
    }
  }
}
</style>
