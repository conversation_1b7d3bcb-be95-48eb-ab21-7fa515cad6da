<template>
  <div>
    <CommonTitle text='案件类型'></CommonTitle>
    <div class='wrap-container'>
      <div class='wrap-container-box'>
        <div class='wrap-container-box-item' v-for='(item,i) in indexList' :key='i'>
          <div class='wrap-container-box-item-number'>{{item.value}}</div>
          <div class='wrap-container-box-item-text'>{{item.name}}</div>
        </div>
      </div>
      <div
        id="chartAjlx"
        style="width: 1030px; height: 470px;margin-top: 90px"
      ></div>
    </div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import { getAjlxtj } from '@/api/dataView'
export default {
  name: 'index',
  components: {
    CommonTitle
  },
  data() {
    return {
      indexList: [
        {
          name: "综合执法",
          value: 34
        },
        {
          name: "运管执法",
          value: 28
        },
        {
          name: "交警执法",
          value: 26
        }
      ],
      chartData: [
        {
          name:"1月",
          zhzf: 34,
          ygzf: 44,
          jjzf: 54
        },
        {
          name:"2月",
          zhzf: 34,
          ygzf: 44,
          jjzf: 54
        },
        {
          name:"3月",
          zhzf: 34,
          ygzf: 44,
          jjzf: 54
        },
        {
          name:"4月",
          zhzf: 34,
          ygzf: 44,
          jjzf: 54
        },
        {
          name:"5月",
          zhzf: 34,
          ygzf: 44,
          jjzf: 54
        },
        {
          name:"6月",
          zhzf: 34,
          ygzf: 44,
          jjzf: 54
        },
        {
          name:"7月",
          zhzf: 34,
          ygzf: 44,
          jjzf: 54
        },
        {
          name:"8月",
          zhzf: 34,
          ygzf: 44,
          jjzf: 54
        },
        {
          name:"9月",
          zhzf: 34,
          ygzf: 44,
          jjzf: 54
        },
        {
          name:"10月",
          zhzf: 34,
          ygzf: 44,
          jjzf: 54
        },
        {
          name:"11月",
          zhzf: 34,
          ygzf: 44,
          jjzf: 54
        },
        {
          name:"12月",
          zhzf: 34,
          ygzf: 44,
          jjzf: 54
        }
      ]
    }
  },
  computed: {},
  mounted() {
    this.initApi()
  },
  methods: {
    initApi() {
      getAjlxtj().then(res => {
        this.indexList[0].value = res.data.zhzf
        this.indexList[1].value = res.data.ygzf
        this.indexList[2].value = res.data.jjzf
        this.chartData = res.data.syzfList.map(item => ({
          name: item.name,
          zhzf: item.num,
          ygzf: item.num1,
          jjzf: item.num2
        }))
        this.getChart()
      })
    },
    getChart() {
      let myEc = this.$echarts.init(document.getElementById('chartAjlx'));
      let xdata = [];
      let ydata = [[], [], []];
      this.chartData.forEach((item) => {
        xdata.push(item.name);
        ydata[0].push(item.zhzf);
        ydata[1].push(item.ygzf);
        ydata[2].push(item.jjzf);
      });
      let legend = ["综合执法", "运管执法", "交警执法"];
      let color = ["76,152,251", "172,171,52", "245,102,121"];
      let option = {
        tooltip: {
          trigger: "axis",
          backgroundColor: "rgba(51, 51, 51, 0.7)",
          borderWidth: 0,
          axisPointer: {
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
          textStyle: {
            color: "white",
            fontSize: "24",
          },
        },
        grid: {
          left: "5%",
          right: "5%",
          top: "15%",
          bottom: "0%",
          containLabel: true,
        },
        legend: {
          right: 60,
          itemWidth: 16,
          itemHeight: 16,
          itemGap: 50,
          icon: "square",
          textStyle: {
            fontSize: 24,
            color: "#fff",
            padding: [3, 0, 0, 0],
          },
          // data: legend,
        },
        xAxis: [
          {
            type: "category",
            data: xdata,
            axisLine: {
              lineStyle: {
                color: "rgb(119,179,241,.4)", // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              rotate: 30,
              textStyle: {
                color: "#D6E7F9",
                fontSize: 20,
              },
            },
          },
        ],
        yAxis: [
          {
            name: "单位(件)",
            type: "value",
            nameTextStyle: {
              fontSize: 24,
              color: "#D6E7F9",
              padding: 5,
            },
            splitLine: {
              lineStyle: {
                color: "rgb(119,179,241,.4)",
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 24,
                color: "#D6E7F9",
              },
            },
          },
        ],
        series: [],
      };
      for (var i = 0; i < legend.length; i++) {
        option.series.push({
          name: legend[i],
          type: "line",
          smooth: false,
          symbolSize: 0,
          barWidth: "40%",
          label: {
            show: false,
            position: "insideRight",
          },
          itemStyle: {
            normal: {
              color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "rgba(" + color[i] + ",1)",
                },
                {
                  offset: 1,
                  color: "rgba(" + color[i] + ",1)",
                },
              ]),
              barBorderRadius: 4,
            },
          },
          data: ydata[i],
        });
      }
      myEc.setOption(option);
      myEc.getZr().on("mousemove", (param) => {
        myEc.getZr().setCursorStyle("default");
      });
    },
  },
  watch: {}
}
</script>

<style scoped lang='less'>
  .wrap-container {
    width: 100%;
    height: 865px;
    margin-bottom: 40px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    .wrap-container-box {
      width: 926px;
      height: 168px;
      background: url('@/assets/zqyzt/ajlx-box.png') no-repeat;
      background-size: 100% 100%;
      margin-top: 51px;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      .wrap-container-box-item {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        .wrap-container-box-item-number {
          font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
          font-weight: 400;
          font-size: 64px;
          text-align: center;
          font-style: normal;
          text-transform: none;
          background: linear-gradient(180deg, #FFFFFF 32%, #F73B53 100%);
          -webkit-background-clip: text;
          color: transparent;
        }
        .wrap-container-box-item-text {
          font-family: Alimama ShuHeiTi, Alimama ShuHeiTi;
          font-weight: 700;
          font-size: 28px;
          color: #FFFFFF;
          text-align: center;
          font-style: normal;
          text-transform: none;
        }
      }
    }
  }
</style>