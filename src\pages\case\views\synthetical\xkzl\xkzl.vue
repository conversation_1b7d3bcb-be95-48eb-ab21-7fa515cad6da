<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      :inline="true"
    >
      <el-form-item label="案件状态">
        <el-dropdown trigger="click" @command="handleCommand">
          <span class="el-dropdown-link">
            <el-input
              v-model="queryParams.statusName"
              placeholder="请选择状态"
              readonly="readonly"
              clearable
              size="small"
              style="width: 150px; margin: 0 5px;"
            />
          </span>
          <el-dropdown-menu slot="dropdown" style="width: 150px;">
            <el-dropdown-item v-for="(v) in caseData" :key="v.id" :command="{...v,type:'case'}">{{ v.value }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form-item>
      <el-form-item label="关键词检索">
        <el-input v-model="queryParams.searchValue" size="small" style="width: 240px; margin: 0 5px;" placeholder="请输入内容" />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">
          重置
        </el-button>
      </el-form-item>
    </el-form>
    <el-table
      v-loading="loading"
      :data="listData"
    >
      <el-table-column label="申报者名称" prop="applyName" align="center" :show-overflow-tooltip="true" width="150" />
      <el-table-column label="申报者证件号码" prop="applyCardNumber" align="center" :show-overflow-tooltip="true" width="150" />
      <el-table-column label="项目性质" prop="applyPropertiy" align="center" :show-overflow-tooltip="true" width="130" />
      <el-table-column label="申报时间" align="center" prop="receiveTime" width="150">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.receiveTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="联系电话" prop="phone" align="center" :show-overflow-tooltip="true" width="150" />
      <el-table-column label="通讯地址" prop="address" align="center" :show-overflow-tooltip="true" width="150" />
      <el-table-column label="状态" align="center" prop="statusName" :show-overflow-tooltip="true" width="150">
        <template slot-scope="scope">
          <span>{{ scope.row.status | statusData }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" style="color: #e6a23c;" @click="handle(scope.row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <fromlist ref="fromlist" :visible.sync="open" :getform="form" :title="title" :detail-id="detailId" @reLoad="handleQuery" />
  </div>
</template>

<script>// editInspection
import shop from '@/api/case/synthetical/shop'
import fromlist from '@/pages/case/views/synthetical/xkzl/components/fromlist'
export default {
  name: 'Patrol',
  filters: {
    statusData(status) {
      let obj = {0: '未受理', 1: '已受理', 2: '审批中', 3: '同意审批', 4: '拒绝审批', 5: '已办结' }
      return obj[status]
    }
  },
  components: {
    fromlist
  },
  data() {
    return {
      $map: null,
      value: [],
      detailId: 0,
      formData: [{id: 0, value: '我的'}, {id: 1, value: '全部'}],
      caseData: [ {id: 0, value: '未受理'}, {id: 1, value: '已受理'}, {id: 2, value: '审批中'}, {id: 3, value: '同意审批'}, {id: 4, value: '拒绝审批'}, {id: 5, value: '已办结'} ],
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [],
      // 弹出层标题
      title: '新增案件',
      // 是否显示弹出层
      open: false,
      // 状态数据字典
      statusOptions: [],
      // 菜单列表
      menuOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        typeName: '',
        type: undefined,
        statusName: '',
        status: undefined,
        // 日期范围
        dateRange: [],
        data: 0,
        dataName: '我的'
      },
      // 表单参数
      form: {},
      qd: true,
      formDisabled: false,
      defaultProps: {
        children: 'children',
        label: 'label'
      }

    }
  },
  computed: {
    listData() {
      let {pageNum, pageSize} = this.queryParams
      let arr = this.roleList.slice((pageNum - 1) * pageSize, (pageNum - 1) * pageSize + pageSize)
      return arr
    }
  },
  created() {
    this.getList()
    this.getDicts('sys_normal_disable').then(response => {
      this.statusOptions = response.data
    })
  },
  methods: {
    handle(row) {
      this.title = '信息详情'
      this.form = row
      this.open = true
    },
    // 类型选择
    handleCommand(command) {
      this.queryParams = {...this.queryParams, status: command.id, statusName: command.value}
      this.handleQuery()
    },
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.queryParams.pageNum = 1
      this.getList()
    },
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.getList()
    },
    // 已下为模板
    /** 查询列表 */
    getList() {
      this.loading = true
      let {searchValue, status, dateRange, pageNum, pageSize} = this.queryParams
      let params = {pageNum, pageSize}
      if (searchValue) params.searchValue = searchValue
      if (status || status == 0) params.status = status
      if (Array.isArray(dateRange) && !dateRange.length == 0) { params = {...params, searchStartTime: dateRange[0], searchEndTime: dateRange[1] } }
      shop.xukeList().then(res => {
        if (pageNum == 1) {
          this.roleList = res.rows
        } else {
          this.roleList = this.roleList.concat(res.rows)
        }
        this.total = res.total
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    // 取消
    cancel() {
      this.open = false
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {dateRange: [], pageNum: 1, pageSize: 10, statusName: '', status: undefined, searchValue: ''}
      this.handleQuery()
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.open = true
      this.formDisabled = false
      if (this.queryParams.data == 1 || row.status == 9) this.formDisabled = true
      this.detailId = row.inspectionId
      this.title = '详情信息'
    }
  }
}
</script>
<style scoped>
  ::v-deep .el-form-item--medium .el-form-item__content {
    line-height: 32px !important;
  }
</style>
