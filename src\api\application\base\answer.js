import {request} from '@/utils/request'

// 查询常见问答库列表
export function listAnswer(query) {
  return request({
    url: '/business/answer/list',
    method: 'get',
    params: query
  })
}

// 查询常见问答库详细
export function getAnswer(id) {
  return request({
    url: '/business/answer/' + id,
    method: 'get'
  })
}

// 新增常见问答库
export function addAnswer(data) {
  return request({
    url: '/business/answer/add',
    method: 'post',
    data: data
  })
}

// 修改常见问答库
export function updateAnswer(data) {
  return request({
    url: '/business/answer/edit',
    method: 'post',
    data: data
  })
}

// 删除常见问答库
export function delAnswer(id) {
  return request({
    url: '/business/answer/remove/' + id,
    method: 'post'
  })
}

// 导出常见问答库
export function exportAnswer(query) {
  return request({
    url: '/business/answer/export',
    method: 'get',
    params: query
  })
}
