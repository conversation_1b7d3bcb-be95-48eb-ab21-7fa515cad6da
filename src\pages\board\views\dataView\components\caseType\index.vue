<template>
  <div v-loading="loading" class="case-type clearfix" element-loading-background="rgba(0, 0, 0, 0.8)">
    <div class="title-box">
      <span class="title">行政执法</span>
      <span class="title">运管执法</span>
      <span class="title">交警执法</span>
    </div>
    <div class="charts-box">
      <m-pie series-name="行政执法" :series-data="chartData.insData" />
    </div>
    <div class="charts-box">
      <m-pie series-name="运管执法" :series-data="chartData.transData" />
    </div>
    <div class="charts-box">
      <m-pie series-name="交警执法" :series-data="chartData.captureData" />
    </div>
  </div>
</template>

<script>
import mPie from './pie2.vue'
import { getCaseType } from '@/api/board/dataView/index'

export default {
  components: {
    mPie
  },
  data() {
    return {
      loading: false,
      chartData: {}
    }
  },
  mounted() {
    this.fetchData()
  },
  methods: {
    fetchData() {
      this.loading = true
      getCaseType().then(res => {
        this.chartData = res.data
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style scoped lang="scss">
.case-type {
  height: 100%;
  .title-box {
    line-height: pxtorem(20);
    height: pxtorem(20);
    color: #00f7ff;
    overflow: hidden;
    margin: pxtorem(5) 0;
    .title {
      width: 33.3%;
      float: left;
      text-align: center;
      font-size: 12px;
    }
  }
  .charts-box {
    width: 33.3%;
    height: calc(100% - 0.15625rem);
    float: left;
  }
}
</style>
