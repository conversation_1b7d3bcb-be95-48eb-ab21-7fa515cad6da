<template>
  <div>
    <CommonTitle text="养犬管理">
      <TabSwitch :tabList="list" :activeIndex="index" @tab-change="handleTabChange" />
    </CommonTitle>
    <div class="wrap-container1">
      <TabSwitch class="tab-wraper" :type="2" :tabList="list1" :activeIndex="index1" @tab-change="handleTabChange1" />
    </div>
    <div id="chartYqgl" style="width: 1030px; height: 320px; margin-top: 90px"></div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import TabSwitch from '@/components/TabSwitch'
import { getDogJscStatistics } from '@/api/csyxgl/index.js'
export default {
  name: 'index',
  components: {
    CommonTitle,
    TabSwitch,
  },
  data() {
    return {
      index: 0,
      list: [
        { name: '本月', value: '1' },
        { name: '本季', value: '2' },
        { name: '本年', value: '3' },
      ],
      index1: 0,
      list1: [
        { name: '登记数', value: '1' },
        { name: '办证数', value: '2' },
        { name: '收容数', value: '3' },
        { name: '免疫到期', value: '4' },
      ],
      chartData: [],
    }
  },
  computed: {},
  mounted() {
    this.getData()
  },
  methods: {
    getData() {
      getDogJscStatistics({ type: this.list[this.index].value }).then((res) => {
        this.chartData = res.data
        // this.getChart()
      })
    },
    handleTabChange(i) {
      this.index = i
      // 这里可以根据tab切换更新图表数据
      this.getData()
    },
    handleTabChange1(i) {
      this.index1 = i
    },
    getChart() {
      let myEc = this.$echarts.init(document.getElementById('chartYqgl'))
      this.chartData.forEach((item) => {
        if(this.index1==0){
          xdata.push(item.name)
        }
      })
      let legend = ['综合执法', '运管执法', '交警执法']
      let color = ['76,152,251', '172,171,52', '245,102,121']
      let option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(51, 51, 51, 0.7)',
          borderWidth: 0,
          axisPointer: {
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          textStyle: {
            color: 'white',
            fontSize: '24',
          },
        },
        grid: {
          left: '5%',
          right: '5%',
          top: '15%',
          bottom: '0%',
          containLabel: true,
        },
        legend: {
          right: 60,
          itemWidth: 16,
          itemHeight: 16,
          itemGap: 50,
          icon: 'square',
          textStyle: {
            fontSize: 24,
            color: '#fff',
            padding: [3, 0, 0, 0],
          },
          // data: legend,
        },
        xAxis: [
          {
            type: 'category',
            data: xdata,
            axisLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)', // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              rotate: 30,
              textStyle: {
                color: '#D6E7F9',
                fontSize: 20,
              },
            },
          },
        ],
        yAxis: [
          {
            name: '单位(件)',
            type: 'value',
            nameTextStyle: {
              fontSize: 24,
              color: '#D6E7F9',
              padding: 5,
            },
            splitLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)',
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 24,
                color: '#D6E7F9',
              },
            },
          },
        ],
        series: [],
      }
      for (var i = 0; i < legend.length; i++) {
        option.series.push({
          name: legend[i],
          type: 'line',
          smooth: false,
          symbolSize: 0,
          barWidth: '40%',
          label: {
            show: false,
            position: 'insideRight',
          },
          itemStyle: {
            normal: {
              color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(' + color[i] + ',1)',
                },
                {
                  offset: 1,
                  color: 'rgba(' + color[i] + ',1)',
                },
              ]),
              barBorderRadius: 4,
            },
          },
          data: ydata[i],
        })
      }
      myEc.setOption(option)
      myEc.getZr().on('mousemove', (param) => {
        myEc.getZr().setCursorStyle('default')
      })
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
.wrap-container1 {
  width: 100%;
  // height: 600px;
  .tab-wraper {
    margin: 10px 40px;
  }
}
</style>