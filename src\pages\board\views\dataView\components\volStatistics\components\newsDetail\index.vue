<template>
  <div class="news-detail">
    <el-scrollbar style="height: 100%;">
      <el-row :gutter="15" style="margin-right: 10px;">
        <el-form ref="form" v-loading="formLoading" :model="form" disabled label-width="100px">
          <el-col :span="12">
            <el-form-item label="动态标题" prop="title">
              <el-input v-model="form.title" placeholder="请输入动态标题" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发布人" prop="creatByUser">
              <el-input v-model="form.creatByUser" disabled placeholder="请输入发布人" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="动态来源" prop="type">
              <el-select v-model="form.type" disabled style="width: 100%;">
                <el-option label="服务记录" value="1" />
                <el-option label="我要赞美" value="2" />
                <el-option label="站前动态" value="3" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="内容">
              <Editor v-model="form.content" :height="210" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="动态图片" prop="files">
              <MFileUpload ref="mainFile" :limit="1" :file-list="mainFileList" />
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
    </el-scrollbar>
  </div>
</template>

<script>
import { getNews } from '@/api/application/vol/news/news'
import { getFiles } from '@/api/supervise/swit'
import MFileUpload from '@/components/MFileUpload/index.vue'
import { AESDecrypt } from '@/utils/aesutil'
import Editor from '@/components/Editor/index.vue'

export default {
  name: 'NewsDetail',
  components: {
    MFileUpload,
    Editor
  },
  data() {
    return {
      formLoading: false,
      form: {},
      mainFileList: []
    }
  },
  methods: {
    reset() {
      this.form = {
        id: null,
        title: null,
        content: null,
        type: '3',
        pageNum: 1,
        pageSize: 10,
        searchValue: null,
        dateRange: []
      }
      this.resetForm('form')
    },
    fetchData(id) {
      this.reset()
      this.open = true
      this.formLoading = true
      this.title = '修改'
      Promise.all([
        getNews(id),
        getFiles({ businessId: id, tableName: 'vol_news' })
      ]).then(response => {
        const [formData, fileData] = response
        const creatByUser = AESDecrypt(formData.data.createBy, 'ivqpsFQwQqxYUr7f')
        this.form = formData.data
        this.form.creatByUser = creatByUser || formData.data.createBy
        this.formLoading = false

        // 获取文件
        this.mainFileList = fileData.rows.map(item => {
          return { name: item.displayName, url: `/zqzfj${item.filePath}`, ...item }
        })

      }).catch(() => this.formLoading = false)
    }
  }
}
</script>

<style scoped lang="scss">
.news-detail {
  height: 70vh;
  ::v-deep {
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }
}
</style>
