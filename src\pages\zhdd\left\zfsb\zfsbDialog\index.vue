<template>
  <ygfDialog :visible='visible' width='1415px'>
    <div id="zlxt" class="rwgz-tc">
    <div class="rw-title flex-between">
      <div class="fs-44 text-mid-yellow dialogTitle" id="rwTitle" style="margin-left: 20px;">{{name}}</div>
      <div class="close cursor" @click="close" style="margin-right: 20px;"></div>
    </div>
    <div class="content">
      <div class="table">
        <div v-show="['机动车辆','非机动车辆','汽车','摩托车','四轮电瓶车','两轮电瓶车'].indexOf(name) != -1">
          <div class="table-line title-line" >
            <div class="table-column table-title" style="flex: 1;margin-left: 30px">序号</div>
            <div class="table-column table-title" style="flex: 2;">区域</div>
            <div class="table-column table-title" style="flex: 4">部门</div>
            <div class="table-column table-title" style="flex: 2">车辆类型</div>
            <div class="table-column table-title" style="flex: 2">车牌号</div>
          </div>
          <div class="table-container">
            <div class="table-line" v-for="(item,i) in cartableData" :key="i" :class="{activeTableLine:i % 2 == 0}">
              <div class="table-column" style="flex: 1;margin-left: 30px">{{i + 1}}</div>
              <div class="table-column" style="flex: 2">{{item.area}}</div>
              <div class="table-column" style="flex: 4" :title="item.depart">{{item.depart}}</div>
              <div class="table-column" style="flex: 2">{{item.carType}}</div>
              <div class="table-column" style="flex: 2">{{item.carNumber}}</div>
            </div>
          </div>
        </div>

        <div v-show="['机动车辆','非机动车辆','汽车','摩托车','四轮电瓶车','两轮电瓶车','执法记录仪'].indexOf(name) == -1">
          <div class="table-line title-line" >
            <div class="table-column table-title" style="flex: 1;margin-left: 30px">序号</div>
            <div class="table-column table-title" style="flex: 2">区域</div>
            <div class="table-column table-title" style="flex: 4">部门</div>
            <div class="table-column table-title" style="flex: 3">品牌</div>
            <div class="table-column table-title" style="flex: 3">型号</div>
            <div class="table-column table-title" style="flex: 2">数量</div>
          </div>
          <div class="table-container">
            <div class="table-line" v-for="(item,i) in othertableData" :key="i" :class="{activeTableLine:i % 2 == 0}">
              <div class="table-column" style="flex: 1;margin-left: 30px">{{i + 1}}</div>
              <div class="table-column" style="flex: 2">{{item.area}}</div>
              <div class="table-column" style="flex: 4" :title="item.depart">{{item.depart}}</div>
              <div class="table-column" style="flex: 3">{{item.pp}}</div>
              <div class="table-column" style="flex: 3" :title="item.xh">{{item.xh}}</div>
              <div class="table-column" style="flex: 2">{{item.num}}</div>
            </div>
          </div>
        </div>

        <div v-show="name == '执法记录仪'">
          <div class="table-line title-line" >
            <div class="table-column table-title" style="flex: 1;margin-left: 30px">序号</div>
            <div class="table-column table-title" style="flex: 2">区域</div>
            <div class="table-column table-title" style="flex: 4">部门</div>
            <div class="table-column table-title" style="flex: 3;margin-left: 30px;">设备人员</div>
            <div class="table-column table-title" style="flex: 3">状态</div>
          </div>
          <div class="table-container">
            <div class="table-line" v-for="(item,i) in jlytableData" :key="i" :class="{activeTableLine:i % 2 == 0}">
              <div class="table-column" style="flex: 1;margin-left: 30px">{{i + 1}}</div>
              <div class="table-column" style="flex: 2">{{item.area}}</div>
              <div class="table-column" style="flex: 4" :title="item.depart">{{item.depart}}</div>
              <div class="table-column" style="flex: 3;margin-left: 30px;">{{item.name}}</div>
              <div class="table-column" style="flex: 3">{{item.status == "0"?"离线":"在线"}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  </ygfDialog>
</template>

<script>
import ygfDialog from '@/components/ygfDialog'
import { indexApi } from '@/api/indexApi'
export default {
  name: 'index',
  props: ['visible','name'],
  components: {
    ygfDialog
  },
  data() {
    return {
      cartableData: [],
      othertableData: [],
      jlytableData: [],
      city: localStorage.getItem("city")
    }
  },
  computed: {},
  mounted() {
    // 监听城市变化
    this.$bus.$on('cityChange', (city) => {
      this.city = city;
      this.getDetail(this.name);
    });
    // 监听年份变化
    this.$bus.$on('yearChange', (year) => {
      this.year = year;
      this.getDetail(this.name);
    });
  },
  methods: {
    //获取数据
    getDetail(name) {
      switch (name) {
        case "机动车辆":
          this.getJdclList();
          break;
        case "非机动车辆":
          this.getFjdclList();
          break;
        case "执法记录仪":
          this.getZfjlyList();
          break;
        case "对讲机":
          this.getDjjList();
          break;
        case "PDA":
          this.getPdaList();
          break;
        case "无人机":
          this.getWrjList();
          break;
        default:
          this.getCarTypeList(name);
      }
    },
    //判断是不是机动车
    isjdc(str) {
      return ["汽车","摩托车"].indexOf(str) != -1?"5":"6"
    },
    getCarTypeList(name) {
      indexApi("/xzzfj_zhdd_sblb",{county:this.city == "金华市"?"":this.city,device_type:this.isjdc(name),carType:name}).then(res => {
        console.log(res);
        this.cartableData = res.data.map(item => {return {
          area:item.area_name,
          depart:item.department,
          carType:item.car_type,
          carNumber:item.car_num
        }})
      })
    },
    //机动车辆
    getJdclList() {
      indexApi("/xzzfj_zhdd_sblb",{county:this.city == "金华市"?"":this.city,device_type:"5"}).then(res => {
        console.log(res);
        this.cartableData = res.data.map(item => {return {
          area:item.area_name,
          depart:item.department,
          carType:item.car_type,
          carNumber:item.car_num
        }})
      })
    },
    //非机动车辆
    getFjdclList() {
      indexApi("/xzzfj_zhdd_sblb",{county:this.city == "金华市"?"":this.city,device_type:"6"}).then(res => {
        console.log(res);
        this.cartableData = res.data.map(item => {return {
          area:item.area_name,
          depart:item.department,
          carType:item.car_type,
          carNumber:item.car_num
        }})
      })
    },
    //执法记录仪
    getZfjlyList() {
      indexApi("/xzzfj_zhdd_sblb",{county:this.city == "金华市"?"":this.city,device_type:"1"}).then(res => {
        console.log(res);
        this.jlytableData = res.data.map(item => {return {
          area:item.area_name,
          depart:item.department,
          name:item.name,
          status:item.lineon
        }})
      })
    },
    //对讲机
    getDjjList() {
      indexApi("/xzzfj_zhdd_sblb",{county:this.city == "金华市"?"":this.city,device_type:"4"}).then(res => {
        this.othertableData = res.data.map(item => {return {
          area:item.area_name,
          depart:item.department,
          pp:item.brand,
          xh:item.model,
          num:item.num
        }})
      })
    },
    //PDA
    getPdaList() {
      indexApi("/xzzfj_zhdd_sblb",{county:this.city == "金华市"?"":this.city,device_type:"3"}).then(res => {
        this.othertableData = res.data.map(item => {return {
          area:item.area_name,
          depart:item.department,
          pp:item.brand,
          xh:item.model,
          num:item.num
        }})
      })
    },
    //无人机
    getWrjList() {
      indexApi("/xzzfj_zhdd_sblb",{county:this.city == "金华市"?"":this.city,device_type:"2"}).then(res => {
        this.othertableData = res.data.map(item => {return {
          area:item.area_name,
          depart:item.department,
          pp:item.brand,
          xh:item.model,
          num:item.num
        }})
      })
    },
    close() {
      this.$emit('close')
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.getDetail(this.name)
      }
    }
  }
}
</script>

<style scoped lang='less'>

::-webkit-scrollbar {
  display: none;
}


ul,
ul li {
  list-style: none;
}

.rwgz-tc {
  width: 1387px;
  height: 726px;
  background: url("@/assets/zhdd/dialogBg.png") no-repeat;
  background-size: 100% 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-direction: column;
}

.rw-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 46px 3% 0;
  width: 95%;
  height: 60px;
  line-height: 60px;
}

.close {
  background: url("@/assets/zhdd/close.png") no-repeat;
  width: 34px;
  height: 34px;
}

.ql-indent-1 img {
  width: 100%;
}

img {
  width: 100%;
}

.content {
  height: 850px;
  margin-top: 10px;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  flex-direction: column;
}

::-webkit-scrollbar {
  width: 0;
  height: 0;
}

.scrollTab {
  width:928px;
  height:70px;
}

.table-container {
  height: 482px;
  overflow-y: scroll;
}

.tableContainer2 {
  height: 400px;
  overflow-y: scroll;
}

.table-line {
  width: 1296px;
  height: 80px;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  background: rgba(50,134,248,0.15);
}

.title-line {
  background: transparent !important;
}

.table-title {
  font-size: 32px;
  font-family: Source Han Sans CN;
  font-weight: bold;
  color: #FFFFFF;
  text-align: left;
}

.table-column {
  font-size: 32px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.activeTableLine {
  background: rgba(50,134,248,0.25);
}

/deep/ .el-input {
  /*position: absolute;*/
  /*top: 229px;*/
  /*left: 1630px;*/
  width: 537px;
  height: 72px;
}

/deep/ .el-input__icon {
  height: 100%;
  width: 25px;
  text-align: center;
  -webkit-transition: all .3s;
  transition: all .3s;
  line-height: 75px;
}

/deep/ .el-scrollbar {
  overflow: hidden;
  /*position: relative;*/
  height: 500px;
  background: #020b28;
  border-radius: 10px;
}

/deep/ .el-autocomplete-suggestion__wrap {
  max-height: unset !important;
  padding: 10px 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

/deep/ .el-input--suffix .el-input__inner {
  border: 1px solid #359CF8;
  border-radius: 8px;
  padding-right: 30px;
  height: 70px;
  font-family: MicrosoftYaHei;
  font-size: 28px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 1px;
  color: #bbe5fd !important;
  background: #020b28;
}

/deep/ .el-input.is-active .el-input__inner, .el-input__inner:focus {
  border: 1px solid #bbe5fd;
  outline: 0;
}

/deep/ .el-input__suffix-inner {
  pointer-events: all;
  font-size: 28px;
  margin: 15px 20px 0 0;
  color: #bbe5fd !important;
}

/deep/ .el-autocomplete-suggestion li {
  padding: 0 20px;
  line-height: 34px;
  cursor: pointer;
  color: #bbe5fd;
  font-size: 28px;
  list-style: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin: 10px 0 25px 0;
}

/deep/ .el-autocomplete-suggestion li:hover {
  background: unset !important;
}

.search {
  width: 100px;
  height: 65px;
  line-height: 65px;
  text-align: center;
  background: #0A619E;
  border: 1px solid #359CF8;
  border-radius: 8px;
  font-size: 28px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FEFEFE;
  margin-left: 10px;
  cursor: pointer;
}
</style>