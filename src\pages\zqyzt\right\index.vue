<template>
    <div class="right">
        
        <!-- 新增图标按钮组 -->
        <div class="icon-buttons">
            <div class="icon-btn monitor" @click="handleOpenPage('dataView')">
                <i class="top-icon icon-monitor-screen"></i>
            </div>
            <div class="icon-btn camera" @click="handleOpenPage('monitor')">
                <i class="top-icon icon-camera"></i>
            </div>
        </div>
        
        <!-- 指挥调度面板 -->
        <div class="command-panel">
            <div class="command-btn" :class="{ active: showPanel }" @click="togglePanel">
                <div class="btn-text">指挥调度</div>
                <div class="btn-subtext">Scheduling command</div>
            </div>
            
            <!-- 功能列表面板 -->
            <div v-show="showPanel" class="function-panel">
                <div class="function-grid">
                    <div class="function-item" :class="{ 'item-noactive': !selectedItems.includes('all') }" @click="handleFunction('all')">
                        <div class="icon-box blue">
                            <i class="icon-all"></i>
                        </div>
                        <div class="item-text">全部</div>
                    </div>
                    <div class="function-item" :class="{ 'item-noactive': !selectedItems.includes('recorder') }" @click="handleFunction('recorder')">
                        <div class="icon-box yellow">
                            <i class="icon-law"></i>
                        </div>
                        <div class="item-text">执法人员({{ count.recorder }})</div>
                    </div>
                    <div class="function-item" :class="{ 'item-noactive': !selectedItems.includes('monitor') }" @click="handleFunction('monitor')">
                        <div class="icon-box purple">
                            <i class="icon-monitor"></i>
                        </div>
                        <div class="item-text">监控({{ count.monitor }})</div>
                    </div>
                    <div class="function-item" :class="{ 'item-noactive': !selectedItems.includes('shop') }" @click="handleFunction('shop')"> 
                        <div class="icon-box blue">
                            <i class="icon-store"></i>
                        </div>
                        <div class="item-text">店铺({{ count.shop }})</div>
                    </div>
                    <div class="function-item" :class="{ 'item-noactive': !selectedItems.includes('lawCar') }" @click="handleFunction('lawCar')">
                        <div class="icon-box orange">
                            <i class="icon-car"></i>
                        </div>
                        <div class="item-text">执法车({{ count.lawCar }})</div>
                    </div>
                    <div class="function-item" :class="{ 'item-noactive': !selectedItems.includes('vol') }"  @click="handleFunction('vol')">
                        <div class="icon-box red">
                            <i class="icon-volunteer"></i>
                        </div>
                        <div class="item-text">志愿者({{ count.volunteer }})</div>
                    </div>
                    <div class="function-item" :class="{ 'item-noactive': !selectedItems.includes('taxi') }"  @click="handleFunction('taxi')">
                        <div class="icon-box green">
                            <i class="icon-taxi"></i>
                        </div>
                        <div class="item-text">出租车({{ count.taxi }})</div>
                    </div>
                    <div class="function-item" :class="{ 'item-noactive': !selectedItems.includes('area') }"  @click="handleFunction('area')">
                        <div class="icon-box pink">
                            <i class="icon-area"></i>
                        </div>
                        <div class="item-text">行政区划({{ count.area }})</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 事件列表面板 -->
        <div class="event-panel">
            <div class="event-btn" :class="{ active: showEventPanel }" @click="toggleEventPanel">
                <div class="btn-text">事件列表</div>
                <div class="btn-subtext">event List</div>
            </div>
            
            <!-- 事件功能列表 -->
            <div v-show="showEventPanel" class="event-function-panel">
                <div class="event-grid">
                    <div class="event-item" :class="{ 'item-noactive': !selectedEventItems.includes('all') }" @click="handleEventFunction('all')">
                        <div class="event-icon-box">
                            <i class="event-icon-all"></i>
                        </div>
                        <div class="event-text">全部</div>
                    </div>
                    <div class="event-item" :class="{ 'item-noactive': !selectedEventItems.includes('punish') }" @click="handleEventFunction('punish')">
                        <div class="event-icon-box">
                            <i class="event-icon-simple"></i>
                        </div>
                        <div class="event-text">简易案件({{ count.punish }})</div>
                    </div>
                    <div class="event-item" :class="{ 'item-noactive': !selectedEventItems.includes('four') }"     @click="handleEventFunction('four')">
                        <div class="event-icon-box">
                            <i class="event-icon-four"></i>
                        </div>
                        <div class="event-text">四位一体({{ count.four }})</div>
                    </div>
                    <div class="event-item" :class="{ 'item-noactive': !selectedEventItems.includes('tout') }" @click="handleEventFunction('tout')">
                        <div class="event-icon-box">
                            <i class="event-icon-yellow"></i>
                        </div>
                        <div class="event-text">黄牛处置({{ count.tout }})</div>
                    </div>
                    <div class="event-item" :class="{ 'item-noactive': !selectedEventItems.includes('capture') }" @click="handleEventFunction('capture')">
                        <div class="event-icon-box">
                            <i class="event-icon-monitor"></i>
                        </div>
                        <div class="event-text">监控抓拍({{ count.capture }})</div>
                    </div>
                    <div class="event-item" :class="{ 'item-noactive': !selectedEventItems.includes('autoCapture') }"   @click="handleEventFunction('autoCapture')">
                        <div class="event-icon-box">
                            <i class="event-icon-ai"></i>
                        </div>
                        <div class="event-text">智能抓拍({{ count.autoCapture }})</div>
                    </div>
                    <div class="event-item" :class="{ 'item-noactive': !selectedEventItems.includes('inspection') }" @click="handleEventFunction('inspection')">
                        <div class="event-icon-box">
                            <i class="event-icon-patrol"></i>
                        </div>
                        <div class="event-text">巡查发现({{ count.inspection }})</div>
                    </div>
                    <div class="event-item" :class="{ 'item-noactive': !selectedEventItems.includes('transport') }" @click="handleEventFunction('transport')">
                        <div class="event-icon-box">
                            <i class="event-icon-report"></i>
                        </div>
                        <div class="event-text">违规处置({{ count.transport }})</div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 执法人员弹窗 -->
        <zfryInfo :visible.sync="showZfryInfo" @close="showZfryInfo = false" :detail-id="recorderId" />
        <!-- 执法车辆弹窗 -->
        <zfcInfo :visible.sync="showZfcInfo" @close="showZfcInfo = false" :detail-id="lawCarId"/>

        <!-- 事件 -->
        <!-- 巡查发现详情 -->
        <el-dialog :append-to-body= "true" :close-on-click-modal="false" title="巡查发现详情" :visible.sync="inspectionWindowVisible" width="1000px">
           <inspectionWindow v-if="inspectionWindowVisible" :detail-id="inspectionWindowId" :form-disabled="true" :qd="false" @oncancel="inspectionWindowVisible = false" />
        </el-dialog>
        <!-- 四位一体详情 -->
        <fourWindow :visible.sync="fourWindowVisible" title="四位一体详情" :hand-disabled="false" :type-data="typeData" :detail-id="fourWindowId" :form-disabled="true" />
        <!-- 简易案件 -->
        <el-dialog :append-to-body= "true" :close-on-click-modal="false" title="简易案件详情" :visible.sync="punishWindowVisible" width="1000px">
           <punishWindow v-if="punishWindowVisible" :detail-id="punishWindowId" :form-disabled="true" :qd="false" @oncancel="punishWindowVisible = false" />
        </el-dialog>
        <!-- 黄牛处置 -->
        <el-dialog :append-to-body= "true" :close-on-click-modal="false" title="黄牛处置详情" :visible.sync="toutWindowVisible" width="1000px">
           <toutWindow v-if="toutWindowVisible" :detail-id="toutWindowId" :qd="true" @oncancel="toutWindowVisible = false" />
        </el-dialog>
        <!-- 智能抓拍 -->
        <trafficCaptureWindow :visible.sync="trafficCaptureWindowVisible" title="智能抓拍详情" :detail-id="trafficCaptureWindowId" :form-disabled="true" />
        <!-- 违规处置 -->
        <transportWindow :visible.sync="transportWindowVisible" title="违规处置详情" :detail-id="transportWindowId" :form-disabled="true" :car-type-options="transportWindowTypeData" />
        <!-- 监控抓拍详情 -->
        <captureWindow :visible.sync="captureWindowVisible" :form-disabled="true" title="监控抓拍详情" :detail-id="captureWindowId" />
         <!-- 视频 -->
        <video-box :visible.sync="videoVisible" :terminal-no="terminalNo" :modal="false" :close-on-click-modal="false" />
        <!-- 海康视频控件 -->
        <windowVideo v-model="hkVideoVisible" :video-name="winVideoName" :video-id="hkVideoId" />
    </div>
</template>

<script>
import { listAll, listAllCase, getShopInfo} from '@/api/zqyzt';
import { getTaskInfo } from '@/api/board/map/index'
import { getFiles } from '@/api/supervise/swit'
import MapService from '../../../components/Map/index.js';
import zfryInfo from '../components/zfryInfo/index.vue'
import zfcInfo from '../components/zfcInfo/index.vue'
import fourWindow from '@/pages/supervise/views/jgdc/quaternity/components/fromlist.vue'
import captureWindow from '@/pages/case/views/synthetical/capture/components/fromlist.vue'
import inspectionWindow from '@/pages/case/views/synthetical/patrol/components/fromlist.vue'
import punishWindow from '@/pages/case/views/synthetical/punishment/components/fromlist.vue'
import toutWindow from '@/pages/case/views/tout/hncz/components/fromlist.vue'
import trafficCaptureWindow from '@/pages/case/views/synthetical/aicapture/components/fromlist.vue'
import transportWindow from '@/pages/case/views/pipe/illegal/components/fromlist.vue'
import videoBox from '../components/videoBox/index.vue'
import windowVideo from '../components/windowVideo/index.vue'
import { nextTick} from 'vue'
import { pointIcons, getPointIcon } from '@/components/Map/pointIcons/index.js'

export default {
    name: 'RightPanel',
    components: {
        zfryInfo,
        zfcInfo,
        inspectionWindow,
        fourWindow,
        captureWindow,
        punishWindow,
        toutWindow,
        trafficCaptureWindow,
        transportWindow,
        // videoBox,
        windowVideo
    },
    data() {
        return {
            showPanel: false,
            showEventPanel: false,
            selectedItems: ['all', 'recorder', 'taxi', 'lawCar', 'shop', 'monitor', 'vol', 'area'],
            selectedEventItems: ['all', 'four', 'capture', 'inspection', 'punish', 'tout', 'autoCapture', 'transport'],
            count: {
                recorder: 0,
                monitor: 0,
                shop: 0,
                lawCar: 0,
                volunteer: 0,
                taxi: 0,
                area: 0,
                punish: 0,
                four: 0,
                tout: 0,
                capture: 0,
                autoCapture: 0,
                inspection: 0,
                transport: 0,
            },
            terminalNo: '',
            listAll: [],
            shopDetail: {},
            volDetail: {},
            fImgSrc: '',
            fSrcList: [],
            fImgSrc1: '',
            fSrcList1: [],
            sSrcLis: [],
            transportWindowTypeData: [],
            showZfryInfo: false,
            showZfcInfo: false,
            fourWindowVisible: false,
            inspectionWindowVisible: false,
            punishWindowVisible: false,
            toutWindowVisible: false,
            trafficCaptureWindowVisible: false,
            transportWindowVisible: false,
            autoWindowVisible: false,
            captureWindowVisible: false,
            videoVisible: false,
            hkVideoVisible: false,
            winVideoName: '',
            hkVideoId: '',

            recorderId: undefined,
            lawCarId: undefined,
            inspectionWindowId: undefined, 
            fourWindowId: undefined,
            captureWindowId: undefined,
            punishWindowId: undefined,
            toutWindowId: undefined,
            trafficCaptureWindowId: undefined,
            transportWindowId: undefined,
            autoCaptureWindowId: undefined,
            typeData: [{id: 1, value: '环卫保洁'}, {id: 2, value: '园林'}, {id: 3, value: '绿化'}, {id: 4, value: '市政'} ],
        }
    },
    mounted() {
        nextTick(() => {
            this.getDicts('transport_car_type').then(res => {
                this.transportWindowTypeData = res.data
            })
            this.initMap()
        })
    },
    methods: {
        initMap() {
            listAll().then(res => {
                // 创建覆盖物
                let resData = res.data
                if (res.data.event && res.data.event.length) {
                    res.data.event.forEach(item => {
                        const type = item.caseType
                        if (Array.isArray(resData[type])) {
                        resData[type].push(item)
                        } else {
                        resData[type] = [item]
                        }
                    })
                }
                if (resData.event) delete resData.event
                 // 遍历resData获取每个类型的数量
                Object.keys(resData).forEach(key => {
                    if (Array.isArray(resData[key])) {
                        switch(key) {
                            case 'recorder':
                                this.count.recorder = resData[key].length;
                                break;
                            case 'monitor': 
                                this.count.monitor = resData[key].length;
                                break;
                            case 'shop':
                                this.count.shop = resData[key].length;
                                break;
                            case 'lawCar':
                                this.count.lawCar = resData[key].length;
                                break;
                            case 'vol':
                                this.count.volunteer = resData[key].length;
                                break;
                            case 'taxi':
                                this.count.taxi = resData[key].length;
                                break;
                            case 'area':
                                this.count.area = resData[key].length;
                                break;
                            case 'punish':
                                this.count.punish = resData[key].length;
                                break;
                            case 'four':
                                this.count.four = resData[key].length;
                                break;
                            case 'tout':
                                this.count.tout = resData[key].length;
                                break;
                            case 'capture':
                                this.count.capture = resData[key].length;
                                break;
                            case 'autoCapture':
                                this.count.autoCapture = resData[key].length;
                                break;
                            case 'inspection':
                                this.count.inspection = resData[key].length;
                                break;
                            case 'transport':
                                this.count.transport = resData[key].length;
                                break;
                        }
                    }
                });
                this.listAll = resData
                this.addLayer('all')
                this.addEventLayer('all')
                console.log(resData)
            }).catch(() => {
                this.loading = false
            })
            this.createArea()
        },
        togglePanel() {
            this.showPanel = !this.showPanel;

        },
        toggleEventPanel() {
            this.showEventPanel = !this.showEventPanel;
        },
        handleFunction(type) {
            if (type === 'all') {
                if (this.selectedItems.includes('all')) {
                    this.selectedItems = [];
                    this.removeLayer('all')
                } else {
                    ['recorder', 'taxi', 'lawCar', 'shop', 'monitor', 'vol', 'area'].filter(item => !this.selectedItems.includes(item)).forEach(item => {
                        this.addLayer(item)
                    })
                    this.selectedItems = ['all', 'recorder', 'taxi', 'lawCar', 'shop', 'monitor', 'vol', 'area'];
                }
            } else {
                const index = this.selectedItems.indexOf(type);
                if (index > -1) {
                    this.selectedItems = this.selectedItems.filter(item => item !== type && item !== 'all');
                    this.removeLayer(type)
                } else {
                    this.selectedItems.push(type);
                    this.addLayer(type)
                    const requiredItems = ['recorder', 'taxi', 'lawCar', 'shop', 'monitor', 'vol', 'area'];
                    const allExist = requiredItems.every(item => this.selectedItems.includes(item));
                    if (allExist && !this.selectedItems.includes('all')) {
                        this.selectedItems.push('all');
                    }
                }
            }
            this.$emit('functionClick', type);
        },
        handleEventFunction(type) {
            if (type === 'all') {
                if (this.selectedEventItems.includes('all')) {
                    this.selectedEventItems = [];
                    this.removeEventLayer('all')
                } else {
                    ['four', 'capture', 'inspection', 'punish', 'tout', 'autoCapture', 'transport'].filter(item => !this.selectedEventItems.includes(item)).forEach(item => {
                        this.addLayer(item)
                    })
                    this.selectedEventItems = ['all', 'four', 'capture', 'inspection', 'punish', 'tout', 'autoCapture', 'transport'];
                }
            } else {
                const index = this.selectedEventItems.indexOf(type);
                if (index > -1) {
                    this.selectedEventItems = this.selectedEventItems.filter(item => item !== type && item !== 'all');
                    this.removeEventLayer(type)
                } else {
                    this.selectedEventItems.push(type);
                    this.addEventLayer(type)
                    const requiredItems = ['four', 'capture', 'inspection', 'punish', 'tout', 'autoCapture', 'transport'];
                    const allExist = requiredItems.every(item => this.selectedEventItems.includes(item));
                    if (allExist && !this.selectedEventItems.includes('all')) {
                        this.selectedEventItems.push('all');
                    }
                }
            }
            this.$emit('eventFunctionClick', type);
        },
        handleOpenPage(url) {
            this.$router.push({ path: url })
        },
        addLayer(type) {
            if (type == 'all') {
                Object.keys(this.listAll).forEach(key => {
                    if (['recorder', 'taxi', 'lawCar', 'shop', 'monitor', 'vol'].includes(key)) {
                        console.log(this.listAll[key], key)
                        MapService.loadPointLayer2({
                            data: this.listAll[key], //点位数据
                            layerid: key, //图层id
                            iconcfg: {
                                // image: getPointIcon(key),
                                image: `./pointAssets/${key}-l.png`,
                                size: 40,
                            },
                            popcfg: {
                                offset: [0, 0],
                                show: false, //关闭按钮
                            },
                            cluster: false,
                            onclick: key == 'shop' ? this.shopPop: key == 'vol'? this.volPop: key == 'recorder'? this.zfryInfo: key == 'lawCar'? this.zfcInfo: key == 'monitor'? this.openVideoWindow : '',
                        });
                    }
                })
            } else {
                if (type != 'area'){
                    MapService.loadPointLayer2({
                        data: this.listAll[type], //点位数据
                        layerid: type, //图层id
                        iconcfg: {
                            // image: getPointIcon(type),
                            image: `./pointAssets/${type}-l.png`,
                            size: 40,
                        },
                        popcfg: {
                            offset: [0, 0],
                            show: false, //关闭按钮
                        },
                        cluster: false,
                        onclick: type == 'shop' ? this.shopPop: type == 'vol'? this.volPop: type == 'recorder'? this.zfryInfo: type == 'lawCar'? this.zfcInfo:'',
                    });
                } else {
                    this.createArea()
                }
            }
        },
        removeLayer(type) {
            if (type == 'all'){
               MapService.removeLayer('area')
               MapService.removeLayer('recorder')
               MapService.removeLayer('taxi')
               MapService.removeLayer('lawCar')
               MapService.removeLayer('shop')
               MapService.removeLayer('monitor')
               MapService.removeLayer('vol')
            } else {
                MapService.removeLayer(type)
            }
        },
        addEventLayer(type) {
            if (type == 'all') {
                Object.keys(this.listAll).forEach(key => {
                    if (['four', 'capture', 'inspection', 'punish', 'tout', 'autoCapture', 'transport'].includes(key)) {
                        MapService.loadPointLayer2({
                            data: this.listAll[key], //点位数据
                            layerid: key, //图层id
                            iconcfg: {
                                // image: getPointIcon(key),
                                image: `./pointAssets/${key}-l.png`,
                                size: 40,
                            },
                            popcfg: {
                                offset: [0, 0],
                                show: false, //关闭按钮
                            },
                            cluster: false,
                            onclick: key == 'four' ? this.openFourWindow: key == 'inspection' ? this. openInspectionWindow: key == 'capture' ? this.openCaptureWindow : key == 'punish' ? this.openPunishWindow : key == 'tout' ? this.openToutWindow : key == 'autoCapture' ? this.openAutoCaptureWindow : this.openTransportWindow,
                        });
                    }
                })
            } else {
                MapService.loadPointLayer2({
                    data: this.listAll[type], //点位数据
                    layerid: type, //图层id
                    iconcfg: {
                        // image: getPointIcon(type),
                        image: `./pointAssets/${type}-l.png`,
                        size: 40,
                    },
                    popcfg: {
                        offset: [0, 0],
                        show: false, //关闭按钮
                    },
                    cluster: false,
                    onclick: type == 'four' ? this.openFourWindow: type == 'inspection' ? this. openInspectionWindow: type == 'capture' ? this.openCaptureWindow : type == 'punish' ? this.openPunishWindow : type == 'tout' ? this.openToutWindow : type == 'autoCapture' ? this.openAutoCaptureWindow : this.openTransportWindow,
                });
            }
        },
        removeEventLayer(type) {
            if (type == 'all'){
               MapService.removeLayer('four')
               MapService.removeLayer('capture')
               MapService.removeLayer('inspection')
               MapService.removeLayer('punish')
               MapService.removeLayer('tout')
               MapService.removeLayer('autoCapture')
               MapService.removeLayer('transport')
            } else {
                MapService.removeLayer(type)
            }
        },
        createArea() {
            const boundary = "119.63256,29.11648;119.63179,29.11599;119.631,29.11553;119.62992,29.11481;119.62925,29.11425;119.62877,29.11384;119.62829,29.11309;119.62694,29.1116;119.62619,29.11014;119.62646,29.10986;119.62672,29.10982;119.62798,29.11003;119.62762,29.11165;119.62866,29.11181;119.62922,29.11141;119.62968,29.11143;119.63159,29.1119;119.63344,29.11231;119.63346,29.11239;119.63358,29.11252;119.63255,29.11316;119.62985,29.11256;119.62965,29.11323;119.63191,29.11387;119.63182,29.11436;119.63183,29.11469;119.63346,29.11513;119.6332,29.11576;119.63256,29.11648"
            // 转换为 GeoJSON
            const geoJSON = {
                type: "FeatureCollection",
                features: [{
                    type: "Feature",
                    properties: {}, // 可以添加自定义属性
                    geometry: {
                        type: "Polygon",
                        coordinates: [
                            boundary.split(';').map(point => {
                            const [lng, lat] = point.split(',').map(Number);
                            // 注意 GeoJSON 坐标顺序是 [经度, 纬度]
                            return [lng, lat]; 
                            })
                        ]
                    }
                }]
            };
            MapService.loadPolygonLayer({
              layerid: "area",
              data: geoJSON,
              style: {
                strokeColor: [255, 50, 40, 0.9], //多边形轮廓颜色透明度
                fillColor: [193, 210, 240, 0.2], //多边形填充色
              },
              onclick: function (e) {
                console.log("多边形点击事件");
              },
            })
        },
        shopPop(e) {
            let this_ = this;
            console.log("点到的",e)
            getShopInfo({ shopId: e.id }).then(res => {
                this_.shopDetail = res.data
                return getFiles({businessId: 16, tableName: 'case_shop'})
            }).then(fileRes => {
                console.log(fileRes)
                if (Array.isArray(fileRes.rows)) {
                    if (fileRes.rows[3]) {
                        // this_.fImgSrc = "https://media.istockphoto.com/id/1914345942/ja/%E3%82%B9%E3%83%88%E3%83%83%E3%82%AF%E3%83%95%E3%82%A9%E3%83%88/construction-with-cranes-in-tokyo.jpg?s=2048x2048&w=is&k=20&c=HCGj72C-97H2J-vI1zt7tkjbentP091IblQHrgvWn-o="
                        this_.fImgSrc = `/zqzfj${fileRes.rows[3].filePath}`
                    } else if (fileRes.rows[0]) {
                        // this_.fImgSrc = "https://media.istockphoto.com/id/1914345942/ja/%E3%82%B9%E3%83%88%E3%83%83%E3%82%AF%E3%83%95%E3%82%A9%E3%83%88/construction-with-cranes-in-tokyo.jpg?s=2048x2048&w=is&k=20&c=HCGj72C-97H2J-vI1zt7tkjbentP091IblQHrgvWn-o="
                        this_.fImgSrc = `/zqzfj${fileRes.rows[0].filePath}`
                    } else {
                        this_.fImgSrc = null
                    }
                    this_.fSrcList = fileRes.rows.map(item => `/zqzfj${item.filePath}`)
                } else {
                    this_.fImgSrc = null
                    this_.fSrcList = []
                }
                console.log(this_.fImgSrc)
                let str = `
                    <div style="position: relative; width: 1200px; background: rgba(16, 24, 44, 0.95); border-radius: 4px; box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3); border: 2px solid rgba(18, 142, 232, 0.5) !important;;">
                        <div style="display: flex; padding: 30px;">
                            <img src="${this_.fImgSrc}" style="width: 220px; height: 250px; object-fit: cover; border-radius: 4px; margin-right: 30px;"></img>
                            <div style="flex: 1;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                                    <div style="font-size: 40px; color: #00d4ff; font-weight: bold;">${e.name}</div>
                                    <img src="./pointAssets/close.png" style="width: 28px; height: 28px; cursor: pointer;" onclick="window.closeShopPop()">
                                </div>
                                <div style="margin-bottom: 15px;">
                                    <img src="${require('@/assets/zqyzt/windowInfo/fulb.png')}" style="width: 32px; height: 32px;  margin-right: 12px;">
                                    <span style="color: #8f9bb3; font-size: 28px;">法人：</span>
                                    <span style="color: #00d4ff; font-size: 28px;">${this_.shopDetail.contactsName}</span>
                                </div>
                                <div style="margin-bottom: 15px;">
                                    <img src="${require('@/assets/zqyzt/windowInfo/dh.png')}" style="width: 32px; height: 32px;  margin-right: 12px;">
                                    <span style="color: #8f9bb3; font-size: 28px;">电话：</span>
                                    <span style="color: #00d4ff; font-size: 28px;">${this_.shopDetail.contactsTelephone}</span>
                                </div>
                                <div>
                                    <img src="${require('@/assets/zqyzt/windowInfo/hdrq.png')}" style="width: 32px; height: 32px;  margin-right: 12px;">
                                    <span style="color: #8f9bb3; font-size: 28px;">地址：</span>
                                    <span style="color: #00d4ff; font-size: 28px;">${this_.shopDetail.address}</span>
                                </div>
                            </div>
                        </div>
                        <div style="background: rgba(20, 30, 54, 0.95); padding: 20px 30px;">
                            <table style="width: 100%; border-collapse: collapse;">
                                <tr style="text-align: center; color: #8f9bb3; font-size: 28px; font-weight: bold;">
                                    <th style="padding: 15px; border-bottom: 1px solid rgba(0,212,255,0.1);">名称</th>
                                    <th style="padding: 15px; border-bottom: 1px solid rgba(0,212,255,0.1);">当事人</th>
                                    <th style="padding: 15px; border-bottom: 1px solid rgba(0,212,255,0.1);">发生时间</th>
                                    <th style="padding: 15px; border-bottom: 1px solid rgba(0,212,255,0.1);">案件类型</th>
                                    <th style="padding: 15px; border-bottom: 1px solid rgba(0,212,255,0.1);">案件类别</th>
                                    <th style="padding: 15px; border-bottom: 1px solid rgba(0,212,255,0.1);">案件内容</th>
                                </tr>
                            </table>
                            <div style="text-align: center; color: #8f9bb3; font-size: 28px; padding: 40px 0;">
                                暂无数据
                            </div>
                        </div>
                        <!-- 添加指向箭头 -->
                        <div style="
                            position: absolute;
                            left: 50%;
                            bottom: -20px;
                            transform: translateX(-50%);
                            width: 0;
                            height: 0;
                            border-left: 20px solid transparent;
                            border-right: 20px solid transparent;
                            border-top: 20px solid rgba(20, 30, 54, 0.95);
                            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
                        "></div>
                    </div>
                `;
                let objData = {
                    layerid: "shop_pop",
                    position: [e.esX, e.esY],
                    offset: [-455, -20],
                    content: str,
                };
                MapService._createPopup(objData);
            }).catch(() => {
                this.loading = false
            })

            window.view.goTo({
                position: {
                  spatialReference: {
                    latestWkid: 4490,
                    wkid: 4490,
                  },
                  x: e.esX,
                  y: e.esY + 0.003,
                  z: 2226.879426258436,
                },
                heading: 0.26874578434742386,
                tilt: 0.49999999999694683,
            })
            
            window.closeShopPop = () => {
                MapService.removeLayer("shop_pop");
            }
        },
        volPop(e) {
            let this_ = this;
            console.log("点到的",e)

            Promise.all([
                getTaskInfo(e.id),
                getFiles({ businessId: e.id, tableName: 'vol_task' })
            ]).then(resAry => {
                let [dataDetail, files] = resAry
                this_.volDetail = dataDetail.data
                // 图片部分
                let fSrcList = [], sSrcList = []
                files.rows.forEach(item => {
                if (item.status == 1) {
                    fSrcList.push('/zqzfj' + item.filePath)
                } else if (item.status == 2) {
                    sSrcList.push('/zqzfj' + item.filePath)
                }
                })
                this_.fSrcList1 = fSrcList
                this_.fImgSrc1 = fSrcList[0]
                this_.sSrcLis = sSrcList

                let str = `
                    <div style="position: relative; width: 1500px; background: rgba(16, 24, 44, 0.95); border-radius: 8px; box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3);  border: 2px solid rgba(18, 142, 232, 0.5) !important;">
                        <!-- 标题栏 -->
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 20px 30px; border-bottom: 1px solid rgba(0,212,255,0.3);">
                            <div style="font-size: 36px; color: #00d4ff; font-weight: 500;">${this_.volDetail.title}</div>
                            <img src="./pointAssets/close.png" style="width: 32px; height: 32px; cursor: pointer;" onclick="window.closeVolPop()">
                        </div>
                        
                        <!-- 内容区 -->
                        <div style="padding: 20px;">
                            <div style="display: flex; padding: 20px;">
                                <img src="${this_.fImgSrc1}" style="width: 300px; height: 300px; object-fit: cover; border-radius: 4px; margin-right: 30px;"></img>
                                <!-- 基本信息 -->
                                <div style="margin-bottom: 30px;">
                                    <div style="display: flex; align-items: center; margin-bottom: 15px;">
                                        <img src="${require('@/assets/zqyzt/windowInfo/fulb.png')}" style="width: 32px; height: 32px; margin-right: 12px;">
                                        <span style="color: #8f9bb3; font-size: 28px; margin-right: 12px;">服务类别:</span>
                                        <span style="color: #00d4ff; font-size: 28px;">${this_.volDetail.typeName}</span>
                                    </div>
                                    <div style="display: flex; align-items: center; margin-bottom: 15px;">
                                        <img src="${require('@/assets/zqyzt/windowInfo/fzr.png')}" style="width: 32px; height: 32px; margin-right: 12px;">
                                        <span style="color: #8f9bb3; font-size: 28px; margin-right: 12px;">负责人:</span>
                                        <span style="color: #00d4ff; font-size: 28px;">${this_.volDetail.userName}</span>
                                    </div>
                                    <div style="display: flex; align-items: center; margin-bottom: 15px;">
                                        <img src="${require('@/assets/zqyzt/windowInfo/dh.png')}" style="width: 32px; height: 32px; margin-right: 12px;">
                                        <span style="color: #8f9bb3; font-size: 28px; margin-right: 12px;">电话:</span>
                                        <span style="color: #00d4ff; font-size: 28px;">${this_.volDetail.phone}</span>
                                    </div>
                                    <div style="display: flex; align-items: center; margin-bottom: 15px;">
                                        <img src="${require('@/assets/zqyzt/windowInfo/hdrq.png')}" style="width: 32px; height: 32px; margin-right: 12px;">
                                        <span style="color: #8f9bb3; font-size: 28px; margin-right: 12px;">活动日期:</span>
                                        <span style="color: #00d4ff; font-size: 28px;">${this_.volDetail.title} 至 ${this_.volDetail.title}</span>
                                    </div>
                                    <div style="display: flex; align-items: center;">
                                        <img src="${require('@/assets/zqyzt/windowInfo/zmrs.png')}" style="width: 32px; height: 32px; margin-right: 12px;">
                                        <span style="color: #8f9bb3; font-size: 28px; margin-right: 12px;">招募人数:</span>
                                        <span style="color: #00d4ff; font-size: 28px;">${this_.volDetail.volUserCount || 0} / ${this_.volDetail.numOfRecruits}</span>
                                    </div>
                                </div>
                            </div>
                            <!-- 详细内容 -->
                            <div>
                                <div style="font-size: 28px; color: #00d4ff; font-weight: 500; padding: 12px 20px; background: rgba(0,212,255,0.1); margin-bottom: 20px; border-radius: 4px;">
                                    详细内容
                                </div>
                                <div style="color: #8f9bb3; line-height: 1.6; font-size: 28px; padding: 0 20px;">
                                    ${this_.volDetail.content}
                                </div>
                            </div>

                            <!-- 精彩内容 -->
                            <div style="margin-top: 30px;">
                                <div style="font-size: 28px; color: #00d4ff; font-weight: 500; padding: 12px 20px; background: rgba(0,212,255,0.1); margin-bottom: 20px; border-radius: 4px;">
                                    精彩内容
                                </div>
                                <div style="padding: 0 20px;">
                                    <img src="${this_.sSrcLis}" style="width: 300px; height: 300px; object-fit: cover; border-radius: 4px;">
                                </div>
                            </div>
                        </div>
                        <!-- 添加指向箭头 -->
                        <div style="
                            position: absolute;
                            left: 50%;
                            bottom: -20px;
                            transform: translateX(-50%);
                            width: 0;
                            height: 0;
                            border-left: 20px solid transparent;
                            border-right: 20px solid transparent;
                            border-top: 20px solid rgba(20, 30, 54, 0.95);
                            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
                        "></div>
                    </div>
                `;
                let objData = {
                    layerid: "vol_pop",
                    position: [e.esX, e.esY],
                    offset: [-599, -20],
                    content: str,
                };
                MapService._createPopup(objData);
            }).catch(() => {
            })

            window.view.goTo({
                position: {
                  spatialReference: {
                    latestWkid: 4490,
                    wkid: 4490,
                  },
                  x: e.esX,
                  y: e.esY + 0.003,
                  z: 2226.879426258436,
                },
                heading: 0.26874578434742386,
                tilt: 0.49999999999694683,
            })
            
            window.closeVolPop = () => {
                MapService.removeLayer("vol_pop");
            }
        },
        zfryInfo(e) {
           console.log(e)
           this.showZfryInfo = ! this.showZfryInfo
           this.recorderId = e.id
        },
        zfcInfo(e) {
           this.showZfcInfo = ! this.showZfcInfo
           this.lawCarId = e.id
        },
        openFourWindow(e) {
            this.fourWindowVisible = !this.fourWindowVisible
        },
        openInspectionWindow(e) {
            console.log(e)
            this.inspectionWindowVisible = !this.inspectionWindowVisible
            this.inspectionWindowId = e.id
        },
        openToutWindow(e) {
            this.toutWindowVisible = !this.toutWindowVisible
        },
        openAutoCaptureWindow(e) {
            console.log(22222)
            this.autoWindowVisible = !this.autoWindowVisible
        },
        openTransportWindow(e) {
            this.transportWindowId = e.id
            this.transportWindowVisible = !this.transportWindowVisible
        },
        openCaptureWindow(e) {
            this.captureWindowId = e.id
            this.captureWindowVisible = !this.captureWindowVisible
        },
        openVideoWindow(e) {
            // this.videoVisible = !this.videoVisible
            this.hkVideoVisible = true
            this.winVideoName = e.name
            this.hkVideoId = e.code
        },
        openCaptureWindow(e) {
            this.captureWindowVisible = true
            this.captureWindowId = e.id
        },
        openAutoCaptureWindow(e) {
            this.trafficCaptureWindowVisible = true
            this.trafficCaptureWindowId = e.id
        }
    },
    beforeDestroy() {
    // 清理全局函数
        if (window.closeShopPop) {
            delete window.closeShopPop;
        }
    }
}
</script>

<style scoped>
.command-panel {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-left: 40%;
    width: 60%;
}

.command-btn {
    background: linear-gradient(90deg, rgba(0,212,255,0.2) 0%, rgba(45,98,255,0.3) 100%);
    border: 1px solid rgba(0,212,255,0.5);
    box-shadow: 0 0 10px rgba(0,212,255,0.2);
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    color: #00d4ff;
    text-align: center;
    width: 300px;
    &:hover, &.active {
      background: linear-gradient(90deg, rgba(0,212,255,0.4) 0%, rgba(45,98,255,0.5) 100%);
      border-color: #00d4ff;
    }
}

.btn-text {
    font-size: 30px;
    font-weight: bold;
}

.btn-subtext {
    font-size: 26px;
    opacity: 0.8;
    margin-top: 2px;
}

.function-panel {
    margin-top: 10px;
    background: rgba(16, 24, 44, 0.8);
    border: 1px solid rgba(0,212,255,0.3);
    border-radius: 4px;
    padding: 15px;
    width: 400px;
    box-shadow: 0 0 20px rgba(0,212,255,0.1);
}

.function-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.function-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    padding: 10px;
    border-radius: 4px;
    transition: all 0.3s;
}

.function-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.icon-box {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 5px;
    border: 1px solid rgba(0,212,255,0.3);
    box-shadow: 0 0 10px rgba(0,212,255,0.2);
}

.icon-box.blue { background: linear-gradient(145deg, #1890ff20, #096dd920); }
.icon-box.yellow { background: linear-gradient(145deg, #faad1420, #d4880620); }
.icon-box.purple { background: linear-gradient(145deg, #722ed120, #531dab20); }
.icon-box.orange { background: linear-gradient(145deg, #fa8c1620, #d46b0820); }
.icon-box.red { background: linear-gradient(145deg, #f5222d20, #cf132220); }
.icon-box.green { background: linear-gradient(145deg, #52c41a20, #389e0d20); }
.icon-box.pink { background: linear-gradient(145deg, #eb2f9620, #c41d7f20); }

.item-text {
    color: #00d4ff;
    font-size: 28px;
    margin-top: 5px;
    text-shadow: 0 0 5px rgba(0,212,255,0.5);
}

.count {
    color: rgba(0,212,255,0.7);
    font-size: 28px;
    margin-top: 2px;
}

/* 事件列表面板样式 */
.event-panel {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-left: 40%;
    width: 60%;
    margin-top: 40px;
}

.event-btn {
    background: linear-gradient(90deg, rgba(0,212,255,0.2) 0%, rgba(45,98,255,0.3) 100%);
    border: 1px solid rgba(0,212,255,0.5);
    box-shadow: 0 0 10px rgba(0,212,255,0.2);
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    color: #00d4ff;
    text-align: center;
    width: 300px;
    &:hover, &.active {
      background: linear-gradient(90deg, rgba(0,212,255,0.4) 0%, rgba(45,98,255,0.5) 100%);
      border-color: #00d4ff;
    }
}

.event-function-panel {
    margin-top: 10px;
    background: rgba(16, 24, 44, 0.8);
    border: 1px solid rgba(0,212,255,0.3);
    border-radius: 4px;
    padding: 15px;
    width: 400px;
    box-shadow: 0 0 20px rgba(0,212,255,0.1);
}

.event-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.event-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    padding: 10px;
    border-radius: 4px;
    transition: all 0.3s;
}

.event-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.event-icon-box {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 5px;
    background: linear-gradient(145deg, rgba(255,107,0,0.2), rgba(255,107,0,0.1));
    border: 1px solid rgba(0,212,255,0.3);
    box-shadow: 0 0 10px rgba(0,212,255,0.2);
}

.event-text {
    color: #00d4ff;
    font-size: 28px;
    margin-top: 5px;
    text-shadow: 0 0 5px rgba(0,212,255,0.5);
}

.event-count {
    color: rgba(0,212,255,0.7);
    font-size: 28px;
    margin-top: 2px;
}

/* 图标样式 */
[class^="icon-"] {
    width: 55px;
    height: 55px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.icon-all { background-image: url('@/assets/zqyzt/menu-1-s.png'); }
.icon-law { background-image: url('@/assets/zqyzt/menu-4-s.png'); }
.icon-monitor { background-image: url('@/assets/zqyzt/menu-2-s.png'); }
.icon-store { background-image: url('@/assets/zqyzt/shop-l.png'); }
.icon-car { background-image: url('@/assets/zqyzt/menu-3-s.png'); }
.icon-volunteer { background-image: url('@/assets/zqyzt/menu-10-s.png'); }
.icon-taxi { background-image: url('@/assets/zqyzt/taxi-l.png'); }
.icon-area { background-image: url('@/assets/zqyzt/menu-8-s.png'); }

/* 事件图标样式 */
[class^="event-icon-"] {
    width: 55px;
    height: 55px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.event-icon-all { background-image: url('@/assets/zqyzt/menu-event-all.png'); }
.event-icon-simple { background-image: url('@/assets/zqyzt/menu-event-4.png'); }
.event-icon-four { background-image: url('@/assets/zqyzt/menu-event-1.png'); }
.event-icon-yellow { background-image: url('@/assets/zqyzt/menu-event-5.png'); }
.event-icon-monitor { background-image: url('@/assets/zqyzt/menu-event-2.png'); }
.event-icon-ai { background-image: url('@/assets/zqyzt/menu-event-6.png'); }
.event-icon-patrol { background-image: url('@/assets/zqyzt/menu-event-3.png'); }
.event-icon-report { background-image: url('@/assets/zqyzt/menu-event-7.png'); }

/* 新增的图标按钮样式 */
.icon-buttons {
    display: flex;
    gap: 20px;
    margin-left: 40%;
    margin-bottom: 60px;
    width: 500px;
}

.icon-btn {
    width: 160px;
    height: 80px;
    background: linear-gradient(90deg, rgba(0,212,255,0.2) 0%, rgba(45,98,255,0.3) 100%);
    border: 1px solid rgba(0,212,255,0.5);
    box-shadow: 0 0 10px rgba(0,212,255,0.2);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s;
}

.icon-btn:hover {
    background: linear-gradient(90deg, rgba(0,212,255,0.4) 0%, rgba(45,98,255,0.5) 100%);
    border-color: #00d4ff;
}

/* 独立的顶部图标样式 */
.top-icon {
    width: 130px;
    height: 55px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.icon-monitor-screen {
    background-image: url('@/assets/zqyzt/monitor-icon.png');
}

.icon-camera {
    background-image: url('@/assets/zqyzt/dataView-icon.png');
}

.function-item.item-noactive,
.event-item.item-noactive {
    opacity: 0.4;
}

.function-item,
.event-item {
    opacity: 1;
    transition: opacity 0.3s;
}
</style>