<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" :inline="true">
      <el-form-item label="案件类型" prop="caseType">
        <el-select v-model="queryParams.caseType" placeholder="案件类型" clearable size="small">
          <el-option value="four" label="四位一体" />
          <el-option value="capture" label="监控抓拍" />
          <el-option value="inspection" label="巡查发现" />
          <el-option value="punish" label="简易案件" />
          <el-option value="tout" label="黄牛处置" />
          <el-option value="trafficCapture" label="电子抓拍" />
          <el-option value="transport" label="违规处置" />
        </el-select>
      </el-form-item>
      <el-form-item label="内容">
        <el-input v-model="queryParams.searchValue" type="text" placeholder="请输入内容" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:attentione:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="attentioneList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="案件内容" align="center" prop="caseContent" />
      <el-table-column label="案件类型" align="center" prop="caseTypeName" />
      <el-table-column label="关注时间" align="center" prop="createTime" width="200">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['business:attentione:edit']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleSelect(scope.row)"
          >
            案件详情
          </el-button>
          <el-button
            v-hasPermi="['business:attentione:remove']"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 四位一体详情 -->
    <fourWindow :visible.sync="fourWindowVisible" title="四位一体详情" :hand-disabled="false" :type-data="typeData" :detail-id="fourWindowId" :form-disabled="true" />
    <!-- 监控抓拍详情 -->
    <captureWindow :visible.sync="captureWindowVisible" :form-disabled="true" title="监控抓拍详情" :detail-id="captureWindowId" />
    <!-- 巡查发现详情 -->
    <el-dialog :close-on-click-modal="false" title="巡查发现详情" :visible.sync="inspectionWindowVisible" width="1000px">
      <inspectionWindow v-if="inspectionWindowVisible" :detail-id="inspectionWindowId" :form-disabled="true" :qd="false" @oncancel="inspectionWindowVisible = false" />
    </el-dialog>
    <!-- 简易案件 -->
    <el-dialog :close-on-click-modal="false" title="简易案件详情" :visible.sync="punishWindowVisible" width="1000px">
      <punishWindow v-if="punishWindowVisible" :detail-id="punishWindowId" :form-disabled="true" :qd="false" @oncancel="punishWindowVisible = false" />
    </el-dialog>
    <!-- 黄牛处置 -->
    <el-dialog :close-on-click-modal="false" title="黄牛处置详情" :visible.sync="toutWindowVisible" width="1000px">
      <toutWindow v-if="toutWindowVisible" :detail-id="toutWindowId" :qd="false" @oncancel="toutWindowVisible = false" />
    </el-dialog>
    <!-- 电子抓拍 -->
    <trafficCaptureWindow :visible.sync="trafficCaptureWindowVisible" title="电子抓拍详情" :detail-id="trafficCaptureWindowId" :form-disabled="true" />
    <!-- 违规处置 -->
    <transportWindow :visible.sync="transportWindowVisible" title="违规处置详情" :detail-id="transportWindowVisibleId" :form-disabled="true" :car-type-options="transportWindowTypeData" />
  </div>
</template>

<script>
import { listAttentione, delAttentione, addAttentione, updateAttentione, exportAttentione } from '@/api/case/attentione/attentione'

import fourWindow from '@/pages/supervise/views/jgdc/quaternity/components/fromlist.vue'
import captureWindow from '@/pages/case/views/synthetical/capture/components/fromlist.vue'
import inspectionWindow from '@/pages/case/views/synthetical/patrol/components/fromlist.vue'
import punishWindow from '@/pages/case/views/synthetical/punishment/components/fromlist.vue'
import toutWindow from '@/pages/case/views/tout/hncz/components/fromlist.vue'
import trafficCaptureWindow from '@/pages/case/views/jjzf/dzzp/components/fromList.vue'
import transportWindow from '@/pages/case/views/pipe/illegal/components/fromlist.vue'

export default {
  name: 'Attentione',
  components: {
    fourWindow,
    captureWindow,
    inspectionWindow,
    punishWindow,
    toutWindow,
    trafficCaptureWindow,
    transportWindow
    // mBMenu
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 我的案件关注表格数据
      attentioneList: [],
      // 状态数据字典
      typeOptions: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        caseType: undefined,
        searchValue: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      typeData: [{id: 1, value: '环卫保洁'}, {id: 2, value: '园林'}, {id: 3, value: '绿化'}, {id: 4, value: '市政'} ],
      fourWindowVisible: false,
      fourWindowId: 0,
      captureWindowVisible: false,
      captureWindowId: 0,
      inspectionWindowVisible: false,
      inspectionWindowId: 0,
      punishWindowVisible: false,
      punishWindowId: 0,
      toutWindowVisible: false,
      toutWindowId: 0,
      trafficCaptureWindowVisible: false,
      trafficCaptureWindowId: 0,
      transportWindowVisible: false,
      transportWindowVisibleId: 0,
      transportWindowTypeData: []
    }
  },
  async mounted() {
    await this.getDicts('transport_car_type').then(res => {
      this.transportWindowTypeData = res.data
    })
    this.getList()
  },
  methods: {
    /** 查询我的案件关注列表 */
    getList() {
      this.loading = true
      listAttentione(this.queryParams).then(response => {
        this.attentioneList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        attentionId: null,
        content: null,
        userId: null,
        userName: null,
        caseId: null,
        caseContent: null,
        caseType: null,
        caseTypeName: null,
        type: null,
        status: '0',
        delFlag: null,
        deptId: null,
        deptName: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {pageNum: 1, pageSize: 10, caseType: '', searchValue: ''}
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.attentionId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加我的案件关注'
    },
    /** 修改按钮操作 */
    handleSelect(row) {
      this.reset()
      const caseId = row.caseId
      this.openEventWindow(row.caseType, caseId)
    },
    openEventWindow(type, id) {
      if (type == 'four') {
        this.fourWindowVisible = true
        this.fourWindowId = id
      } else if (type == 'capture') {
        this.captureWindowVisible = true
        this.captureWindowId = id
      } else if (type == 'inspection') {
        this.inspectionWindowVisible = true
        this.inspectionWindowId = id
      } else if (type == 'tout') {
        this.toutWindowVisible = true
        this.toutWindowId = id
      } else if (type == 'punish') {
        this.punishWindowVisible = true
        this.punishWindowId = id
      } else if (type == 'trafficCapture') {
        this.trafficCaptureWindowVisible = true
        this.trafficCaptureWindowId = id
      } else if (type == 'transport') {
        this.transportWindowVisible = true
        this.transportWindowVisibleId = id
      } else {
        this.$message.error('信息错误，请联系管理员')
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.attentionId != null) {
            updateAttentione(this.form).then(() => {
              this.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addAttentione(this.form).then(() => {
              this.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const attentionIds = row.attentionId || this.ids
      this.$confirm('是否确认删除?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return delAttentione(attentionIds)
      }).then(() => {
        this.getList()
        this.msgSuccess('删除成功')
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams
      this.$confirm('是否确认导出所有我的案件关注数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return exportAttentione(queryParams)
      }).then(response => {
        this.download(response.msg)
      })
    }
  }
}
</script>
