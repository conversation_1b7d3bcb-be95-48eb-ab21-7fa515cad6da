import * as externalRenderers from "@arcgis/core/views/3d/externalRenderers.js";
import { libRoot } from "../config.js";

var THREE = window.THREE_r116;

const materialColor = {
  blue: 0x0000ff,
  cyan: 0x40e9c8,
  orange: 0xec9e42,
  red: 0xec4141,
  yellow: 0xe6ec42,
  yellowgreen: 0x42ecaa,
};

class WallLayer {
  constructor({ view, points, height, color = "cyan", isDynamic = true }) {
    this.view = view;
    this.points = points;
    this.offset = 0;
    this.height = height;
    this.camera = null;
    this.color = color;
    this.materialColor = materialColor[color];
    this.isDynamic = isDynamic;
  }

  setup(context) {
    this.renderer = new THREE.WebGLRenderer({
      context: context.gl, // 可用于将渲染器附加到已有的渲染环境(RenderingContext)中
      premultipliedAlpha: false, // renderer是否假设颜色有 premultiplied alpha. 默认为true
    });
    this.renderer.setPixelRatio(window.devicePixelRatio); // 设置设备像素比。通常用于避免HiDPI设备上绘图模糊
    this.renderer.setViewport(0, 0, view.width, view.height); // 视口大小设置

    // 防止THREE.js清除ArcGIS JS API提供的缓冲区。
    this.renderer.autoClearDepth = false; // 定义renderer是否清除深度缓存
    this.renderer.autoClearStencil = false; // 定义renderer是否清除模板缓存
    this.renderer.autoClearColor = false; // 定义renderer是否清除颜色缓存

    // ArcGIS JS API渲染自定义离屏缓冲区，而不是默认的帧缓冲区。
    // 我们必须将这段代码注入到THREE.js运行时中，以便绑定这些缓冲区而不是默认的缓冲区。
    const originalSetRenderTarget = this.renderer.setRenderTarget.bind(
      this.renderer
    );
    this.renderer.setRenderTarget = function (target) {
      originalSetRenderTarget(target);
      if (target == null) {
        // 绑定外部渲染器应该渲染到的颜色和深度缓冲区
        context.bindRenderTarget();
      }
    };

    this.scene = new THREE.Scene(); // 场景
    this.camera = new THREE.PerspectiveCamera(); // 相机

    // 添加坐标轴辅助工具
    const axesHelper = new THREE.AxesHelper(10000000);
    this.scene.add(axesHelper);

    // 计算顶点
    let transform = new THREE.Matrix4(); // 变换矩阵
    let transformation = new Array(16);
    let vector3List = []; // 顶点数组
    let faceList = []; // 三角面数组
    let faceVertexUvs = []; // 面的 UV 层的队列，该队列用于将纹理和几何信息进行映射
    // 转换顶点坐标
    this.points.forEach((point) => {
      // 将经纬度坐标转换为xy值\
      // let pointXY = webMercatorUtils.lngLatToXY(point[0], point[1]);
      // 先转换高度为0的点
      transform.fromArray(
        externalRenderers.renderCoordinateTransformAt(
          this.view,
          [point[0], point[1], 0], // 坐标在地面上的点[x值, y值, 高度值]
          this.view.spatialReference,
          transformation
        )
      );
      vector3List.push(
        new THREE.Vector3(
          transform.elements[12],
          transform.elements[13],
          transform.elements[14]
        )
      );
      // 再转换距离地面高度为height的点
      transform.fromArray(
        externalRenderers.renderCoordinateTransformAt(
          this.view,
          [point[0], point[1], this.height], // 坐标在空中的点[x值, y值, 高度值]
          this.view.spatialReference,
          transformation
        )
      );
      vector3List.push(
        new THREE.Vector3(
          transform.elements[12],
          transform.elements[13],
          transform.elements[14]
        )
      );
    });
    // 纹理坐标
    const t0 = new THREE.Vector2(0, 0); // 图片左下角
    const t1 = new THREE.Vector2(1, 0); // 图片右下角
    const t2 = new THREE.Vector2(1, 1); // 图片右上角
    const t3 = new THREE.Vector2(0, 1); // 图片左上角
    // 生成几何体三角面
    for (let i = 0; i < vector3List.length - 2; i++) {
      if (i % 2 === 0) {
        faceList.push(new THREE.Face3(i, i + 2, i + 1));
        faceVertexUvs.push([t0, t1, t3]);
      } else {
        faceList.push(new THREE.Face3(i, i + 1, i + 2));
        faceVertexUvs.push([t3, t1, t2]);
      }
    }
    // 几何体
    const geometry = new THREE.Geometry();
    geometry.vertices = vector3List;
    geometry.faces = faceList;
    geometry.faceVertexUvs[0] = faceVertexUvs;
    const geometry2 = geometry.clone();
    // 纹理
    this.alphaMap = new THREE.TextureLoader().load(
      `${libRoot}img/texture_1.png`
    );
    // todo 此处做成可配置的图片，支持多种颜色
    this.texture = new THREE.TextureLoader().load(
      `${libRoot}img/texture_${this.color}.png`
    );
    // this.texture = new THREE.TextureLoader().load(`${libRoot}img/texture_2.png`);
    this.texture.wrapS = THREE.RepeatWrapping;
    this.texture.wrapT = THREE.RepeatWrapping;
    this.texture.offset.set(0, 0.5);
    const material = new THREE.MeshBasicMaterial({
      // color: 0x0000ff,
      color: this.materialColor,
      side: THREE.DoubleSide,
      transparent: true, // 必须设置为true,alphaMap才有效果
      depthWrite: false, // 渲染此材质是否对深度缓冲区有任何影响
      alphaMap: this.alphaMap,
    });
    const mesh = new THREE.Mesh(geometry, material);
    const material2 = new THREE.MeshBasicMaterial({
      side: THREE.DoubleSide,
      transparent: true,
      depthWrite: false, // 渲染此材质是否对深度缓冲区有任何影响
      map: this.texture,
    });
    const mesh2 = new THREE.Mesh(geometry2, material2);
    this.scene.add(mesh);
    this.isDynamic && this.scene.add(mesh2);
    context.resetWebGLState();
  }
  render(context) {
    // 更新相机参数
    const cam = context.camera;
    this.camera.position.set(cam.eye[0], cam.eye[1], cam.eye[2]);
    this.camera.up.set(cam.up[0], cam.up[1], cam.up[2]);
    this.camera.lookAt(
      new THREE.Vector3(cam.center[0], cam.center[1], cam.center[2])
    );
    // 投影矩阵可以直接复制
    this.camera.projectionMatrix.fromArray(cam.projectionMatrix);
    // 更新
    if (this.isDynamic) {
      if (this.offset <= 0) {
        this.offset = 1;
      } else {
        this.offset -= 0.02;
      }
      if (this.texture) {
        this.texture.offset.set(0, this.offset);
      }
    }

    // 绘制场景
    this.renderer.state.reset();
    this.renderer.render(this.scene, this.camera);
    // 请求重绘视图。
    externalRenderers.requestRender(view);
    // cleanup
    context.resetWebGLState();
  }
}

export default WallLayer;
