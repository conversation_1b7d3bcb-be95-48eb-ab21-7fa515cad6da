<template>
  <div class="left-Map">
    <dyfwrc></dyfwrc>
    <ljfwrc></ljfwrc>
    <fwlx></fwlx>
  </div>
</template>

<script>
import dyfwrc from './dyfwrc'
import ljfwrc from './ljfwrc'
import fwlx from './fwlx'
export default {
  name: 'index',
  components: {
    fwlx,
    dyfwrc,
    ljfwrc,
  },
  data() {
    return {}
  },
  watch: {},
  computed: {},
  mounted() {},
  methods: {
    getList() {},
  },
}
</script>

<style scoped lang='less'>
</style>