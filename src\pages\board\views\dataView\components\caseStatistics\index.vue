<template>
  <div class="case-statistics">
    <div v-for="(item, idx) in data" :key="idx" class="item" @click="handleOpenDialog">
      <span class="num">{{ numData[item.key] || 0 }}</span>
      <span>{{ item.name }}</span>
    </div>
    <!-- 列表弹窗 -->
    <el-dialog title="案件统计" :visible.sync="visible" width="80%">
      <!-- 主体 -->
      <el-tabs v-model="activeName">
        <el-tab-pane label="月报" name="monthly">
          <group-report ref="groupReport" />
        </el-tab-pane>
        <el-tab-pane label="年报" name="annual">
          <annual-report ref="annualReport" />
        </el-tab-pane>
        <el-tab-pane label="考核" name="assessment">
          <grid-report ref="gridReport" />
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { caseStatistics } from '@/api/board/dataView/index'
import groupReport from './components/group/index.vue'
import gridReport from './components/grid/index.vue'
import annualReport from './components/annualReport/index.vue'

export default {
  name: 'CaseStatistics',
  components: {
    groupReport,
    gridReport,
    annualReport
  },
  data() {
    return {
      numData: {},
      data: [
        { key: 'inspectionCount', name: '综合执法' },
        { key: 'transportCount', name: '运管执法' },
        { key: 'trafficCaptureCount', name: '交警执法' }
      ],
      visible: false,
      activeName: 'monthly',
      componentList: [
        'groupReport',
        'annualReport',
        'gridReport'
      ]
    }
  },
  mounted() {
    this.fetchData()
  },
  methods: {
    handleOpenDialog() {
      this.visible = true
      this.$nextTick(() => {
        this.componentList.forEach(name => {
          this.$refs[name].getList()
        })
      })
    },
    fetchData() {
      caseStatistics().then(res => {
        this.numData = res.data
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .case-statistics {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 30px;
    .item {
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
      font-size: pxtorem(14);
      .num {
        color: #00f7ff;
        font-size: pxtorem(28);
        font-weight: 700;
      }
    }
    .full-dialog {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 999;
      .mask {
        width: 100%;
        height: 100%;
        background: rgba(14,12,51,0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        .dialog {
          width: 70%;
          height: 80%;
          background: url(@/assets/images/full-dialog-bg.png) no-repeat 0 0 / 100% 100%;
        }
      }
    }
  }
</style>
