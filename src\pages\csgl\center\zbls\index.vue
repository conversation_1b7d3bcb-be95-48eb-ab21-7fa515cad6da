<template>
  <div>
    <CommonTitle text="指标晾晒"></CommonTitle>
    <div class="chartzbls" id="chartzbls"></div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import { getIndicatorDrying } from '@/api/csgl/index.js'
export default {
  name: 'index',
  components: {
    CommonTitle,
  },
  data() {
    return {
      chartsData: [],
    }
  },
  computed: {},
  mounted() {
    this.getData()
  },
  methods: {
    getData() {
      getIndicatorDrying().then((res) => {
        this.chartsData = res.data.map((item) => {
          return {
            name: item.area,
            registerPercent: item.registerPercent,
            handlePercent: item.handlePercent,
            completePercent: item.completePercent,
          }
        })
        this.initChart()
      })
    },
    initChart() {
      let myChart = this.$echarts.init(document.getElementById('chartzbls'))
      let option = {
        tooltip: {
          trigger: 'axis',
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        legend: {
          orient: 'horizontal',
          // icon: "circle",
          padding: [30, 10, 10, 10],
          itemGap: 45,
          textStyle: {
            color: '#D6E7F9',
            fontSize: 28,
          },
        },
        grid: {
          left: '8%',
          right: '6%',
          top: '22%',
          bottom: '1%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            data: this.chartsData.map((item) => item.name),
            axisLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)', // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              textStyle: {
                color: '#D6E7F9',
                fontSize: 28,
              },
            },
          },
        ],
        yAxis: [
          {
            name: '单位(%)',
            type: 'value',
            nameTextStyle: {
              fontSize: 24,
              color: '#D6E7F9',
              padding: 20,
            },
            splitLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)',
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 28,
                color: '#D6E7F9',
              },
            },
          },
        ],
        series: [
          {
            name: '按时立案率',
            type: 'bar',
            barWidth: '10px',
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: '#22E197',
                  },
                  {
                    offset: 1,
                    color: 'rgba(34, 225, 151, 0)',
                  },
                ]),
                barBorderRadius: 4,
              },
            },
            data: this.chartsData.map((item) => item.registerPercent),
          },
          {
            name: '处置率',
            type: 'bar',
            barWidth: '10px',
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: '#E0D722',
                  },
                  {
                    offset: 1,
                    color: 'rgba(224, 215, 34, 0)',
                  },
                ]),
                barBorderRadius: 4,
              },
            },
            data: this.chartsData.map((item) => item.handlePercent),
          },
          {
            name: '按时结案率',
            type: 'bar',
            barWidth: '10px',
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(63, 217, 255, 1)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(63, 217, 255, 0)',
                  },
                ]),
                barBorderRadius: 4,
              },
            },
            data: this.chartsData.map((item) => item.completePercent),
          },
        ],
      }
      myChart.setOption(option)
      myChart.getZr().on('mousemove', (param) => {
        myChart.getZr().setCursorStyle('default')
      })
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
.chartzbls {
  width: 100%;
  height: 580px;
}
</style>