<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" :inline="true" label-width="100px">
      <el-form-item label="发生时间">
        <el-date-picker
          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px;"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="getList"
        />
      </el-form-item>
      <el-form-item label="关键词搜索">
        <el-input
          v-model="queryParams.searchValue"
          placeholder="请输入关键词搜索"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:record:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >
          新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:record:edit']"
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >
          修改
        </el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:record:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="recordList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="发生时间" align="center" prop="happenTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.happenTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="执法队员" align="center" prop="userName" />
      <el-table-column label="违规人" align="center" prop="lawbreakers" />
      <el-table-column label="车牌号" align="center" prop="carNo" />
      <!-- <el-table-column label="身份证" align="center" prop="identityCard" /> -->
      <el-table-column label="联系电话" align="center" prop="phone" />
      <el-table-column label="地址" align="center" prop="address" />
      <el-table-column label="案件类型" align="center" prop="caseTypeName" />
      <el-table-column label="处理类型" align="center" prop="handleType">
        <template slot-scope="scope">{{ scope.row.handleType | handleTypeName }}</template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">{{ scope.row.status | statusName }}</template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['business:record:edit']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            style="color: #e6a23c;"
            @click="handleUpdate(scope.row, 'disabled')"
          >
            详情
          </el-button>
          <!-- <el-button
            v-hasPermi="['business:record:edit']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >
            修改
          </el-button> -->
          <el-button
            v-hasPermi="['business:record:remove']"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改违规记录对话框 -->
    <div>
      <el-dialog class="m-dialog" :title="title" :visible.sync="open">
        <el-scrollbar style="height: 100%;">
          <el-row :gutter="15" style="margin-right: 10px;">
            <el-form ref="form" v-loading="formLoading" :model="form" :rules="rules" label-width="100px">
              <el-col :span="12">
                <el-form-item label="执法队员" prop="userName">
                  <el-input v-model="form.userName" disabled placeholder="请输入执法队员" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="部门名称" prop="deptName">
                  <el-input v-model="form.deptName" disabled placeholder="请输入部门名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="违规人" prop="lawbreakers">
                  <el-input v-model="form.lawbreakers" disabled placeholder="请输入违规人" />
                </el-form-item>
              </el-col>
              <!-- <el-col :span="12">
                <el-form-item label="身份证" prop="identityCard">
                  <el-input v-model="form.identityCard" disabled placeholder="请输入身份证" />
                </el-form-item>
              </el-col> -->
              <el-col :span="12">
                <el-form-item label="联系电话" prop="phone">
                  <el-input v-model="form.phone" disabled placeholder="请输入联系电话" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="车牌号" prop="carNo">
                  <el-input v-model="form.carNo" disabled placeholder="请输入车牌号" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="发生时间" prop="happenTime">
                  <el-date-picker
                    v-model="form.happenTime"
                    disabled
                    clearable
                    size="small"
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="选择发生时间"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="地址" prop="address">
                  <el-input v-model="form.address" disabled placeholder="请选择地址" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="案件类型" prop="caseTypeName">
                  <el-select v-model="form.caseTypeName" disabled placeholder="请选择案件类型" :style="{ width: '100%' }">
                    <el-option
                      v-for="dict in typeOptions"
                      :key="dict.dictValue"
                      :label="dict.dictLabel"
                      :value="dict.dictValue"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="处理类型" prop="handleType">
                  <el-select v-model="form.handleType" placeholder="请选择处理类型" disabled :style="{ width: '100%' }">
                    <el-option label="口头教育" value="1" />
                    <el-option label="答题教育" value="2" />
                    <el-option label="朋友圈检讨" value="3" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="违规内容">
                  <el-input v-model="form.content" type="textarea" rows="5" disabled placeholder="请输入违规内容" :style="{ width: '100%' }" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="违规图片" prop="files">
                  <MFileUpload ref="mainFile" :limit="4" disabled :file-list="mainFileList" />
                </el-form-item>
              </el-col>
              <!-- 答题情况 -->
              <h3 v-if="form.examId != null" class="title">答题情况</h3>
              <el-col v-if="form.examId != null" :span="24">
                <el-row>
                  <el-col :span="16">
                    <div v-if="num != null" class="question">
                      <div class="type"><span style="color: #1890ff;">[{{ questionList[num].questionBank.typeName }}]</span> 第{{ num + 1 }}题</div>
                      <div class="content">
                        <p>{{ questionList[num].questionBank.content }}</p>
                        <!-- 单选题 -->
                        <div v-if="questionList[num].questionBank.type == 1">
                          <div v-for="option in optionList" :key="option">
                            <p v-if="questionList[num].questionBank[`choice${option}`]" :class="{ 'user-checked': questionList[num].userAnswer == option, check: questionList[num].answer == option }">
                              <span>{{ option }}、</span>
                              <span>{{ questionList[num].questionBank[`choice${option}`] }}</span>
                            </p>
                          </div>
                        </div>
                        <!-- 多选题 -->
                        <div v-if="questionList[num].questionBank.type == 2">
                          <div v-for="option in optionList" :key="option">
                            <p v-if="questionList[num].questionBank[`choice${option}`]" :class="{ 'user-checked': questionList[num].userAnswer.includes(option), check: questionList[num].answer.includes(option) }">
                              <span>{{ option }}、</span>
                              <span>{{ questionList[num].questionBank[`choice${option}`] }}</span>
                            </p>
                          </div>
                        </div>
                        <!-- 判断题 -->
                        <div v-if="questionList[num].questionBank.type == 3">
                          <p :class="{ 'user-checked': questionList[num].userAnswer == 'T', check: questionList[num].answer == 'T'}">
                            <span><i class="el-icon-check" />、</span>
                            <span>正确</span>
                          </p>
                          <p :class="{ 'user-checked': questionList[num].userAnswer == 'F', check: questionList[num].answer == 'F'}">
                            <span><i class="el-icon-close" />、</span>
                            <span>错误</span>
                          </p>
                        </div>
                        <!-- 回答答案 -->
                        <!-- <div v-if="questionList[num].status == 1 || questionList[num].status == 2" class="answer"> -->
                        <div class="answer">
                          <p>
                            <span>正确答案</span>
                            <span style="color: #00ca00;"> {{ questionList[num].answer | answerName }}</span>
                            <span v-if="questionList[num].userAnswer == questionList[num].answer">，回答正确</span>
                            <span v-if="questionList[num].userAnswer != questionList[num].answer">，您的答案</span>
                            <span v-if="questionList[num].userAnswer != questionList[num].answer" style="color: #ea0000;"> {{ questionList[num].userAnswer | answerName }}</span>
                          </p>
                        </div>
                        <!-- 答案解析 -->
                        <div>
                          <p style="color: #1890ff;">[问题解析]</p>
                          <p>{{ questionList[num].questionBank.knowledgePoint || '略' }}</p>
                        </div>
                      </div>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="answerList">
                      <div v-for="(item,idx) in answerList" :key="idx" class="item" :class="{ correct: item.status == 1, wrong: item.status == 2 }" @click="handleClick(idx)">
                        <span>{{ idx + 1 }}</span>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </el-col>
              <!-- /答题情况 -->
            </el-form>
          </el-row>
        </el-scrollbar>
        <div slot="footer" class="dialog-footer">
          <!-- <el-button type="primary" @click="submitForm">确 定</el-button> -->
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { listRecord, getRecord, delRecord, addRecord, updateRecord, exportRecord, getExamAnswer } from '@/api/case/synthetical/violation'
import MFileUpload from '@/components/MFileUpload/index.vue'
import { getFiles } from '@/api/supervise/swit'

export default {
  name: 'Record',
  filters: {
    handleTypeName(status) {
      const names = {1: '口头教育', 2: '答题教育', 3: '朋友圈检讨' }
      return names[status]
    },
    statusName(status) {
      const statusObj = { 1: '处理中', 9: '已办结'}
      if (statusObj) return statusObj[status]
    },
    answerName(answer) {
      if (answer == 'T') return '正确'
      if (answer == 'F') return '错误'
      if (!answer) return '未答题'
      return answer
    }
  },
  components: {
    MFileUpload
  },
  data() {
    return {
      formLoading: false,
      num: null,
      questionList: [],
      answerList: [],
      optionList: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'],
      mainFileList: [],

      // 案件类型数据字典
      typeOptions: [],
      // 多选选中上报人id
      userIds: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 违规记录表格数据
      recordList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dateRange: [],
        searchValue: ''
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        examName: [
          { required: true, message: '试卷名称不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getList()
    this.getDicts('case_Violation_type').then(response => {
      this.typeOptions = response.data
    })
  },
  methods: {
    handleClick(idx) {
      this.num = idx
    },
    /** 查询违规记录列表 */
    getList() {
      this.loading = true
      const { dateRange,  pageNum, pageSize, searchValue} = this.queryParams
      let params = { pageNum, pageSize }
      if (dateRange && dateRange.length) params = { ...params, searchStartTime: dateRange[0], searchEndTime: dateRange[1] }
      if (searchValue) params = { ...params, searchValue }

      listRecord(params).then(response => {
        this.recordList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.mainFileList = []
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        violationId: null,
        title: null,
        content: null,
        lawbreakers: null,
        carNo: null,
        identityCard: null,
        phone: null,
        userId: null,
        userName: null,
        userIds: null,
        userNames: null,
        happenTime: null,
        handleTime: null,
        completeTime: null,
        caseType: null,
        caseTypeName: null,
        handleType: null,
        examId: null,
        examName: null,
        description: null,
        type: null,
        status: '0',
        address: null,
        longitude: null,
        latitude: null,
        delFlag: null,
        deptId: null,
        deptName: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null,
        isUnion: null,
        unionId: null,
        createById: null,
        code: null,
        unionName: null
      }
      this.questionList = []
      this.answerList = []
      this.num = null
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        dateRange: [],
        searchValue: ''
      }
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.violationId)
      this.userIds = selection.map(item => item.userId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    // handleAdd() {
    //   this.reset()
    //   this.open = true
    //   this.title = '添加违规记录'
    // },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.violationId || this.ids
      this.open = true
      this.formLoading = true
      this.title = '柔性执法记录'
      Promise.all([
        getRecord(id),
        getFiles({ businessId: id, tableName: 'case_violation_record' })
      ]).then(response => {
        const [formData, fileData] = response
        this.form = formData.data
        this.formLoading = false

        // 获取文件
        this.mainFileList = fileData.rows.map(item => {
          return { name: item.displayName, url: `/zqzfj${item.filePath}`, ...item }
        })

      }).catch(() => this.formLoading = false)
      // 试卷
      if (row.examId) {
        getExamAnswer({ examId: row.examId }).then(response => {
          this.formLoading = false
          this.form = {...this.form, ...response.data}
          this.questionList = response.questionList
          this.answerList = response.questionList.map(item => {
            return {
              id: item.id,
              answer: item.answer,
              userAnswer: item.userAnswer,
              answerDate: item.answerDate,
              status: item.status,
              examId: response.data.id
            }
          })
          this.num = 0
        }).catch(() => this.formLoading = false)
      }

    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.violationId != null) {
            updateRecord(this.form).then(() => {
              this.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addRecord(this.form).then(() => {
              this.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      if (this.userIds.length != 0) {
        if (!this.userIds.every(item => item == this.$store.getters.uid)) return this.msgError('您不是执法队员，没有操作权限！')
      } else {
        if (row.userId != this.$store.getters.uid) return this.msgError('您不是执法队员，没有操作权限！')
      }
      const violationIds = row.violationId || this.ids
      this.$confirm('是否确认删除违规记录编号为"' + violationIds + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return delRecord(violationIds)
      }).then(() => {
        this.getList()
        this.msgSuccess('删除成功')
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams
      this.$confirm('是否确认导出所有违规记录数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return exportRecord(queryParams)
      }).then(response => {
        this.download(response.msg)
      })
    }

  }
}
</script>

<style scoped lang="scss">
::v-deep {
  .question {
    .user-checked {
      color: #ea0000;
    }
    .check {
      color: #00ca00;
    }
  }
}
::v-deep .answerList {
  overflow: hidden;
  .item {
    width: 20%;
    float: left;
    text-align: center;
    padding-bottom: 10px;
    &.correct span {
      background: #00ca00;
      border-color: #00ca00;
      color: #fff;
    }
    &.wrong span {
      background: #ea0000;
      border-color: #ea0000;
      color: #fff;
    }
    span {
      display: inline-block;
      width: 30px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      border-radius: 50%;
      border: 1px solid #ccc;
      user-select: none;
      cursor: pointer;
    }
  }
}
</style>
