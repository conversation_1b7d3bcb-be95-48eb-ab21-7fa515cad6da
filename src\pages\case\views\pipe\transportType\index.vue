<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" :inline="true">
      <el-form-item label="日期范围">
        <el-date-picker

          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px;"
          value-format="yyyy-MM-dd hh:mm:ss"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="getList"
        />
      </el-form-item>
      <el-form-item label="案件类型">
        <el-select v-model="queryParams.type" @change="getList">
          <el-option label="全部" value="" />
          <el-option label="日常巡查" value="0" />
          <el-option label="违规处置" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <!-- 刷新工具 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="20">
        <h2 style="margin: 0;">巡查处置数量类型统计图</h2>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>
    <!-- 主体内容 -->
    <div v-loading="loading" class="container" :class="{ hideSearch: !showSearch }">
      <div ref="chart" style="width: 100%; height: 100%;" />
    </div>
  </div>
</template>

<script>
import { getTransportType } from '@/api/case/pipe/transportType'

export default {
  data() {
    return {
      loading: false,
      showSearch: true,
      queryParams: {
        dateRange: [],
        type: ''
      },
      myChart: null,
      xAxisData: [],
      seriesData: []
    }
  },
  computed: {
    options() {
      return {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [
          {
            name: '车辆类型',
            type: 'pie',
            radius: '50%',
            data: this.seriesData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]

      }
    }
  },
  watch: {
    /* 隐藏搜索框重置图表大小 */
    showSearch() {
      this.resizeChart()
    }
  },
  mounted() {
    this.initChart()
    this.getList()
    window.addEventListener('resize', this.resizeChart)
  },
  destroyed() {
    window.removeEventListener('resize', this.resizeChart)
  },
  methods: {
    /* 获取数据 */
    getList() {
      this.loading = true
      const { dateRange, type } = this.queryParams
      let params = {}
      if (dateRange && dateRange.length) params = { ...params, startTime: dateRange[0], endTime: dateRange[1] }
      if (type) params = { ...params, type }
      getTransportType(params).then(res => {
        this.seriesData = res.data.seriesData
        this.myChart.setOption(this.options)
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    /* 重置数据 */
    resetQuery() {
      this.queryParams = {
        dateRange: [],
        type: ''
      }
      this.getList()
    },
    /* 初始化空数据图表 */
    initChart() {
      this.myChart = this.$echarts.init(this.$refs.chart)
      this.myChart.setOption(this.options)
    },
    /* 页面大小变更，图标自适应 */
    resizeChart() {
      setTimeout(() => {
        this.myChart.resize()
      }, 500)
    }
  }
}
</script>
<style scoped lang="scss">
.app-container {
  .container {
    width: 100%;
    height: calc(100vh - 225px);
    &.hideSearch {
      height: calc(100vh - 170px);
    }
  }
}
</style>
