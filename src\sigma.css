span,
p,
div,
i,
b,
a {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.s-w4 {
  font-weight: 400;
}

.s-w7 {
  font-weight: 700;
}

.s-sty {
  font-style: italic;
}

.s-relative,
.s-rela {
  position: relative;
}

.s-absolute,
.s-abso {
  position: absolute;
}

.s-c-main {
  color: #132c4e;
}

.s-c-white {
  color: #ffffff;
}
.s-c-blue {
  color: #00c0ff;
}

.s-c-grey-light {
  color: #e3edff;
}

.s-c-bul-light {
  color: #83bbeb;
}
.s-c-yellow-gradient1 {
  background: linear-gradient(to bottom, #ffebce, #ffffff, #ffc559, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
}
.s-c-blue-gradient1 {
  background: linear-gradient(to bottom, #ccf4ff, #ffffff, #00baf8, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
}

.s-c-blue-gradient {
  background: linear-gradient(to bottom, #ccf4ff, #ffffff, #00baf8, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
}

.s-c-blue2-gradient {
  background: linear-gradient(to bottom, #ccf4ff, #98b6d7, #42aacd, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
}
.s-c-blue3-gradient {
  background: linear-gradient(to bottom, #baeeee, #ffffff, #00f6f7, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
}
.s-c-pop-gradient {
  background: linear-gradient(to bottom, #f3faff, #f3faff, #b76fd8, #f3faff);
  -webkit-background-clip: text;
  color: transparent;
}

.s-c-red-gradient {
  background: linear-gradient(to bottom, #df5151, #f4f1ff, #ff4949, #e03e3e);
  -webkit-background-clip: text;
  color: transparent;
}

.s-c-yellow-gradient {
  background: linear-gradient(to bottom, #ffebce, #ffffff, #ffc559, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
}

.s-c-orange-gradient {
  background: linear-gradient(to bottom, #ffe3cd, #fff, #fd832e, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
}

.s-c-green-gradient {
  background: linear-gradient(to bottom, #c8f185, #fff, #a7dc4c, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
}
.s-c-green2-gradient {
  background: linear-gradient(to bottom, #efffd6, #fff, #a7dc4c);
  -webkit-background-clip: text;
  color: transparent;
}

.s-bg-main {
  background-color: #132c4e;

  color: #ffffff;
}

.s-bg-main-light {
  background-color: #39516a;
}

.s-bg-red {
  background-color: #e84832;
}

.s-bg-yellow {
  background-color: #f8d309;
}

.s-bg-blue {
  background-color: #00c0ff;
}

.s-border-main {
  border: 1px #359cf8 solid;
}

.s-border-main-light {
  border: 1px #cde7fe solid;
}

.s-border-w2 {
  border-width: 2px;
}

.s-box-radius5 {
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  -o-border-radius: 5px;
  border-radius: 5px;
}

.s-box-radius10 {
  -moz-border-radius: 10px;
  -webkit-border-radius: 10px;
  -o-border-radius: 10px;
  border-radius: 10px;
}

.s-box-radius100 {
  -moz-border-radius: 100%;
  -webkit-border-radius: 100%;
  -o-border-radius: 100%;
  border-radius: 100%;
}

.s-flex {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  right: -13px;
}

.s-flex-wrap {
  flex-wrap: wrap;
}

.s-flex-nowrap {
  flex-wrap: nowrap;
}

.s-col-center {
  align-items: center;
}

.s-col-top {
  align-items: flex-start;
}

.s-col-bottom {
  align-items: flex-end;
}

.s-row-center {
  justify-content: center;
}

.s-row-left {
  justify-content: flex-start;
}

.s-row-right {
  justify-content: flex-end;
}

.s-row-between {
  justify-content: space-between;
}

.s-row-around {
  justify-content: space-around;
}
.s-row-evenly {
  justify-content: space-evenly;
}

.s-text-left {
  text-align: left;
}

.s-text-center {
  text-align: center;
}

.s-text-right {
  text-align: right;
}

.s-flex-col {
  display: flex;
  flex-direction: column;
}

.s-flex-0 {
  flex: 0;
}

.s-flex-1 {
  flex: 1;
}

.s-flex-2 {
  flex: 2;
}

.s-flex-3 {
  flex: 3;
}

.s-flex-4 {
  flex: 4;
}

.s-flex-5 {
  flex: 5;
}

.s-flex-6 {
  flex: 6;
}

.s-flex-7 {
  flex: 7;
}

.s-flex-8 {
  flex: 8;
}

.s-flex-9 {
  flex: 9;
}

.s-flex-10 {
  flex: 10;
}

.s-flex-11 {
  flex: 11;
}

.s-flex-12 {
  flex: 12;
}

.s-font-15 {
  font-size: 15px;
}

.s-font-20 {
  font-size: 20px;
}

.s-font-25 {
  font-size: 25px;
}
.s-font-24 {
  font-size: 24px;
}
.s-font-26 {
  font-size: 26px;
}
.s-font-28 {
  font-size: 28px;
}
.s-font-30 {
  font-size: 30px;
}
.s-font-32 {
  font-size: 32px;
}

.s-font-35 {
  font-size: 35px;
}

.s-font-38 {
  font-size: 38px;
}

.s-font-40 {
  font-size: 40px;
}

.s-font-45 {
  font-size: 45px;
}

.s-font-50 {
  font-size: 50px;
}

.s-font-55 {
  font-size: 55px;
}

.s-font-60 {
  font-size: 60px;
}

.s-font-65 {
  font-size: 65px;
}

.s-font-70 {
  font-size: 70px;
}

.s-font-75 {
  font-size: 75px;
}

.s-font-80 {
  font-size: 80px;
}

.s-font-85 {
  font-size: 85px;
}

.s-font-90 {
  font-size: 90px;
}

.s-font-95 {
  font-size: 95px;
}

.s-m-x-auto {
  margin: 0 auto;
}

.s-m-l-auto {
  margin-left: auto;
}

.s-m-0 {
  margin: 0px !important;
}

.s-p-0 {
  padding: 0px !important;
}

.s-m-l-0 {
  margin-left: 0px !important;
}

.s-p-l-0 {
  padding-left: 0px !important;
}

.s-m-t-0 {
  margin-top: 0px !important;
}

.s-p-t-0 {
  padding-top: 0px !important;
}

.s-m-r-0 {
  margin-right: 0px !important;
}

.s-p-r-0 {
  padding-right: 0px !important;
}

.s-m-b-0 {
  margin-bottom: 0px !important;
}

.s-p-b-0 {
  padding-bottom: 0px !important;
}

.s-m-5 {
  margin: 5px !important;
}

.s-p-5 {
  padding: 5px !important;
}

.s-m-l-5 {
  margin-left: 5px !important;
}

.s-p-l-5 {
  padding-left: 5px !important;
}

.s-m-t-5 {
  margin-top: 5px !important;
}

.s-p-t-5 {
  padding-top: 5px !important;
}

.s-m-r-5 {
  margin-right: 5px !important;
}

.s-p-r-5 {
  padding-right: 5px !important;
}

.s-m-b-5 {
  margin-bottom: 5px !important;
}

.s-p-b-5 {
  padding-bottom: 5px !important;
}

.s-m-10 {
  margin: 10px !important;
}

.s-p-10 {
  padding: 10px !important;
}

.s-m-l-10 {
  margin-left: 10px !important;
}

.s-p-l-10 {
  padding-left: 10px !important;
}

.s-m-t-10 {
  margin-top: 10px !important;
}

.s-p-t-10 {
  padding-top: 10px !important;
}

.s-m-r-10 {
  margin-right: 10px !important;
}

.s-p-r-10 {
  padding-right: 10px !important;
}

.s-m-b-10 {
  margin-bottom: 10px !important;
}

.s-p-b-10 {
  padding-bottom: 10px !important;
}

.s-m-15 {
  margin: 15px !important;
}

.s-p-15 {
  padding: 15px !important;
}

.s-m-l-15 {
  margin-left: 15px !important;
}

.s-p-l-15 {
  padding-left: 15px !important;
}

.s-m-t-15 {
  margin-top: 15px !important;
}

.s-p-t-15 {
  padding-top: 15px !important;
}

.s-m-r-15 {
  margin-right: 15px !important;
}

.s-p-r-15 {
  padding-right: 15px !important;
}

.s-m-b-15 {
  margin-bottom: 15px !important;
}

.s-p-b-15 {
  padding-bottom: 15px !important;
}

.s-m-20 {
  margin: 20px !important;
}

.s-p-20 {
  padding: 20px !important;
}

/*.s-m-l-20 {*/
/*  margin-left: 20px !important;*/
/*}*/

.s-p-l-20 {
  padding-left: 20px !important;
}

.s-m-t-20 {
  margin-top: 20px !important;
}

.s-p-t-20 {
  padding-top: 20px !important;
}

.s-m-r-20 {
  margin-right: 30px !important;
}

.s-p-r-20 {
  padding-right: 20px !important;
}

.s-m-b-20 {
  margin-bottom: 20px !important;
}

.s-p-b-20 {
  padding-bottom: 20px !important;
}

.s-m-25 {
  margin: 25px !important;
}

.s-p-25 {
  padding: 25px !important;
}

.s-m-l-25 {
  margin-left: 25px !important;
}

.s-p-l-25 {
  padding-left: 25px !important;
}

.s-m-t-25 {
  margin-top: 25px !important;
}

.s-p-t-25 {
  padding-top: 25px !important;
}

.s-m-r-25 {
  margin-right: 25px !important;
}

.s-p-r-25 {
  padding-right: 25px !important;
}

.s-m-b-25 {
  margin-bottom: 25px !important;
}

.s-p-b-25 {
  padding-bottom: 25px !important;
}

.s-m-30 {
  margin: 30px !important;
}

.s-p-30 {
  padding: 30px !important;
}

.s-m-l-30 {
  margin-left: 30px !important;
}

.s-p-l-30 {
  padding-left: 30px !important;
}

.s-m-t-30 {
  margin-top: 30px !important;
}

.s-p-t-30 {
  padding-top: 30px !important;
}

.s-m-r-30 {
  margin-right: 30px !important;
}

.s-p-r-30 {
  padding-right: 30px !important;
}

.s-m-b-30 {
  margin-bottom: 30px !important;
}

.s-p-b-30 {
  padding-bottom: 30px !important;
}

.s-m-35 {
  margin: 35px !important;
}

.s-p-35 {
  padding: 35px !important;
}

.s-m-l-35 {
  margin-left: 35px !important;
}

.s-p-l-35 {
  padding-left: 35px !important;
}

.s-m-t-35 {
  margin-top: 35px !important;
}

.s-p-t-35 {
  padding-top: 35px !important;
}

.s-m-r-35 {
  margin-right: 35px !important;
}

.s-p-r-35 {
  padding-right: 35px !important;
}

.s-m-b-35 {
  margin-bottom: 35px !important;
}

.s-p-b-35 {
  padding-bottom: 35px !important;
}

.s-m-40 {
  margin: 40px !important;
}

.s-p-40 {
  padding: 40px !important;
}

.s-m-l-40 {
  margin-left: 40px !important;
}

.s-p-l-40 {
  padding-left: 40px !important;
}

.s-m-t-40 {
  margin-top: 40px !important;
}

.s-p-t-40 {
  padding-top: 40px !important;
}

.s-m-r-40 {
  margin-right: 40px !important;
}

.s-p-r-40 {
  padding-right: 40px !important;
}

.s-m-b-40 {
  margin-bottom: 40px !important;
}

.s-p-b-40 {
  padding-bottom: 40px !important;
}

.s-m-45 {
  margin: 45px !important;
}

.s-p-45 {
  padding: 45px !important;
}

.s-m-l-45 {
  margin-left: 45px !important;
}

.s-p-l-45 {
  padding-left: 45px !important;
}

.s-m-t-45 {
  margin-top: 45px !important;
}

.s-p-t-45 {
  padding-top: 45px !important;
}

.s-m-r-45 {
  margin-right: 45px !important;
}

.s-p-r-45 {
  padding-right: 45px !important;
}

.s-m-b-45 {
  margin-bottom: 45px !important;
}

.s-p-b-45 {
  padding-bottom: 45px !important;
}

.s-m-50 {
  margin: 50px !important;
}

.s-p-50 {
  padding: 50px !important;
}

.s-m-l-50 {
  margin-left: 50px !important;
}

.s-p-l-50 {
  padding-left: 50px !important;
}

.s-m-t-50 {
  margin-top: 50px !important;
}

.s-p-t-50 {
  padding-top: 50px !important;
}

.s-m-r-50 {
  margin-right: 50px !important;
}

.s-p-r-50 {
  padding-right: 50px !important;
}

.s-m-b-50 {
  margin-bottom: 50px !important;
}

.s-p-b-50 {
  padding-bottom: 50px !important;
}

.s-m-55 {
  margin: 55px !important;
}

.s-p-55 {
  padding: 55px !important;
}

.s-m-l-55 {
  margin-left: 55px !important;
}

.s-p-l-55 {
  padding-left: 55px !important;
}

.s-m-t-55 {
  margin-top: 55px !important;
}

.s-p-t-55 {
  padding-top: 55px !important;
}

.s-m-r-55 {
  margin-right: 55px !important;
}

.s-p-r-55 {
  padding-right: 55px !important;
}

.s-m-b-55 {
  margin-bottom: 55px !important;
}

.s-p-b-55 {
  padding-bottom: 55px !important;
}

.s-m-60 {
  margin: 60px !important;
}

.s-p-60 {
  padding: 60px !important;
}

.s-m-l-60 {
  margin-left: 60px !important;
}

.s-p-l-60 {
  padding-left: 60px !important;
}

.s-m-t-60 {
  margin-top: 60px !important;
}

.s-p-t-60 {
  padding-top: 60px !important;
}

.s-m-r-60 {
  margin-right: 60px !important;
}

.s-p-r-60 {
  padding-right: 60px !important;
}

.s-m-b-60 {
  margin-bottom: 60px !important;
}

.s-p-b-60 {
  padding-bottom: 60px !important;
}

.s-m-65 {
  margin: 65px !important;
}

.s-p-65 {
  padding: 65px !important;
}

.s-m-l-65 {
  margin-left: 65px !important;
}

.s-p-l-65 {
  padding-left: 65px !important;
}

.s-m-t-65 {
  margin-top: 65px !important;
}

.s-p-t-65 {
  padding-top: 65px !important;
}

.s-m-r-65 {
  margin-right: 65px !important;
}

.s-p-r-65 {
  padding-right: 65px !important;
}

.s-m-b-65 {
  margin-bottom: 65px !important;
}

.s-p-b-65 {
  padding-bottom: 65px !important;
}

.s-m-70 {
  margin: 70px !important;
}

.s-p-70 {
  padding: 70px !important;
}

.s-m-l-70 {
  margin-left: 70px !important;
}

.s-p-l-70 {
  padding-left: 70px !important;
}

.s-m-t-70 {
  margin-top: 70px !important;
}

.s-p-t-70 {
  padding-top: 70px !important;
}

.s-m-r-70 {
  margin-right: 70px !important;
}

.s-p-r-70 {
  padding-right: 70px !important;
}

.s-m-b-70 {
  margin-bottom: 70px !important;
}

.s-p-b-70 {
  padding-bottom: 70px !important;
}

.s-m-75 {
  margin: 75px !important;
}

.s-p-75 {
  padding: 75px !important;
}

.s-m-l-75 {
  margin-left: 75px !important;
}

.s-p-l-75 {
  padding-left: 75px !important;
}

.s-m-t-75 {
  margin-top: 75px !important;
}

.s-p-t-75 {
  padding-top: 75px !important;
}

.s-m-r-75 {
  margin-right: 75px !important;
}

.s-p-r-75 {
  padding-right: 75px !important;
}

.s-m-b-75 {
  margin-bottom: 75px !important;
}

.s-p-b-75 {
  padding-bottom: 75px !important;
}

.s-m-80 {
  margin: 80px !important;
}

.s-p-80 {
  padding: 80px !important;
}

.s-m-l-80 {
  margin-left: 80px !important;
}

.s-p-l-80 {
  padding-left: 80px !important;
}

.s-m-t-80 {
  margin-top: 80px !important;
}

.s-p-t-80 {
  padding-top: 80px !important;
}

.s-m-r-80 {
  margin-right: 80px !important;
}

.s-p-r-80 {
  padding-right: 80px !important;
}

.s-m-b-80 {
  margin-bottom: 80px !important;
}

.s-p-b-80 {
  padding-bottom: 80px !important;
}

.s-m-85 {
  margin: 85px !important;
}

.s-p-85 {
  padding: 85px !important;
}

.s-m-l-85 {
  margin-left: 85px !important;
}

.s-p-l-85 {
  padding-left: 85px !important;
}

.s-m-t-85 {
  margin-top: 85px !important;
}

.s-p-t-85 {
  padding-top: 85px !important;
}

.s-m-r-85 {
  margin-right: 85px !important;
}

.s-p-r-85 {
  padding-right: 85px !important;
}

.s-m-b-85 {
  margin-bottom: 85px !important;
}

.s-p-b-85 {
  padding-bottom: 85px !important;
}

.s-m-90 {
  margin: 90px !important;
}

.s-p-90 {
  padding: 90px !important;
}

.s-m-l-90 {
  margin-left: 90px !important;
}

.s-p-l-90 {
  padding-left: 90px !important;
}

.s-m-t-90 {
  margin-top: 90px !important;
}

.s-p-t-90 {
  padding-top: 90px !important;
}

.s-m-r-90 {
  margin-right: 90px !important;
}

.s-p-r-90 {
  padding-right: 90px !important;
}

.s-m-b-90 {
  margin-bottom: 90px !important;
}

.s-p-b-90 {
  padding-bottom: 90px !important;
}

.s-m-95 {
  margin: 95px !important;
}

.s-p-95 {
  padding: 95px !important;
}

.s-m-l-95 {
  margin-left: 95px !important;
}

.s-p-l-95 {
  padding-left: 95px !important;
}

.s-m-t-95 {
  margin-top: 95px !important;
}

.s-p-t-95 {
  padding-top: 95px !important;
}

.s-m-r-95 {
  margin-right: 95px !important;
}

.s-p-r-95 {
  padding-right: 95px !important;
}

.s-m-b-95 {
  margin-bottom: 95px !important;
}

.s-p-b-95 {
  padding-bottom: 95px !important;
}

.s-m-100 {
  margin: 100px !important;
}

.s-p-100 {
  padding: 100px !important;
}

.s-m-l-100 {
  margin-left: 100px !important;
}

.s-p-l-100 {
  padding-left: 100px !important;
}

.s-m-t-100 {
  margin-top: 100px !important;
}

.s-m-t-120 {
  margin-top: 120px !important;
}

.s-p-t-100 {
  padding-top: 100px !important;
}

.s-m-r-100 {
  margin-right: 100px !important;
}

.s-p-r-100 {
  padding-right: 100px !important;
}

.s-m-b-100 {
  margin-bottom: 100px !important;
}

.s-p-b-100 {
  padding-bottom: 100px !important;
}

/*# sourceMappingURL=sigma.css.map */
