<template>
  <div class="header">
    <h1>
      <img class="logo-img" src="@/assets/logo/logo.png" alt="金华火车站交通枢纽联合执法管理应用" @click="handleClick">
      <div class="title">
        <span class="c-title">金华火车站交通枢纽联合执法管理应用</span>
        <!-- <span class="e-title">Jinhua Railway Station Front Area Integrated Management Information Command Platform</span> -->
      </div>
    </h1>
    <div class="left">
      <span>{{ date }}</span>
      <span>{{ dataTime }}</span>
      <span>{{ week }}</span>
    </div>
    <div class="right">
      <span>{{ weather }}</span>
      <span>{{ temperature }}</span>
      <span style="cursor: pointer;" @click="logout">[退出登录]</span>
    </div>
    <!-- 跳转图标 -->
    <div v-if="$route.fullPath !== '/board/dataHomeView'" class="page-link">
      <div v-if="$route.fullPath != '/board/monitor'" class="data-view" @click="handleOpen('monitor')" />
      <div v-if="$route.fullPath != '/board/dataView'" class="monitor" @click="handleOpen('dataView')" />
      <div v-if="$route.fullPath != '/board/map'" class="mapMenu" @click="handleOpen('map')" />
    </div>
  </div>
</template>

<script>
import { loadMap } from '@/utils/amapLoad'

export default {
  data() {
    return {
      date: '',
      dataTime: '',
      week: '',
      timer: null,
      weather: '',
      temperature: ''
    }
  },
  async mounted() {
    this.getDateTime()
    if (this.timer) clearInterval(this.timer)
    this.timer = setInterval(() => this.getDateTime(), 1000)
    // 查询天气
    const AMap = await loadMap(['AMap.Weather'])
    const amapWeather = new AMap.Weather()
    amapWeather.getLive('金华市', (err, data) => {
      if (err) {
        this.weather = '天气查询失败'
      } else {
        console.log(data)
        this.weather = data.weather
        this.temperature = `${data.temperature}℃`
      }
    })
  },
  destroyed() {
    clearInterval(this.timer)
  },
  methods: {
    handleClick() {
      window.location.href = '/'
    },
    getDateTime() {
      const nDate = new Date()
      this.date = this.parseTime(nDate, '{y}年{m}月{d}日')
      this.dataTime = this.parseTime(nDate, '{h}:{i}:{s}')
      this.week = this.parseTime(nDate, '星期{a}')
    },
    async logout() {
      this.$confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('LogOut').then(() => {
          location.href = '/'
        })
      })
    },
    handleOpen(url) {
      this.$router.push({ path: url })
    }
  }
}
</script>

<style scoped lang="scss">
@font-face {
  font-family: ybbih;
  src: url(@/assets/font/ybbih.ttf), url(@/assets/font/ybbih.eot), url(@/assets/font/ybbih.woff);
}
.header {
  width: 100%;
  height: pxtorem(100);
  background: url(@/assets/images/data-header.png) no-repeat center center / 100% 100%;
  position: relative;
  z-index: 10;
  color: #fff;
  font-size: pxtorem(18);
  letter-spacing: 2px;
  font-weight: 700;
  h1 {
    height: inherit;
    font-family: ybbih;
    text-align: center;
    font-weight: normal;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0;
    .logo-img {
      width: pxtorem(60);
      height: pxtorem(60);
      margin-right: pxtorem(20);
      cursor: pointer;
    }
    span {
      text-shadow: 0 0 20px rgba(0, 247, 255, 0.8);
    }
    .title {
      display: flex;
      flex-direction: column;
    }
    .c-title {
      font-size: pxtorem(38);
    }
    .e-title {
      font-size: pxtorem(12);
      letter-spacing: pxtorem(1.2);
    }
  }
  .left,
  .right {
    position: absolute;
    bottom: 5px;
    text-shadow: 0 0 6px #00f7ff;
    font-family: Conv_GemunuLibre-ExtraBold;
  }
  .left {
    left: pxtorem(30);
    span {
      margin-right: pxtorem(40);
    }
  }
  .right {
    right: pxtorem(30);
    span {
      margin-left: pxtorem(40);
    }
  }
  .page-link {
    position: absolute;
    right: pxtorem(30);
    bottom: pxtorem(-40);
    height: pxtorem(40);
    .data-view {
      width: pxtorem(72);
      height: pxtorem(40);
      background: url(@/assets/images/dataView-icon.png) no-repeat center center / 100% 100%;
      display: inline-block;
      margin-left: 15px;
      cursor: pointer;
    }
    .monitor {
      width: pxtorem(72);
      height: pxtorem(40);
      background: url(@/assets/images/monitor-icon.png) no-repeat center center / 100% 100%;
      display: inline-block;
      margin-left: 15px;
      cursor: pointer;
    }
    .mapMenu {
      width: pxtorem(72);
      height: pxtorem(40);
      background: url(@/assets/images/map-icon.png) no-repeat center center / 100% 100%;
      display: inline-block;
      margin-left: 15px;
      cursor: pointer;
    }
  }
}
</style>
