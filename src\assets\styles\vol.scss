// 动态管理
.news-container {
  padding: 20px
}
.category-dialog{
  // min-height: 300px;
  .el-dialog__body{
    height: 200px;
    margin-top: 30px;
  }
  .el-dialog:not(.is-fullscreen) {
    margin-top: 25vh !important;
}
}

// 请假页面
.leave_history_container{
  .title_h3{
    font-size: 16px;
    padding-bottom: 10px;
    padding-left: 20px;
    border-bottom: 1px solid #e4e7ec;
    margin-bottom: 20px;
    color: #000 !important;
  }
 .el-input__inner,
  .el-textarea__inner{
    color: #666 !important;
  }
  .approve_history_content{
    display: flex;
    flex-wrap: wrap;

    .approve_history{
      display: flex;
      flex-direction: column;
      margin-bottom: 30px;
      margin-right: 20px;
      &:nth-child(2n){
        margin-right: 0;
      }
      .taskNodeName{
        width: 250px;
        padding-left: 20px;
        color: rgb(17, 97, 202);
      }
      .el-input{
        width: 380px;
      }
    }

  }

}
// 请假审批页面
.title_h3{
  font-size: 16px;
  padding-bottom: 10px;
  padding-left: 20px;
  border-bottom: 1px solid #e4e7ec;
  margin-bottom: 20px;
  color: #000 !important;
}

// 视频监控
.el-range-separator{
  color: #fff !important;
}
