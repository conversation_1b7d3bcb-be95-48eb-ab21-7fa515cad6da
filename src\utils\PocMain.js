// const poc = require('./poc_sdk.js');
import { hex_md5 } from './md5'
import { getPwdNow,getDyPocList } from '@/api/home'
import lxs_zfy_person from '@/data/lxs_zfy_person.json'
// 初始化标志
let initialized = false
let initializationPromise = null

// 初始化函数
async function init() {
  if (initialized) return Promise.resolve()
  if (initializationPromise) return initializationPromise

  initializationPromise = new Promise(async (resolve, reject) => {
    try {
      // 确保所有模块正确初始化
      initialized = true
      
      // 初始化东阳POC数据
      try {
        if (typeof newDyVm !== 'undefined') {
          await newDyVm.getDeviceList()
        }
      } catch (err) {
        console.error("初始化东阳POC数据失败:", err)
      }
      
      resolve()
    } catch (error) {
      console.error('[POC] 初始化失败:', error)
      reject(error)
    }
  })

  return initializationPromise
}

let ywsPassword = ''
let otherPassword = ''
const mainObj = {
  // 初始化函数
  init: init,

  // 查询认证信息
  async queryAuth() {
    try {
      // 确保 POC 已初始化
      if (!initialized) {
        await init()
      }

      // 确保 poc.data.auth 函数存在
      if (typeof poc.data.auth !== 'function') {
        console.error('poc.data.auth 函数未定义，请检查 dataAction.js 是否正确加载')
        return { isAuth: false, error: 'auth function not defined' }
      }

      // 获取密码
      try {
        await getYwsPassword()
        await getOthersPassword()
        if (typeof sbjVm !== 'undefined' && typeof sbjVm.query === 'function') {
          sbjVm.query()
        }
      } catch (error) {
        console.warn('获取密码或查询失败:', error)
      }

      // 调用认证接口
      return new Promise((resolve) => {
        var obj = {
          Url: 'https://************:4482/station/mobile/serverapi.action',
          HostIp: '', // (传空字符串即可)
          CustomId: 'POC-1370',
          CustomPwd: hex_md5('Ddfk13!@'),
          Callback: function (res) {
            try {
              if (typeof lxsVm !== 'undefined') lxsVm.getSession(res)
              if (typeof ywsVm !== 'undefined') ywsVm.getSession(res)
              if (typeof otherVm !== 'undefined') otherVm.getSession(res)
              if (typeof newDyVm !== 'undefined') newDyVm.getDeviceList()
            } catch (error) {
              console.warn('处理认证响应时出错:', error)
            }
            resolve(res)
          },
        }

        try {
          poc.data.auth(obj)
        } catch (error) {
          console.error('调用 poc.data.auth 失败:', error)
          resolve({ isAuth: false, error: error.message })
        }
      })
    } catch (error) {
      console.error('查询认证失败:', error)
      return { isAuth: false, error: error.message }
    }
  },

  //获取人员在线离线状态并合并数组
  async getOnlineStatus(arr, SessionId) {
    let result = []
    result = JSON.parse(JSON.stringify(arr))
    let onlineArr = []
    let uids = result.map((item) => item.Uid)
    let newArr = mainObj.getSplitArray(uids, 59) //以每个数组长度为59切割成的二维数组

    let promise = null
    promise = newArr.map((uidArr) => {
      return mainObj.getStatus(uidArr, SessionId) //将每个请求封装为promise
    })
    await Promise.all(promise).then((res) => {
      //遍历所有请求的数据
      console.log(res, 'result')
      res.forEach((uidsArr, i) => {
        uidsArr.forEach((uid, i) => {
          onlineArr.push(uid)
        })
      })
    })
    onlineArr = onlineArr.filter((obj) => obj.PresentStatus != -1)
    result.forEach((item, i) => {
      // PresentStatus 0离线 1在线 -1不在管辖范围 转 2: 在线, 3: 离线
      item.ipocid = item.Uid
      item.userstate = onlineArr.find((item2) => item2.Uid == item.Uid).PresentStatus == 1 ? 2 : 3
    })
    console.log(result)
    return result
  },
  //根据指定数组长度切割数组
  getSplitArray(list, splitCount) {
    const result = []
    for (let i = 0; i < list.length; i += splitCount) {
      result.push(list.slice(i, i + splitCount))
    }
    return result
  },
  getStatus(uidArr, SessionId) {
    return new Promise((resolve) => {
      poc.data.userOnlineStatus({
        SessionId: SessionId,
        Uids: uidArr,
        Callback: function (res) {
          console.log(res)
          if (res.Result == 200) {
            resolve(res.Users)
          }
        },
      })
    })
  },
}
var sbjVm = {
  dataList: [],
  query() {
    sbjVm.login()
    sbjVm.getUserList()
  },
  login() {
    fn_Start()
  },
  getUserList() {
    if (localStorage.getItem('MsgBody')) {
      let userList = JSON.parse(localStorage.getItem('MsgBody'))
      sbjVm.dataList = userList.map((item) => {
        return {
          name: item.Name,
          phone: '',
          sys_depart: item.Num,
          dept_name: '',
          lon: item.GpsInfo[0].Longitude,
          lat: item.GpsInfo[0].Latitude,
          county: '市本级',
          lasttime: item.GpsInfo[0].Time,
          hostcode: '',
          lineon: item.status == 1 ? 1 : 0,
          type: 'idt',
        }
      })
      console.log(sbjVm.dataList, 'qwe')
    }
  },
}
var lxsVm = {
  personList: null,
  dataList: [],
  gVideoSession: 0,
  getSession(e) {
    var obj = {
      ServiceCode: e.ServiceCode,
      DispatcherId: '9879310001',
      DispatcherPwd: hex_md5('LXzfj@123'),
      LoginType: 0,
      Callback: function (res) {
        lxsVm.getUserList(res)
      },
    }
    poc.data.getSession(obj)
  },
  getUserList(e) {
    lxsVm.personList = lxs_zfy_person.data
    lxsVm.getLocation(e)
  },
  getLocation(e) {
    poc.data.orgMemberAll({
      SessionId: e.SessionId,
      Callback: function (res) {
        if (res.Result == 200) {
          mainObj.getOnlineStatus(res.Users, e.SessionId).then((userList) => {
            let ids = []
            userList.length > 0 &&
              userList.forEach((item) => {
                ids.push(item.ipocid)
                let i = lxsVm.personList.findIndex((a) => a.Uid == item.ipocid)
                i != -1 && (lxsVm.personList[i].lineon = item.userstate == 2 ? 1 : 0)
              })
            var obj = {
              SessionId: e.SessionId,
              Uids: ids,
              Callback: function (res) {
                res.Locations.map((item1) => {
                  lxsVm.personList.map((item2) => {
                    if (item1.Uid == item2.Uid) {
                      Object.assign(item1, item2)
                      lxsVm.dataList.push(item1)
                    }
                  })
                })
                lxsVm.dataList.forEach((obj) => Object.assign(obj, { county: '兰溪市', type: '' }))
                lxsVm.dataList = JSON.parse(
                  JSON.stringify(lxsVm.dataList)
                    .replace(/GpsLongitude/g, 'lon')
                    .replace(/GpsLatitude/g, 'lat')
                    .replace(/Uid/g, 'sys_depart')
                    .replace(/Time/g, 'lasttime')
                )
                console.log(lxsVm.dataList, 'lxsList')
              },
            }
            poc.data.locationGet(obj)
          })
        }
      },
    })
  },
  dialogBye() {
    var session = lxsVm.gVideoSession
    poc.ptt.doLeaveCall(session)
    console.log('dialogBye: session=' + session)
  },
}
var ywsVm = {
  personList: null,
  dataList: [],
  gVideoSession: 0,
  getSession(e) {
    var obj = {
      ServiceCode: e.ServiceCode,
      DispatcherId: '9883210002',
      DispatcherPwd: hex_md5(ywsPassword),
      LoginType: 0,
      Callback: function (res) {
        ywsVm.getUserList(res)
      },
    }
    poc.data.getSession(obj)
  },
  getUserList(e) {
    var obj = {
      SessionId: e.SessionId,
      Callback: function (res) {
        ywsVm.personList = res.Users
        ywsVm.getLocation(e)
      },
    }
    poc.data.orgMemberAll(obj)
  },
  getLocation(e) {
    poc.data.orgMemberAll({
      SessionId: e.SessionId,
      Callback: function (res) {
        if (res.Result == 200) {
          mainObj.getOnlineStatus(res.Users, e.SessionId).then((userList) => {
            let ids = []
            userList.length > 0 &&
              userList.forEach((item) => {
                ids.push(item.ipocid)
                let i = ywsVm.personList.findIndex((a) => a.Uid == item.ipocid)
                i != -1 && (ywsVm.personList[i].lineon = item.userstate == 2 ? 1 : 0)
              })
            var obj = {
              SessionId: e.SessionId,
              Uids: ids,
              Callback: function (res) {
                res.Locations.map((item1) => {
                  ywsVm.personList.map((item2) => {
                    if (item1.Uid == item2.Uid) {
                      Object.assign(item1, item2)
                      ywsVm.dataList.push(item1)
                    }
                  })
                })
                if (ywsVm.dataList.length > 0) {
                  ywsVm.dataList.forEach((obj) => Object.assign(obj, { county: '义乌市', type: '' }))
                  ywsVm.dataList = JSON.parse(
                    JSON.stringify(ywsVm.dataList)
                      .replace(/GpsLongitude/g, 'lon')
                      .replace(/GpsLatitude/g, 'lat')
                      .replace(/Uid/g, 'sys_depart')
                      .replace(/Time/g, 'lasttime')
                      .replace(/Type/g, 'lineon')
                      .replace(/Name/g, 'name')
                      .replace(/Phone/g, 'phone')
                      .replace(/Orgname/g, 'dept_name')
                  )
                }
                console.log(ywsVm.dataList, 'ywsList')
              },
            }
            poc.data.locationGet(obj)
          })
        }
      },
    })
  },
  dialogBye() {
    var session = ywsVm.gVideoSession
    poc.ptt.doLeaveCall(session)
    console.log('dialogBye: session=' + session)
  },
}
var otherVm = {
  personList: null,
  dataList: [],
  gVideoSession: 0,
  getSession(e) {
    var obj = {
      ServiceCode: e.ServiceCode,
      DispatcherId: '11268810002',
      DispatcherPwd: hex_md5(otherPassword),
      LoginType: 0,
      Callback: function (res) {
        otherVm.getUserList(res)
      },
    }
    poc.data.getSession(obj)
  },
  getUserList(e) {
    var obj = {
      SessionId: e.SessionId,
      Callback: function (res) {
        otherVm.personList = res.Users
        otherVm.getLocation(e)
      },
    }
    poc.data.orgMemberAll(obj)
  },
  getLocation(e) {
    poc.data.orgMemberAll({
      SessionId: e.SessionId,
      Callback: function (res) {
        if (res.Result == 200) {
          mainObj.getOnlineStatus(res.Users, e.SessionId).then((userList) => {
            let ids = []
            userList.length > 0 &&
              userList.forEach((item) => {
                ids.push(item.ipocid)
                let i = otherVm.personList.findIndex((a) => a.Uid == item.ipocid)
                i != -1 && (otherVm.personList[i].lineon = item.userstate == 2 ? 1 : 0)
              })
            var obj = {
              SessionId: e.SessionId,
              Uids: ids,
              Callback: function (res) {
                res.Locations.map((item1) => {
                  otherVm.personList.map((item2) => {
                    if (item1.Uid == item2.Uid) {
                      Object.assign(item1, item2)
                      otherVm.dataList.push(item1)
                    }
                  })
                })
                if (otherVm.dataList.length > 0) {
                  otherVm.dataList.forEach((obj) => Object.assign(obj, { county: '市本级', type: 'poc' }))
                  otherVm.dataList = JSON.parse(
                    JSON.stringify(otherVm.dataList)
                      .replace(/GpsLongitude/g, 'lon')
                      .replace(/GpsLatitude/g, 'lat')
                      .replace(/Uid/g, 'sys_depart')
                      .replace(/Time/g, 'lasttime')
                      .replace(/Type/g, 'lineon')
                      .replace(/Name/g, 'name')
                      .replace(/Phone/g, 'phone')
                      .replace(/Orgname/g, 'dept_name')
                  )
                }
                console.log(otherVm.dataList, 'otherList')
              },
            }
            poc.data.locationGet(obj)
          })
        }
      },
    })
  },
  dialogBye() {
    var session = ywsVm.gVideoSession
    poc.ptt.doLeaveCall(session)
    console.log('dialogBye: session=' + session)
  },
}

//poc新平台（东阳）
var newDyVm = {
  dataList: [],
  getDeviceList() {
    return new Promise((resolve, reject) => {
      getDyPocList().then(res => {
        if (res.code == 200) {
          newDyVm.dataList = res.data.map(item => ({
            ...item,
            lon: item.longitude,
            lat: item.latitude,
            type: 'poc',
            county: '东阳市',
            sys_depart: item.deviceId,
            name: item.displayName,
            phone: "",
            dept_name: item.departName,
            lineon: item.presence == "online" ? 1 : 0,
          }))
          console.log("东阳POC数据加载成功:", newDyVm.dataList.length)
          resolve(newDyVm.dataList)
        } else {
          console.error("获取东阳POC数据失败:", res)
          reject(res)
        }
      }).catch(err => {
        console.error("获取东阳POC数据异常:", err)
        newDyVm.dataList = []
        reject(err)
      })
    })
  }
}

var getYwsPassword = function () {
  getPwdNow({ status: 1 }).then((res) => {
    ywsPassword = res.data.pwd
    sessionStorage.setItem('ywsPassword', ywsPassword)
  })
}
var getOthersPassword = function () {
  getPwdNow({ status: 2 }).then((res) => {
    otherPassword = res.data.pwd
    sessionStorage.setItem('otherPassword', otherPassword)
  })
}

// 导出所有需要的模块
export default mainObj
export { newDyVm, lxsVm, sbjVm, otherVm, ywsVm }
