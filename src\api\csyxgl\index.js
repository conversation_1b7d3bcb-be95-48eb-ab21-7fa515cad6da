import { request } from '@/utils/request'

//获取系统运行状态
export function getServer(params) {
  return request({
    url: `/monitor/server`,
    method: 'get',
    params
  })
}

//养犬管理统计接口
export function getDogJscStatistics(params) {
  return request({
    url: `/petCertificates/getDogJscStatistics`,
    method: 'get',
    params
  })
}

//执法案件-饼图统计
export function getEventTypeJscStatistics(params) {
  return request({
    url: `/zhcg/event/getEventTypeJscStatistics`,
    method: 'get',
    params
  })
}

//执法案件类型、区县统计接口
export function getEventJscStatistics(params) {
  return request({
    url: `/zhcg/event/getEventJscStatistics`,
    method: 'get',
    params
  })
}

//获取案件类型、行政区划下拉框数据
export function getDictType(id) {
  return request({
    url: `/system/dict/data/type/`+id,
    method: 'get',
  })
}

//获取案件分布数据
export function getAJDist(id) {
  return request({
    url: `/zhcg/event/getEventXY`,
    method: 'get',
  })
}

