import {request} from '@/utils/request'

// 查询举报记录列表
export function listOffs(query) {
  return request({
    url: '/business/vol/offs/list',
    method: 'get',
    params: query
  })
}

// 查询举报记录详细
export function getOffs(id) {
  return request({
    url: '/business/vol/offs/' + id,
    method: 'get'
  })
}

// 新增举报记录
export function addOffs(data) {
  return request({
    url: '/business/vol/offs/add',
    method: 'post',
    data: data
  })
}

// 修改举报记录
export function updateOffs(data) {
  return request({
    url: '/business/vol/offs/edit',
    method: 'post',
    data: data
  })
}

// 删除举报记录
export function delOffs(id) {
  return request({
    url: '/business/vol/offs/remove/' + id,
    method: 'post'
  })
}

// 导出举报记录
export function exportOffs(query) {
  return request({
    url: '/business/vol/offs/export',
    method: 'get',
    params: query
  })
}
