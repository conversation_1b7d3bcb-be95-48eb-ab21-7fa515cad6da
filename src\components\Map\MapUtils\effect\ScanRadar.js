// 动态扫描

import * as externalRenderers from "@arcgis/core/views/3d/externalRenderers.js";
import { libRoot } from "../config.js";

var THREE = window.THREE_r116;

class ScanRadar {
  constructor({ view, height, position, radius, color = "white" }) {
    this.view = view;
    this.height = height || 1000;
    this.radius = radius || 5000;
    this.center = position || [0, 0, 0];
    this.uniforms = [];
    this.color = color;
  }

  setup(context) {
    this.renderer = new THREE.WebGLRenderer({
      context: context.gl, // 可用于将渲染器附加到已有的渲染环境(RenderingContext)中
      premultipliedAlpha: false, // renderer是否假设颜色有 premultiplied alpha. 默认为true
    });
    this.renderer.setPixelRatio(window.devicePixelRatio); // 设置设备像素比。通常用于避免HiDPI设备上绘图模糊
    this.renderer.setViewport(0, 0, view.width, view.height); // 视口大小设置

    // 防止Three.js清除ArcGIS JS API提供的缓冲区。
    this.renderer.autoClearDepth = false; // 定义renderer是否清除深度缓存
    this.renderer.autoClearStencil = false; // 定义renderer是否清除模板缓存
    this.renderer.autoClearColor = false; // 定义renderer是否清除颜色缓存

    // ArcGIS JS API渲染自定义离屏缓冲区，而不是默认的帧缓冲区。
    // 我们必须将这段代码注入到three.js运行时中，以便绑定这些缓冲区而不是默认的缓冲区。
    const originalSetRenderTarget = this.renderer.setRenderTarget.bind(
      this.renderer
    );
    this.renderer.setRenderTarget = function (target) {
      originalSetRenderTarget(target);
      if (target == null) {
        // 绑定外部渲染器应该渲染到的颜色和深度缓冲区
        context.bindRenderTarget();
      }
    };

    this.scene = new THREE.Scene(); // 场景
    this.camera = new THREE.PerspectiveCamera(); // 相机

    let transform = new THREE.Matrix4(); // 变换矩阵
    let transformation = new Array(16);
    transform.fromArray(
      externalRenderers.renderCoordinateTransformAt(
        this.view,
        [this.center[0], this.center[1], this.center[2]], // 坐标在地面上的点[x值, y值, 高度值]
        this.view.spatialReference,
        transformation
      )
    );

    // 添加坐标轴辅助工具
    const axesHelper = new THREE.AxesHelper(10000000);
    this.scene.add(axesHelper);

    // setup scene lighting
    this.ambient = new THREE.AmbientLight(0xffffff, 0.5);
    this.scene.add(this.ambient);
    this.sun = new THREE.DirectionalLight(0xffffff, 0.5);
    this.sun.position.set(-600, 300, 60000);
    this.scene.add(this.sun);
    this.uniform = {
      boxH: { value: this.height },
      vColor: { value: new THREE.Color("#4d4aea") },
    };

    var _self = this;
    var img = new Image();
    img.crossOrigin = "";

    img.src = `${libRoot}img/circle_${this.color}.png`;
    img.onload = function () {
      var texture = new THREE.CanvasTexture(_self.produceCanvas(img));
      texture.needsUpdate = true;
      var geometry = new THREE.CircleGeometry(
        _self.radius,
        300,
        Math.PI,
        2 * Math.PI
      );
      _self.ground = new THREE.Mesh(
        geometry,
        new THREE.MeshPhongMaterial({
          map: texture,
          opacity: 1,
          transparent: true,
        })
      );
      var cenP = [];
      externalRenderers.toRenderCoordinates(
        _self.view,
        _self.center,
        0,
        _self.view.spatialReference,
        cenP,
        0,
        1
      );
      const length = Math.sqrt(
        cenP[0] * cenP[0] + cenP[1] * cenP[1] + cenP[2] * cenP[2]
      );
      var stt = new THREE.Vector3(...cenP.map((e) => e / length));
      _self.ground.lookAt(stt);
      _self.ground.position.set(cenP[0], cenP[1], cenP[2]);
      _self.scene.add(_self.ground);
    };

    this.clock = new THREE.Clock();
    context.resetWebGLState();
  }
  render(context) {
    // 更新相机参数
    const cam = context.camera;
    this.camera.position.set(cam.eye[0], cam.eye[1], cam.eye[2]);
    this.camera.up.set(cam.up[0], cam.up[1], cam.up[2]);
    this.camera.lookAt(
      new THREE.Vector3(cam.center[0], cam.center[1], cam.center[2])
    );
    // 投影矩阵可以直接复制
    this.camera.projectionMatrix.fromArray(cam.projectionMatrix);
    // update lighting
    /////////////////////////////////////////////////////////////////////////////////////////////////////
    // view.environment.lighting.date = Date.now();
    var l = context.sunLight;
    this.sun.position.set(l.direction[0], l.direction[1], l.direction[2]);
    this.sun.intensity = l.diffuse.intensity;
    this.sun.color = new THREE.Color(
      l.diffuse.color[0],
      l.diffuse.color[1],
      l.diffuse.color[2]
    );
    this.ambient.intensity = l.ambient.intensity;
    this.ambient.color = new THREE.Color(
      l.ambient.color[0],
      l.ambient.color[1],
      l.ambient.color[2]
    );

    // 更新
    // if (this.uniforms.length > 0) {

    // }
    if (this.uniform) {
      this.uniform.boxH.value += 150;
      if (this.uniform.boxH.value > 8000) {
        this.uniform.boxH.value = 5000.0;
      }
    }
    if (this.ground) {
      this.ground.rotation.z += 0.05;
      if (this.ground.rotation.z > 2 * Math.PI) {
        this.ground.rotation.z = 0;
      }
    }
    this.renderer.state.reset();
    this.renderer.render(this.scene, this.camera);

    // 请求重绘视图。
    externalRenderers.requestRender(view);
    // cleanup
    context.resetWebGLState();
  }
  produceCanvas(img) {
    var canvas = document.createElement("canvas");
    canvas.width = 512;
    canvas.height = 512;
    var ctx = canvas.getContext("2d");
    ctx.clearRect(0, 0, 512, 512);
    ctx.beginPath();
    ctx.strokeStyle = "rgb(128, 128, 128)"; // 圆边框颜色
    ctx.lineWidth = 1;
    ctx.arc(256, 256, 250, 0, Math.PI * 2, true);
    ctx.stroke();
    ctx.save();
    ctx.translate(256, 256);
    ctx.rotate((30 / 180.0) * Math.PI);
    ctx.translate(-256, -256);
    ctx.drawImage(img, 0, 0);
    ctx.restore();
    return canvas;
  }
}

export default ScanRadar;
