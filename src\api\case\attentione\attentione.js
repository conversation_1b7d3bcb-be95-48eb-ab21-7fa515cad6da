import {request} from '@/utils/request'

// 查询我的案件关注列表
export function listAttentione(query) {
  return request({
    url: '/business/attentione/list',
    method: 'get',
    params: query
  })
}

// 查询我的案件关注详细
export function getAttentione(attentionId) {
  return request({
    url: '/business/attentione/' + attentionId,
    method: 'get'
  })
}

// 新增我的案件关注
export function addAttentione(data) {
  return request({
    url: '/business/attentione/add',
    method: 'post',
    data: data
  })
}

// 修改我的案件关注
export function updateAttentione(data) {
  return request({
    url: '/business/attentione/edit',
    method: 'post',
    data: data
  })
}

// 删除我的案件关注
export function delAttentione(attentionId) {
  return request({
    url: '/business/attentione/remove/' + attentionId,
    method: 'post'
  })
}

// 导出我的案件关注
export function exportAttentione(query) {
  return request({
    url: '/business/attentione/export',
    method: 'get',
    params: query
  })
}
