<template>
  <div class='bottom'>
    <div class="bottom-container" v-cloak>
      <div class="left-container">
        <CommonTitle2 text='处罚事项TOP10'></CommonTitle2>
        <div class="table-wrapper">
          <TableComponent
            :thConfig="thConfig1"
            :tableData="formattedTableData1"
            :tableHeight="310"
            :autoScroll="true"
            :scrollInterval="1500"
          />
        </div>
      </div>
      <div class="right-container">
        <CommonTitle2 text='处罚案件'></CommonTitle2>
        <div class="table-wrapper">
          <TableComponent
            :thConfig="thConfig2"
            :tableData="formattedTableData2"
            :tableHeight="310"
            :autoScroll="true"
            :scrollInterval="1500"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CommonTitle2 from '@/components/CommonTitle2'
import TableComponent from '@/components/TableComponent'
import { indexApi } from '@/api/indexApi'
export default {
  name: 'index',
  components: {
    CommonTitle2,
    TableComponent
  },
  data() {
    return {
      year: localStorage.getItem("year"),
      city: localStorage.getItem("city"),
      tableData1: [],
      tableData2: [],
      thConfig1: [
        { th: '序号', field: 'index', width: '20%' },
        { th: '事项名称', field: 'label', width: '40%', hover: true },
        { th: '案件数', field: 'num', width: '40%' }
      ],
      thConfig2: [
        { th: '序号', field: 'index', width: '20%' },
        { th: '领域', field: 'ywwd1', width: '40%', hover: true },
        { th: '案件数', field: 'num', width: '40%' }
      ]
    }
  },
  computed: {
    formattedTableData1() {
      return this.tableData1.map((item, index) => {
        return {
          ...item,
          index: index + 1
        }
      });
    },
    formattedTableData2() {
      return this.tableData2.map((item, index) => {
        return {
          ...item,
          index: index + 1
        }
      });
    }
  },
  mounted() {
    this.$bus.$on('cityChange', (city) => {
      this.city = city;
      this.initApi(city, localStorage.getItem("year"));
    })
    this.$bus.$on('yearChange', (year) => {
      this.year = year;
      this.initApi(localStorage.getItem("city"), year);
    })
    this.initApi(localStorage.getItem("city"), localStorage.getItem("year"));
  },
  methods: {
    initApi(city, year) {
      indexApi("/csdn_yjyp10", { area_code: city, sjwd2: year }).then((res) => {
        this.tableData2 = res.data.sort(function (a, b) {
          return b.num - a.num;
        });
      });
      indexApi("/csdn_yjyp17", { area_code: city, sjwd2: year }).then((res) => {
        this.tableData1 = res.data.sort(function (a, b) {
          return b.num - a.num;
        });
      });
    }
  },
  watch: {}
}
</script>

<style scoped lang='less'>
  .bottom-container {
    width: 1760px;
    height: 525px;
    padding: 20px;
    box-sizing: border-box;
    background: url("@/assets/index/bottom-bg.png") no-repeat;
    background-size: 100% 100%;
    display: flex;
    .left-container {
      width: 60%;
    }
    .right-container {
      width: 40%;
    }
    .table-wrapper {
      padding: 10px 30px;
      box-sizing: border-box;
    }
  }
</style>