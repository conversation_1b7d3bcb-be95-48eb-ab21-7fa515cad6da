<template>
  <ygfDialog :visible='visible' width='1615px'>
    <div id="jcdx" class="rwgz-tc">
    <div class="rw-title flex-between">
      <div class="fs-44 text-mid-yellow dialogTitle" id="rwTitle" style="margin-left: 20px;margin-top: 20px;">{{type}}</div>
      <div class="close cursor" @click="close" style="margin-right: 20px;"></div>
    </div>
    <div class="content">
      <div style="width: 100%;display: flex;justify-content: center;align-items: center">
        <div class="tab-con s-flex" v-show="activeValue != '部门'">
          <span class="tab-item" v-for="(item,i) in tabList" :class="{tabConActive:btnActive==i}"
                @click="changeTab(i)">
            {{item.name}}{{"(" + item.number + ")个"}}
          </span>
        </div>
      </div>
      <div class="table">
        <div class="table-line title-line">
          <div class="table-column table-title" style="flex: 1;margin-left: 30px">序号</div>
          <div class="table-column table-title" style="flex: 3">部门</div>
          <div class="table-column table-title" style="flex: 4;margin-left: 25px">任务名称</div>
          <div class="table-column table-title" style="flex: 2;margin-left: 40px">进度</div>
        </div>
        <div class="table-container" :class="{activeContainer:activeValue == '部门'}">
          <div class="table-line" v-for="(item,i) in tableData" :key="i" :class="{activeTableLine:i % 2 == 0}">
            <div class="table-column" style="flex: 1;margin-left: 30px">{{i + 1}}</div>
            <div class="table-column" style="flex: 2" v-show="activeValue == '县市区'" :title="item.depart">{{item.depart}}</div>
            <div class="table-column" style="flex: 3" :title="item.depart" v-show="activeValue != '县市区'">{{item.depart}}
            </div>
            <div class="table-column" style="flex: 4;margin-left: 25px" :title="item.taskName">{{item.taskName}}</div>
            <div class="table-column" style="flex: 2;margin-left: 40px" :title="item.schedule">{{item.schedule}}</div>
          </div>
        </div>
      </div>
      <!-- 分页 -->
      <div class="el_page_sjzx" v-show="total > 0">
        <el-pagination
          @current-change="getDetail(changeParams(type))"
          layout="prev, pager, next"
          :page-size="queryParams.pageSize"
          :current-page.sync="queryParams.pageNum"
          :total="total"
        ></el-pagination>
      </div>
    </div>
  </div>
  </ygfDialog>
</template>

<script>
import ygfDialog from '@/components/ygfDialog'
import { getAdmActuator } from '@/api/admApi'
export default {
  name: 'index',
  props: ['type','visible'],
  components: {
    ygfDialog
  },
  data() {
    return {
      btnActive: 0,
      btnList: [{ name: "市直部门", type: "金华市",number: 0 }, { name: "县市区", type: "金华市",number: 0 }, { name: "部门", type: "" }],
      tableData: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      total: 0
    }
  },
  computed: {
    city() {
      return localStorage.getItem("city")
    },
    activeValue() {
      return this.tabList[this.btnActive].name
    },
    tabList() {
      return this.city == '金华市' ? this.btnList.filter(item => item.type == '金华市') : this.btnList.filter(item => item.type != '金华市')
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.queryParams.pageNum = 1;
      this.getNumber(this.changeParams(this.type));
      this.getDetail(this.changeParams(this.type));
    },
    changeTab(i) {
      this.btnActive = i;
      this.queryParams.pageNum = 1;
      this.getDetail(this.changeParams(this.type))
    },
    changeParams(str) {
      let result = "";
      result = str == "正在实施计划数"?"执行中":str == "待实施计划数"?"待执行":"已完成";
      return result;
    },
    getNumber(type) {
      if (localStorage.getItem("city") == "金华市") {
        getAdmActuator({
          name: "省回流_监管库_掌上执法检查行为事项明细（新）",
          url: "http://dw.jinhua.gov.cn/gateway/api/001008007012243/dataSharing/764ccyFB3MTaoze6.htm",
          params: {
            fl: '市直部门',
            qx: "",
            jhztms:type,
            pageSize: this.queryParams.pageSize,
            pageNum: this.queryParams.pageNum
          },
        }).then((res) => {
          let rawData = JSON.parse(JSON.stringify(res.datas))
          let data = JSON.parse(rawData)
          this.btnList[0].number = data.data.total
        })
        getAdmActuator({
            name: "省回流_监管库_掌上执法检查行为事项明细（新）",
            url: "http://dw.jinhua.gov.cn/gateway/api/001008007012243/dataSharing/764ccyFB3MTaoze6.htm",
            params: {
              fl: '地区',
              qx: "",
              jhztms:type,
              pageSize: this.queryParams.pageSize,
              pageNum: this.queryParams.pageNum
            }}).then((res) => {
          let rawData = JSON.parse(JSON.stringify(res.datas))
          let data = JSON.parse(rawData)
          this.btnList[1].number = data.data.total
        })
      }
    },
    //获取数据
    getDetail(type) {
      // console.log(this.tabList[this.btnActive].name);
      console.log(localStorage.getItem("city"));
      this.tableData=[]
      getAdmActuator({
        name: "省回流_监管库_掌上执法检查行为事项明细（新）",
        url: "http://dw.jinhua.gov.cn/gateway/api/001008007012243/dataSharing/764ccyFB3MTaoze6.htm",
        params: {
          fl: this.tabList[this.btnActive].name == '市直部门' ? this.tabList[this.btnActive].name : '地区',
          qx: localStorage.getItem("city") == "金华市"?"":localStorage.getItem("city"),
          jhztms:type,
          pageSize: this.queryParams.pageSize,
          pageNum: this.queryParams.pageNum
        },
      }).then((res) => {
        let rawData = JSON.parse(JSON.stringify(res.datas))
        let data = JSON.parse(rawData)
        this.total = data.data.total
        this.tableData = data.data.list.map((item) => {
          return {
            depart: item.jhzdbmmc,
            schedule: item.jhztms,
            taskName: item.jhmc
          }
        })

        // this.tableData = type == "正在实施计划数"?this.tableData.filter(item => item.schedule == "执行中"):type == "待实施计划数"?this.tableData.filter(item => item.schedule == "待执行"):this.tableData.filter(item => item.schedule == "已完成")
        console.log(data.data, '数据1');
        console.log(data.data.list, '数据2');
      })
    },
    close() {
      this.$emit('close')
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.init()
      }
    }
  }
}
</script>

<style scoped lang='less'>

/* 分页 */
/deep/ .el-pagination{
  text-align: center;
  font-weight: normal;
  margin-top: 20px;
}
/deep/ .el-pager li{
  background: none;
  color: #fff;
  font-size: 30px;
  padding: 0 20px;
}
/deep/ .el-pagination button:disabled{
  background: none;
}
/deep/ .el-pagination .btn-next, .el-pagination .btn-prev{
  color: #b5b8bf;
  background: none;
}
/deep/ .el-pagination .btn-next .el-icon, .el-pagination .btn-prev .el-icon{
  font-size: 30px;
}
/deep/ .el-pager li.btn-quicknext, .el-pager li.btn-quickprev{
  color: #b5b8bf;
}

::-webkit-scrollbar {
  display: none;
}

.dialogTitle {
  font-size: 44px;
  background-image: linear-gradient(180deg, #ffb637, #ffb637, #fff, #ffb637, #ffb637);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

ul,
ul li {
  list-style: none;
}

.rwgz-tc {
  width: 1730px;
  height: 1164px;
  background: url("@/assets/zhdd/dialogBg.png") no-repeat;
  background-size: 100% 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-direction: column;
}

.rw-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 46px 3% 0;
  width: 95%;
  height: 60px;
  line-height: 60px;
}

.close {
  background: url("@/assets/zhdd/close.png") no-repeat;
  width: 34px;
  height: 34px;
}

.ql-indent-1 img {
  width: 100%;
}

img {
  width: 100%;
}

.content {
  height: 1090px;
  margin-top: 10px;
  display: flex;
  justify-content: flex-start;
  align-items: flex-end;
  flex-direction: column;
}

::-webkit-scrollbar {
  width: 0;
  height: 0;
}

.scrollTab {
  width: 928px;
  height: 70px;
}

.table-container {
  height: 800px;
  overflow-y: scroll;
}

.activeContainer {
  height: 640px;
}

.tableContainer2 {
  height: 400px;
  overflow-y: scroll;
}

.table-line {
  width: 1625px;
  height: 80px;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  background: rgba(50, 134, 248, 0.15);
}

.title-line {
  background: transparent !important;
}

.table-title {
  font-size: 32px;
  font-family: Source Han Sans CN;
  font-weight: bold;
  color: #FFFFFF;
  text-align: left;
}

.table-column {
  font-size: 32px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.activeTableLine {
  background: rgba(50, 134, 248, 0.25);
}

/deep/ .el-input {
  /*position: absolute;*/
  /*top: 229px;*/
  /*left: 1630px;*/
  width: 537px;
  height: 72px;
}

/deep/ .el-input__icon {
  height: 100%;
  width: 25px;
  text-align: center;
  -webkit-transition: all .3s;
  transition: all .3s;
  line-height: 75px;
}

/deep/ .el-scrollbar {
  overflow: hidden;
  /*position: relative;*/
  height: 500px;
  background: #020b28;
  border-radius: 10px;
}

/deep/ .el-autocomplete-suggestion__wrap {
  max-height: unset !important;
  padding: 10px 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

/deep/ .el-input--suffix .el-input__inner {
  border: 1px solid #359CF8;
  border-radius: 8px;
  padding-right: 30px;
  height: 70px;
  font-family: MicrosoftYaHei;
  font-size: 28px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 1px;
  color: #bbe5fd !important;
  background: #020b28;
}

/deep/ .el-input.is-active .el-input__inner,
/deep/ .el-input__inner:focus {
  border: 1px solid #bbe5fd;
  outline: 0;
}

/deep/ .el-input__suffix-inner {
  pointer-events: all;
  font-size: 28px;
  margin: 15px 20px 0 0;
  color: #bbe5fd !important;
}

/deep/ .el-autocomplete-suggestion li {
  padding: 0 20px;
  line-height: 34px;
  cursor: pointer;
  color: #bbe5fd;
  font-size: 28px;
  list-style: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin: 10px 0 25px 0;
}

/deep/ .el-autocomplete-suggestion li:hover {
  background: unset !important;
}

.search {
  width: 100px;
  height: 65px;
  line-height: 65px;
  text-align: center;
  background: #0A619E;
  border: 1px solid #359CF8;
  border-radius: 8px;
  font-size: 28px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FEFEFE;
  margin-left: 10px;
  cursor: pointer;
}

.tab-con {
  width: 500px;
  height: 70px;
  line-height: 70px;
  color: #fff;
  font-size: 32px;
}

.s-flex {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-evenly;
}

.tab-item {
  font-size: 36px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  font-style: italic;
  color: rgba(171, 206, 239, 0.7);
  line-height: 59px;
}

.tabConActive {
  background: url('@/assets/zfts/tab-active.png') no-repeat;
  background-size: 110% 100%;
  font-family: Source Han Sans CN;
  font-weight: bold;
  font-size: 36px;
  color: #ffffff;
}

.pagination {
  width: 100%;
  margin-top: 50px;
  text-align: center;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

/deep/ .el-pagination.is-background .btn-next,
/deep/ .el-pagination.is-background .btn-prev,
/deep/ .el-pagination.is-background .el-pager li {
  background: transparent;
  color: #c1e2fa;
  font-size: 34px;
  font-weight: normal;
  height: 42px;
  line-height: 40px;
  box-sizing: border-box;
}

/deep/ .el-pager li.active+li {
  border-left: 1px solid #0badc1 !important;
}

/deep/ .el-pagination.is-background .el-pager li:not(.disabled).active {
  background: transparent;
  border-radius: 3px 3px 3px 3px;
  height: 42px;
  line-height: 42px;
  box-sizing: border-box;
}

/deep/ .el-pager li {
  background: transparent;
  padding: 0 20px;
  border: 1px solid #0badc1;
  box-sizing: border-box;
}

/deep/ .el-pagination .btn-next .el-icon,
/deep/ .el-pagination .btn-prev .el-icon {
  font-size: 30px;
}

/deep/ .el-pagination__jump {
  font-size: 32px !important;
  height: 42px !important;
  line-height: 42px !important;
  color: #c1e2fa;
  display: inline-flex !important;
  align-items: center;
}

/deep/ .el-pagination__editor.el-input {
  height: 32px;
  width: 77px;
  margin: 0 12px 0 12px;
}

/deep/ .el-pagination__editor.el-input .el-input__inner {
  height: 32px;
  font-size: 28px;
  background: #132c4e;
  color: #C1E2FA;
  border: 1px solid;
  border-image: linear-gradient(-32deg, #359cf8, #afdcfb) 1 1;
}
</style>