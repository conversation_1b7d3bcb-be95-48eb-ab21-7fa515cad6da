import {request} from '@/utils/request'

// 查询志愿者活动列表
export function listTask(query) {
  return request({
    url: '/business/vol/task/list',
    method: 'get',
    params: query
  })
}

// 查询志愿者活动详细
export function getTask(taskId) {
  return request({
    url: '/business/vol/task/' + taskId,
    method: 'get'
  })
}

// 新增志愿者活动
export function addTask(data) {
  return request({
    url: '/business/vol/task/add',
    method: 'post',
    data: data
  })
}

// 修改志愿者活动
export function updateTask(data) {
  return request({
    url: '/business/vol/task/edit',
    method: 'post',
    data: data
  })
}

// 删除志愿者活动
export function delTask(taskId) {
  return request({
    url: '/business/vol/task/remove/' + taskId,
    method: 'post'
  })
}

// 导出志愿者活动
export function exportTask(query) {
  return request({
    url: '/business/vol/task/export',
    method: 'get',
    params: query
  })
}
