/* zhdd模块公共样式 */

/* 通用标题样式 */
.zhdd-title {
  font-size: 32px;
  font-weight: bold;
  color: #fff;
  margin-bottom: 20px;
}

/* 数值显示样式 */
.zhdd-number {
  font-family: DINCondensed;
  font-size: 48px;
  font-style: italic;
  font-weight: bold;
}

.zhdd-number-blue {
  color: #3CFDFF;
}

.zhdd-number-yellow {
  color: #eed252;
}

.zhdd-number-green {
  color: #45f95e;
}

/* 单位样式 */
.zhdd-unit {
  font-size: 20px;
  margin-left: 10px;
}

/* 容器样式 */
.zhdd-container {
  width: 100%;
  padding: 20px;
  box-sizing: border-box;
  position: relative;
}

/* 表格样式 */
.zhdd-table {
  width: 100%;
  border-collapse: collapse;
}

.zhdd-table-header {
  background-color: rgba(14, 92, 158, 0.5);
  color: #fff;
  font-size: 28px;
  height: 60px;
  display: flex;
  align-items: center;
}

.zhdd-table-body {
  height: 400px;
  overflow-y: auto;
}

.zhdd-table-row {
  height: 60px;
  border-bottom: 1px solid rgba(14, 92, 158, 0.3);
  display: flex;
  align-items: center;
}

.zhdd-table-cell {
  padding: 0 10px;
  font-size: 24px;
  color: #fff;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 按钮样式 */
.zhdd-button {
  display: inline-block;
  padding: 8px 16px;
  background-color: rgba(14, 92, 158, 0.7);
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 24px;
  transition: background-color 0.3s;
}

.zhdd-button:hover {
  background-color: rgba(14, 92, 158, 0.9);
}

/* 图标样式 */
.zhdd-icon {
  display: inline-block;
  width: 32px;
  height: 32px;
  background-size: contain;
  background-repeat: no-repeat;
  cursor: pointer;
}

.zhdd-icon-phone {
  background-image: url('../../assets/zhdd/phone.png');
}

.zhdd-icon-video {
  background-image: url('../../assets/zhdd/video.png');
}

/* 选项卡样式 */
.zhdd-tabs {
  display: flex;
  margin-bottom: 20px;
}

.zhdd-tab {
  padding: 10px 20px;
  font-size: 28px;
  color: #fff;
  cursor: pointer;
  border-bottom: 2px solid transparent;
}

.zhdd-tab-active {
  border-bottom-color: #3CFDFF;
  color: #3CFDFF;
}

/* 视频容器样式 */
.zhdd-video-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.zhdd-video-item {
  width: 440px;
  height: 270px;
  position: relative;
  background-color: rgba(0, 0, 0, 0.3);
}

.zhdd-video-title {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 8px;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  font-size: 24px;
  text-align: center;
}