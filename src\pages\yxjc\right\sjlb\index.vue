<template>
  <div class="wrap">
    <CommonTitle2 text="时间列表"></CommonTitle2>
    <div class="reportBtn flex-c" @click="openDialog">
      <i class="el-icon-document"></i>
      <div style="margin-left: 10px">查看详情</div>
    </div>
    <div class="search_box">
      <el-input
        placeholder="请输入事件名称"
        suffix-icon="el-icon-search"
        v-model="input"
        style="width: 400px"
        @change="queryData"
      ></el-input>
      <el-select v-model="value" placeholder="请选择" style="width: 450px; margin-left: 20px" @change="queryData">
        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
      </el-select>
    </div>
    <div style="margin: 0 30px">
      <CommonTable :height="'570px'" :tableData="tableData"></CommonTable>
    </div>
  </div>
</template>

<script>
import CommonTitle2 from '@/components/CommonTitle2'
import CommonTable from '@/components/CommonTable'
import { geteventList } from '@/api/yxjc/index.js'

export default {
  name: 'index',
  components: {
    CommonTitle2,
    CommonTable,
  },
  data() {
    return {
      options: [],
      input: '',
      value: '',
      options: [
        { label: '全部', value: '' },
        { label: '物联设备监测报警', value: '物联设备监测报警' },
        { label: '环卫设施安全运行预测预警', value: '环卫设施安全运行预测预警' },
        { label: '人员密集场所安全运行预测预警', value: '人员密集场所安全运行预测预警' },
      ],
      tableData: {
        thead: [
          { label: '事件名称', property: 'eventName', width: 140, align: 'left' },
          { label: '事件类型', property: 'eventType', width: 140, align: 'left' },
          { label: '事件等级', property: 'eventLevel', width: 100, align: 'left' },
          { label: '发生时间', property: 'eventTime', width: 140, align: 'left' },
        ],
        tbody: [],
      },
    }
  },
  computed: {},
  mounted() {
    this.queryData()
  },
  methods: {
    queryData() {
      geteventList({ eventName: this.input, eventType: this.value, pageSize: 9999 }).then((res) => {
        if (res.code == 200) {
          this.tableData.tbody = res.rows
        }
      })
    },
    openDialog() {
      this.$emit('openDialog')
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
.wrap {
  display: relative;
  height: 710px;
  .reportBtn {
    position: absolute;
    bottom: 765px;
    right: 20px;
    // width: 284px;
    padding: 0 14px 0 14px;
    box-sizing: border-box;
    height: 60px;
    border-radius: 10px;
    background: linear-gradient(180deg, #17aee0 0%, #17aee0 0%, #0c8ac3 50%, #0166a6 100%);
    font-size: 28px;
    color: #ffffff;
    line-height: 32px;
    cursor: pointer;
  }
}
.search_box {
  padding: 30px 30px;
  box-sizing: border-box;
  /deep/.el-input__inner {
    height: 48px !important;
    background-color: #132c4e !important;
    border: 2px solid #afdcfb !important;
    color: #fff !important;
    border-radius: 15px !important;
    font-size: 24px;
  }
}

.flex-c {
  display: flex;
  align-items: center;
}
</style>
<style lang='less'>
.el-select-dropdown__list {
  background: #132c4e !important;
  .el-select-dropdown__item {
    color: #fff !important;
    font-size: 10px;
    line-height: 20px;
    height: 20px;
  }
  .el-select-dropdown__item.hover {
    color: #132c4e !important;
  }
}
</style>