<template>
  <div class='left'>
    <xzjc></xzjc>
    <sqxzjc></sqxzjc>
<!--    <jcjhtc></jcjhtc>-->
<!--    <jgyjs></jgyjs>-->
  </div>
</template>

<script>
import jcjhtc from './jcjhtc'
import jgyjs from './jgyjs'
import xzjc from './xzjc'
import sqxzjc from './sqxzjc'
export default {
  name: 'index',
  components: {
    jcjhtc,
    jgyjs,
    xzjc,
    sqxzjc
  },
  data() {
    return {}
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped>

</style>