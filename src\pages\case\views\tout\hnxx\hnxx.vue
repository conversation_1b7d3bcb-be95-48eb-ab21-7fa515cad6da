<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      :inline="true"
    >
      <el-form-item label="登记时间">
        <el-date-picker
          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px;"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item>
        <el-input v-model="queryParams.searchValue" size="small" style="width: 240px; margin: 0 5px;" placeholder="请输入内容" />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:role:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >
          新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:role:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="listData"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />

      <el-table-column
        label="姓名"
        prop="name"
        align="center"
        :show-overflow-tooltip="true"
        width="150"
      />
      <el-table-column
        label="联系电话"
        prop="phone"
        align="center"
        :show-overflow-tooltip="true"
        width="150"
      />
      <el-table-column
        label="登记时间"
        align="center"
        prop="createTime"
        width="200"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="地址"
        prop="address"
        align="center"
        :show-overflow-tooltip="true"
        width="200"
      />
      <el-table-column
        label="内容"
        align="center"
        prop="content"
        :show-overflow-tooltip="true"
        width="200"
      />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['system:role:edit']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >
            修改
          </el-button>

          <el-button
            v-hasPermi="['system:role:remove']"
            size="mini"
            type="text"
            icon="el-icon-delete"
            style="color: red;"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- <el-pagination
      :page-sizes="[10, 20, 30]"
      :page-size="1"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      style="margin-top: 20px;"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    /> -->

    <!-- 添加或修改角色配置对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="1000px">
      <fromlist v-if="open" ref="fromlist" :get-form="form" :qd="qd" @onPrimary="primary" @oncancel="cancel" />
    </el-dialog>
  </div>
</template>

<script>
import fromlist from './components/fromlist'
import tout from '@/api/case/tout/hnxx'
export default {
  components: {
    fromlist
  },
  data() {
    return {
      form: {status: 1},
      queryParams: { data: 0, pageNum: 1, pageSize: 10, dateRange: [] /* 日期范围 */},
      loading: true,
      ids: [],
      total: 0,
      title: '',
      open: false,
      qd: true,
      timer: null,
      // 非多个禁用
      multiple: true,
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      // 显示搜索条件
      showSearch: true,
      // 表格数据
      roleList: []
    }
  },
  computed: {
    listData() {
      let {pageNum, pageSize} = this.queryParams
      let arr = this.roleList.slice((pageNum - 1) * pageSize, (pageNum - 1) * pageSize + pageSize)
      return arr
    }
  },
  created() {
    this.getList()
  },
  beforeDestroy() {
    clearTimeout(this.timer)
    this.timer = null
  },
  methods: {
    // 确定按钮
    primary(params) {
      this.qd = false
      if (params.toutId) {
        tout.toutEdit(params).then(res => {
          console.log(res)
          this.$refs.fromlist.upload({id: params.toutId, status: params.status})
          this.qd = true
          this.msgSuccess('修改成功')
          this.timer = setTimeout(() => {
            this.open = false
            this.getList()
          }, 1000)
        }).catch(() => { this.qd = true })
      } else {
        tout.toutAdd(params).then(addres => {
          this.qd = true
          this.msgSuccess('添加成功')
          this.$refs.fromlist.upload({id: addres.data.toutId, status: addres.data.status})
          this.timer = setTimeout(() => {
            this.open = false
            this.getList()
          }, 1000)
        }).catch(() => { this.qd = true })
      }
    },
    // 取消
    cancel() {
      this.open = false
    },
    // 已下为模板
    /** 查询列表 */
    getList() {
      this.loading = true
      let {searchValue, dateRange, pageNum, pageSize} = this.queryParams
      let params = { pageNum, pageSize}
      if (searchValue) params.searchValue = searchValue
      if (Array.isArray(dateRange) && !dateRange.length == 0) { params = {...params, searchStartTime: dateRange[0], searchEndTime: dateRange[1] } }
      tout.toutList(params).then(res => {
        if (pageNum == 1) {
          this.roleList = res.rows
        } else {
          this.roleList = this.roleList.concat(res.rows)
        }
        console.log(this.roleList)
        this.total = res.total
        this.loading = false
      })
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.toutId)
      this.single = selection.length != 1
      this.multiple = !selection.length
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {dateRange: [], pageNum: 1, pageSize: 10}
      this.handleQuery()
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const fourIds = row.toutId || this.ids
      console.log(fourIds)
      this.$confirm('是否确认删除?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.roleList = this.roleList.filter(
            irem => row.toutId != irem.toutId
          )
          this.total--
        })
        .then(() => {
          tout.toutRemove(fourIds).then(() => {
            this.getList()
            this.msgSuccess('删除成功')
          })
        })
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.open = true
      if (row) {
        this.form = row
      }
      this.title = '修改信息'
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.form = {}
      this.open = true
      this.title = '添加黄牛信息'
    },
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.queryParams.pageNum = 1
      this.getList()
    },
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.getList()
    }
  }
}
</script>
