import { request } from '@/utils/request'

//物联总览
export function getSummary(params) {
  return request({
    url: `/screen/device/summary`,
    method: 'get',
    params
  })
}

//获取设备类型分布统计
export function getDeviceTypes(params) {
  return request({
    url: `/screen/device/deviceTypes`,
    method: 'get',
    params
  })
}

//获取在线设备列表
export function getOnlineDevices(params) {
  return request({
    url: `/screen/device/onlineDevices`,
    method: 'get',
    params
  })
}

//获取设备详细信息
export function getDeviceDetail(params) {
  return request({
    url: `/screen/device/deviceDetail`,
    method: 'get',
    params
  })
}

//获取设备告警记录
export function getAlarmRecords(params) {
  return request({
    url: `/screen/device/alarmRecords`,
    method: 'get',
    params
  })
}

//获取实时监测数据
export function getRealTimeData(params) {
  return request({
    url: `/screen/device/realTimeData`,
    method: 'get',
    params
  })
}

//获取监测点位
export function getDevicePoint(params) {
  return request({
    url: `/screen/device/devicePoint`,
    method: 'get',
    params
  })
}