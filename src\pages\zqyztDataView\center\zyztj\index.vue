<template>
  <div class='wrap-bg'>
    <CommonTitle text='志愿者统计' style='margin-top: 40px;'></CommonTitle>
    <div class='wrap-container'>
      <div class='wrap-container-chart' ref="chartBottom"></div>
      <div class='chartList'>
        <div v-for="(item, index) in chartList" :key="index" class='chartItem'>
          <pie-chart
            :chart-id="`pie-chart-${index}`"
            :percentage="item.percent"
            :color="item.colors"
          />
          <div class='chartText'>{{item.text}}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import PieChart from '@/components/PieChart'
import * as echarts from 'echarts'
import { getZyztj } from '@/api/dataView'
export default {
  name: 'index',
  components: {
    CommonTitle,
    PieChart
  },
  data() {
    return {
      chartData: [],
      chartList: [
        {
          text: '男性',
          percent: 75,
          colors: [
            '#00C0FF',
            'rgba(0,192,255,0.7)',
            'rgba(0,192,255,0.4)',
            'rgba(0,192,255,0)',
          ]
        },
        {
          text: '女性',
          percent: 75,
          colors: [
            '#22E8E8',
            'rgba(34,232,232,0.7)',
            'rgba(34,232,232,0.4)',
            'rgba(34,232,232,0)',
          ]
        },
        {
          text: '其他',
          percent: 75,
          colors: [
            '#FFC460',
            'rgba(255,196,96,0.7)',
            'rgba(255,196,96,0.4)',
            'rgba(255,196,96,0)',
          ]
        }
      ]
    }
  },
  computed: {},
  mounted() {
    this.initApi()
  },
  methods: {
    initApi() {
      getZyztj().then(res => {
        const colors = [[
          '#00C0FF',
          'rgba(0,192,255,0.7)',
          'rgba(0,192,255,0.4)',
          'rgba(0,192,255,0)',
        ],[
          '#22E8E8',
          'rgba(34,232,232,0.7)',
          'rgba(34,232,232,0.4)',
          'rgba(34,232,232,0)',
        ],[
          '#FFC460',
          'rgba(255,196,96,0.7)',
          'rgba(255,196,96,0.4)',
          'rgba(255,196,96,0)',
        ]];
        this.chartData = res.data.gztj.map((item,i) => ({
          name: item.name,
          value: item.num
        }))
        this.chartList = res.data.xbtj.map((item,i) => ({
          text: item.name,
          percent: item.num?item.num:0,
          colors: colors[i]
        }))
        this.initChart()
      })
    },
    initChart() {
      const that = this;
      this.chart = echarts.init(this.$refs.chartBottom)

      const option = {
        backgroundColor: 'transparent',
        graphic: {
          type: 'image',
          style: {
            image: require('@/assets/zqyzt/pie-bg.png'),
            width: 360,
            height: 360
          },
          left: '24.8%',  // 调整背景图位置，与环形图对齐
          top: 'center'
        },
        title: [{
          text: '',
          left: '18.5%',
          top: '48%',
          textStyle: {
            color: '#E3F4FF',
            fontSize: 52,
            fontWeight: 'normal',
            fontFamily: 'DIN',
            lineHeight: 72
          },
          z: 10  // 确保文字在背景图之上
        }, {
          text: '',
          left: '19%',
          top: '38%',
          textStyle: {
            color: '#E3F4FF',
            fontSize: 28,
            fontWeight: 'normal',
            lineHeight: 28
          },
          z: 10  // 确保文字在背景图之上
        }],
        legend: {
          orient: 'vertical',
          left: '55%',
          y: 'center',
          itemWidth: 16,
          itemHeight: 16,
          itemGap: 28,
          icon: 'circle',
          formatter: (name) => {
            // 查找所有系列中的数据项
            let value = '';
            const item = that.chartData.find(d => d.name === name);
            if (item) value = item.value;
            return `{name|${name}}{value|${value}}`
          },
          textStyle: {
            rich: {
              name: {
                fontSize: 26,
                color: '#fff',
                padding: [0, 30, 0, 0],
                width: 140,
              },
              value: {
                fontSize: 26,
                color: '#fff',
                padding: [0, 0, 0, 0]
              }
            }
          }
        },
        series: [{
          type: 'pie',
          radius: ['55%', '65%'],
          center: ['35%', '50%'],
          startAngle: 90,
          itemStyle: {
            borderRadius: 0,
            borderColor: 'rgba(2,47,115,0.5)',
            borderWidth: 2,
          },
          data: this.chartData,
          label: {
            show: false
          },
          labelLine: {
            show: false
          },
          emphasis: {
            scale: false
          }
        }]
      }

      this.chart.setOption(option)
    },
  },
  watch: {}
}
</script>

<style scoped lang='less'>
.wrap-container {
  width: 100%;
  height: 806px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  .wrap-container-chart {
    width: 100%;
    height: 656px;
  }
  .chartList {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    position: relative;
    bottom: 70px;
    .chartItem {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      .chartText {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 400;
        font-size: 28px;
        color: #C1D3E3;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
    }
  }
}
</style>