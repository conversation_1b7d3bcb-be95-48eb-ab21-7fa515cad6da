<template>
  <div v-if="visible" class="largeWindowInfo">
    <div class="right">
      <div class="search-top">
        <el-row :gutter="20">
          <el-col :span="1.5">
            <el-input v-model="userName" class="w-150" size="mini" placeholder="按用户名搜索" @keyup.enter.native="searchList" @blur="searchList" />
          </el-col>
          <el-col :span="1.5">
            <el-input v-model="terminalNo" class="w-150" size="mini" placeholder="按终端号搜索" @keyup.enter.native="searchList" @blur="searchList" />
          </el-col>
          <el-col :span="1.5">
            <el-select v-model="online" class="w-150" size="mini" @change="searchList">
              <el-option value="" label="全部" />
              <el-option value="1" label="在线" />
              <el-option value="0" label="离线" />
            </el-select>
          </el-col>
        </el-row>
        <span class="close" @click="handleClose">×</span>
      </div>
      <!-- 列表 -->
      <el-table :data="tableList" height="calc(100% - 0.15625rem)">
        <el-table-column property="name" label="姓名" />
        <el-table-column property="terminalNo" label="终端号" />
        <el-table-column label="状态">
          <template slot-scope="scope">
            <span :style="{ color: scope.row.status == 1 ? '#00cc19': '#ccc' }">{{ scope.row.status == 1 ? '在线': '离线' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-tooltip class="item" :open-delay="500" effect="dark" content="查看位置" placement="top">
              <el-button type="text" style="padding: 0;" icon="el-icon-location-outline" @click="handleClick(scope.row.id)" />
            </el-tooltip>
            <el-tooltip class="item" :open-delay="500" effect="dark" content="查看详情" placement="top">
              <el-button type="text" style="padding: 0;" icon="el-icon-document" @click="handleOpenDetail({ id: scope.row.id, terminalNo: scope.row.terminalNo})" />
            </el-tooltip>
            <el-tooltip class="item" :open-delay="500" effect="dark" content="视频呼叫" placement="top">
              <el-button type="text" style="padding: 0;" icon="el-icon-video-camera" @click="handleOpenVideo(scope.row.terminalNo)" />
            </el-tooltip>
            <el-tooltip class="item" :open-delay="500" effect="dark" content="语音呼叫" placement="top">
              <el-button type="text" style="padding: 0;" icon="el-icon-phone-outline" @click="handleOpenVoice(scope.row.terminalNo)" />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    userlistData: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      userName: '',
      online: '',
      terminalNo: '',
      searchResList: []
    }
  },
  computed: {
    tableList() {
      if (this.searchResList == null) {
        return []
      } else if (this.searchResList.length) {
        return this.searchResList
      } else {
        return this.userlistData
      }
    }
  },
  methods: {
    handleClose() {
      console.log(this.userlistData)
      this.dateRange = []
      this.$emit('update:visible', false)
    },
    handleClick(id) {
      this.$emit('changeCenter', id)
    },
    handleOpenDetail(info) {
      this.$emit('openDetail', info)
    },
    searchList() {
      const userName = this.userName.trim()
      const terminalNo = this.terminalNo.trim()
      if (this.online == '' && userName == '' && terminalNo == '') {
        this.searchResList = []
        return
      }
      const resAry = this.userlistData.filter(item => {
        let nameFlag = item.name ? item.name.includes(userName) : false
        const terminalNoFlag = item.terminalNo.includes(terminalNo)
        if (terminalNoFlag && !userName) nameFlag = true
        const onlineFlag = this.online == '' ? true : item.status == this.online
        console.log(nameFlag, terminalNoFlag, onlineFlag)
        return (nameFlag && terminalNoFlag && onlineFlag)
      })
      this.searchResList = resAry.length ? resAry : null
    },
    handleOpenVideo(terminalNo) {
      this.$emit('setTerminalNo', terminalNo)
      this.$store.commit('board/SET_CALL_TYPE', { type: 'callRecv', ARx: 1, ATx: 1, VRx: 1, VTx: 0 }) // 修改为呼出状态
      this.$store.commit('board/SET_VIDEO_VISIBLE', true)
    },
    handleOpenVoice(terminalNo) {
      this.$emit('setTerminalNo', terminalNo)
      this.$store.commit('board/SET_CALL_TYPE', { type: 'callRecv', ARx: 1, ATx: 1, VRx: 0, VTx: 0 }) // 修改为呼出状态
      this.$store.commit('board/SET_VIDEO_VISIBLE', true)
    }
  }
}
</script>

<style scoped lang="scss">
.largeWindowInfo {
  width: pxtorem(600);
  height: pxtorem(400);
  // position: absolute;
  // top: pxtorem(280);
  // left: pxtorem(30);
  background: #fff;
  // z-index: 1000;
  box-shadow: 0 4px 14px rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  position: relative;
  .w-150 {
    width: pxtorem(150);
  }
  .right {
    position: absolute;
    left: pxtorem(20);
    top: pxtorem(20);
    bottom: pxtorem(20);
    right: pxtorem(20);
    .search-top {
      height: pxtorem(30);
      position: relative;
      .el-date-editor.el-input {
        width: 220px;
        ::v-deep .el-input__inner {
          background: #eee;
        }
      }
      .close {
        position: absolute;
        top: 0;
        right: 0;
        font-size: 20px;
        color: #c3c3c3;
        cursor: pointer;
        &:hover {
          color: #999;
        }
      }
    }
  }
}
</style>
