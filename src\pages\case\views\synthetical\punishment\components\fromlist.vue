<template>
  <div class="m-dialog">
    <el-form ref="form" v-loading="loading" :model="form" :disabled="formDisabled" :rules="rules" label-width="130px">
      <el-row>
        <h3 class="title" style="position: relative;">
          <span>基本信息</span>
          <attentionBtn :case-id="form.punishmentId || 0" case-type="punish" :case-content="form.content" />
        </h3>
        <el-col :span="24">
          <el-form-item label="处罚对象" prop="userType">
            <el-radio-group v-model="form.userType">
              <el-radio :label="1">公民</el-radio>
              <el-radio :label="2">法人、其它组织</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col v-if="form.userType == 2" :span="24">
          <el-row>
            <el-col :span="8">
              <el-form-item label="是否个体工商户">
                <el-select v-model="form.isSelfEmployed">
                  <el-option label="是" :value="1" />
                  <el-option label="否" :value="0" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="单位名称" prop="companyName">
                <el-input v-model="form.companyName" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="统一社会信用代码" prop="companyCode">
                <el-input v-model="form.companyCode" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="单位地址" prop="companyAddress">
                <el-input v-model="form.companyAddress" placeholder="" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="8">
          <el-form-item label="姓名" prop="party">
            <el-input v-model="form.party" placeholder="请输入当事人姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系电话" prop="phone">
            <el-input v-model="form.phone" placeholder="请输入联系电话" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="性别" prop="gender">
            <el-select v-model="form.gender">
              <el-option label="男" :value="1" />
              <el-option label="女" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="年龄" prop="age">
            <el-input v-model="form.age" placeholder="请输入年龄" />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="身份证" prop="identityCard">
            <el-input v-model="form.identityCard" placeholder="请输入当事人身份证" />
          </el-form-item>
        </el-col> -->

        <h3 class="title">案件信息</h3>
        <el-col :span="12">
          <el-form-item label="案发时间" prop="caseTime">
            <div class="block">
              <el-date-picker
                v-model="form.caseTime"
                value-format="yyyy-MM-dd"
                type="datetime"
                placeholder="选择日期时间"
                default-time="12:00:00"
              />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="案由名称" prop="summaryName">
            <el-input v-model="form.summaryName" placeholder="请选择案由名称" readonly>
              <!-- <el-button slot="append" type="primary" @click="userOpens = true">选择</el-button> -->
            </el-input>
            <userSelect id="summaryName" v-model="userOpens" :name="'anyou'" :multiple="false" :select-user-keys="[form.summaryId]" :default-expanded-keys="[form.summaryId+'']" @confirm="summary" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="案件地址" prop="address">
            <!-- <el-tooltip class="item" effect="dark" :content="form.address" placement="top-start">
              <div @click="openMap = true">

              </div>
            </el-tooltip> -->
            <el-input v-model="form.address" placeholder="请选择案件地址">
              <svg-icon slot="suffix" icon-class="map" class="svg-icon" @click="openMap = true" />
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="立案简介" prop="caseContent">
            <el-input v-model="form.caseContent" :autosize="{ minRows: 2, maxRows: 5}" type="textarea" placeholder="" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="上传图片" prop="files">
            <el-upload
              ref="upload"
              multiple
              :limit="4"
              list-type="picture-card"
              class="upload-demo"
              accept=".jpg,.jpeg,.png,.JPG,.JPEG,.PNG"
              action="/zqzfj/system/file/upload"
              :headers="headers"
              :on-remove="handleRemove"
              :on-preview="handlePictureCardPreview"
              :on-error="handleError"
              :on-success="handleSuccess"
              :data="formData"
              :before-remove="beforeRemove"
              :file-list="formFiles"
              :auto-upload="false"
              name="files"
              :on-exceed="exceed"
            >
              <i class="el-icon-plus" />
            </el-upload>
          </el-form-item>
        </el-col>
        <h3 class="title">处罚信息</h3>
        <el-col :span="12">
          <el-form-item label="处罚类型" prop="punishType">
            <el-radio-group v-model="form.punishType">
              <el-radio label="0">警告</el-radio>
              <el-radio label="1">罚款</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col v-if="form.punishType == 1" :span="12">
          <el-form-item label="处罚金额" prop="punishMoney">
            <el-input v-model="form.punishMoney" prefix-icon="el-icon-money" placeholder="请输入标题" />
          </el-form-item>
        </el-col>
      </el-row>
      <tdtMap ref="bindmap" :visible.sync="openMap" append-to-body :map-search="true" :dw="form" @onlnglat="onlnglat" />
      <el-row>
        <el-col :span="12">
          <el-form-item label="主办人" prop="userName">
            <el-input v-model="form.userName" placeholder="主办人" readonly />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="协办人" prop="userIds">
            <el-input v-model="form.userNames" placeholder="请选择协办人" readonly>
              <!-- <el-button slot="append" type="primary" @click="userOpen1 = true">选择</el-button> -->
            </el-input>
            <userSelect id="userNames" v-model="userOpen1" :select-user-keys="form.userIds?(form.userIds+'').split(','):[]" :default-expanded-keys="form.userIds?(form.userIds+'').split(','):[]" @confirm="confirm" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="网格小组" prop="deptName">
            <el-input v-model="form.deptName" readonly placeholder="请选择网格小组" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <div slot="footer" class="dialog-footer">
        <!-- <el-button type="success" :disabled="!fromQd || formDisabled" @click="primary(9)">办 结</el-button> -->
        <!-- <el-button type="primary" :disabled="!fromQd || formDisabled" @click="primary(2)">确 定</el-button> -->
        <AddClassic v-if="!isBoard" :before-processing="form.caseContent" :after-processing="afterProcessing" />
        <el-button @click="cancel">取 消 </el-button>
      </div>
    </div>
    <!-- 图片预览 -->
    <el-dialog :close-on-click-modal="false" :visible.sync="dialogVisible" append-to-body>
      <img width="100%" :src="dialogImageUrl">
    </el-dialog>
  </div>
</template>

<script>
import tdtMap from '@/components/tdtMap/tdtMap'
import { getToken } from '@/utils/auth'
import { getFiles, removeFiles} from '@/api/supervise/swit'
import userSelect from '@/components/userselect/index'
import {punishmentOne} from '@/api/case/synthetical/punishment'
import attentionBtn from '@/components/attentioneBtn/index.vue'
import AddClassic from '@/components/AddClassic/index.vue'

export default {
  name: 'Fromlist',
  components: {
    userSelect,
    tdtMap,
    attentionBtn,
    AddClassic
  },
  props: {
    qd: {
      type: Boolean,
      default() {
        return true
      }
    },
    detailId: Number,
    formDisabled: Boolean,
    isBoard: Boolean
  },
  data() {
    return {
      userTypeList: [{id: 1, value: '公民'}, {id: 2, value: '法人'}, {id: 3, value: '其他'}],
      form: { userId: '', userName: '', userIds: '', userNames: '', summaryId: '', files: []},
      loading: true,
      openMap: false,
      userOpen: false,
      userOpen1: false,
      userOpens: false,
      fromQd: true,
      headers: {Authorization: 'Bearer ' + getToken()},
      formData: {businessId: null, tableName: 'case_punishment'},
      rules: {
        content: [{ required: true, message: '请输入内容', trigger: 'blur' }],
        party: [{ required: true, message: '请输入当事人', trigger: 'blur' }],
        companyName: [{ required: true, message: '请扫码填写', trigger: 'blur' }],
        // identityCard: [{ required: true, message: '请输入身份证', trigger: 'blur' }],
        caseTime: [{ required: true, message: '请选择案发时间', trigger: 'change' }],
        userName: [{ required: true, message: '请选择发起人员', trigger: 'change' }],
        summaryId: [{ required: true, message: '请选择案由', trigger: 'change' }],
        phone: [{ required: true, message: '请输入当事人联系电话', trigger: 'blur' }],
        address: [{ required: true, message: '请选择地址', trigger: 'change' }],
        punishMoney: [{ required: true, message: '请输入金额', trigger: 'blur' }],
        caseContent: [{ required: true, message: '请输入店铺名称', trigger: 'change' }]
      },
      formFiles: [], // 文件
      dialogVisible: false,
      dialogImageUrl: ''
    }
  },
  computed: {
    afterProcessing() {
      if (this.form.punishType == 0) {
        return '警告'
      } else if (this.form.punishType == 1) {
        return `罚款，罚款金额为${this.form.punishMoney}元`
      } else {
        return ''
      }
    }
  },
  created() {
    let params = {businessId: this.detailId, tableName: 'case_punishment'}
    Promise.all([this.getFile(params), punishmentOne(this.detailId)]).then(res => {
      this.form = {...this.form, ...res[1].data}
      if (this.form.status == 9  && this.form.status) { this.fromQd = false }
    }).catch(err => { console.log(err) })
    this.loading = false
  },
  methods: {
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    getFile(params) {
      getFiles(params).then(res => {
        let filter = res.rows.reduce((arr, v) => {
          let url = {name: v.displayName, url: `/zqzfj${v.filePath}?id=${v.fileId}`, ...v}
          arr.push(url)
          return arr
        }, [])
        this.formFiles = filter
      })
    },
    onlnglat(lenlat) {      // 经纬度  详细地址
      let {lng: longitude, lat: latitude, address} = lenlat
      this.form = {...this.form, longitude, latitude, address}
      this.openMap = false
    },
    inputs(e) {
      this.userOpen = e
      this.userOpen1 = e
      this.userOpens = e
    },
    userConfirm(e) {    // 上报人名称
      this.form = {...this.form, userName: e.name, userId: e.id}
    },
    confirm(e) {    // 辅助人员名称
      this.form = {...this.form, userNames: e.name, userIds: e.id}
    },
    summary(e) { // anyou
      this.form = {...this.form, summaryName: e.name, summaryId: e.id}
    },
    primary(status) {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (!this.$refs.upload.uploadFiles.length) return this.msgError('请上传违规图片')
          if (!this.fromQd) return
          let params = {...this.form, status}
          this.$emit('onPrimary', params)
          this.openMap = false
        } else {
          return false
        }
      })
    },
    // 取消
    cancel() {
      this.openMap = false
      this.form = {}
      this.$emit('oncancel')
    },
    // 上传函数
    beforeRemove(file) {
      return new Promise((resolve, reject) => {
        this.$confirm('是否确认删除？ 删除后将无法恢复', '提示', { type: 'warning' }).then(() => {
          if (file.fileId) {
            removeFiles(file.fileId).then(resolve).catch(reject)
          } else {
            resolve()
          }
        }).catch(() => {
          reject()
        })
      })
    },
    handleRemove() {   // 删除图片
    },
    exceed(files, filesList) {
      if (filesList.length >= 4) this.msgError('已超出最大上传数量')
    },
    handleSuccess(files) {    // 上传成功
      if (files.code == 200) {
        this.msgSuccess('上传成功')
      } else {
        this.msgSuccess('上传失败,请修改后重试')
      }
    },
    handleError() { // 上传失败
      this.msgError('上传失败,请修改后重试')
    },
    // 上传
    upload(data) {
      console.log(data)
      this.formData.businessId = data.id
      this.formData.status = data.status
      this.$refs.upload.submit()
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  margin-top: 20px;
  text-align: right;
}
.el-upload__tip {
  display: inline-block;
  margin-left: 5%;
}
.map {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10000;
  padding: 5vh 18px 5vh 0;
  background-color: rgba(0, 0, 0, 0.6);
}
::v-deep .el-upload--picture-card,
::v-deep .el-upload-list--picture-card .el-upload-list__item {
  width: 100px;
  height: 100px;
  line-height: 100px;
}
:v-deep .svg-icon {
  font-size: 25px;
  margin-left: 2px;
}
.title {
  width: 100%;
  float: left;
  display: block;
  padding: 10px 0 10px 30px;
  margin-bottom: 20px;
  border-bottom: 1px solid #e4e7ec;
  color: #000;
}

</style>
