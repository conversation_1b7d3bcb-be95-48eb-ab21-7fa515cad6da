<template>
  <el-dialog
    title="提示"
    :modal='false'
    :visible.sync="visible"
    :show-close="false"
    :width="width"
    :append-to-body='false'
    :modal-append-to-body='false'
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    custom-class="custom-class-dialog">
    <slot></slot>
  </el-dialog>
</template>

<script>
export default {
  name: 'index',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    width: {
      type: String,
      default: ''
    }
  },
  data() {
    return {}
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang='less'>
/*重置element-ui弹框*/

/deep/.el-dialog {
  background: transparent;
  box-shadow: unset;
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  border: unset;

  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    padding: 0;
  }
}
</style>