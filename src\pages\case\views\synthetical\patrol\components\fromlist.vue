<template>
  <div class="m-dialog">
    <el-form ref="form" v-loading="loading" :model="form" :disabled="formDisabled" :rules="rules" label-width="110px">
      <el-row>
        <!-- <el-col :span="8">
          <el-form-item label="标题" prop="inspectionName">
            <el-tooltip class="item" effect="dark" :content="form.inspectionName" placement="top-start">
              <el-input v-model="form.inspectionName" placeholder="" clearable />
            </el-tooltip>
          </el-form-item>
        </el-col> -->
        <h3 class="title">
          <span>基本信息</span>
          <attentionBtn :case-id="form.inspectionId || 0" case-type="inspection" :case-content="form.inspectionContent" />
        </h3>
        <el-col :span="12">
          <el-form-item label="当事人">
            <el-input v-model="form.contact" placeholder="" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系号码">
            <el-input v-model="form.phone" placeholder="" />
          </el-form-item>
        </el-col>
      </el-row>
      <tdtMap ref="bindmap" :visible.sync="openMap" append-to-body :map-search="true" :dw="form" @onlnglat="onlnglat" />
      <el-row v-if="form.businessNo">
        <el-col :span="12">
          <el-form-item label="店铺名称" prop="businessName">
            <el-input v-model="form.businessName" placeholder="" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="营业执照号码" prop="businessNo">
            <el-input v-model="form.businessNo" placeholder="" disabled />
          </el-form-item>
        </el-col>
      </el-row>
      <el-col :span="24">
        <el-form-item label="性别" prop="sex">
          <el-radio v-model="form.sex" label="1">男</el-radio>
          <el-radio v-model="form.sex" label="2">女</el-radio>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="发生时间" prop="happenDate">
          <el-tooltip class="item" effect="dark" :content="form.happenDate" placement="top-start">
            <div class="block">
              <el-date-picker
                v-model="form.happenDate"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="datetime"
                placeholder="选择日期时间"
                default-time="12:00:00"
              />
            </div>
          </el-tooltip>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="案件地址" prop="address">
          <el-tooltip class="item" effect="dark" :content="form.address" placement="top-start">
            <div>
              <el-input v-model="form.address" placeholder="请选择案件地址">
                <svg-icon slot="suffix" icon-class="map" class="svg-icon" @click="openMap = true" />
              </el-input>
            </div>
          </el-tooltip>
        </el-form-item>
      </el-col>
      <el-row>
        <el-col :span="24">
          <el-form-item label="现场图片" prop="files">
            <el-upload
              ref="upload"
              multiple
              :limit="4"
              list-type="picture-card"
              class="upload-demo"
              accept=".jpg,.jpeg,.png,.JPG,.JPEG,.PNG"
              action="/zqzfj/system/file/upload"
              :headers="headers"
              :on-preview="handlePictureCardPreview"
              :on-remove="handleRemove"
              :on-error="handleError"
              :on-success="handleSuccess"
              :data="formData"
              :file-list="formFiles"
              :auto-upload="false"
              name="files"
              :before-remove="beforeRemove"
              :on-exceed="exceed"
            >
              <i class="el-icon-plus" />
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
      <h3 class="title">警情信息</h3>
      <el-row>
        <el-col :span="12">
          <el-form-item label="巡查人名称" prop="userId">
            <el-input v-model="form.userName" readonly placeholder="请选择巡查人名称">
              <el-button slot="append" type="primary" @click="userOpen = true">选择</el-button>
            </el-input>
            <userSelect id="userName" v-model="userOpen" :select-user-keys="[parseInt(form.userId)]" :multiple="false" :default-expanded-keys="[form.userId+'']" @confirm="userConfirm" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="网格小组" prop="deptName">
            <el-input v-model="form.deptName" readonly placeholder="请选择网格小组" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="辅助人员名称" prop="userIds">
            <el-input v-model="form.userNames" readonly placeholder="请选择辅助人员名称">
              <el-button slot="append" type="primary" @click="userOpen1 = true">选择</el-button>
            </el-input>
            <userSelect id="userNames" v-model="userOpen1" :select-user-keys="form.userIds?(form.userIds+'').split(','):[]" :default-expanded-keys="form.userIds?(form.userIds+'').split(','):[]" @confirm="confirm" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="是否有问题" prop="isProblem">
            <el-radio v-model="form.isProblem" label="1">有</el-radio>
            <el-radio v-model="form.isProblem" label="0">无</el-radio>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="form.isProblem == 1">
        <el-row>
          <el-col :span="12">
            <el-form-item label="警情类型" prop="policeCategory">
              <el-select v-model="form.policeCategoryName" style="width: 100%;" placeholder="请选择警情类型" @change="selectOne">
                <el-option
                  v-for="item in policeCategoryData"
                  :key="item.dictSort"
                  :label="item.dictLabel"
                  :value="item.dictSort"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="警情类别" prop="policeType">
              <el-select v-model="form.policeTypeName" style="width: 100%;" placeholder="请选择警情类别" @change="selectpoliceType">
                <el-option
                  v-for="item in policeTypeData"
                  :key="item.dictSort"
                  :label="item.dictLabel"
                  :value="item.dictSort"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="案由名称" prop="summaryName">
              <el-input v-model="form.summaryName" readonly placeholder="请选择案由名称">
                <el-button slot="append" type="primary" @click="userOpens = true">选择</el-button>
              </el-input>
              <userSelect id="summaryName" v-model="userOpens" :multiple="false" :name="'anyou'" :select-user-keys="[form.summaryId]" :default-expanded-keys="[form.summaryId+'']" @confirm="summary" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="处理类型" prop="handleType">
              <el-select v-model="form.handleType" style="width: 100%;" placeholder="请选择处理类型">
                <el-option label="当场整改" value="1" />
                <el-option label="简易处罚" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="案情内容" prop="policeContent">
              <el-input v-model="form.policeContent" :autosize="{ minRows: 2, maxRows: 5}" type="textarea" placeholder="" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="是否联合执法" prop="isUnion">
            <el-radio v-model="form.isUnion" label="1">是</el-radio>
            <el-radio v-model="form.isUnion" label="0">否</el-radio>
          </el-form-item>
        </el-col>
        <el-col v-if="form.isUnion == 1" :span="24">
          <el-form-item label="联合执法" prop="unionName">
            <el-input v-model="form.unionName" placeholder="请选择联合执法" @focus="unionOpen = true" />
            <unionLow id="unionName" :value.sync="unionOpen" @onConfirm="onConfirm" />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-row>
        <el-col :span="24">
          <el-form-item label="是否临时任务" prop="isTemporary">
            <el-radio v-model="form.isTemporary" label="1">是</el-radio>
            <el-radio v-model="form.isTemporary" label="0">否</el-radio>
          </el-form-item>
        </el-col>
      </el-row> -->
      <h3 class="title">警情信息</h3>
      <el-row>
        <el-col :span="24">
          <el-form-item label="处理结果" prop="inspectionContent">
            <el-input v-model="form.inspectionContent" :autosize="{ minRows: 2, maxRows: 5}" type="textarea" placeholder="" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="处理后图片" prop="files">
            <el-upload
              ref="upload"
              multiple
              :limit="4"
              list-type="picture-card"
              class="upload-demo"
              accept=".jpg,.jpeg,.png,.JPG,.JPEG,.PNG"
              action="/zqzfj/system/file/upload"
              :headers="headers"
              :on-preview="handlePictureCardPreview"
              :on-remove="handleRemove"
              :on-error="handleError"
              :on-success="handleSuccess"
              :data="formData"
              :file-list="resFile"
              :auto-upload="false"
              name="files"
              :before-remove="beforeRemove"
              :on-exceed="exceed"
            >
              <i class="el-icon-plus" />
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <!-- <el-button type="success" :disabled="formDisabled" @click="primary(9)">办 结</el-button> -->
      <!-- <el-button type="primary" :disabled="formDisabled" @click="primary(2)">确 定</el-button> -->
      <AddClassic v-if="!isBoard" :before-processing="form.policeContent" :after-processing="form.inspectionContent" />
      <el-button @click="cancel">取 消 </el-button>
    </div>
    <!-- 图片预览 -->
    <el-dialog :close-on-click-modal="false" :visible.sync="dialogVisible" append-to-body>
      <img width="100%" :src="dialogImageUrl">
    </el-dialog>
  </div>
</template>
<script>
import tdtMap from '@/components/tdtMap/tdtMap'
import { getFiles, removeFiles} from '@/api/supervise/swit'
import { getToken } from '@/utils/auth'
import userSelect from '@/components/userselect/index'
import unionLow from '@/components/unionLow/unionLow'
import {listData} from '@/api/system/dict/type'
import rwfq from '@/api/case/union/rwfq'
import {inspectionOne} from '@/api/case/synthetical/patrol'
import attentionBtn from '@/components/attentioneBtn/index.vue'
import AddClassic from '@/components/AddClassic/index.vue'

export default {
  name: 'Fromlist',
  components: {
    userSelect,
    tdtMap,
    unionLow,
    attentionBtn,
    AddClassic
  },
  props: {
    getForm: {
      type: Object,
      default() {
        return null
      }
    },
    qd: {
      type: Boolean,
      default() {
        return true
      }
    },
    formDisabled: Boolean,
    detailId: Number,
    isBoard: Boolean

  },

  data() {
    return {
      unionOpen: false,
      headers: {Authorization: 'Bearer ' + getToken()},
      loading: true,
      formData: {businessId: null, tableName: 'case_inspection'},
      form: {userId: '', userName: '', userIds: '', userNames: '', summaryId: '', files: []},
      fileList: [],
      resFile: [],
      policeTypeData: [],
      policeCategoryData: [],
      mapAdderssData: [],
      // 表单校验
      userOpen: false,
      userOpen1: false,
      userOpens: false,
      openMap: false,
      dialogImageUrl: '',
      dialogVisible: false,
      disabled: false,
      formFiles: [], // 文件
      rules: {
        // inspectionName: [
        //   { required: true, message: '请输入标题', trigger: 'blur' }
        // ],
        inspectionContent: [
          { required: true, message: '请输入案件内容', trigger: 'blur' }
        ],
        businessNo: [
          { required: true, message: '请输入营业执照号码', trigger: 'blur' }
        ],
        userId: [
          { required: true, message: '请选择上报人名称', trigger: 'change' }
        ],
        // userIds: [
        //   { required: true, message: '请选择处理人名称', trigger: 'change' }
        // ],
        // phone: [
        //   {required: true, message: '号码不能为空', trigger: 'blur'},
        //   {pattern: /^[1][3,4,5,7,8,9][0-9]{9}$/, message: '请输入正确的手机号'}
        // ],
        // contact: [
        //   { required: true, message: '请输入联系人', trigger: 'change' }
        // ],
        address: [
          { required: true, message: '请输入地址', trigger: 'change' }
        ],
        policeType: [
          { required: true, message: '请选择警情类型', trigger: 'change' }
        ],
        policeCategory: [
          { required: true, message: '请选择警情类别', trigger: 'change' }
        ],
        policeContent: [
          { required: true, message: '请输入警情内容', trigger: 'change' }
        ],
        happenDate: [
          {  required: true, message: '请选择发生时间', trigger: 'change' }
        ],
        unionName: [
          {  required: true, message: '请选择联合执法', trigger: 'change' }
        ]

      },
      open: ''
    }
  },
  mounted() {
    if (this.detailId) {

      let params = {businessId: this.detailId, tableName: 'case_inspection'}
      Promise.all([listData({dictType: 'case_alert_type'}), listData({dictType: 'case_call_type'}), this.getFile(params), rwfq.List({status: 2}), inspectionOne(this.detailId)]).then(res => {
        this.form = {...this.form, ...res[4].data}
        this.policeTypeData = res[0].rows
        this.policeTypeData.map(v => {
          if (v.dictSort == this.form.policeType) this.form.policeTypeName = v.dictLabel
        })
        this.policeCategoryData = res[1].rows
        this.policeCategoryData.map(v => {
          if (v.dictSort == this.form.policeCategory) this.form.policeCategoryName = v.dictLabel
        })
        res[3].rows.map(v => { if (v.unionId == this.form.unionId) this.form = {...this.form, unionName: v.title} })
        this.policeTypeData.map(v => {
          if (v.dictSort == this.form.policeType) this.form.policeTypeName = v.dictLabel
        })
        this.loading = false
      }).catch(() => this.loading = false)
    }
  },
  methods: {
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    onConfirm(data) {
      this.unionOpen = false
      this.form = {...this.form, unionId: data.unionId, unionName: data.title}
      console.log(this.form)
    },
    getFile(params) {
      getFiles(params).then(res => {
        let formFiles = [], resFile = []
        res.rows.forEach(v => {
          let url = {name: v.displayName, url: `/zqzfj${v.filePath}?id=${v.fileId}`, ...v}
          if (v.status == 2) {
            formFiles.push(url)
          } else if (v.status == 9) {
            resFile.push(url)
          }
        })
        this.resFile = resFile
        this.formFiles = formFiles
      })
    },
    onlnglat(lenlat) {      // 经纬度  详细地址
      let {lng: longitude, lat: latitude, address} = lenlat
      this.form = {...this.form, longitude, latitude, address}
      this.openMap = false
    },
    inputs(e) {
      this.userOpen = e
      this.userOpen1 = e
      this.userOpens = e
    },
    userConfirm(e) {    // 上报人名称
      this.form = {...this.form, userName: e.name, userId: e.id}
    },
    confirm(e) {    // 辅助人员名称
      this.form = {...this.form, userNames: e.name, userIds: e.id}
    },
    summary(e) { // anyou
      this.form = {...this.form, summaryName: e.name, summaryId: e.id}
    },
    selectpoliceType(e) { //	警情类型
      let data = this.policeTypeData.filter(item => { return item.dictSort == e })
      this.form = {...this.form, policeTypeName: data[0].dictLabel, policeType: data[0].dictSort}
    },
    selectOne(e) {    // 警情类bie
      let data = this.policeCategoryData.filter(item => { return item.dictSort == e })
      this.form = {...this.form, policeCategoryName: data[0].dictLabel, policeCategory: data[0].dictSort}
    },
    // 上传函数
    beforeRemove(file) {
      return new Promise((resolve, reject) => {
        this.$confirm('是否确认删除？ 删除后将无法恢复', '提示', { type: 'warning' }).then(() => {
          if (file.fileId) {
            removeFiles(file.fileId).then(resolve).catch(reject)
          } else {
            resolve()
          }
        }).catch(() => {
          reject()
        })
      })
    },
    handleRemove() {   // 删除图片
    },
    exceed(files, filesList) {
      if (filesList.length >= 4) this.msgError('已超出最大上传数量')
    },
    handleSuccess(files) {    // 上传成功
      if (files.code == 200) {
        this.msgSuccess('图片上传成功')
      } else {
        this.msgSuccess('上传失败,请修改后重试')
      }
    },
    handleError() { // 上传失败
      this.msgError('上传失败,请修改后重试')
    },
    // 上传
    upload(data) {
      this.formData.businessId = data.id
      this.formData.status = data.status
      this.$refs.upload.submit()
    },
    primary(status) {
      let params = {...this.form, status}
      console.log(params)
      if (status == 2) return this.$emit('onPrimary', params)
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (!this.$refs.upload.uploadFiles.length) return this.msgError('请上传违规图片')
          this.$emit('onPrimary', params)
          this.openMap = false
        } else {
          return false
        }
      })
    },
    // 取消
    cancel() {
      this.openMap = false
      this.form = {}
      this.$emit('oncancel')
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  margin-top: 20px;
  text-align: right;
}
.el-upload__tip {
  display: inline-block;
  margin-left: 5%;
}
.map {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10000;
  padding: 5vh 18px 5vh 0;
  background-color: rgba(0, 0, 0, 0.6);
}
::v-deep .el-upload--picture-card,
::v-deep .el-upload-list--picture-card .el-upload-list__item {
  width: 100px;
  height: 100px;
  line-height: 100px;
}
:v-deep .svg-icon {
  font-size: 25px;
  margin-left: 2px;
}
.title {
  display: block;
  padding: 10px 0 10px 30px;
  margin-bottom: 20px;
  border-bottom: 1px solid #e4e7ec;
  color: #000;
  width: 100%;
  float: left;
}
</style>
