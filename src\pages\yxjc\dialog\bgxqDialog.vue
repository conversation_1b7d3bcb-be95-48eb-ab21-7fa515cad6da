<template>
  <ygfDialog :visible="visible" width="2034px" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.8)">
    <!--startprint-->
    <div class="bgxq-dialog" :class="printing ? 'bgxq-dialog2' : ''" id="bgxq-dialog" ref="bgxq">
      <div class="rw-title flex-between" style="margin-top: 70px">
        <div class="fs-44 titleText" style="margin-left: 20px">报告详情</div>
        <div class="close cursor" @click="close" style="margin-right: 20px"></div>
      </div>
      <div class="content">
        <CommonTitle2 text="风险管理"></CommonTitle2>
        <div class="flex-b">
          <div id="fxgl1" style="width: 50%; height: 430px"></div>
          <!-- <div id="fxgl2" style="width: 50%; height: 430px"></div> -->
        </div>
        <CommonTitle2 text="风险管理"></CommonTitle2>
        <div class="table">
          <div class="tableHead flex-c">
            <div class="td">事件编号</div>
            <div class="td" style="flex: 2">事件名称</div>
            <div class="td">事件类型</div>
            <div class="td">发生时间</div>
            <div class="td" style="flex: 2">事件地址</div>
            <div class="td" style="flex: 0.5">操作</div>
          </div>
          <div
            class="tableLine flex-c"
            v-for="(item, i) in dataList"
            :key="i"
            :class="{ tableLine_active: i % 2 == 1 }"
          >
            <div class="td">{{ item.name }}</div>
            <div class="td" style="flex: 2">{{ item.name }}</div>
            <div class="td">{{ item.name }}</div>
            <div class="td">{{ item.name }}</div>
            <div class="td" style="flex: 2">{{ item.address }}</div>
            <div class="td" style="color: #22e097; flex: 0.5; cursor: pointer">操作</div>
          </div>
        </div>
        <!-- <div class="pagi">
          <el-pagination
            background
            layout="total,prev, pager, next,jumper"
            :current-page.sync="params.pageNum"
            :total="total"
            :page-size="10"
            @current-change="changePage"
          ></el-pagination>
        </div> -->
        <CommonTitle2 text="运行检测"></CommonTitle2>
        <div class="item-wrap flex-c">
          <div class="itemCon" v-for="(item, i) in itemList" :key="i" @click="showDetail(i)">
            <div class="title">{{ item.title }}</div>
            <div class="num flex-c">
              <div>{{ item.name }}</div>
              <div class="text-yellow">{{ item.num || '-' }}</div>
              <span>{{ item.unit }}</span>
            </div>
            <div class="time">({{ item.time }})</div>
            <div class="flex-b">
              <div class="rate">
                同比
                <span :class="item.tb >= 0 ? 'text-red' : 'text-green'">
                  <span class="rateValue">{{ item.tb > 0 ? '+' + item.tb : item.tb }}%</span>
                  <img v-if="item.tb > 0" src="@/assets/yxjc/up.png" class="icon" />
                  <img v-if="item.tb < 0" src="@/assets/yxjc/down.png" class="icon" />
                </span>
              </div>
              <div class="rate">
                环比
                <span :class="item.hb >= 0 ? 'text-red' : 'text-green'">
                  <span class="rateValue">{{ item.hb > 0 ? '+' + item.hb : item.hb }}%</span>
                  <img v-if="item.hb > 0" src="@/assets/yxjc/up.png" class="icon" />
                  <img v-if="item.hb < 0" src="@/assets/yxjc/down.png" class="icon" />
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!--endprint-->

    <img id="snapshotImageElement" v-show="false" />
  </ygfDialog>
</template>

<script>
import html2canvas from './html2canvas.min.js'
import Canvas2Image from './Canvas2Image.js'
import jsPDF from 'jspdf'
import ygfDialog from '@/components/ygfDialog'
import CommonTitle2 from '@/components/CommonTitle2'
import { getPublicList2, getwarningCount, geteventList } from '@/api/yxjc/index.js'

export default {
  name: 'index',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    isPrint: {
      type: Boolean,
      default: false,
    },
    isPdf: {
      type: Boolean,
      default: false,
    },
    reportMonth: {
      type: String,
      default: '',
    },
  },
  components: {
    ygfDialog,
    CommonTitle2,
  },
  data() {
    return {
      //图表
      pieChartData: [
        { name: '重点任务', value: 779 },
        { name: '案件类型1', value: 779 },
        { name: '案件类型2', value: 779 },
        { name: '物联设备预警事件', value: 779 },
      ],
      //表格
      params: {
        pageNum: 1,
      },
      total: 0,
      dataList: [
        // { name: 'XXXXX', time: '2024-03-11 05:06:20', id: '' },
        // { name: 'XXXXX', time: '2024-03-11 05:06:20', id: '' },
      ],
      //运行监测
      itemList: [
        { title: '累计用电量', name: '总用电量', num: '4244', unit: '亿吨', tb: 0, hb: 0, time: '' },
        { title: '累计用水量', name: '总用水量', num: '', unit: '亿吨', tb: 0, hb: 0, time: '' },
        { title: '累计用气量', name: '总用气量', num: '', unit: '亿吨', tb: 0, hb: 0, time: '' },
        {
          title: '累计废弃排放量',
          name: '总废气排放量',
          num: '',
          unit: '亿吨',
          tb: 0,
          hb: 0,
          time: '',
        },
        {
          title: '环境空气指数（AQI）',
          name: '',
          num: '',
          unit: '',
          tb: 0,
          hb: 0,
          time: '',
        },
        {
          title: '累计交通流量',
          name: '',
          num: '',
          unit: '辆/月',
          tb: 0,
          hb: 0,
          time: '',
        },
      ],
      //
      loading: false,
      printing: false,
    }
  },
  computed: {},
  watch: {
    visible(val) {
      if (val) {
        console.log(this.reportMonth, this.isPrint, this.isPdf)
        this.getDetail()
      }
    },
  },
  mounted() {
    // if (this.visible) {
    //   this.getDetail()
    // }
    // this.createImg()
  },
  methods: {
    getDetail() {
      getwarningCount({ monthDate: this.reportMonth }).then((res) => {
        this.pieChartData = res.data.map((item) => {
          return {
            name: item.key,
            value: item.value,
          }
        })
        this.initChart('fxgl1', this.pieChartData)
      })

      let params = {
        pageNum: 1,
        monthDate: this.reportMonth,
        pageSize: 9999,
      }
      geteventList(params).then((res) => {
        if (res.code == 200) {
          this.dataList = res.rows
          this.total = res.total
        }
      })

      getPublicList2({ monthDate: this.reportMonth }).then((res) => {
        this.itemList = res.data.map((item) => {
          return {
            title: item.name,
            name: '总' + item.name.substring(2),
            num: item.value,
            unit: item.unit,
            tb: item.tongRate,
            hb: item.huanRate,
            time: item.indicatorDate,
          }
        })
      })

      if (this.isPrint || this.isPdf) {
        this.loading = true
        this.createImg()
      }
    },
    changePage() {},
    initChart(id, data) {
      let total = 0
      data.forEach((item) => {
        total += item.value
      })
      this.chart = this.$echarts.init(document.getElementById(id))
      const option = {
        animation: false,
        backgroundColor: 'transparent',
        color: ['#22E197', '#98DC3E', '#00EAFF', '#FF6A00'],
        graphic: {
          type: 'image',
          style: {
            image: require('@/assets/dog/chartBg.png'),
            width: 380,
            height: 380,
          },
          left: '6.9%', // 调整背景图位置，与环形图对齐
          top: 'center',
        },
        title: [
          {
            text: total,
            left: '19.5%',
            top: '48%',
            textStyle: {
              color: '#E3F4FF',
              fontSize: 52,
              fontWeight: 'normal',
              fontFamily: 'DIN',
              lineHeight: 72,
              textAlign: 'center',
            },
            z: 10, // 确保文字在背景图之上
          },
          {
            text: '事件总数',
            left: '19.5%',
            top: '38%',
            textStyle: {
              color: '#E3F4FF',
              fontSize: 28,
              fontWeight: 'normal',
              lineHeight: 28,
              textAlign: 'center',
            },
            z: 10, // 确保文字在背景图之上
          },
        ],
        legend: {
          orient: 'vertical',
          left: '45%',
          y: 'center',
          itemWidth: 20,
          itemHeight: 16,
          itemGap: 32,
          icon: 'circle',
          formatter: (name) => {
            var data = option.series[0].data //获取series中的data
            let tarValue = 0
            for (var i = 0, l = data.length; i < l; i++) {
              if (data[i].name == name) {
                tarValue = data[i].value
                return `{name|${name}}{value|${tarValue}}`
              }
            }
          },
          textStyle: {
            rich: {
              name: {
                fontSize: 28,
                color: '#fff',
                padding: [0, 30, 0, 0],
                width: 390,
              },
              value: {
                fontSize: 28,
                color: '#fff',
                padding: [0, 0, 0, 0],
              },
            },
          },
        },
        series: [
          {
            type: 'pie',
            radius: ['55%', '65%'],
            center: ['26%', '50%'],
            startAngle: 90,
            itemStyle: {
              borderRadius: 0,
              borderColor: 'rgba(2,47,115,0.5)',
              borderWidth: 2,
            },
            data: data,
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
            emphasis: {
              scale: false,
            },
          },
        ],
      }

      this.chart.setOption(option)
    },
    createImg() {
      this.printing = true
      let that = this
      setTimeout(() => {
        this.loading = false
        const node = this.$refs.bgxq
        // 手动设置关键样式
        // const titleElements = node.querySelectorAll('.title')
        // titleElements.forEach((el) => {
        //   el.style.fontSize = '20px'
        //   el.style.lineHeight = '30px'
        //   el.style.color = '#ffffff'
        //   el.style.background = 'unset'
        // })
        var opts = {
          useCORS: true, //支持图片跨域
          allowTaint: false, //这个属性和useCORS不能同时为true哦
          logging: false,
          letterRendering: true,
        }
        html2canvas(this.$refs.bgxq, opts) //传入你想生成图片的dom
          .then(function (canvas) {
            //Canvas2Image是将canvas转换成图片哦
            var img = Canvas2Image.convertToImage(canvas, canvas.width, canvas.height)
            img.style.width = '100%'
            img.style.height = '100%'
            // document.getElementById('bgxq-dialog').appendChild(img)

            if (that.isPrint) {
              let dataUrl = canvas.toDataURL()
              const printWindow = window.open('', '_blank')
              printWindow.document.write('<img src="' + dataUrl + '" onload="window.print();window.close();" />')
              printWindow.document.close()
            }
            if (that.isPdf) {
              const imgData = canvas.toDataURL('image/png')
              const pdf = new jsPDF()
              pdf.addImage(imgData, 'PNG', 10, 10, 190, 190)
              pdf.save('download.pdf')
              // let customName = prompt('请输入文件名:', '报告详情')
              // const blob = pdf.output('blob')
              // const link = document.createElement('a')
              // link.href = URL.createObjectURL(blob)
              // link.download = customName ? `${customName}.pdf` : 'download.pdf'
              // document.body.appendChild(link)
              // link.click()
              // URL.revokeObjectURL(link.href)
              // document.body.removeChild(link)
            }
          })
        this.printing = false
        this.close()
      }, 1000)
    },
    close() {
      this.$emit('close')
    },
  },
}
</script>

<style scoped lang='less'>
::-webkit-scrollbar {
  display: none;
}

ul,
ul li {
  list-style: none;
}

.bgxq-dialog {
  width: 2030px;
  height: 2012px;
  background: url('@/assets/yxjc/dialogBg2.png') no-repeat;
  background-size: 100% 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-direction: column;
}

.rw-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 46px 3% 0;
  width: 95%;
  height: 60px;
  line-height: 60px;
}

.close {
  background: url('@/assets/zhdd/close.png') no-repeat;
  width: 34px;
  height: 34px;
}

.ql-indent-1 img {
  width: 100%;
}

img {
  width: 100%;
}

::-webkit-scrollbar {
  width: 0;
  height: 0;
}

.scrollTab {
  width: 928px;
  height: 70px;
}

.content {
  width: 100%;
  padding: 20px 36px 60px 26px;
  box-sizing: border-box;
}

.table {
  padding: 20px 30px 30px 30px;
  box-sizing: border-box;
  width: 100%;
  height: 480px;
  overflow: scroll;
  .td {
    padding: 20px 30px;
    box-sizing: border-box;
    flex: 1;
    text-align: left;
  }
  .tableHead {
    height: 80px;
    background-color: #0a6cff33;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 500;
    font-size: 32px;
    color: #cde7ff;
    text-align: left;
  }
  .tableLine {
    min-height: 80px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 500;
    font-size: 32px;
    color: #cde7ff;
    text-align: left;
  }
  .tableLine_active {
    background-color: #0a6cff33;
  }
}
.pagi {
  display: flex;
  justify-content: flex-end;
  margin-top: 40px;
}
.item-wrap {
  flex-wrap: wrap;
  align-items: flex-start;
  padding: 0 20px 0 40px;
  box-sizing: border-box;
  width: 100%;
  .itemCon {
    width: calc((100% - 70px * 2) / 3);
    margin-right: 70px;
    margin-top: 30px;
    padding: 20px 20px 20px 20px;
    box-sizing: border-box;
    height: fit-content;
    background: url('@/assets/yxjc/itemBg.png') no-repeat;
    background-size: 100% 100%;
    cursor: pointer;
    &:nth-child(3n + 3) {
      margin-right: 0;
    }
    .title {
      // font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
      font-weight: 400;
      font-size: 40px;
      line-height: 52px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      background: linear-gradient(180deg, #0ec5ec 0%, #effcfe 69%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      // color: #0ec5ec; /* 起始颜色 */
      // font-size: 12px;
      // line-height: 22px;
      // letter-spacing: 1.2em;
    }
    .num {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 24px;
      line-height: 70px;
      color: #ffffff;
      text-align: center;
    }
    .time {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 24px;
      line-height: 50px;
      color: #ffffff;
      text-align: center;
    }
    .text-yellow {
      font-family: YouSheBiaoTiHei;
      font-weight: 600;
      font-size: 40px;
      vertical-align: bottom;
      background: linear-gradient(90deg, #ffffff 32%, #f7b23b 100%);
      background: linear-gradient(0deg, #ffffff 32%, #f7b23b 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .rate {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 24px;
      color: #ffffff;
      .rateValue {
        margin: 0 8px;
      }
      .text-red {
        color: #fd364f;
      }
      .text-green {
        color: #22e097;
      }
    }
    .icon {
      width: 18px;
      height: 20px;
    }
  }
}

.flex-c {
  display: flex;
  align-items: center;
}
.flex-b {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

::v-deep .el-dialog {
  margin-top: 9vh !important;
}

/* 分页 */
/deep/ .el-pagination button {
  height: 60px;
  width: 40px;
  background: #5f7b96 !important;
  // background: transparent !important;
}

/deep/ .el-pagination button .el-icon {
  font-size: 32px !important;
  // color: #c1e2fa;
  color: #c1e2fa !important;
  font-weight: 400;
}

/deep/ ul li {
  border: 2px solid transparent;
  margin-left: 10px !important;
  // height: 60px;
  height: fit-content;
  padding: 10px 10px !important;
  box-sizing: border-box;
  font-size: 32px !important;
  color: #c1e2fa !important;
  // background: transparent !important;
  background: #5f7b96 !important;
  font-weight: 500;
  line-height: 36px !important;
  border-radius: 4px;
}

/deep/ li.active {
  // margin: 0;
  // padding: 0;
  color: #fff !important;
  /* border: 2px solid #035b86; */
  background-color: #0166a6 !important;
}
/deep/ .el-pagination__total {
  color: #fff;
  font-size: 32px !important;
  padding-top: 15px;
  padding-right: 20px;
}

/deep/ .el-pagination button,
/deep/ .el-pagination span:not([class*='suffix']) {
  font-size: 32px;
  color: #fff;
}

/deep/ .el-pagination__editor.el-input .el-input__inner {
  height: 60px;
  font-size: 32px;
  background-color: transparent;
  color: #fff;
  border-color: #6f788a;
}

/deep/ .el-pagination__editor.el-input {
  width: 100px;
  height: 60px;
  margin: 0 10px;
}

//打印时候修改部分css
.bgxq-dialog2 {
  min-height: 2300px;
  height: fit-content;
  .titleText {
    background: unset;
    color: #fff;
    font-family: unset;
    font-size: 20px;
    letter-spacing: 36px;
  }
  .close {
    // width: 15px;
    // height: 15px;
    background-size: 100% 100%;
  }
  /deep/.hearder_h2 {
    padding-bottom: 60px !important;
    padding-left: 40px !important;
    & > span {
      background: unset !important;
      color: #fff !important;
      font-family: unset !important;
      font-size: 16px !important;
      letter-spacing: 36px !important;
      line-height: 20px !important;
    }
  }
  .table {
    min-height: 480px;
    height: unset;
    .tableHead,
    .tableLine {
      font-size: 14px;
      line-height: 42px;
      padding-bottom: 20px;
      letter-spacing: 1.2em;
    }
  }
  .item-wrap {
    .itemCon {
      height: 280px;
      .title {
        background: unset;
        color: #0ec5ec;
        font-size: 14px;
        letter-spacing: 1.2em;
        margin-bottom: 10px;
      }
      .num {
        background: unset;
        color: #fff;
        font-size: 10px;
        line-height: 60px;
        letter-spacing: 1.2em;
      }
      .text-yellow {
        background: unset;
        color: #f7b23b;
        font-size: 20px;
        line-height: 60px;
        font-family: unset;
        margin: -20px 20px 0 20px;
      }
      .time {
        font-size: 14px;
        line-height: 50px;
        letter-spacing: 1.2em;
        margin-bottom: 20px;
      }
      .rate {
        font-size: 10px !important;
        line-height: 20px !important;
        letter-spacing: 1em !important;
      }
    }
  }
}
.flex-c {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>