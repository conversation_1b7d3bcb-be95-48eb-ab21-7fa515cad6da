import {request} from '@/utils/request'
/** 巡查发现 */
// 列表
export function inspectionList(params) {
  return request({
    url: '/business/inspection/list',
    method: 'get',
    params
  })
}
export function inspectionOne(params) {
  return request({
    url: '/business/inspection/' + params,
    method: 'get'
  })
}
// 我的列表
export function myInspectionList(params) {
  return request({
    url: '/business/inspection/myCases',
    method: 'get',
    params
  })
}
// 修改
export function editInspection(data) {
  return request({
    url: '/business/inspection/edit',
    method: 'post',
    data
  })
}
// 删除
export function removeInspection(data) {
  return request({
    url: '/business/inspection/remove/' + data,
    method: 'post'
  })
}
