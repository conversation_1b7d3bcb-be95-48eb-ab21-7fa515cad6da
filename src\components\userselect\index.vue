<template>
  <el-dialog
    :close-on-click-modal="false"
    :title="title"
    :visible.sync="value"
    width="40%"
    :before-close="handleClose"
    append-to-body
    class="selectUserDialog"
    @open="handleOpen"
  >
    <div>
      <el-input
        v-model="keywords"
        size="medium"
        placeholder="请输入人员姓名"
        suffix-icon="el-icon-search"
        style="margin-bottom: 10px;"
        @change="(val) => this.$refs.tree.filter(val)"
      />
      <el-scrollbar wrap-class="scrollbar-wrapper" class="tree-box">
        <el-tree
          ref="tree"
          v-loading="loading"
          :data="data"
          node-key="id"
          show-checkbox
          :default-expanded-keys="expandedKeys"
          :check-on-click-node="!multiple || name == 'bumen' || name == 'anyou'"
          :check-strictly="!multiple || name == 'bumen' || name == 'anyou'"
          :default-checked-keys="selectKeys"
          :filter-node-method="filterNode"
          @check-change="checks"
        >
          <div slot-scope="{ node }" class="custom-tree-node">
            <div slot="reference">
              <span>{{ node.label }}</span>
              <span v-if="node.data.baseType">{{ node.data.position }}</span>
            </div>
          </div>
        </el-tree>
      </el-scrollbar>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { summaryList, userList, deptList} from '@/api/system/dict/type'
export default {
  name: 'Userselect',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    selectUserKeys: {
      type: Array,
      default: () => {
        return []
      }
    },
    disabledKeys: {
      type: Array,
      default: () => {
        return []
      }
    },
    defaultExpandedKeys: {
      type: Array,
      default: () => {
        return [100]
      }
    },
    name: {
      type: String,
      default: ''
    },
    multiple: {
      type: Boolean,
      default: true
    },
    type: {
      type: String,
      default: ''
    },
    typeList: {
      type: String,
      default: '0,1,2'
    },
    notDis: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      title: '选择人员',
      loading: false,
      data: [],
      checkedBaseType: 0,
      checkedAry: [],
      keywords: ''
    }
  },
  computed: {
    selectKeys() {
      return this.selectUserKeys.map(key => {
        if (this.name == 'anyou' || this.name == 'bumen') return key
        return key == 100 ? `${key}d` : `${key}u`
      })
    },
    expandedKeys() {
      return this.defaultExpandedKeys.map(key => {
        if (this.name == 'anyou' || this.name == 'bumen') return key
        return key == 100 ? `${key}d` : `${key}u`
      })
    }
  },
  methods: {
    treeMap(item) {
      const haveChildren = Array.isArray(item.children) && item.children.length > 0
      let tree = {
        id: `${item.id}${item.type}`,
        label: item.label,
        parentId: item.parentId,
        type: item.type,
        disabled: !!item.disabled
      }
      if (haveChildren) tree.children = item.children.map(i => this.treeMap(i))
      return tree
    },
    fetchData() {
      this.loading = true
      this.title = this.name == 'anyou' ? '选择案由' : this.name == 'bumen' ? '选择部门' : '选择人员'
      let api = this.name == 'anyou' ? summaryList() : this.name == 'bumen' ? deptList() : userList({ typeList: this.typeList })
      api.then(res => {
        if (!this.notDis) this.children(res.data)
        if (this.name != 'anyou' && this.name != 'bumen') {
          this.data = res.data.map(item => this.treeMap(item))
        } else {
          this.data = res.data
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
      })

    },
    children(data) {
      data.forEach(v => {
        v.id = v.id + ''
        if (this.name != 'anyou' && this.name != 'bumen' && this.multiple) {
          if (v.children) {
            this.children(v.children)
          } else {
            return data
          }
        } else {
          if (v.children) {
            v.disabled = true
            this.children(v.children)
          } else {
            if (v.type == 'd' || v.type == 'D') v.disabled = true
            return data
          }
        }
      })
    },
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    handleClose() {
      this.$emit('input', false)
      this.checkedBaseType = 0
      this.checkedAry = []
    },
    checks(data, checked) {
      if (checked && !this.multiple) this.$refs.tree.setCheckedKeys([data.id])
    },
    handleConfirm() {
      let userName = this.$refs.tree.getCheckedNodes()
      let parent = null
      // console.log(userName)
      // console.log(this.$refs.tree.getNode(userName[0].parentId).data)
      if (userName[0]) {
        parent = this.$refs.tree.getNode(`${userName[0].parentId}d`) ? this.$refs.tree.getNode(`${userName[0].parentId}d`).data : null
        // console.log(parent)
        if (parent) parent.id = parseInt(parent.id)
      }

      // console.log(parent)
      if (this.name != 'anyou' && this.name != 'bumen' && this.multiple) {
        userName = userName.filter(item => item.type == 'u')
      }
      let ids =  userName.reduce((preValue, curValue, i) => {
        if (i == userName.length - 1) {
          return preValue + parseInt(curValue.id)
        }
        return preValue + parseInt(curValue.id) + ','
      }, '')
      let names =  userName.reduce((preValue, curValue, i) => {
        if (i == userName.length - 1) {
          return preValue + curValue.label
        }
        return preValue + curValue.label + ','
      }, '')
      // const node = this.$refs.tree.getCheckedNodes().filter(item => item.type === 'USER')
      const allChecks = this.$refs.tree.getCheckedNodes(false, true).map(item => {
        item.id = parseInt(item.id)
        return item
      })
      this.$emit('confirm', { id: ids, name: names, type: this.type, allChecks, parent })
      this.handleClose()
    },
    handleOpen() {
      console.log(this.selectUserKeys)
      this.fetchData()
    },
    handleChange(data, {checkedNodes, checkedKeys}) {
      if (checkedKeys.length <= 1) return
      let notChecked = []
      if (data.baseType === 1 || data.baseType === 2) {
        notChecked = checkedKeys.filter(key => data.nodeKey !== key)
      } else if (data.baseType === 3 || data.baseType === 4) {
        checkedNodes.forEach(item => {
          if (item.baseType < 3 || item.pId !== data.pId) {
            notChecked.push(item.nodeKey)
          }
        })
      }
      notChecked.forEach(key => {
        this.$refs.tree.setChecked(key, false)
      })
    }
  }
}
</script>

<style>
.selectUserDialog .el-dialog__body {
  padding-top: 0;
}
.selectUserDialog .tree-box {
  height: 300px;
}
</style>
