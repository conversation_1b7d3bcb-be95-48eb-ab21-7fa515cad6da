import {request} from '@/utils/request'
// 新增任务
export default  {
  Add(data) {
    return request({
      url: '/business/union/add',
      method: 'post',
      data
    })
  },
  List(params) {
    return request({
      url: '/business/union/list',
      method: 'get',
      params
    })
  },
  OneList(params) {
    return request({
      url: '/business/union/' + params,
      method: 'get'

    })
  },
  Edit(data) {
    return request({
      url: '/business/union/edit',
      method: 'post',
      data
    })
  },
  Remove(data) {
    return request({
      url: '/business/union/remove/' + data,
      method: 'post'
    })
  },
  // 查询子任务
  detailList(params) {
    return request({
      url: '/business/union/detailList',
      method: 'get',
      params
    })
  }

}

