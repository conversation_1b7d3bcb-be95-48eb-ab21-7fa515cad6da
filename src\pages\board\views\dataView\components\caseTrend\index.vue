<template>
  <div ref="chart" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.8)" class="line" />
</template>

<script>
import { getCaseTrend } from '@/api/board/dataView/index'

export default {
  data() {
    return {
      loading: false,
      myChart: null,
      inspectionData: [],
      transportData: [],
      caseCaptureData: []
    }
  },
  computed: {
    options() {
      return {
        color: ['#FFA00D', '#00F7FF', '#42c14c'],
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          show: false,
          top: 10,
          left: 40,
          bottom: 20,
          right: 20

        },
        xAxis: {
          data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
          boundaryGap: false,
          axisLine: {
            show: false
          },
          axisLabel: {
            color: '#fff'
          },
          axisTick: {
            lineStyle: {
              color: '#fff'
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#6d6d6d'
            }
          }
        },
        yAxis: {
          type: 'value',
          offset: 15,
          axisLine: {
            show: false
          },
          axisLabel: {
            color: '#fff'
          },
          axisTick: {
            show: false,
            lineStyle: {
              color: '#fff'
            }
          },
          splitLine: {
            show: false
          }
        },
        series: [{
          name: '行政执法',
          data: this.inspectionData,
          type: 'line',
          lineStyle: {
            shadowColor: '#FFA00D',
            shadowBlur: 10
          }
        }, {
          name: '运管执法',
          data: this.transportData,
          type: 'line',
          lineStyle: {
            shadowColor: '#00F7FF',
            shadowBlur: 10
          }
        }, {
          name: '电子抓拍执法',
          data: this.caseCaptureData,
          type: 'line',
          lineStyle: {
            shadowColor: '#42c14c',
            shadowBlur: 10
          }
        }]
      }
    }
  },
  mounted() {
    this.initChart()
    window.addEventListener('resize', this.resizeChart)
    this.fetchData()
  },
  destroyed() {
    window.removeEventListener('resize', this.resizeChart)
  },
  methods: {
    fetchData() {
      this.loading = false
      getCaseTrend().then(res => {
        this.loading = false
        this.inspectionData = res.data.inspectionData
        this.transportData = res.data.transportData
        this.caseCaptureData = res.data.caseCaptureData
        this.myChart.setOption(this.options)
      }).catch(() => {
        this.loading = false
      })
    },
    initChart() {
      this.myChart = this.$echarts.init(this.$refs.chart)
      this.myChart.setOption(this.options)
    },
    resizeChart() {
      setTimeout(() => {
        this.myChart.resize()
      }, 500)
    }
  }
}
</script>

<style scoped>
.line {
  width: 100%;
  height: 100%;
}
</style>
