import {request} from '@/utils/request'

// 查询试卷配置列表
export function listConfig(query) {
  return request({
    url: '/business/exam/config/list',
    method: 'get',
    params: query
  })
}

// 查询试卷配置详细
export function getConfig(id) {
  return request({
    url: '/business/exam/config/' + id,
    method: 'get'
  })
}

// 新增试卷配置
export function addConfig(data) {
  return request({
    url: '/business/exam/config/add',
    method: 'post',
    data: data
  })
}

// 修改试卷配置
export function updateConfig(data) {
  return request({
    url: '/business/exam/config/edit',
    method: 'post',
    data: data
  })
}

// 删除试卷配置
export function delConfig(id) {
  return request({
    url: '/business/exam/config/remove/' + id,
    method: 'post'
  })
}

// 导出试卷配置
export function exportConfig(query) {
  return request({
    url: '/business/exam/config/export',
    method: 'get',
    params: query
  })
}
