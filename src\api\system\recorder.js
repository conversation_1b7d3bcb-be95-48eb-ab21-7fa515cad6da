import {request} from '@/utils/request'

// 查询执法记录仪列表
export function listRecorder(query) {
  return request({
    url: '/business/recorder/list',
    method: 'get',
    params: query
  })
}

// 查询执法记录仪详细
export function getRecorder(recorderId) {
  return request({
    url: '/business/recorder/' + recorderId,
    method: 'get'
  })
}

// 新增执法记录仪
export function addRecorder(data) {
  return request({
    url: '/business/recorder/add',
    method: 'post',
    data: data
  })
}

// 修改执法记录仪
export function updateRecorder(data) {
  return request({
    url: '/business/recorder/edit',
    method: 'post',
    data: data
  })
}

// 删除执法记录仪
export function delRecorder(recorderId) {
  return request({
    url: '/business/recorder/remove/' + recorderId,
    method: 'post'
  })
}

// 导出执法记录仪
export function exportRecorder(query) {
  return request({
    url: '/business/recorder/export',
    method: 'get',
    params: query
  })
}
