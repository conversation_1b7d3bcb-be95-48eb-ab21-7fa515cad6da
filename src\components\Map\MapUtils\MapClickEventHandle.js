class MapClickEventHandle {
  constructor(view) {
    this.view = view;
    this._init(view);
    this._callbackEvent = [];
    this._viewClickCallbackEvent = [];
    this._coordinateClientList = [];
  }

  _init(view) {
    const that = this;
    view.on("click", function (event) {
      // 修正点击坐标 这里太重要了 因为transform了 所以需要修正
      const scaleWidth1 = 1.0 / window.scaleWidth;
      const scaleHeight1 = 1.0 / window.scaleHeight;
      event.x = event.x * scaleWidth1;
      event.y = event.y * scaleHeight1;
      
      if (event.screenPoint) {
        event.screenPoint.x = event.screenPoint.x * scaleWidth1;
        event.screenPoint.y = event.screenPoint.y * scaleHeight1;
      }

      const { mapPoint } = event;
      for (let i = 0; i < that._coordinateClientList.length; i++) {
        const item = that._coordinateClientList[i];
        item(mapPoint);
      }
      
      view.hitTest(event).then(function (response) {
        let { results } = response;
        for (let i = 0; i < that._callbackEvent.length; i++) {
          const { layerId, callback } = that._callbackEvent[i];
          const clickGraphics = that._getClickGraphic(layerId, results);
          if (clickGraphics.length > 0) {
            callback(mapPoint, clickGraphics[0], clickGraphics);
          }
        }
      });
    });
  }

  _getClickGraphic(layerId, graphicHits) {
    let clickGraphic = [];
    for (let i = 0; i < graphicHits.length; i++) {
      const item = graphicHits[i];
      const { graphic } = item;
      if (graphic.layer.id === layerId) {
        clickGraphic.push(graphic);
      }
    }

    // 聚合图层，重新获取对应的graphics
    let clusterGraphics = [];
    for (let i = 0, len = clickGraphic.length; i < len; i++) {
      const { attributes } = clickGraphic[i];
      if (attributes?.clusterAttr) {
        const objectids = attributes?.clusterAttr.split(",");
        const curClusterGraphics = clickGraphic[i].layer.initClusterData;        ;
        const temGraphics=[];
        for(let j = 0; j < curClusterGraphics.length; j++) {
          if(objectids.includes(curClusterGraphics[j].attributes.id.toString())){
            temGraphics.push(curClusterGraphics[j])
          }
        }
        clusterGraphics=[...clusterGraphics,...temGraphics]
      }else{
        clusterGraphics.push( clickGraphic[i]);
      }
    }

    return clusterGraphics;
  }

  add(layerId, callback) {
    if (typeof callback !== "function") {
      throw new Error("第二个参数必须为函数");
    }
    this._callbackEvent.push({ layerId, callback });
  }

  // 地图点击订阅
  addCoordinateListener(callback) {
    this._coordinateClientList.push(callback);
  }
}

export default MapClickEventHandle;
