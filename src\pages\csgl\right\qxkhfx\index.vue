<template>
  <div>
    <CommonTitle text="区县考核分析">
      <TabSwitch :tabList="list" :activeIndex="index" @tab-change="handleTabChange" />
    </CommonTitle>
    <!-- <div class="yearChange">
      <el-date-picker
        v-model="datas"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="queryData"
        :append-to-body="false"
      ></el-date-picker>
    </div> -->
    <div class="chartqxkh" id="chartqxkh"></div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import TabSwitch from '@/components/TabSwitch'
import { getCountyAnalysis } from '@/api/csgl/index.js'
// import moment from 'moment'
export default {
  name: 'index',
  components: {
    CommonTitle,
    TabSwitch,
  },
  data() {
    return {
      index: 0,
      list: [
        { name: '本周', value: '2' },
        { name: '本月', value: '3' },
        { name: '本年', value: '4' },
      ],
      // datas: [new Date().getFullYear() + '-01-01', moment(new Date()).format('YYYY-MM-DD')],
      chartsData: [],
    }
  },
  computed: {},
  mounted() {
    this.getData()
  },
  methods: {
    queryData() {},
    handleTabChange(i) {
      this.index = i
      this.getData()
    },
    getData() {
      getCountyAnalysis({ type: this.list[this.index].value }).then((res) => {
        this.chartsData = res.data.map((item) => {
          return {
            name: item.key,
            value: item.value,
          }
        })
        this.initChart()
      })
    },
    initChart() {
      let myChart = this.$echarts.init(document.getElementById('chartqxkh'))
      let option = {
        tooltip: {
          trigger: 'axis',
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        legend: {
          orient: 'horizontal',
          // icon: "circle",
          padding: [30, 10, 10, 10],
          itemGap: 45,
          textStyle: {
            color: '#D6E7F9',
            fontSize: 28,
          },
        },
        grid: {
          left: '8%',
          right: '6%',
          top: '15%',
          bottom: '1%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            data: this.chartsData.map((item) => item.name),
            axisLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)', // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              rotate: 30,
              textStyle: {
                color: '#D6E7F9',
                fontSize: 28,
              },
            },
          },
        ],
        yAxis: [
          {
            name: '单位(分)',
            type: 'value',
            nameTextStyle: {
              fontSize: 24,
              color: '#D6E7F9',
              padding: 20,
            },
            splitLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)',
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 28,
                color: '#D6E7F9',
              },
            },
          },
        ],
        series: [
          {
            name: '',
            type: 'bar',
            barWidth: '24px',
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(0, 123, 255, 1)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(0, 123, 255, 0)',
                  },
                ]),
                barBorderRadius: 4,
              },
            },
            data: this.chartsData.map((item) => item.value),
          },
        ],
      }
      myChart.setOption(option)
      myChart.getZr().on('mousemove', (param) => {
        myChart.getZr().setCursorStyle('default')
      })
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
/deep/ .yearChange {
  margin: 20px 40px;
  .el-input__inner {
    height: 48px !important;
    background-color: #132c4e !important;
    border: 2px solid #afdcfb !important;
    color: #fff !important;
    border-radius: 15px !important;
  }
}
.chartqxkh {
  width: 100%;
  height: 580px;
  margin-bottom: 40px;
}
</style>