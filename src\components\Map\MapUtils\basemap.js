import Basemap from "@arcgis/core/Basemap.js";
import { layerCreate } from "./core.js";
import { getLayerConfigById } from "./layerConfig.js";

/**
 * 天地图市域底图  CGCS2000
 */
export function getTDTCGCS2000ImageLayer() {
  const lyr = layerCreate({
    type: "tile",
    url: "https://ps.geoscene.cn/tianditu/xmap/default/ima/c/default/MapServer",
  });

  const baseMap = new Basemap({
    baseLayers: [lyr],
    title: "天地图影像CGCS2000",
  });
  return baseMap;
}

/**
 * 天地图黑色底图  CGCS2000
 */
export function getTDTCGCS2000DarkLayer() {
  const lyr = layerCreate({
    type: "tile",
    url: "https://ps.geoscene.cn/tianditu/xmap/default/vea/c/dark/MapServer",
  });

  const baseMap = new Basemap({
    baseLayers: [lyr],
    title: "天地图影像CGCS2000Dark",
  });
  return baseMap;
}

/**
 * 矢量底图  
 */
export function getIRSLayer() {
  const baseLayersConfig = getLayerConfigById("VECTOR_BASEMAP");
  const baseLayers = [];
  for (let i = 0; i < baseLayersConfig.length; i++) {
    const itemLayer = layerCreate(baseLayersConfig[i]);
    baseLayers.push(itemLayer);
  }

  let baseMap = new Basemap({
    baseLayers: baseLayers,
  });
  return baseMap;
}
/**
 * 0.2影像  CGCS2000
 */
export async function getIRSlabelLayer(
  code = "d7b6220a34ea4b34b862ec8596372a04ba88f801",
  onError,
) {
  if (window.__checkCode !== code) {
    try {
      const { data } = await fetch(
        "https://sdi.zjzwfw.gov.cn/resources-server/auth/usage/zheZhengDing2Token.do?token=sy-6786f525-a9bd-477f-ab4a-f26d29524077&code=" +
        code
      ).then((res) => {
        return res.json();
      });
      console.log(JSON.stringify(data), "0.2影像授权回调信息");
      if (data.msg === "code失效或不存在") {
        onError(data.msg);
        return;
      }
    } catch (error) {
      onError(error);
      return;
    }
    window.__checkCode = code;
  }
  const baseLayersConfig = getLayerConfigById("IMAGE_BASEMAP2");
  const baseLayers = [];
  for (let i = 0; i < baseLayersConfig.length; i++) {
    if(baseLayersConfig[i].id==="baseMap2"){
      baseLayersConfig[i].urlTemplate+=code
    }
    const itemLayer = layerCreate(baseLayersConfig[i]);
    baseLayers.push(itemLayer);
  }
  let baseMap = new Basemap({
    baseLayers,
  });
  return baseMap;
}

/**
 *  矢量图
 */
export function getSzLightBasemap() {
  const baseLayersConfig = getLayerConfigById("VECTOR_BASEMAP");
  const baseLayers = [];
  for (let i = 0; i < baseLayersConfig.length; i++) {
    const itemLayer = layerCreate(baseLayersConfig[i]);
    baseLayers.push(itemLayer);
  }
  const baseMap = new Basemap({
    baseLayers: baseLayers,
    title: "底图",
  });
  return baseMap;
}
/**
 *  开发区
 */
export function getKaiFaQuBasemap() {
  const baseLayersConfig = getLayerConfigById("KAIFAQU_BASEMAP");
  const baseLayers = [];
  for (let i = 0; i < baseLayersConfig.length; i++) {
    const itemLayer = layerCreate(baseLayersConfig[i]);
    baseLayers.push(itemLayer);
  }
  const baseMap = new Basemap({
    baseLayers: baseLayers,
  });
  return baseMap;
}
/*
 *  自定义底图
*/

export function getCustomBasemp({ layerConfigs }) {
  let layers = [];
  if (Array.isArray(layerConfigs)) {
    for (let i = 0, len = layerConfigs.length; i < len; i++) {
      const item = layerConfigs[i];
      const layer = layerCreate(item);
      layers.push(layer);
    }
  } else {
    layers = layerCreate(layerConfigs);
  }
  const basemap = new Basemap({
    baseLayers: layers
  });
  return basemap;
}

/**
 * 2023 影像底图
 */
export function get2023ImageLayer() {
  const baseLayersConfig = getLayerConfigById("IMAGE_2023");
  const baseLayers = [];
  for (let i = 0; i < baseLayersConfig.length; i++) {
    const itemLayer = layerCreate(baseLayersConfig[i]);
    baseLayers.push(itemLayer);
  }

  let baseMap = new Basemap({
    baseLayers: baseLayers,
  });
  return baseMap;
}