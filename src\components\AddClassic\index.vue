<template>
  <div style="display: inline-block; margin-right: 10px;">
    <el-button type="primary" @click="handleOpen">转为典型案例</el-button>
    <div>
      <el-dialog class="m-dialog" :close-on-click-modal="false" title="添加经典案例" :visible.sync="open" append-to-body>
        <el-scrollbar style="height: 100%;">
          <el-row :gutter="15" style="margin-right: 10px;">
            <el-form ref="form" v-loading="loading" :model="form" :rules="rules" label-width="100px">
              <el-col :span="12">
                <el-form-item label="案例标题" prop="title">
                  <el-input v-model="form.title" placeholder="请输入经典案例标题" />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="类型" prop="type">
                  <el-select v-model="form.type" placeholder="请选择类型" :style="{ width: '100%' }">
                    <el-option v-for="item in classicTypeList" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="案例详情">
                  <editor v-model="form.content" :min-height="192" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="备注" prop="remark">
                  <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
                </el-form-item>
              </el-col>
            </el-form>
          </el-row>
        </el-scrollbar>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import editor from '@/components/Editor/index.vue'
import { addCase } from '@/api/application/case/classicCase'

export default {
  name: 'NewClassic',
  components: {
    editor
  },
  props: {
    beforeProcessing: {
      type: String,
      default: ''
    },
    afterProcessing: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      form: {},
      rules: {
        title: [{ required: true, message: '案例标题不能为空', trigger: 'blur' }],
        content: [{ required: true, message: '案例详情不能为空', trigger: 'blur' }]
      },
      open: false,
      classicTypeList: [],
      loading: false
    }
  },
  created() {
    this.getClassicType()
  },
  methods: {
    handleOpen() {
      this.open = true
      this.form.content = `
        <p><strong>【案件简介】</strong></p>
        <p>${this.beforeProcessing}</p>
        <p><br/><p>
        <p><strong>【处罚结果】</strong><p>
        <p>${this.afterProcessing}</p>
      `
    },
    getClassicType() {
      this.getDicts('classic_type').then(res => {
        this.classicTypeList = res.data
      })
    },
    reset() {
      this.form = {
        id: null,
        title: null,
        content: null,
        type: null,
        userId: null,
        deptId: null,
        deptName: null,
        status: '0',
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      }
      this.resetForm('form')
    },
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.loading = true
          addCase(this.form).then(() => {
            this.loading = false
            this.msgSuccess('新增成功')
            this.open = false
          }).catch(() => {
            this.loading = false
          })
        }
      })
    },
    cancel() {
      this.open = false
      this.reset()
    }
  }
}
</script>
