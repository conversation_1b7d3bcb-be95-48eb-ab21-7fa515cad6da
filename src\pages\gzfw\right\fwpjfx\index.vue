<template>
  <div>
    <CommonTitle text="服务评价分析"></CommonTitle>
    <div class="table-container">
      <TableComponent :thConfig="thConfig" :tableData="tableData" :tableHeight="460"></TableComponent>
    </div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import TableComponent from '@/components/TableComponent'
import { getAvgAnalysis } from '@/api/gzfw/index.js'
export default {
  name: 'index',
  components: {
    CommonTitle,
    TableComponent,
  },
  data() {
    return {
      //表格
      tableData: [],
      thConfig: [
        {
          th: '序号',
          field: 'volId',
          width: '15%',
          hover: false,
        },
        {
          th: '志愿者姓名',
          field: 'volName',
          width: '25%',
          hover: true,
        },
        {
          th: '评价数',
          field: 'evaluateCount',
          width: '20%',
          hover: true,
        },
        {
          th: '满意数',
          field: 'satisfyCount',
          width: '20%',
          hover: false,
        },
        {
          th: '满意率',
          field: 'satisfyPercent',
          width: '20%',
          hover: false,
        },
      ],
    }
  },
  computed: {},
  mounted() {
    getAvgAnalysis().then((res) => {
      this.tableData = res.data
    })
  },
  methods: {},
  watch: {},
}
</script>

<style scoped lang='less'>
.table-container {
  width: 100%;
  // // height:600px;
  padding: 20px 20px 30px 20px;
  box-sizing: border-box;
}
</style>