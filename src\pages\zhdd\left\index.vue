<template>
  <div class='left'>
    <zhtx></zhtx>
    <zfsb></zfsb>
    <zfll></zfll>
  </div>
</template>

<script>
import zhtx from './zhtx'
import zfsb from './zfsb'
import zfll from './zfll'
export default {
  name: 'index',
  components: {
    zhtx,
    zfsb,
    zfll
  },
  data() {
    return {}
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped>

</style>