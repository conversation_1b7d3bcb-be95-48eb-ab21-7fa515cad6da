import GraphicsLayer from "@arcgis/core/layers/GraphicsLayer.js";
import Graphic from "@arcgis/core/Graphic.js";
import Point from "@arcgis/core/geometry/Point.js";
import Polyline from "@arcgis/core/geometry/Polyline.js";
import Polygon from "@arcgis/core/geometry/Polygon.js";

class DrawGeometry {
  constructor(view) {
    this.view = view;
    this.drawLayer = null;
    this.points = [];
    this.tempGraphic = null;
    this.cursorGraphic = null;
    this.clickHandler = null;
    this.moveHandler = null;
    this.dbClickHandler = null;
    this.drawType = null;
    this.rightClickHandler = null;
    this.originalCursor = null;
    this.startPoint = null;
  }

  // 初始化绘制图层
  initDrawLayer() {
    if (!this.drawLayer) {
      this.drawLayer = new GraphicsLayer({
        id: "drawGeometryLayer"
      });
      this.view.map.add(this.drawLayer);
    }
  }

  // 开始绘制
  startDraw(type) {
    return new Promise((resolve) => {
      this.drawType = type;
      this.initDrawLayer();
      this.points = [];
      this.startPoint = null;
      
      // 保存并设置鼠标样式
      this.originalCursor = this.view.container.style.cursor;
      this.view.container.style.cursor = 'crosshair';
      console.log(this)
      // 点击事件
      this.clickHandler = this.view.on("click", (event) => {
        let that = this
        console.log(this)
        if (event.button === 0) {
          const scaleWidth = 1.0 / window.scaleWidth;
          const scaleHeight = 1.0 / window.scaleHeight;
          event.x = event.x * scaleWidth;
          event.y = event.y * scaleHeight;
          
          if (event.screenPoint) {
            event.screenPoint.x = event.screenPoint.x * scaleWidth;
            event.screenPoint.y = event.screenPoint.y * scaleHeight;
          }

            // 获取地图坐标
            const point = that.view.toMap({
                x: event.x,
                y: event.y
            });
            console.log("fffff",point)
          
          // 对于圆和矩形，第一次点击记录起始点，第二次点击完成绘制
          if (["circle", "rectangle"].includes(that.drawType)) {
            console.log("this.startPoint",that.startPoint)
            if (!that.startPoint) {
              that.startPoint = [point.x, point.y || 0];
            } else {
              that.points = [point.x, point.y || 0];
              that.completeDrawing(resolve);
            }
          } else {
            that.points.push([point.x, point.y || 0]);
            that.drawGeometry();
          }
        }
      });

      // 右击事件
      this.rightClickHandler = this.view.on("click", (event) => {
        // 处理右键点击
        if (event.button === 2) {
          event.preventDefault(); // 阻止默认右键菜单
          this.completeDrawing(resolve);
        }
      });

      // 移动事件
      this.moveHandler = this.view.on("pointer-move", (event) => {
        const scaleWidth = 1.0 / window.scaleWidth;
        const scaleHeight = 1.0 / window.scaleHeight;
        event.x = event.x * scaleWidth;
        event.y = event.y * scaleHeight;

        if (event.screenPoint) {
          event.screenPoint.x = event.screenPoint.x * scaleWidth;
          event.screenPoint.y = event.screenPoint.y * scaleHeight;
        }

        const point = this.view.toMap({
          x: event.x,
          y: event.y
        });

        if (point) {
          this.updateCursorGraphic([point.x, point.y, this.points[0]?.[2] || 0]);
          
          if (this.startPoint && ["circle", "rectangle"].includes(this.drawType)) {
            this.drawTempShape(point);
          } else if (this.points.length > 0) {
            this.drawTempGeometry([point.x, point.y, this.points[0]?.[2] || 0]);
          }
        }
      });

      // 双击完成绘制
      this.dbClickHandler = this.view.on("double-click", (event) => {
        event.preventDefault(); // 阻止双击缩放
        const scaleWidth = 1.0 / window.scaleWidth;
        const scaleHeight = 1.0 / window.scaleHeight;
        event.x = event.x * scaleWidth;
        event.y = event.y * scaleHeight;

        if (event.screenPoint) {
          event.screenPoint.x = event.screenPoint.x * scaleWidth;
          event.screenPoint.y = event.screenPoint.y * scaleHeight;
        }

        // 如果是双击左键
        if (event.button === 0) {
          this.completeDrawing(resolve);
        }
      });

      // 添加右键菜单阻止
      this.view.container.addEventListener('contextmenu', (e) => {
        e.preventDefault();
      });
    });
  }

  // 绘制几何图形
  drawGeometry() {
    this.drawLayer.removeAll();
    let geometry = null;
    let symbol = null;

    switch (this.drawType) {
      case "point":
        geometry = new Point({
          x: this.points[0][0],
          y: this.points[0][1],
          spatialReference: this.view.spatialReference
        });
        symbol = {
          type: "simple-marker",
          color: [255, 0, 0],
          size: 8
        };
        break;

      case "polyline":
        geometry = new Polyline({
          paths: [this.points],
          spatialReference: this.view.spatialReference
        });
        symbol = {
          type: "simple-line",
          color: [255, 0, 0],
          width: 2
        };
        break;

      case "polygon":
        geometry = new Polygon({
          rings: [this.points],
          spatialReference: this.view.spatialReference
        });
        symbol = {
          type: "simple-fill",
          color: [255, 0, 0, 0.3],
          outline: {
            color: [255, 0, 0],
            width: 2
          }
        };
        break;
    }

    if (geometry) {
      const graphic = new Graphic({
        geometry,
        symbol
      });
      this.drawLayer.add(graphic);
    }
  }

  // 添加鼠标悬浮点
  updateCursorGraphic(point) {
    if (this.cursorGraphic) {
      this.drawLayer.remove(this.cursorGraphic);
    }

    const geometry = {
      type: "point",
      x: point[0],
      y: point[1],
      z: point[2],
      spatialReference: this.view.spatialReference
    };

    const symbol = {
      type: "simple-marker",
      style: "circle",
      color: [255, 0, 0, 0.8],
      size: 8,
      outline: {
        color: [255, 255, 255],
        width: 1
      }
    };

    this.cursorGraphic = new Graphic({
      geometry,
      symbol
    });

    this.drawLayer.add(this.cursorGraphic);
  }

  // 绘制临时图形
  drawTempGeometry(point) {
    if (this.tempGraphic) {
      this.drawLayer.remove(this.tempGraphic);
    }

    let geometry = null;
    let symbol = null;
    const tempPoints = [...this.points, point];

    switch (this.drawType) {
      case "polyline":
        geometry = {
          type: "polyline",
          paths: [tempPoints],
          spatialReference: this.view.spatialReference
        };
        symbol = {
          type: "simple-line",
          color: [255, 0, 0],
          width: 2,
          style: "solid"
        };
        break;

      case "polygon":
        // 如果是多边形，自动闭合
        const closedPoints = [...tempPoints];
        if (closedPoints.length > 2) {
          closedPoints.push(closedPoints[0]);
        }
        
        geometry = {
          type: "polygon",
          rings: [closedPoints],
          spatialReference: this.view.spatialReference
        };
        symbol = {
          type: "simple-fill",
          color: [255, 0, 0, 0.3],
          outline: {
            color: [255, 0, 0],
            width: 2,
            style: "solid"
          }
        };
        break;
    }

    if (geometry) {
      this.tempGraphic = new Graphic({
        geometry,
        symbol
      });
      this.drawLayer.add(this.tempGraphic);
    }
  }

  // 添加绘制临时形状方法
  drawTempShape(point) {
    if (this.tempGraphic) {
      this.drawLayer.remove(this.tempGraphic);
    }

    const [startX, startY] = this.startPoint;
    const endX = point.x;
    const endY = point.y;

    let geometry, symbol;

    if (this.drawType === "rectangle") {
      // 创建矩形的四个角点
      const rings = [
        [startX, startY],
        [endX, startY],
        [endX, endY],
        [startX, endY],
        [startX, startY]
      ];

      geometry = {
        type: "polygon",
        rings: [rings],
        spatialReference: this.view.spatialReference
      };

      symbol = {
        type: "simple-fill",
        color: [255, 0, 0, 0.3],
        outline: {
          color: [255, 0, 0],
          width: 2
        }
      };
    } else if (this.drawType === "circle") {
      // 计算圆的半径
      const radius = Math.sqrt(
        Math.pow(endX - startX, 2) + Math.pow(endY - startY, 2)
      );

      // 创建圆形的点集
      const rings = [];
      const numberOfPoints = 72; // 每5度一个点
      for (let i = 0; i <= numberOfPoints; i++) {
        const angle = (Math.PI * 2 * i) / numberOfPoints;
        const x = startX + radius * Math.cos(angle);
        const y = startY + radius * Math.sin(angle);
        rings.push([x, y]);
      }

      geometry = {
        type: "polygon",
        rings: [rings],
        spatialReference: this.view.spatialReference
      };

      symbol = {
        type: "simple-fill",
        color: [255, 0, 0, 0.3],
        outline: {
          color: [255, 0, 0],
          width: 2
        }
      };
    }

    this.tempGraphic = new Graphic({
      geometry,
      symbol
    });
    this.drawLayer.add(this.tempGraphic);
  }

  // 完成绘制
  completeDrawing(resolve) {
    // 恢复原始鼠标样式
    if (this.originalCursor !== null) {
      this.view.container.style.cursor = this.originalCursor;
      this.originalCursor = null;
    }

    // 移除所有事件监听
    if (this.clickHandler) {
      this.clickHandler.remove();
      this.clickHandler = null;
    }
    if (this.moveHandler) {
      this.moveHandler.remove();
      this.moveHandler = null;
    }
    if (this.dbClickHandler) {
      this.dbClickHandler.remove();
      this.dbClickHandler = null;
    }
    if (this.rightClickHandler) {
      this.rightClickHandler.remove();
      this.rightClickHandler = null;
    }
    
    // 清除临时图形和鼠标悬浮点
    if (this.tempGraphic) {
      this.drawLayer.remove(this.tempGraphic);
      this.tempGraphic = null;
    }
    if (this.cursorGraphic) {
      this.drawLayer.remove(this.cursorGraphic);
      this.cursorGraphic = null;
    }

    // 如果没有点，直接返回
    if (this.points.length === 0) {
      resolve(null);
      return;
    }

    let graphic = null;
    switch (this.drawType) {
      case "point":
        graphic = new Graphic({
          geometry: new Point({
            x: this.points[0][0],
            y: this.points[0][1],
            spatialReference: this.view.spatialReference
          }),
          symbol: {
            type: "simple-marker",
            color: [255, 0, 0],
            size: 8,
            outline: {
              color: [255, 255, 255],
              width: 1
            }
          }
        });
        break;

      case "polyline":
        graphic = new Graphic({
          geometry: new Polyline({
            paths: [this.points],
            spatialReference: this.view.spatialReference
          }),
          symbol: {
            type: "simple-line",
            color: [255, 0, 0],
            width: 2
          }
        });
        break;

      case "polygon":
        // 确保多边形至少有3个点
        if (this.points.length >= 3) {
          const rings = [...this.points];
          // 确保多边形闭合
          if (rings[0][0] !== rings[rings.length - 1][0] || 
              rings[0][1] !== rings[rings.length - 1][1]) {
            rings.push(rings[0]);
          }
          graphic = new Graphic({
            geometry: new Polygon({
              rings: [rings],
              spatialReference: this.view.spatialReference
            }),
            symbol: {
              type: "simple-fill",
              color: [255, 0, 0, 0.2],
              outline: {
                color: [255, 0, 0],
                width: 2
              }
            }
          });
        }
        break;

      case "rectangle":
        if (this.startPoint) {
          const currentPoint = [this.points[this.points.length - 1][0], this.points[this.points.length - 1][1]];
          const [startX, startY] = this.startPoint;
          const endX = currentPoint[0];
          const endY = currentPoint[1];

          const rings = [
            [startX, startY],
            [endX, startY],
            [endX, endY],
            [startX, endY],
            [startX, startY]
          ];

          graphic = new Graphic({
            geometry: new Polygon({
              rings: [rings],
              spatialReference: this.view.spatialReference
            }),
            symbol: {
              type: "simple-fill",
              color: [255, 0, 0, 0.2],
              outline: {
                color: [255, 0, 0],
                width: 2
              }
            }
          });
        }
        break;

      case "circle":
        if (this.startPoint) {
          const currentPoint = [this.points[0], this.points[1]];
          const [centerX, centerY] = this.startPoint;
          
          // 计算半径
          const radius = Math.sqrt(
            Math.pow(currentPoint[0] - centerX, 2) + Math.pow(currentPoint[1] - centerY, 2)
          );

          // 创建圆形的点集
          const rings = [];
          const numberOfPoints = 72; // 每5度一个点
          for (let i = 0; i <= numberOfPoints; i++) {
            const angle = (Math.PI * 2 * i) / numberOfPoints;
            const x = centerX + radius * Math.cos(angle);
            const y = centerY + radius * Math.sin(angle);
            rings.push([x, y]);
          }

          graphic = new Graphic({
            geometry: new Polygon({
              rings: [rings],
              spatialReference: this.view.spatialReference
            }),
            symbol: {
              type: "simple-fill",
              color: [255, 0, 0, 0.2],
              outline: {
                color: [255, 0, 0],
                width: 2
              }
            }
          });
          this.drawLayer.add(graphic);
        }
        break;
    }

    resolve(graphic);
  }

  // 清除绘制
  clear() {
    // 恢复原始鼠标样式
    if (this.originalCursor !== null) {
      this.view.container.style.cursor = this.originalCursor;
      this.originalCursor = null;
    }

    if (this.drawLayer) {
      this.drawLayer.removeAll();
    }
    this.points = [];
    this.tempGraphic = null;
    this.cursorGraphic = null;
  }
}

export default DrawGeometry;