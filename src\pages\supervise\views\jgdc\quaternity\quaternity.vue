<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      :inline="true"
    >
      <el-form-item label="数据源">
        <el-dropdown trigger="click" @command="handleCommand">
          <span class="el-dropdown-link">
            <el-input
              v-model="queryParams.dataName"
              placeholder="请选择"
              readonly="readonly"
              clearable
              size="small"
              style="width: 100px; margin: 0 5px;"
              @keyup.enter.native="handleQuery"
            />
          </span>
          <el-dropdown-menu slot="dropdown" style="width: 100px;">
            <el-dropdown-item v-for="(v) in formData" :key="v.id" :command="{...v,type:'user'}">{{ v.value }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form-item>
      <el-form-item label="案件类型" prop="typeName">
        <el-select v-model="form.typeName" style="width: 150px;" placeholder="请选择案件类型" @change="selectOne">
          <el-option
            v-for="item in typeData"
            :key="item.id"
            :label="item.value"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px;"
          value-format="yyyy-MM-dd"
          type="daterange"
          unlink-panels
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item>
        <!-- label="创建时间"  -->
        <el-input v-model="queryParams.searchValue" size="small" style="width: 150px; margin: 0 5px;" placeholder="请输入内容" />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">
          重置
        </el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:role:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >
          新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:role:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>
    <el-table
      v-loading="loading"
      :data="dataList"
      @selection-change="handleSelectionChange"
    >
      <!-- :cell-style="cellStyle" -->
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="类型" align="center" prop="type" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.type | types(typeData) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="上报时间" align="center" prop="happenDate" width="200">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.happenDate) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="上报人" align="center" prop="userName" width="100" show-overflow-tooltip />
      <el-table-column label="联系电话" align="center" prop="phone" width="150" show-overflow-tooltip />
      <el-table-column label="详细地址" align="center" prop="address" show-overflow-tooltip />
      <el-table-column label="内容" align="center" prop="content" show-overflow-tooltip />
      <el-table-column label="状态" align="center" prop="status" show-overflow-tooltip>
        <template slot-scope="scope">
          <span :style="{ color: circleColor[scope.row.status] }">{{ scope.row.status | statusName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button v-if="scope.row.status != 1 || queryParams.data == 1" v-hasPermi="['system:role:edit']" size="mini" type="text" icon="el-icon-edit" style="color: #e6a23c;" @click="handleUpdate(scope.row)">详情</el-button>
          <el-button v-else size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button v-if="queryParams.data != 1" size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 详情弹窗 -->
    <from-list :visible.sync="open" :title="title" :hand-disabled="handDisabled" :type-data="typeData" :detail-id="detailId" :form-disabled="formDisabled" @reLoad="parQuery" />
  </div>
</template>

<script>
import {policeList, removePolice, policeAll} from '@/api/supervise/swit'
import fromList from '@/pages/supervise/views/jgdc/quaternity/components/fromlist'
export default {
  components: {
    fromList
  },
  filters: {
    types(type, data) {
      let obj = data.find(v => v.id == type)
      return obj.value
    },
    statusName(status) {
      const statusObj = { 0: '已驳回', 1: '已下派', 2: '处理完成', 3: '确认完成' }
      return statusObj[status]
    }
  },
  data() {
    return {
      formData: [{id: 0, value: '我的'}, {id: 1, value: '全部'}],
      typeData: [{id: 1, value: '绿化管养'}, {id: 2, value: '环卫保洁'}, {id: 3, value: '市政设施'}],
      formDisabled: false,
      handDisabled: false,
      detailId: 0,
      title: '',
      open: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      dataList: [],
      circleColor: {
        0: '#EC5656',
        1: '#FAB71C',
        2: '#25c548',
        3: '#bdc3bf'
      },
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        searchValue: '',
        type: '',
        typeName: '',
        data: 0,
        dataName: '我的',
        dateRange: []
      },
      form: {}
    }
  },

  mounted() {
    this.getList()
  },
  methods: {
    selectOne(e) {    // 选择案件类型
      let data = this.typeData.filter(item => { return item.id == e })
      if (data) this.queryParams = {...this.queryParams, type: data[0].id, typeName: data[0].value}
      this.handleQuery()
    },
    /** 查询列表 */
    getList() {
      this.loading = true
      let { pageNum, pageSize, searchValue, type} = this.queryParams
      let params = { pageNum, pageSize }
      if (searchValue) params.searchValue = searchValue
      if (type) params.type = type
      let api = this.queryParams.data == 0 ? policeList(params) : policeAll(params)
      api.then(res => {
        this.dataList = res.rows
        this.total = res.total
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    // 类型选择
    handleCommand(command) {
      if (command.type == 'user') {
        this.queryParams = {...this.queryParams, data: command.id, dataName: command.value}
        this.handleQuery()
      } else {
        this.queryParams = {...this.queryParams, type: command.dictSort, typeName: command.dictLabel}
      }
    },
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.queryParams.pageNum = 1
      this.getList()
    },
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.getList()
    },
    parQuery() {
      this.getList()
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.form.typeName = ''
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        searchValue: '',
        type: '',
        typeName: '',
        data: 0,
        dataName: '我的',
        dateRange: []
      }
      // this.queryParams = { pageNum: 1, pageSize: 10, searchValue: ''}
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.fourId)
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.open = true
      this.title = '新增信息'
      this.detailId = 0
      this.formDisabled = false
      this.handDisabled = false
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.open = true
      this.title = '修改信息'
      this.detailId = row.fourId
      row.status == 2 && this.queryParams.data != 1 ? this.handDisabled = true : this.handDisabled = false
      if (row.status != 1 || this.queryParams.data == 1) return this.formDisabled = true
      this.formDisabled = false
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const transportIds = row.fourId || this.ids
      if (Array.isArray(transportIds) && !transportIds.length) {
        this.$message.warning('请选择需要删除的数据')
        return
      }
      this.$confirm('是否确认删除?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        removePolice(transportIds).then(() => {
          this.getList()
          this.msgSuccess('删除成功')
        })
      }).catch(() => {})
    }
  }
}
</script>
