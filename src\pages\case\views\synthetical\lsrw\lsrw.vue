<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      :inline="true"
    >
      <el-form-item label="指派时间">
        <el-date-picker
          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px;"
          value-format="yyyy-MM-dd"
          type="daterange"
          unlink-panels
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item label="关键词搜索">
        <el-input v-model="queryParams.searchValue" size="small" style="width: 200px; margin: 0 5px;" placeholder="请输入内容" />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:role:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >
          新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:role:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="listData"
      :cell-style="cellStyle"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="负责人" prop="userName" align="center" :show-overflow-tooltip="true" width="80" />
      <el-table-column label="指派时间" align="center" prop="assignDate" :show-overflow-tooltip="true" width="170">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.assignDate) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="分配店铺" align="center" prop="assignShopNames" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ scope.row.assignShopNames }}</span>
        </template>
      </el-table-column>
      <el-table-column label="分配部门名称" prop="assignDeptNames" align="center" :show-overflow-tooltip="true" />
      <el-table-column label="案件内容" align="center" prop="content" :show-overflow-tooltip="true" />
      <el-table-column label="状态" align="center" prop="status" :show-overflow-tooltip="true" width="90">
        <template slot-scope="scope">
          <span :style="{ color: circleColor[scope.row.status] }">{{ scope.row.status | statusData }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120">
        <template slot-scope="scope">
          <el-button v-if="scope.row.status == 9 || queryParams.data == 1" v-hasPermi="['system:role:edit']" size="mini" type="text" icon="el-icon-edit" style="color: #e6a23c;" @click="handleHis(scope.row)">详情</el-button>
          <el-button v-else v-hasPermi="['system:role:edit']" size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button v-hasPermi="['system:role:remove']" size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改角色配置对话框 -->
    <fromlist ref="fromlist" :visible.sync="open" :form-disabled="formDisabled" :title="title" :detail-id="detailId" @onPrimary="primary" @reLoad="handleQuery" />
  </div>
</template>

<script>
import {lsrwList, lsrwRemove} from '@/api/case/synthetical/lsrw'
import fromlist from '@/pages/case/views/synthetical/lsrw/components/fromlist.vue'
// import shop from '@/api/case/synthetical/shop'

export default {
  name: 'Capture',
  filters: {
    statusData(type) {
      let data = { 2: '处理中', 8: '待审核', 9: '已办结' }
      if (type) return data[type]
    },
    handCheckbox(type, shopList) {
      if (!type) return ''
      let arr = type.split(',')
      let arr2 = []
      shopList.forEach(v => {
        arr.forEach(v2 => {
          if (v.shopId == v2) arr2.push(v.shopName)
        })
      })
      return arr2.join(',')
    }

  },
  components: {
    fromlist
  },
  data() {
    return {
      $map: null,
      value: [],
      userOptions: [],
      circleColor: {8: '#409EFF', 2: '#FAB71C', 9: '#bdc3bf'},
      formData: [{id: 0, value: '我的'}, {id: 1, value: '全部'}],
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      shopList: [],
      // 表格数据
      roleList: [],
      // 弹出层标题
      title: '新增案件',
      // 是否显示弹出层
      open: false,
      detailId: 0,
      showSearch: true,

      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        // 日期范围
        dateRange: []
      },
      formDisabled: false,
      // 表单参数
      form: {},
      qd: true,
      violationData: [{id: 1, value: '日常劝说'}, {id: 2, value: '非法上下客'} ]
    }
  },
  computed: {
    listData() {
      let {pageNum, pageSize} = this.queryParams
      let arr = this.roleList.slice((pageNum - 1) * pageSize, (pageNum - 1) * pageSize + pageSize)
      return arr
    }
  },
  async created() {
    await this.getList()
    /* shop.list().then(res => {
      this.shopList = res.rows
    }) */

  },
  methods: {
    // 类型选择
    handleCommand(command) {
      this.queryParams = {...this.queryParams, type: command.id, typeName: command.value}
      this.handleQuery()
    },
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.queryParams.pageNum = 1
    },
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
    },
    // 状态颜色
    cellStyle(row) {
      if (row.column.label == '状态') return `color: ${row.row.statusColor}`
    },
    // 已下为模板
    /** 查询列表 */
    getList() {
      this.loading = true
      let { pageSize, pageNum, dateRange, searchValue, caseType} = this.queryParams
      let params = {pageNum, pageSize}
      if (Array.isArray(dateRange) && !dateRange.length == 0) { params = {...params, searchStartTime: dateRange[0], searchEndTime: dateRange[1] } }
      if (searchValue) params.searchValue = searchValue
      if (caseType) params.caseType = caseType
      let api =  lsrwList({...params})
      api.then(res => {
        this.roleList = res.rows
        this.total = res.total
        this.loading = false
      })
    },
    // 确定按钮
    primary() {
      this.open = false
      this.getList()
    },
    // 表单重置
    reset() {},
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {dateRange: [], pageNum: 1, pageSize: 10,  searchValue: ''}
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.temporaryId)
      this.single = selection.length != 1
      this.multiple = !selection.length
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.detailId = 0
      this.open = true
      this.title = '新增任务'
      this.formDisabled = false
    },
    /** 详情 */
    handleHis(row) {
      this.open = true
      this.formDisabled = true
      this.detailId = row.temporaryId
      this.title = '任务详情'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.open = true
      this.formDisabled = false
      this.detailId = row.temporaryId
      this.title = '修改信息'
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const fourIds = row.temporaryId || this.ids
      this.$confirm('是否确认删除?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // return delRole(roleIds)
          this.roleList = this.roleList.filter(
            irem => row.temporaryId != irem.temporaryId
          )
          this.total--
        })
        .then(() => {
          lsrwRemove(fourIds).then(() => {
            this.getList()
            this.msgSuccess('删除成功')
          })
        })
    }
  }
}
</script>
