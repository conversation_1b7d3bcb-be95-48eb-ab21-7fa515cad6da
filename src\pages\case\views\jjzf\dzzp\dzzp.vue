<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      :inline="true"
    >
      <el-form-item label="案件状态">
        <el-dropdown trigger="click" @command="handleCommand">
          <span class="el-dropdown-link">
            <el-input
              v-model="queryParams.statusName"
              placeholder="请选择状态"
              readonly="readonly"
              clearable
              size="small"
              style="width: 100px; margin: 0 5px;"
            />
          </span>
          <el-dropdown-menu slot="dropdown" style="width: 150px;">
            <el-dropdown-item v-for="(v) in caseData" :key="v.id" :command="{...v,type:'case'}">{{ v.value }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form-item>
      <el-form-item label="违法时间">
        <el-date-picker
          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px;"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          unlink-panels
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item>
        <!-- label="创建时间"  -->
        <el-input v-model="queryParams.searchValue" size="small" style="width: 240px; margin: 0 5px;" placeholder="请输入内容" />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">
          重置
        </el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:role:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >
          新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:role:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>
    <el-table
      v-loading="loading"
      :data="dataList"
      @selection-change="handleSelectionChange"
    >
      <!-- :cell-style="cellStyle" -->
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="抄告单号" align="center" prop="noticeNo" width="150" show-overflow-tooltip /> -->
      <!-- <el-table-column label="执勤民警" align="center" prop="policeman" width="100" /> -->
      <el-table-column label="上报人" align="center" prop="userName" width="100" />
      <el-table-column label="号牌号码" prop="carNo" align="center" width="150" show-overflow-tooltip />
      <el-table-column label="违法时间" align="center" prop="happenTime" width="150">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.happenTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="地址描述" align="center" prop="caseAddress" show-overflow-tooltip />
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <span :style="{ color: circleColor[scope.row.status] }">{{ scope.row.status | statusName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button v-if="scope.row.status != 9" size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button v-else size="mini" type="text" icon="el-icon-edit" style="color: #e6a23c;" @click="handleQueryData(scope.row)">详情</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 详情弹窗 -->
    <from-list :visible.sync="open" :title="title" :detail-id="detailId" :form-disabled="formDisabled" @reLoad="handleQuery" />
  </div>
</template>

<script>
import dzzp from '@/api/case/dzzp'
import fromList from '@/pages/case/views/jjzf/dzzp/components/fromList'
export default {
  components: {
    fromList
  },
  filters: {
    statusName(status) {
      const statusObj = { 2: '处理中', 9: '已办结' }
      return statusObj[status]
    }
  },
  data() {
    return {
      caseData: [{id: 2, value: '处理中', statusColor: '#E6A23C'}, {id: 9, value: '已办结', statusColor: '#bdc3bf'}],
      formDisabled: false,
      detailId: 0,
      title: '',
      open: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      dataList: [],
      circleColor: {
        2: '#FAB71C',
        9: '#bdc3bf'
      },
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        searchValue: '',
        dateRange: []
      },
      form: {}
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true
      let { pageNum, pageSize, status, searchValue, dateRange } = this.queryParams
      let params = { pageNum, pageSize }
      if (status || status == 0) params.status = status
      if (searchValue) params.searchValue = searchValue
      if (Array.isArray(dateRange) && !dateRange.length == 0) { params = {...params, searchStartTime: dateRange[0], searchEndTime: dateRange[1] } }
      dzzp.list(params).then(res => {
        this.dataList = res.rows
        this.total = res.total
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    // 类型选择
    handleCommand(command) {
      this.queryParams = {...this.queryParams, status: command.id, statusName: command.value}
      this.handleQuery()
    },
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.queryParams.pageNum = 1
      this.getList()
    },
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.getList()
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = { pageNum: 1, pageSize: 10, searchValue: '', dateRange: ''}
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.trafficCaptureId)
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.open = true
      this.title = '新增案件'
      this.detailId = 0
      this.formDisabled = false
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.open = true
      this.title = '修改案件'
      this.detailId = row.trafficCaptureId
      this.formDisabled = false
    },
    handleQueryData(row) {
      this.open = true
      this.title = '案件详情'
      this.detailId = row.trafficCaptureId
      this.formDisabled = true
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const transportIds = row.trafficCaptureId || this.ids
      if (Array.isArray(transportIds) && !transportIds.length) {
        this.$message.warning('请选择需要删除的数据')
        return
      }
      this.$confirm('是否确认删除?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        dzzp.remove(transportIds).then(() => {
          this.getList()
          this.msgSuccess('删除成功')
        })
      }).catch(() => {})
    }
  }
}
</script>
