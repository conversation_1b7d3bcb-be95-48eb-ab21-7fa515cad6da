<template>
  <div>
    <CommonTitle :text="title"></CommonTitle>
    <div class="hjwsglChart" id="hjwsglChart"></div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
export default {
  name: 'index',
  components: {
    CommonTitle,
  },
  data() {
    return {
      title: '环境卫生管理：80分',
      chartsData: [
        {
          name: '婺城',
          value: 2800,
        },
        {
          name: '金东',
          value: 3800,
        },
        {
          name: '兰溪',
          value: 2000,
        },
        {
          name: '东阳',
          value: 3000,
        },
        {
          name: '义乌',
          value: 3000,
        },
        {
          name: '永康',
          value: 3000,
        },
        {
          name: '武义',
          value: 3000,
        },
        {
          name: '浦江',
          value: 3000,
        },
        {
          name: '磐安',
          value: 3000,
        },
      ],
    }
  },
  computed: {},
  mounted() {
    this.initCharts()
  },
  methods: {
    handleTabChange(i) {
      this.index = i
    },
    initCharts() {
      const chartDom = document.getElementById('hjwsglChart')
      const myChart = this.$echarts.init(chartDom)

      const option = {
        grid: {
          left: '7%',
          right: '7%',
          bottom: '5%',
          top: '14%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.chartsData.map((item) => item.name),
          axisLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.6)',
            },
          },
          axisLabel: {
            textStyle: {
              fontSize: 24,
            },
          },
        },
        yAxis: {
          name: '单位(分)',
          nameTextStyle: {
            color: 'rgba(255,255,255,0.6)',
            padding: [20, 10, 10, 10],
            fontSize: 24,
          },
          type: 'value',
          axisLabel: {
            textStyle: {
              fontSize: 24,
            },
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.6)',
            },
          },
          splitLine: {
            lineStyle: {
              color: '#4A6C89',
            },
          },
        },
        series: [
          {
            data: this.chartsData.map((item) => item.value),
            type: 'line',
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(62, 215, 253, 0.30)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(62, 215, 253, 0)',
                  },
                ],
              },
            },
            lineStyle: {
              color: '#00ffff',
            },
            smooth: true,
            symbol: 'none',
          },
        ],
      }

      myChart.setOption(option)

      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
.hjwsglChart {
  width: 100%;
  height: 380px;
  margin-bottom: 40px;
}
</style>