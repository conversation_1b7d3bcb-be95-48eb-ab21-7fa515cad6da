<template>
  <div class="m-table">
    <div class="m-header">
      <span>车牌号</span>
      <!-- <span>车辆类型</span> -->
      <span>进场时间</span>
      <span>出场时间</span>
    </div>
    <!-- 主体内容 -->
    <div v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.8)" class="m-body">
      <vueSeamlessScroll :data="tableList" :class-option="defaultOption">
        <div v-for="item in tableList" :key="item.id" class="m-tr">
          <span>{{ item.carNo }}</span>
          <!-- <span>{{ item.carModel }}</span> -->
          <span>{{ item.inTime }}</span>
          <span>{{ item.outTime }}</span>
        </div>
        <div v-if="!tableList.length" class="m-tr">
          暂无数据
        </div>
      </vueSeamlessScroll>
    </div>
  </div>
</template>

<script>
import { getTaxiList } from '@/api/board/dataView/index'
import vueSeamlessScroll from 'vue-seamless-scroll'

export default {
  components: {
    vueSeamlessScroll
  },
  data() {
    return {
      tableList: [],
      loading: false
    }
  },
  computed: {
    defaultOption() {
      return {
        step: 0.2, // 数值越大速度滚动越快
        limitMoveNum: 4, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 1000 // 单步运动停止的时间(默认值1000ms)
      }
    }
  },
  mounted() {
    this.fetchData()
  },
  methods: {
    fetchData() {
      this.loading = true
      const inTime = this.parseTime(new Date(), '{y}-{m}-{d} 00:00:00')
      getTaxiList({ inTime, pageSize: 30, pageNum: 1 }).then(res => {
        this.tableList = res.rows
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style scoped lang="scss">
.m-table {
  height: 100%;
  padding-top: pxtorem(10);
  .m-header {
    height: pxtorem(28);
    line-height: pxtorem(28);
    background: url(@/assets/images/m-header.png) no-repeat center center / 100% 100%;
    font-size: pxtorem(14);
    display: flex;
    padding: 0 10px 0 10px;
    span {
      text-align: center;
      flex: 1;
    }
  }
  .m-body {
    height: calc(100% - 0.14583rem);
    overflow: hidden;
    .m-tr {
      overflow: hidden;
      height: pxtorem(26);
      line-height: pxtorem(26);
      font-size: pxtorem(12);
      color: #00f7ff;
      background: rgba(0, 0, 0, 0.3);
      padding: 0 10px;
      margin-top: 5px;
      text-align: center;
      cursor: pointer;
      span {
        width: 33.3%;
        height: pxtorem(26);
        float: left;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
  }
}
</style>
