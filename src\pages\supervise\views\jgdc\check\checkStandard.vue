<template>
  <el-dialog :close-on-click-modal="false" v-bind="$attrs" title="选择考核标准" width="1000px" v-on="$listeners" @close="onClose">
    <el-row :gutter="20">
      <el-form ref="queryForm" :model="queryParams" :inline="true">
        <el-col :span="1.5">
          <el-form-item label="项目名称">
            <el-input v-model="queryParams.title" size="small" />
          </el-form-item>
        </el-col>
        <el-col :span="1.5">
          <el-form-item label="考核类型">
            <el-select v-model="queryParams.type" placeholder="考核类型" clearable size="small">
              <el-option label="全部" value="" />
              <el-option label="加分" value="0" />
              <el-option label="扣分" value="1" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="1.5">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="getList">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
    <el-table
      v-loading="loading"
      class="m-table"
      :data="standardList"
      row-key="checkStandardId"
      default-expand-all
      height="500px"
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      @row-click="handleCurrentChange"
    >
      <el-table-column prop="title" label="考核项目">
        <template slot-scope="scope">
          <div class="tb-cell">
            <el-checkbox v-if="scope.row.status == 1" v-model="scope.row.checked" />
            <el-tooltip class="item" effect="dark" :content="scope.row.title" :open-delay="500" placement="top-start">
              <span class="tb-cell-text">{{ scope.row.title }}</span>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="title" label="考核项目" show-overflow-tooltip /> -->
      <el-table-column prop="content" label="考核标准" show-overflow-tooltip />
      <el-table-column prop="type" label="考核类型" align="center" width="80">
        <template slot-scope="scope">
          <span>{{ scope.row.type | typeName }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="code" label="小类代码" align="center" width="80" />
    </el-table>
    <!-- 确认按钮 -->
    <div slot="footer" class="dialog-footer">
      <el-button :loading="loading" @click="close">取 消 </el-button>
      <el-button type="primary" :loading="loading" @click="confirm">保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { listStandard } from '@/api/supervise/standard'
import { debounce } from '@/utils/index'
export default {
  name: 'CheckStandard',
  filters: {
    typeName(type) {
      const name = { 0: '加分', 1: '扣分' }
      return name[type] || '无'
    }
  },
  data() {
    return {
      loading: false,
      standardList: [],
      queryParams: {
        type: '',
        title: ''
      },
      radioId: null,
      listData: [],
      checkRow: {}
    }
  },
  watch: {
    '$attrs.visible'(nVal) {
      if (nVal) {
        this.getList()
      }
    }
  },
  methods: {
    onClose() {
      this.queryParams = {
        type: '',
        title: ''
      }
      this.checkRow = {}
    },
    getList() {
      this.loading = true
      listStandard(this.queryParams).then(response => {
        this.listData = response.data
        this.standardList = this.handleTree(response.data, 'checkStandardId')
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    resetQuery() {
      this.queryParams = {
        type: '',
        title: ''
      }
      this.resetForm('queryForm')
      this.getList()
    },
    handleCurrentChange(row) {
      console.log(row)
      debounce(() => {
        console.log(row)
        if (row.status == 1) {
          this.checkRow = row
          const listData = this.listData.map(item => {
            return { ...item, checked: item.checkStandardId === row.checkStandardId }
          })
          this.standardList = this.handleTree(listData, 'checkStandardId')
        }
      }, 100, false)()
    },
    close() {
      this.$emit('update:visible', false)
    },
    confirm() {
      this.$emit('confirm', this.checkRow)
      this.close()
    }
  }
}
</script>

<style scoped lang="scss">
.m-table {
  ::v-deep {
    .tb-cell {
      // width: 150;
      display: inline-flex;
      &-text {
        margin-left: 10px;
        width: 160px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}

</style>
