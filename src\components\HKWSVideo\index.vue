<template>
  <div class="video-container">
    <div :id="id" :ref="id" :style="{ height: height + 'px', width: width + 'px' }"></div>
  </div>
</template>

<script>
import JSEncrypt from 'jsencrypt'

export default {
  name: 'HKWSVideo',
  props: {
    id: {
      type: String,
      default: 'corpvideo'
    },
    code: {
      type: [String, Array],
      required: true
    },
    width: {
      type: Number,
      default: 310
    },
    height: {
      type: Number,
      default: 264.26
    },
    noInit: {
      type: Boolean,
      default: false
    },
    videoGrid: {
      type: String,
      default: '1x1'
    },
    // 外部控制的播放模式：0-实时预览，1-录像回放
    playMode: {
      type: Number,
      default: 0
    },
    // 录像回放时间范围
    timeRange: {
      type: Object,
      default: () => ({
        startTime: '',
        endTime: ''
      })
    },
    // 是否显示内部控制按钮（已移除，保留兼容性）
    enableSwitch: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      videoContainer: this.id,
      initCount: 0,
      pubKey: '',
      oWebControl: null,
      cId: '',
      currentMode: 0 // 当前播放模式，0-实时预览，1-录像回放
    }
  },
  mounted() {
    if (!this.noInit) {
      this.initVideo()
    }

    // 添加窗口事件监听
    window.onresize = this.handleResize
    window.onscroll = this.handleScroll

    // 监听父组件的刷新事件
    this.$bus && this.$bus.$on('refreshHkVideo', this.handleRefreshEvent)

    // 添加路由守卫
    this.routerGuard = this.$router.beforeEach((_, __, next) => {
      if (this.oWebControl) {
        this.closeVideo(() => {
          next();
        });
      } else {
        next();
      }
    });
  },
  methods: {
    initVideo() {
      const videoElement = document.getElementById(this.id)
      this.initPlugin(this.videoContainer, videoElement.clientWidth, videoElement.clientHeight, () => {
        // 根据外部传入的播放模式初始化
        if (this.playMode === 1) {
          // 录像回放模式
          this.initForPlayback({ mlayout: this.videoGrid }).then(() => {
            this.startPlayback(this.code)
          })
        } else {
          // 实时预览模式
          this.initForPreview({ mlayout: this.videoGrid }).then(() => {
            this.startPreview(this.code, '')
          })
        }
        this.currentMode = this.playMode
      })
    },

    // 创建播放实例
    initPlugin(containerId, w, h, onSuccess, callback) {
      this.cId = containerId
      const width = w || this.width
      const height = h || this.height
      this.oWebControl = new WebControl({
        szPluginContainer: containerId, // 指定容器id
        iServicePortStart: 15900, // 指定起止端口号，建议使用该值
        iServicePortEnd: 15909,
        szClassId: '23BF3B0A-2C56-4D97-9C03-0CB103AA8F11', // 用于IE10使用ActiveX的clsid
        cbConnectSuccess: () => {
          // 创建WebControl实例成功
          this.oWebControl
            .JS_StartService('window', {
              // WebControl实例创建成功后需要启动服务
              dllPath: './VideoPluginConnect.dll' // 值"./VideoPluginConnect.dll"写死
            })
            .then(
              () => {
                // 启动插件服务成功
                this.oWebControl.JS_SetWindowControlCallback({
                  // 设置消息回调
                  cbIntegrationCallBack: callback // 创建播放实例成功后消息回调函数
                })

                this.oWebControl.JS_CreateWnd(containerId, width, height).then(() => {
                  //JS_CreateWnd创建视频播放窗口，宽高可设定
                  onSuccess() // 创建播放实例成功后初始化
                })
              },
              () => {
                // 启动插件服务失败
              }
            )
        },
        cbConnectError: () => {
          // 创建WebControl实例失败
          this.oWebControl = null
          WebControl.JS_WakeUp('VideoWebPlugin://') // 程序未启动时执行error函数，采用wakeup来启动程序
          this.initCount++
          if (this.initCount < 3) {
            setTimeout(() => {
              this.initPlugin(containerId, w, h, onSuccess)
            }, 1000)
          } else {
            document.getElementById(this.cId).innerHTML = `<a style='color:white;' href='https://open.hikvision.com/fileserver/filesonline/videowebplugin_1.5.4.20241015201015_20241016113001.zip' download='VideoWebPlugin.exe'>插件启动失败，请点击此处下载插件并安装！</a>`
          }
        },
        cbConnectClose: () => {
          // 异常断开：bNormalClose = false
          // JS_Disconnect正常断开：bNormalClose = true
          console.log('cbConnectClose')
          this.oWebControl = null
        }
      })
    },

    // 初始化回放模式
    initForPlayback(option) {
      return this.getPubKey().then(() => {
        this.initPlayPlugin({ mplayMode: 1, ...option })
      })
    },

    intPluginForPlayback(option) {
      return this.getPubKey().then(() => {
        this.initPlayPlugin({ mplayMode: 1, ...option })
      })
    },

    initPlayPlugin(option) {
        const { mlayout, mplayMode } = option || { mlayout: '1x1', mplayMode: 0 }
        ////////////////////////////////// 请自行修改以下变量值 ////////////////////////////////////
        let appkey = '29377963' //综合安防管理平台提供的appkey，必填
        let secret = this.setEncrypt('adYjapTwZTUfMyV33RLq') //综合安防管理平台提供的secret，必填
        let ip = '************' //综合安防管理平台IP地址，必填
        let playMode = mplayMode || 0 //初始播放模式：0-预览，1-回放
        let port = 443 //综合安防管理平台端口，若启用HTTPS协议，默认443
        let snapDir = 'D:\\SnapDir' //抓图存储路径
        let videoDir = 'D:\\VideoDir' //紧急录像或录像剪辑存储路径
        let layout = mlayout || '1x1' //playMode指定模式的布局
        let enableHTTPS = 1 //是否启用HTTPS协议与综合安防管理平台交互，这里总是填1
        let encryptedFields = 'secret' //加密字段，默认加密领域为secret
        let showToolbar = 1 //是否显示工具栏，0-不显示，非0-显示
        let showSmart = 1 //是否显示智能信息（如配置移动侦测后画面上的线框），0-不显示，非0-显示
        let buttonIDs = '0,16,256,257,258,259,260,512,513,514,515,516,517,768,769' //自定义工具条按钮
        ////////////////////////////////// 请自行修改以上变量值 ////////////////////////////////////

        this.oWebControl
          .JS_RequestInterface({
            funcName: 'init',
            argument: JSON.stringify({
              appkey: appkey, //API网关提供的appkey
              secret: secret, //API网关提供的secret
              ip: ip, //API网关IP地址
              playMode: playMode, //播放模式（决定显示预览还是回放界面）
              port: port, //端口
              snapDir: snapDir, //抓图存储路径
              videoDir: videoDir, //紧急录像或录像剪辑存储路径
              layout: layout, //布局
              enableHTTPS: enableHTTPS, //是否启用HTTPS协议
              encryptedFields: encryptedFields, //加密字段
              showToolbar: showToolbar, //是否显示工具栏
              showSmart: showSmart, //是否显示智能信息
              buttonIDs: buttonIDs //自定义工具条按钮
            })
          })
          .then(() => {
            this.oWebControl.JS_Resize(w || this.width, h || this.height) // 初始化后resize一次，规避firefox下首次显示窗口后插件窗口未与DIV窗口重合问题
            return Promise.resolve()
          })
      },

      //初始化
      initForPreview(option) {
        return this.getPubKey().then(() => {
          this.initPlayPlugin(option)
        })
      },

      //获取公钥
      getPubKey() {
        return this.oWebControl
          .JS_RequestInterface({
            funcName: 'getRSAPubKey',
            argument: JSON.stringify({
              keyLength: 1024
            })
          })
          .then(oData => {
            if (oData.responseMsg.data) {
              this.pubKey = oData.responseMsg.data
              return Promise.resolve()
            }
          })
      },

      //RSA加密
      setEncrypt(value) {
        let encrypt = new JSEncrypt()
        encrypt.setPublicKey(this.pubKey)
        return encrypt.encrypt(value)
      },

      // 处理窗口大小变化
      handleResize() {
        if (this.oWebControl != null) {
          this.oWebControl.JS_Resize(this.width, this.height)
          this.setWndCover()
        }
      },

      // 处理滚动事件
      handleScroll() {
        if (this.oWebControl != null) {
          this.oWebControl.JS_Resize(this.width, this.height)
          this.setWndCover()
        }
      },

      // 设置窗口裁剪，当因滚动条滚动导致窗口需要被遮住的情况下需要JS_CuttingPartWindow部分窗口
      setWndCover(cw, ch) {
        const width = cw || this.width
        const height = ch || this.height
        const playDiv = document.getElementById(this.cId)
        if (playDiv) {
          if (this.oWebControl != null) {
            this.oWebControl.JS_Resize(width, height)
          }
          let iWidth = window.innerWidth
          let iHeight = window.innerHeight
          let oDivRect = playDiv.getBoundingClientRect()

          let iCoverLeft = oDivRect.left < 0 ? Math.abs(oDivRect.left) : 0
          let iCoverTop = oDivRect.top < 0 ? Math.abs(oDivRect.top) : 0
          let iCoverRight = oDivRect.right - iWidth > 0 ? Math.round(oDivRect.right - iWidth) : 0
          let iCoverBottom = oDivRect.bottom - iHeight > 0 ? Math.round(oDivRect.bottom - iHeight) : 0

          iCoverLeft = iCoverLeft > width ? width : iCoverLeft
          iCoverTop = iCoverTop > height ? height : iCoverTop
          iCoverRight = iCoverRight > width ? width : iCoverRight
          iCoverBottom = iCoverBottom > height ? height : iCoverBottom

          this.oWebControl.JS_RepairPartWindow(0, 0, width, height) // 多1个像素点防止还原后边界缺失一个像素条
          if (iCoverLeft !== 0) {
            this.oWebControl.JS_CuttingPartWindow(0, 0, iCoverLeft, height)
          }
          if (iCoverTop !== 0) {
            this.oWebControl.JS_CuttingPartWindow(0, 0, width, iCoverTop) // 多剪掉一个像素条，防止出现剪掉一部分窗口后出现一个像素条
          }
          if (iCoverRight !== 0) {
            this.oWebControl.JS_CuttingPartWindow(width - iCoverRight, 0, iCoverRight, height)
          }
          if (iCoverBottom !== 0) {
            this.oWebControl.JS_CuttingPartWindow(0, height - iCoverBottom, width, iCoverBottom)
          }
        }
      },

      startPreview(cameraIndexCode, wzId) {
        // 处理单个点位或多个点位的情况
        if (Array.isArray(cameraIndexCode)) {
          cameraIndexCode.forEach((code, index) => {
            this.initSinglePreview(code.trim(), index)
          })
        } else {
          this.initSinglePreview(cameraIndexCode, wzId)
        }
      },

      // 抽取单个预览的逻辑为独立函数
      initSinglePreview(code, wzId) {
        let streamMode = 0
        let transMode = 1
        let gpuMode = 0
        let authUuid = ""
        let ezvizDirect = 0
        let cascade = 1
        let wndId = ''
        if (undefined === wzId || '' === wzId || null === wzId) {
          wndId = -1 //播放窗口序号（在2x2以上布局下可指定播放窗口）
        } else {
          wndId = wzId //播放窗口序号（在2x2以上布局下可指定播放窗口）
        }
        this.oWebControl.JS_RequestInterface({
          funcName: 'startPreview',
          argument: JSON.stringify({
            cameraIndexCode: code,
            streamMode: streamMode,
            transMode: transMode,
            gpuMode: gpuMode,
            wndId: Number(wndId) + 1, // 使用索引作为窗口ID 索引从1开始不要从0
            authUuid: authUuid,
            ezvizDirect: ezvizDirect,
            cascade: cascade
          })
        })
      },

    startPlayback(cameraIndexCode) {
      // 使用外部传入的时间范围，如果没有则使用默认值
      let startTimeStamp, endTimeStamp

      if (this.timeRange && this.timeRange.startTime && this.timeRange.endTime) {
        // 使用外部传入的时间范围
        startTimeStamp = new Date(this.timeRange.startTime).getTime()
        endTimeStamp = new Date(this.timeRange.endTime).getTime()
      } else {
        // 默认时间范围：最近24小时
        startTimeStamp = new Date().getTime() - 24 * 3600000
        endTimeStamp = new Date().getTime()
      }

      let recordLocation = 1 //录像存储位置：0-中心存储，1-设备存储
      let transMode = 1 //传输协议：0-UDP，1-TCP
      let gpuMode = 0 //是否启用GPU硬解，0-不启用，1-启用
      let wndId = -1 //播放窗口序号（在2x2以上布局下可指定播放窗口）

      console.log('海康威视录像回放参数:', {
        cameraIndexCode,
        startTime: this.timeRange?.startTime,
        endTime: this.timeRange?.endTime,
        startTimeStamp,
        endTimeStamp
      })

      this.oWebControl.JS_RequestInterface({
        funcName: 'startPlayback',
        argument: JSON.stringify({
          cameraIndexCode: cameraIndexCode, //监控点编号
          startTimeStamp: Math.floor(startTimeStamp / 1000).toString(), //录像查询开始时间戳，单位：秒
          endTimeStamp: Math.floor(endTimeStamp / 1000).toString(), //录像结束开始时间戳，单位：秒
          recordLocation: recordLocation, //录像存储类型：0-中心存储，1-设备存储
          transMode: transMode, //传输协议：0-UDP，1-TCP
          gpuMode: gpuMode, //是否启用GPU硬解，0-不启用，1-启用
          wndId: wndId //可指定播放窗口
        })
      })
    },

      // 标签关闭
      closeVideo(callback) {
        if (this.oWebControl != null) {
          this.oWebControl.JS_HideWnd() // 先让窗口隐藏，规避可能的插件窗口滞后于浏览器消失问题
          return this.oWebControl.JS_Disconnect().then(
            () => {
              // 断开与插件服务连接成功
              setTimeout(callback, 1000)
            },
            () => {
              // 断开与插件服务连接失败
            }
          )
        }
      },

    capture() {
      this.oWebControl.JS_RequestInterface({
        argument: {
          wndId: 1
        },
        funcName: 'snapShot'
      })
      return Promise.resolve()
    },

    // 公开方法：切换视频源（供外部调用）
    switchVideo(cameraIndexCode) {
      if (cameraIndexCode && cameraIndexCode !== this.code) {
        console.log('外部调用切换海康威视视频源:', cameraIndexCode)
        this.switchVideoSource(cameraIndexCode)
      }
    },

    // 公开方法：刷新当前视频（供外部调用）
    refreshVideo() {
      console.log('海康威视视频刷新')
      if (!this.oWebControl || !this.code) {
        console.warn('海康威视视频控件未初始化或无摄像头编码')
        return
      }

      // 刷新当前视频源
      this.switchVideoSource(this.code)
    },

    // 公开方法：重新初始化视频（供外部调用）
    reinitVideo() {
      console.log('海康威视视频重新初始化')
      if (this.oWebControl) {
        // 先关闭当前视频
        this.closeVideo(() => {
          // 延迟重新初始化
          setTimeout(() => {
            this.initVideo()
          }, 1000)
        })
      } else {
        // 直接初始化
        this.initVideo()
      }
    },

    // 处理刷新事件（事件总线监听）
    handleRefreshEvent(data) {
      console.log('海康威视视频收到刷新事件:', {
        componentId: this.id,
        eventData: data,
        currentCode: this.code
      })

      // 检查是否是针对当前组件的刷新事件
      if (data && data.componentId && data.componentId !== this.id) {
        console.log('刷新事件不是针对当前组件，忽略')
        return // 不是针对当前组件的事件，忽略
      }

      // 检查组件是否已初始化
      if (!this.oWebControl) {
        console.warn('海康威视视频控件未初始化，无法执行刷新操作')
        return
      }

      // 根据刷新类型执行不同的刷新操作
      if (data && data.type === 'reinit') {
        console.log('执行海康威视视频重新初始化')
        this.reinitVideo()
      } else {
        console.log('执行海康威视视频刷新')
        this.refreshVideo()
      }

      // 发送刷新完成事件
      this.$emit('refresh-completed', {
        componentId: this.id,
        type: data?.type || 'refresh',
        timestamp: Date.now()
      })
    },

    // 切换视频源（不重新初始化控件）
    switchVideoSource(newCode) {
      if (!this.oWebControl || !newCode) return

      console.log('海康威视切换视频源:', newCode)

      // 停止当前所有预览/回放
      const stopFunction = this.currentMode === 1 ? 'stopAllPlayback' : 'stopAllPreview'

      this.oWebControl.JS_RequestInterface({
        funcName: stopFunction
      }).then(() => {
        // 根据当前模式启动新的视频源
        if (this.currentMode === 1) {
          // 回放模式
          this.startPlayback(Array.isArray(newCode) ? newCode[0] : newCode)
        } else {
          // 预览模式
          this.startPreview(newCode, '')
        }
      }).catch(error => {
        console.error('停止视频失败:', error)
        // 即使停止失败，也尝试启动新视频
        if (this.currentMode === 1) {
          this.startPlayback(Array.isArray(newCode) ? newCode[0] : newCode)
        } else {
          this.startPreview(newCode, '')
        }
      })
    },

    // 切换播放模式（由外部控制）
    switchToMode(mode) {
      if (this.currentMode === mode) return

      console.log(`海康威视切换模式: ${this.currentMode} -> ${mode}`)

      // 停止当前所有预览/回放
      this.oWebControl?.JS_RequestInterface({
        funcName: mode === 1 ? 'stopAllPreview' : 'stopAllPlayback'
      }).then(() => {
        if (mode === 1) {
          // 切换到回放模式
          this.oWebControl.JS_HideWnd();
          const videoElement = document.getElementById(this.id)
          this.initPlugin(this.videoContainer, videoElement.clientWidth, videoElement.clientHeight, () => {
            this.initForPlayback({ mlayout: this.videoGrid }).then(() => {
              this.startPlayback(Array.isArray(this.code) ? this.code[0] : this.code)
            })
          })
        } else {
          // 切换到预览模式
          this.oWebControl.JS_HideWnd();
          const videoElement = document.getElementById(this.id)
          this.initPlugin(this.videoContainer, videoElement.clientWidth, videoElement.clientHeight, () => {
            this.initForPreview({ mlayout: this.videoGrid }).then(() => {
              this.startPreview(this.code, '')
            })
          })
        }
        this.currentMode = mode
      })
    }
  },

  watch: {
    // 监听摄像头编码变化
    code: {
      handler(newCode, oldCode) {
        if (newCode && this.oWebControl && newCode !== oldCode) {
          console.log('海康威视摄像头编码变化:', { oldCode, newCode })
          // 切换视频源而不是重新初始化
          this.switchVideoSource(newCode)
        }
      },
      deep: true
    },

    // 监听播放模式变化
    playMode: {
      handler(newMode) {
        if (this.oWebControl && newMode !== this.currentMode) {
          this.switchToMode(newMode)
        }
      },
      immediate: false
    },

    // 监听时间范围变化
    timeRange: {
      handler(newTimeRange) {
        // 如果当前是回放模式且时间范围发生变化，重新开始回放
        if (this.currentMode === 1 && this.oWebControl && newTimeRange && newTimeRange.startTime && newTimeRange.endTime) {
          console.log('海康威视时间范围变化，重新开始回放:', newTimeRange)
          this.startPlayback(Array.isArray(this.code) ? this.code[0] : this.code)
        }
      },
      deep: true
    }
  },

  beforeDestroy() {
    // 移除路由守卫
    if (this.routerGuard) {
      this.routerGuard();
    }

    // 移除窗口事件监听
    window.onresize = null;
    window.onscroll = null;

    // 移除事件总线监听
    this.$bus && this.$bus.$off('refreshHkVideo', this.handleRefreshEvent)

    // 关闭视频
    if (this.oWebControl) {
      this.closeVideo(() => {
        console.log('HKWSVideo closed on component unmount');
      });
    }
  }
}
</script>

<style scoped lang="scss">
  .videosp {
    width: 100%;
    height: 100%;
    background: black;
  }

  .video-container {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .switch-button {
    padding: 5px 10px;
    background: rgba(0, 0, 0, 0.6);
    color: white;
    border-radius: 4px;
    cursor: pointer;
    z-index: 1;

    &:hover {
      background: rgba(0, 0, 0, 0.8);
    }
  }
</style>
