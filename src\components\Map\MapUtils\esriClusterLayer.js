/* eslint-disable no-undef */
/* eslint-disable import/no-amd */

// eslint-disable-next-line no-undef
import Point from "@arcgis/core/geometry/Point.js";
import Graphic from "@arcgis/core/Graphic.js";
import FeatureLayer from "@arcgis/core/layers/FeatureLayer.js";
import { subclass } from "@arcgis/core/core/accessorSupport/decorators.js";
import * as watchUtils from "@arcgis/core/core/watchUtils.js";
import Symbol from "@arcgis/core/symbols/Symbol.js";
import GraphicsLayer from "@arcgis/core/layers/GraphicsLayer.js"
class ClusterLayer extends FeatureLayer {
  constructor(options) {
    super();
    this.isCluster = options.isCluster || true;
    this.source = options.source;
    var clusterField = [
      {
        name: "clusterCount",
        alias: "clusterCount",
        type: "integer",
      },
      {
        name: "clusterId",
        alias: "clusterId",
        type: "integer",
      },
      {
        name: "clusterAttr",
        alias: "clusterAttr",
        type: "string",
      },
    ];
    this.fields = options.fields || [];
    this.fields = this.fields.concat(clusterField);
    this.objectIdField = options.objectIdField;
    this.geometryType = options.geometryType;
    this.popupEnabled = options.popupEnabled || false;
    this.definitionExpression = options.definitionExpression || "";
    this.labelingInfo = this.isCluster ? null : options.labelingInfo || null;
    this.maxScale = options.maxScale || 0;
    this.minScale = options.minScale || 0;
    this.outFields = options.outFields || ["*"];
    this.screenSizePerspectiveEnabled =
      options.screenSizePerspectiveEnabled || false;
    this.title = options.title || "";
    this.visible = options.visible || true;
    this._clusterDistance = options.distance || 50;
    this._clusterTolerance = this._clusterDistance;
    this._clusters = [];
    this._clusterLabelColor = options.labelColor || "#fff";
    this._sizeMax = options.sizeMax || 100;
    this._sizeMin = options.sizeMin || 40;
    this.view = null;
    this.initClusterData = [];
    this._clusterColor = options.clusterColor || "rgba(244,164,96,0.8)";
    this._clusterImgSrc = options.clusterImgSrc || null;
    this.renderer = options.renderer;
    this.singleRenderer = options.renderer || null;
    this.defaultElevationInfo = options.elevationInfo || {
      mode: "relative-to-ground",
      offset: 2,
      unit: "meters",
    };
    this._clusterHeight = options.clusterHeight || 100;
    this._defaultClusterSym = (options.renderer &&
      options.renderer.defaultSymbol) || {
      type: "point-3d",
      symbolLayers: [
        {
          type: "icon",
          size: 6,
          material: { color: "rgba(0,191,255,0.8)" },
        },
      ],
    };
    this._clusterLabelSym = options.clusterLabelSym || {
      type: "text",
      color: "#fff",
      font: {
        weight: "bold",
        size: "16px",
      },
    };
    this._clusterType = options.clusterType || "";
    this._clusterLabelPosition =
      options.clusterLabelPosition || "center-center";
    this.curClusterGraphics = []; //当前地图上的聚类要素
    this.isfresh = true;
    this.lastDefinition = "";
    this.lastLabelingInfo = options.labelingInfo || null;
    this.isDefinition = false; //是否修改了过滤条件
    this.criticalZoom = options.criticalZoom || 18;
    this.currentObjectIds = []; //当前单个显示的要素
    this.extentGra = null;
    this.isZoom = true;
    this._handles.add(
      this.on("layerview-create", this._layerviewCreate.bind(this))
    );
    this._handles.add(this.on("edits", this._featureEdits.bind(this)));
    // this.uniqueRenderValueArr =  options.uniqueRenderValueArr
    // this.uniqueRenderImgSrcArr =  options.uniqueRenderImgSrcArr
    // this.uniqueRenderSizeArr =  options.uniqueRenderSizeArr
    return this;
  }

  load(loadPromise) {
    var self = this;
    var c = isSome(loadPromise) ? loadPromise.signal : null;

    var popup = self.popupTemplate;
    if (self.isCluster) {
      self.labelingInfo = null;
      self.popupTemplate = popup
        ? {
            title: popup.title || "",
            outFields: ["*"],
            content: function (ft) {
              if (
                ft.graphic.attributes.clusterCount &&
                ft.graphic.attributes.clusterCount != 1
              )
                return "<div>有 {clusterCount} 个聚类点</div>";
              else return popup.content || "";
            },
          }
        : null;
    }

    self.lastDefinition = self.definitionExpression;
    self.source &&
      self.source.forEach(function (graphic) {
        graphic.attributes.clusterCount = 0;
      });

    if (this.portalItem && this.portalItem.loaded && this.source)
      this.addResolvingPromise(
        this.createGraphicsSource(c).then(function (a) {
          return self.initLayerProperties(a);
        })
      );
    else {
      const r = this.loadFromPortal(
        { supportedTypes: ["Feature Service", "Feature Collection"] },
        loadPromise
      )
        .catch(function (a) {
          return a;
        })
        .then(async () => {
          if (
            this.url &&
            null == this.layerId &&
            /FeatureServer|MapServer\/*$/i.test(this.url)
          ) {
            const e = await this._fetchFirstLayerId(c);
            null != e && (this.layerId = e);
          }
          if (!this.url && !this._hasMemorySource())
            throw new Error(
              "feature-layer:missing-url-or-source",
              "Feature layer must be created with either a url or a source"
            );
          let graphicsSource = await this.createGraphicsSource(c);
          return this.initLayerProperties(graphicsSource);
        })
        .then(() => this.finishLoadEditablePortalLayer(loadPromise));
      this.addResolvingPromise(r);
      return Promise.resolve(this);
    }
  }

  _layerviewCreate(evt) {
    var that = this;
    this.view = evt.view;
    this.layerView = evt.layerView;
    window.__layerView = evt.layerView;
    this.getInitData();
    if (that.isCluster) {
      var k = 0;
      that.layerView.watch("updating", function (value) {
        // console.log(11111111);
        if (
          !value &&
          !that.isDefinition &&
          that.isfresh &&
          that.isCluster &&
          that.isZoom &&
          that.visible
        ) {
          // console.log("聚类" + k++);
          // console.log("聚类要素长度" + that._clusters.length);
          // console.log(that._clusters);
          that._clusters = [];
          that.addClusterGraphics();
        }
      });
      var zoomIndex = 0;
      var lastZoom = that.view.zoom.toFixed(6);
      var isDrag = false;
      var mouseWheel = false;
      that.view.watch(
        "zoom",
        // throttle(
        function (evt) {
          const currentZoom = evt;
          if (isDrag) {
            lastZoom = currentZoom.toFixed(6);
            return;
          }
          if (!mouseWheel) {
            if (lastZoom != currentZoom.toFixed(6)) {
              if (that.view.zoom < that.criticalZoom) {
                that.isfresh = true;
                if (!that.isZoom) {
                  that.isZoom = true;
                  that.definitionExpression = "clusterCount>0";
                }
                that.refresh();
              } else {
                that.isZoom = false;
                that
                  .applyEdits({ deleteFeatures: that.curClusterGraphics })
                  .then(function () {
                    that.curClusterGraphics = [];
                    //超过一定级别 取消聚类
                    that.definitionExpression = that.lastDefinition;
                    that.elevationInfo = that.defaultElevationInfo;
                  });
              }
            }
            lastZoom = currentZoom.toFixed(6);
            return;
          }
          zoomIndex = zoomIndex + 1;
          if (zoomIndex === 2) {
            zoomIndex = 0;
            if (lastZoom != currentZoom.toFixed(6)) {
              if (that.view.zoom < that.criticalZoom) {
                that.isfresh = true;
                if (!that.isZoom) {
                  that.isZoom = true;
                  that.definitionExpression = "clusterCount>0";
                }
                that.refresh();
                mouseWheel = false;
              } else {
                that.isZoom = false;
                that
                  .applyEdits({ deleteFeatures: that.curClusterGraphics })
                  .then(function () {
                    that.curClusterGraphics = [];
                    //超过一定级别 取消聚类
                    that.definitionExpression = that.lastDefinition;
                    that.elevationInfo = that.defaultElevationInfo;
                  });
              }
            }
            lastZoom = currentZoom.toFixed(6);
          }
        }
        // , 1200)
      );
      that.view.on("mouse-wheel", function () {
        mouseWheel = true;
      });

      watchUtils.watch(that.layerView, "filter", function (filterInfo) {
        that.isDefinition = true;
        that.isfresh = true;
        that
          .applyEdits({ deleteFeatures: that.curClusterGraphics })
          .then(function () {
            that.getInitData();
          });
      });
      watchUtils.watch(that, "definitionExpression", function (where) {
        if (where.indexOf("clusterCount>0") == -1) {
          that.isDefinition = false;
          that.lastDefinition = where;
          that.isfresh = true;
          that
            .applyEdits({ deleteFeatures: that.curClusterGraphics })
            .then(function () {
              that.isCluster && that.isZoom && that.getInitData();
            });
        }
      });
      watchUtils.watch(that, "visible", function (bool) {
        if (!bool) {
          that.isfresh = false;
          that
            .applyEdits({ deleteFeatures: that.curClusterGraphics })
            .then(function () {
              that.curClusterGraphics = [];
            });
        } else {
          that.isfresh = true;
        }
      });
      watchUtils.whenFalse(that, "isCluster", function (evt) {
        //无法监听到
      });
    }
  }

  changeCluster(bool) {
    var that = this;
    if (bool) {
      this.isCluster = true;
      this.isDefinition = false;
      this.isfresh = true;
      this.curClusterGraphics = [];
      this.definitionExpression = "clusterCount>0";
      this.refresh();
    } else {
      //取消聚类
      this.isCluster = false;
      this.applyEdits({ deleteFeatures: that.curClusterGraphics }).then(
        function () {
          that.curClusterGraphics = [];
          that.definitionExpression = that.lastDefinition;
          that.elevationInfo = that.defaultElevationInfo;
        }
      );
    }
    return bool;
  }

  _featureEdits(evt) {
    //如果不是通过聚类实现的增加删除要素，需要重新获取图上的要素
    if (!this.isfresh) {
      this.definitionExpression = this.lastDefinition;
    } else {
      this.isfresh = false;
    }
  }

  getInitData() {
    var that = this;
    var query = that.createQuery();
    query.where = that.lastDefinition;

    if (that.layerView.filter) {
      query.geometry = that.layerView.filter.geometry;
      query.where = that.layerView.filter.where;
    }

    that.queryFeatures(query).then(function (results) {
      that.initClusterData = results.features;
      that.curClusterGraphics = [];

      that._clusters = [];
      that.addClusterGraphics();
      that.isDefinition = false;
    });
  }

  addClusterGraphics() {
    var filterPoints = this.initClusterFilter();
    // console.log("聚类点的个数：" + filterPoints.length);
    // if (filterPoints.length < 2) return;
    // console.time("addClusterGraphics");
    for (var j = 0; j < filterPoints.length; j++) {
      //判断当前点是否该被聚类
      var point = filterPoints[j];
      var clustered = false;
      for (var i = 0; i < this._clusters.length; i++) {
        var c = this._clusters[i];
        if (this.testCluster(point, c)) {
          this.addClusterPoint(point, c);
          clustered = true;
          break;
        }
      }
      if (!clustered) {
        this.createCluster(point);
      }
    }
    // console.timeEnd("addClusterGraphics");
    this.showClusters();
  }

  initClusterFilter() {
    var self = this;
    var filterExtent = self.view.extent;
    var filterArray = this.initClusterData;
    // this.view.graphics.remove(this.extentGra);
    // this.extentGra = new Graphic(b.view.extent.expand(2.5), {
    //   type: "simple-fill",  // autocasts as new SimpleFillSymbol()
    //   color: [255, 0, 0, 0.5],
    //   outline: {  // autocasts as new SimpleLineSymbol()
    //     color: [128, 128, 128, 1],
    //     width: "0.5px"
    //   }
    // });
    //this.view.graphics.add(this.extentGra);
    if (self.view.zoom > 7) {
      filterArray = [];
      if (self.view.camera.tilt > 35) {
        if (self.view.camera.tilt < 62) {
          filterExtent.expand(2);
        } else {
          filterExtent.expand(2.5);
        }
      }
      this.initClusterData.forEach((data) => {
        filterExtent.contains(data.geometry) && filterArray.push(data);
      });
    }

    return filterArray;
  }

  createCluster(p) {
    var clusterId = this._clusters.length + 1;
    if (!p.attributes) {
      p.attributes = {};
    }
    p.attributes.clusterId = clusterId;
    if (
      this._clusterImgSrc &&
      this.singleRenderer &&
      this.singleRenderer.field
    ) {
      p.attributes[this.singleRenderer.field] = clusterId;
    }
    for (var k in p.attributes) {
      // if (k == this.objectIdField) {
      //   delete p.attributes[k];
      // }
      if (k == "clusterCount") {
        delete p.attributes[k];
      }
    }
    var cluster = {
      x: p.geometry.x,
      y: p.geometry.y,
      attributes: {
        clusterCount: 1,
        clusterId: clusterId,
        extent: [p.geometry.x, p.geometry.y, p.geometry.x, p.geometry.y],
        ...p.attributes,
        clusterAttr: "" + p.attributes.id,
      },
    };
    this._clusters.push(cluster);
  }

  testCluster(p, cluster) {
    var distance =
      Math.sqrt(
        Math.pow(cluster.x - p.geometry.x, 2) +
          Math.pow(cluster.y - p.geometry.y, 2)
      ) / this.view.resolution;
    var hasSameArea = true;
    if (
      this._clusterType === "Mutiple" &&
      this.singleRenderer &&
      this.singleRenderer.type == "unique-value" &&
      this.singleRenderer.field &&
      p.attributes[this.singleRenderer.field]
    ) {
      //如果有区域划分
      var hasSameArea =
        p.attributes[this.singleRenderer.field] ===
        cluster.attributes[this.singleRenderer.field]
          ? true
          : false;
    }
    if (this.view.zoom <= 8)
      this._clusterTolerance = this._clusterDistance * 20;
    if (this.view.zoom <= 10)
      this._clusterTolerance = this._clusterDistance * 10;
    if (this.view.zoom <= 12)
      this._clusterTolerance = this._clusterDistance * 5;
    else if (this.view.zoom <= 16)
      this._clusterTolerance = this._clusterDistance;
    else if (this.view.zoom <= 18) this._clusterTolerance = 10;
    else this._clusterTolerance = 5;
    return distance <= this._clusterTolerance && hasSameArea;
  }

  addClusterPoint(p, cluster) {
    var count, x, y;
    count = cluster.attributes.clusterCount;
    x = (p.geometry.x + cluster.x * count) / (count + 1);
    y = (p.geometry.y + cluster.y * count) / (count + 1);
    cluster.x = x;
    cluster.y = y;

    if (p.geometry.x < cluster.attributes.extent[0]) {
      cluster.attributes.extent[0] = p.geometry.x;
    } else if (p.geometry.x > cluster.attributes.extent[2]) {
      cluster.attributes.extent[2] = p.geometry.x;
    }
    if (p.geometry.y < cluster.attributes.extent[1]) {
      cluster.attributes.extent[1] = p.geometry.y;
    } else if (p.geometry.y > cluster.attributes.extent[3]) {
      cluster.attributes.extent[3] = p.geometry.y;
    }
    cluster.attributes.clusterAttr = `${
      cluster.attributes.clusterAttr ? cluster.attributes.clusterAttr + "," : ""
    }${p.attributes.id}`;
    cluster.attributes.clusterCount++;
    if (!p.hasOwnProperty("attributes")) {
      p.attributes = {};
    }
    p.attributes.clusterId = cluster.attributes.clusterId;
  }

  showClusters() {
    var that = this;
    var newAddGraphics = [];
    // var countArray = [];
    var objectIds = [];
    var uniqueValueInfos = [];
    for (var i = 0; i < that._clusters.length; i++) {
      var c = that._clusters[i];
      var count = parseInt(c.attributes.clusterCount);
      var size = that.getIconSize(count) ? that.getIconSize(count) : 20;
      // if (countArray.indexOf(count) == -1) {
      //   countArray.push(count);
      // }
      if (count === 1) {
        objectIds.push(c.attributes[that.objectIdField]);
        continue;
      }

      var point = new Point(c.x, c.y, that.view.spatialReference);
      newAddGraphics.push(new Graphic(point, null, c.attributes));
      if (
        that.singleRenderer &&
        that.singleRenderer.type == "unique-value" &&
        that.singleRenderer.field &&
        that.singleRenderer.uniqueValueInfos
      ) {
        if (that._clusterType == "Mutiple") {
          var hasSym = false;
          for (
            var k = 0;
            k < that.singleRenderer.uniqueValueInfos.length;
            k++
          ) {
            var info = that.singleRenderer.uniqueValueInfos[k];

            if (c.attributes[that.singleRenderer.field] === info.value) {
              var symbol = JSON.parse(JSON.stringify(info.symbol));
              symbol.symbolLayers[0].size = size;
              uniqueValueInfos.push({
                value: count + ", " + c.attributes[that.singleRenderer.field],
                symbol: symbol,
              });
              hasSym = true;
              break;
            }
          }
          if (!hasSym) {
            var defaultSys = JSON.parse(
              JSON.stringify(that._defaultClusterSym)
            );
            defaultSys.symbolLayers[0].size = size;
            uniqueValueInfos.push({
              value: count + ", " + c.attributes[that.singleRenderer.field],
              symbol: defaultSys,
              label: count,
            });
          }
        } else {
          // console.log(
          //   c.attributes.clusterCount,
          //   c.attributes[this.singleRenderer.field],
          //   "啊打发发发"
          // );
          that._clusterImgSrc
            ? uniqueValueInfos.push({
                // value: c.attributes[that.singleRenderer.field],
                // value: count,
                value: count + ", " + c.attributes[this.singleRenderer.field],
                // value: count + ", " + c.attributes[this.singleRenderer.field],
                symbol: {
                  type: "point-3d",
                  symbolLayers: [
                    {
                      type: "icon",
                      size: size,
                      resource: { href: that._clusterImgSrc },
                    },
                  ],
                },
                label: count,
              })
            : uniqueValueInfos.push({
                value: count + ", " + c.attributes[that.singleRenderer.field],
                symbol: {
                  type: "point-3d",
                  symbolLayers: [
                    {
                      type: "icon",
                      size: size,
                      material: { color: that._clusterColor },
                    },
                  ],
                },
                label: count,
              });
        }
      } else {
        that._clusterImgSrc
          ? uniqueValueInfos.push({
              value: count,
              symbol: {
                type: "point-3d",
                symbolLayers: [
                  {
                    type: "icon",
                    size: size,
                    resource: { href: that._clusterImgSrc },
                  },
                ],
              },
              label: count,
            })
          : uniqueValueInfos.push({
              value: count,
              symbol: {
                type: "point-3d",
                symbolLayers: [
                  {
                    type: "icon",
                    size: size,
                    material: { color: that._clusterColor },
                  },
                ],
              },
              label: count,
            });
      }

      // 当默认render 和 uniquerender不一样时，盖住值为0的cluster点
      uniqueValueInfos.push({
        value: 0,
        symbol: {
          type: "point-3d",
          symbolLayers: [
            {
              type: "icon",
              size: 0,
              material: { color: "rgba(0,0,0,0)" },
            },
          ],
        },
        label: "",
      });
    }
    that.currentObjectIds = objectIds;

    that.setClusterPoint(newAddGraphics, uniqueValueInfos);
  }

  setClusterPoint(newAddGraphics, uniqueValueInfos) {
    var that = this;
    if (that.curClusterGraphics.length <= 0) {
      that.isfresh = true;
      that.applyEdits({
        addFeatures: newAddGraphics,
      });
      that.updateRenderer(uniqueValueInfos);
      that.curClusterGraphics = newAddGraphics;
      that.definitionExpression = that.getObjectIdfilter();
      that.elevationInfo =
        that.view.zoom < 8
          ? {
              mode: "relative-to-ground",
              offset: that._clusterHeight,
              unit: "meters",
            }
          : that.defaultElevationInfo;
      return;
    }

    if (that.curClusterGraphics.length == newAddGraphics.length) {
      for (var k = 0; k < that.curClusterGraphics.length; k++) {
        var currentFea = that.curClusterGraphics[k];
        var hasGeo = false;
        newAddGraphics.forEach(function (refreshFea) {
          if (
            currentFea.geometry.x == refreshFea.geometry.x &&
            currentFea.geometry.y == refreshFea.geometry.y &&
            currentFea.attributes.clusterCount ==
              refreshFea.attributes.clusterCount
          ) {
            hasGeo = true;
          }
        });
        if (!hasGeo) {
          that.isfresh = true;
          that.applyEdits({
            deleteFeatures: that.curClusterGraphics,
            addFeatures: newAddGraphics,
          });
          that.updateRenderer(uniqueValueInfos);
          that.curClusterGraphics = newAddGraphics;
          that.definitionExpression = that.getObjectIdfilter();
          that.elevationInfo =
            that.view.zoom < 8
              ? {
                  mode: "relative-to-ground",
                  offset: that._clusterHeight,
                  unit: "meters",
                }
              : that.defaultElevationInfo;
          break;
        }
      }
    } else {
      that.isfresh = true;
      that.applyEdits({
        deleteFeatures: that.curClusterGraphics,
        addFeatures: newAddGraphics,
      });
      that.updateRenderer(uniqueValueInfos);
      that.curClusterGraphics = newAddGraphics;
      that.definitionExpression = that.getObjectIdfilter();
      that.elevationInfo =
        that.view.zoom < 8
          ? {
              mode: "relative-to-ground",
              offset: that._clusterHeight,
              unit: "meters",
            }
          : that.defaultElevationInfo;
    }
  }

  updateRenderer(uniqueValueInfos) {
    var that = this;
    if (
      that.singleRenderer &&
      that.singleRenderer.type == "unique-value" &&
      that.singleRenderer.uniqueValueInfos &&
      that.singleRenderer.field
    ) {
      var field2Infos = [];
      that.singleRenderer.uniqueValueInfos.forEach(function (info) {
        // console.log("-----------------", info);

        var obj = {};
        obj.value = "0, " + info.value;
        obj.symbol = info.symbol;
        if (info.symbolLayers) {
          obj.symbolLayers = info.symbolLayers;
          // obj = new Symbol(obj);
        }
        field2Infos.push(obj);
      });
      that.renderer = {
        type: "unique-value",
        field: "clusterCount",
        field2: that.singleRenderer.field,
        fieldDelimiter: ", ",
        defaultSymbol: that._defaultClusterSym,
        uniqueValueInfos: uniqueValueInfos.concat(field2Infos),
      };
    } else {
      that.renderer = {
        type: "unique-value",
        field: "clusterCount",
        defaultSymbol: that._defaultClusterSym,
        uniqueValueInfos: uniqueValueInfos,
      };
    }
    that.labelingInfo = [
      {
        //deconflictionStrategy: "none",
        labelExpressionInfo: {
          expression: "$feature.clusterCount",
        },
        symbol: that._clusterLabelSym,
        labelPlacement: that._clusterLabelPosition,
        where: "clusterCount > 1",
      },
    ];
  }

  getIconSize(count) {
    var that = this;
    var size = 40;
    var minCount = that._clusters[0].attributes.clusterCount;
    var maxCount = that._clusters[0].attributes.clusterCount;
    for (var k = 0; k < that._clusters.length; k++) {
      var num = that._clusters[k].attributes.clusterCount;
      if (minCount >= num) {
        minCount = num;
      }
      if (maxCount < num) {
        maxCount = num;
      }
    }
    if (maxCount - minCount == 0) size = that._sizeMax;
    else
      size =
        that._sizeMin +
        parseInt(
          ((count - minCount) / (maxCount - minCount)) *
            (that._sizeMax - that._sizeMin)
        );
    return size;
  }

  getObjectIdfilter() {
    var that = this;
    var where = "clusterCount>0";
    this.currentObjectIds.forEach(function (id, k) {
      where = where + " or " + that.objectIdField + "=" + id;
    });
    return where;
  }
}

/**
 *
 * @param {*} view
 * @param {} config
 * @param {[]} config.data  带有坐标的点位数组
 * @param {number} config.code  如何处理点位字段
 * @param {string} config.objectIdField  用作objectId的字段
 * @param {string} config.clusterImgSrc  聚合图像地址
 * @param {string} config.defaultImgSrc  聚合图像地址
 * @param {number} config.criticalZoom  取消聚合的级别
 *
 * @returns
 */

ClusterLayer.createClusterLayer = function (view, config) {
  const {
    data,
    code,
    objectIdField,
    defaultImgSrc,
    defaultField,
    defaultImgSize,
    clusterImgSrc,
    uniqueRenderValueArr=[],
    uniqueRenderImgSrcArr=[],
    uniqueRenderSizeArr=[],
    ...restConfig
  } = config;
  if (!data || !code || !objectIdField) {
    console.error("有必传参数未传");
    return;
  }
  if (data.length < 2) {
    const cfg = {
      code,
      objectIdField: "id", // 接口返回值：唯一的字段
      rendererIcon: {
        size: defaultImgSize, // 默认图片大小
        src: defaultImgSrc, // 默认图片src
        field: "", // 唯一值渲染字段
        uniqueValueInfos: [], //唯一值渲染匹配信息
      },
      data,
    };
    const layer= customOneFeature({...cfg});
    view.map.add(layer)
    return layer
  }

  const source = [];
  let fields = [];
  for (let i = 0; i < data.length; i++) {
    const item = data[i];
    if (!i) {
      fields = _getFields(objectIdField, data[0]);
    }

    const coordinate = _getCoordinate(code, item);
    const point = new Point({
      ...coordinate,
    });
    const graphic = new Graphic({
      geometry: point,
      attributes: {
        ...item,
      },
    });
    // console.log(graphic.attributes,'🙂')
    source.push(graphic);
  }

  const defaultCfg = {
    id: "cluster-layer",
    visible: true,
    spatialReference: view.spatialReference,
    // spatialReference: { latestWkid: 3857, wkid: 102100 },
    // source: results.features, // or url
    objectIdField: "id",
    fields: [
      // {
      //   name: "OBJECTID",
      //   alias: "OBJECTID",
      //   type: "oid",
      // },
    ],
    geometryType: "point",

    elevationInfo: {
      mode: "relative-to-ground",
    },
    isCluster: true, //通过changeCluster(bool)函数控制
    clusterImgSrc:
      clusterImgSrc ||
      "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/img/circle-cluster.png", //  聚合图像
    clusterLabelPosition: "center-center",
    clusterLabelSym: {
      type: "text",
      color: "#fff",
      font: {
        weight: "bold",
        size: "16px",
      },
    },
    renderer: {
      type: "unique-value",
      field: defaultField || "name", // 如默认点位，需要用到唯一值渲染，在这里设置字段
      defaultSymbol: {
        //默认点位样式
        type: "point-3d",
        symbolLayers: [
          // {
          //   type: "icon",
          //   size: 4,
          //   material: { color: "rgba(255,50,0,0.8)" },
          //   //resource: { href: './images/circle.png' },
          // },
          {
            type: "icon",
            size: defaultImgSize || 24,
            resource: {
              href:
                defaultImgSrc ||
                "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/img/circle-cluster.png",
            },
          },
        ],
      },
      uniqueValueInfos: uniqueRenderValueArr.length
        ? uniqueRenderValueArr.map((value, i) => {
            return {
              value,
              symbol: {
                type: "point-3d",
                symbolLayers: [
                  {
                    type: "icon",
                    size: uniqueRenderSizeArr[i] || 24,
                    resource: {
                      href:
                        uniqueRenderImgSrcArr[i] ||
                        "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/img/circle-cluster.png",
                    },
                  },
                ],
              },
              // label: count,
            };
          })
        : [],
    },
  };

  var clusterLayer = new ClusterLayer({
    ...defaultCfg,
    ...restConfig,
    source,
    objectIdField,
    fields,
    outFields: ["*"],
  });
  view.map.add(clusterLayer);
  // window.temp1 = clusterLayer;
  // console.log("开始构建实例----------");
  // num=num+1
  // if(num>1)return
  return clusterLayer;
};

/**
 * 获取对应的经纬度坐标
 * @param {*} code 接口返回的code字段
 */
function _getCoordinate(code, item) {
  const CoordinateFactory = {
    1: () => {
      const { esX, esY } = item;
      return {
        longitude: esX,
        latitude: esY,
      };
    }, // 加载地图的geojson/list接口
    3: () => {
      const { longitude, latitude } = item;
      return {
        longitude,
        latitude,
      };
    }, // 加载物联感知的list接口加载点位
    5: () => {
      const { lng } = item;
      return {
        longitude: Number(lng.split(",")[0]),
        latitude: Number(lng.split(",")[1]),
      };
    }, // 调用自己的后端的接口，带参数
  };

  if (code in CoordinateFactory) {
    return CoordinateFactory[code](item);
  } else {
    throw new Error(
      `无法从code=${code}对应接口数据获取经纬度坐标的方法，请自行扩展`
    );
  }
}

//#region  金华扩展，自定义创建要素图层
function _getFields(objectId, attributes) {
  const fields = [{ name: objectId, alias: "OBJECTID", type: "oid" }];
  for (let key in attributes) {
    if (key.toUpperCase() !== objectId) {
      if (typeof attributes[key] === "string") {
        fields.push({
          name: key,
          alias: key,
          type: "string",
        });
      } else if (typeof attributes[key] === "number") {
        if (attributes[key] % 1 == 0) {
          fields.push({
            name: key,
            alias: key,
            type: "integer",
          });
        } else {
          fields.push({
            name: key,
            alias: key,
            type: "double",
          });
        }
      }
      // 日期格式设置为Date报错？
      else if (attributes[key] instanceof Date) {
        fields.push({
          name: key,
          alias: key,
          type: "string",
        });
      }
    }
  }

  return fields;
}

function __decorate(decorators, target, key, desc) {
  var c = arguments.length,
    r =
      c < 3
        ? target
        : desc === null
        ? (desc = Object.getOwnPropertyDescriptor(target, key))
        : desc,
    d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if ((d = decorators[i]))
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}

function throttle(fn, threshhold, scope) {
  threshhold || (threshhold = 250);
  var last, deferTimer;
  return function () {
    var context = scope || this;

    var now = +new Date(),
      args = arguments;
    if (last && now < last + threshhold) {
      // hold on to it
      clearTimeout(deferTimer);
      deferTimer = setTimeout(function () {
        last = now;
        fn.apply(context, args);
      }, threshhold);
    } else {
      last = now;
      fn.apply(context, args);
    }
  };
}

function isSome(anyInput) {
  return null != anyInput;
}

// export default ClusterLayer;
export default __decorate([subclass("ClusterLayer")], ClusterLayer);

function customOneFeature(props) {
  let { code, data } = props;

    const item = data[0];
    const coordinate = _getCoordinate(code, item);
    const point = new Point({
      ...coordinate,
      spatialReference: { wkid: 4490 }
    });
    const graphic = new Graphic({
      geometry: point,
      attributes: {
        ...item,
      },
    });

  // view.graphics.add(graphic)
  // const graphicLayer = new GraphicsLayer({
  //   graphics: graphics,
  // });
  let renderer = {
    type: "simple",
    symbol: {
      type: "simple-marker",
      color: "red",
      size: "30px", // pixels
      outline: {
        color: [255, 255, 0],
        width: 10,
      },
    },
  };

  if (props?.rendererIcon) {
    const { rendererIcon } = props;
    renderer =  {
      type: "simple",
      symbol: {
        type: "point-3d",
        symbolLayers: [
          {
            type: "icon",
            size:rendererIcon.size,
            resource: {
              href:
               rendererIcon.src
            },
          },
        ],
      },
    };
  }

  const { objectIdField } = props;
  const fields = _getFields(objectIdField, data[0]);
  const layer = new FeatureLayer({
    objectIdField: objectIdField,
    outFields: ["*"],
    fields: fields,
    source: [graphic], // 使用自定义的数据源
    renderer: renderer,
    elevationInfo: {
      mode: "relative-to-ground",
      offset: 2,
      unit: "meters",
    },
   
  });
  return layer;
}
