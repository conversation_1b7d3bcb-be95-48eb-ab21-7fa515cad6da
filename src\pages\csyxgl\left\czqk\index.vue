<template>
  <div>
    <CommonTitle text="处置情况"></CommonTitle>
    <div class="chart_box">
      <div class="chart_item1">
        <CommonTitle2 text="问题处置率"></CommonTitle2>
        <div class="chartItem">
          <pie-chart
            chart-id="wtczl-chart1"
            :percentage="70"
            :color="['#00C0FF', 'rgba(0,192,255,0.7)', 'rgba(0,192,255,0.4)', 'rgba(0,192,255,0)']"
          />
          <div class="chartText blue_linear">3375个</div>
        </div>
      </div>
      <div class="chart_item">
        <CommonTitle2 text="处置时长TOP5（事件类型）"></CommonTitle2>
        <div class="wrap-container" id="chartsjzyyph"></div>
      </div>
    </div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import CommonTitle2 from '@/components/CommonTitle2'
import PieChart from '@/components/PieChart'
export default {
  name: 'index',
  components: {
    CommonTitle,
    CommonTitle2,
    PieChart,
  },
  data() {
    return {
      chartsData: [
        {
          name: '类型一',
          value: 268.25,
        },
        {
          name: '类型一',
          value: 258.15,
        },
        {
          name: '类型一',
          value: 242.32,
        },
        {
          name: '类型一',
          value: 231.54,
        },
        {
          name: '类型一',
          value: 220.9,
        },
      ],
    }
  },
  computed: {},
  mounted() {
    this.initCharts()
  },
  methods: {
    initCharts() {
      let myChart = this.$echarts.init(document.getElementById('chartsjzyyph'))
      const maxValue = Math.ceil(Math.max(...this.chartsData.map((item) => item.value)) / 100) * 100
      let option = {
        tooltip: {
          trigger: 'axis',
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          formatter: function (params) {
            return (
              params.find((item) => item.seriesName != '背景').axisValue +
              ': ' +
              params.find((item) => item.seriesName != '背景').value +
              'h'
            )
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '32',
          },
        },
        grid: {
          top: '8%',
          left: '5%',
          right: '5%',
          bottom: '-10%',
          containLabel: true,
        },
        xAxis: {
          type: 'value',
          show: false,
        },
        yAxis: {
          name: '',
          type: 'category',
          triggerEvent: false,
          inverse: true,
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
            length: 20,
          },
          axisLabel: {
            show: true,
            inside: true, // 将标签放置在坐标轴内部
            verticalAlign: 'bottom', // 垂直对齐方式
            padding: [0, 0, 20, 0], // 上右下左的内边距，调整标签位置
            textStyle: {
              color: '#FFFFFF',
              fontSize: 32,
            },
          },
          data: this.chartsData.map((item) => item.name),
        },
        series: [
          {
            type: 'bar',
            name: '',
            showBackground: true,
            backgroundStyle: {
              color: 'transparent',
            },
            itemStyle: {
              normal: {
                barBorderRadius: 30,
                color: new this.$echarts.graphic.LinearGradient(0, 0, 1, 1, [
                  { 
                    offset: 0,
                    color: 'rgba(24, 144, 255, 0.2)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(24, 144, 255, 1)',
                  },
                ]),
              },
            },
            label: {
              show: true,
              position: [460, -46],
              color: '#fff',
              formatter: function (params) {
                return params.value + 'h'
              },
              fontSize: 32,
              textStyle: {
                color: '#FFFFFF',
                fontWeight: 'normal',
                fontFamily: 'Source Han Sans CN',
              },
            },
            barWidth: 20,
            color: '#539FF7',
            data: this.chartsData.map((item) => item.value),
          },
          {
            name: '背景',
            type: 'bar',
            barWidth: 20,
            barGap: '-100%',
            data: this.chartsData.map((item) => maxValue),
            itemStyle: {
              normal: {
                barBorderRadius: 30,
                color: 'rgba(255, 255, 255, 0.20)',
              },
            },
            z: 0,
          },
        ],
      }
      myChart.setOption(option)
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
.chart_box {
  display: flex;
  margin-bottom: 40px;
  .chart_item {
    flex: 1;
    .wrap-container {
      width: 100%;
      height: 380px;
    }
  }
  .chart_item1 {
    width: 380px;
    .chartItem {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      .chartText {
        font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
        font-weight: 400;
        font-size: 49px;
        line-height: 89px;
      }
    }
  }
}
</style>