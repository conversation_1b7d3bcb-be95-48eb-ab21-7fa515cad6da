// 动态扫描

import * as externalRenderers from "@arcgis/core/views/3d/externalRenderers.js";
import { libRoot } from "../config.js";

var THREE = window.THREE_r116;

class PyramidLayer {
  constructor(view, data, color = "#ff0000") {
    this.view = view;
    this.data = data;
    this.renderObject = [];
    this.angle = 0;
    this.isUp = true;
    this.color = color;
  }
  setup(context) {
    this.renderer = new THREE.WebGLRenderer({
      context: context.gl, // 可用于将渲染器附加到已有的渲染环境(RenderingContext)中
      premultipliedAlpha: false, // renderer是否假设颜色有 premultiplied alpha. 默认为true
    });
    this.renderer.setPixelRatio(window.devicePixelRatio); // 设置设备像素比。通常用于避免HiDPI设备上绘图模糊
    this.renderer.setViewport(0, 0, this.view.width, this.view.height); // 视口大小设置
    // this.renderer.setSize(context.camera.fullWidth, context.camera.fullHeight);

    // Make sure it does not clear anything before rendering
    this.renderer.autoClear = false;
    this.renderer.autoClearDepth = false;
    this.renderer.autoClearColor = false;
    // this.renderer.autoClearStencil = false;

    // The ArcGIS JS API renders to custom offscreen buffers, and not to the default framebuffers.
    // We have to inject this bit of code into the three.js runtime in order for it to bind those
    // buffers instead of the default ones.
    var originalSetRenderTarget = this.renderer.setRenderTarget.bind(
      this.renderer
    );
    this.renderer.setRenderTarget = function (target) {
      originalSetRenderTarget(target);
      if (target == null) {
        context.bindRenderTarget();
      }
    };

    this.scene = new THREE.Scene();
    // setup the camera
    var cam = context.camera;
    this.camera = new THREE.PerspectiveCamera(
      cam.fovY,
      cam.aspect,
      cam.near,
      cam.far
    );

    // 添加坐标轴辅助工具
    const axesHelper = new THREE.AxesHelper(1000);
    // var cenP = [];
    // externalRenderers.toRenderCoordinates(
    //   this.view,
    //   this.position,
    //   0,
    //   this.view.spatialReference,
    //   cenP,
    //   0,
    //   1
    // );
    // axesHelper.position.set(cenP[0], cenP[1], cenP[2]);
    this.scene.add(axesHelper);

    // setup scene lighting
    this.ambient = new THREE.AmbientLight(0xffffff, 0.5);
    this.scene.add(this.ambient);
    this.sun = new THREE.DirectionalLight(0xffffff, 0.5);
    this.sun.position.set(-600, 300, 60000);
    this.scene.add(this.sun);
    //var geometrys = this.getCoords(context);
    //var canvas = this.produceCanvas();
    this.clock = new THREE.Clock();
    for (let i = 0; i < this.data.length; i++) {
      const item = this.data[i];
      const renderObject = this.getGeometry(item);
      this.renderObject.push(renderObject);
    }

    // 添加点击事件
    view.on("click", (e) => {
      const { x, y, z } = e.mapPoint;
      var originP = [];
      externalRenderers.toRenderCoordinates(
        this.view,
        [x, y, -1000],
        0,
        this.view.spatialReference,
        originP,
        0,
        1
      );

      var directionP = [];
      externalRenderers.toRenderCoordinates(
        this.view,
        [x, y, 10000],
        0,
        this.view.spatialReference,
        directionP,
        0,
        1
      );
      const raycaster = new THREE.Raycaster();
      //射线原点
      const rayOrigin = new THREE.Vector3(originP[0], originP[1], originP[2]);
      //射线方向
      const rayDirection = new THREE.Vector3(
        directionP[0],
        directionP[1],
        directionP[2]
      );
      raycaster.set(rayOrigin, rayDirection);
      const intersectObject = raycaster.intersectObjects(
        this.renderObject,
        true
      );
      console.log(intersectObject);
    });

    context.resetWebGLState();
  }
  render(context) {
    var cam = context.camera;
    //需要调整相机的视角
    this.camera.position.set(cam.eye[0], cam.eye[1], cam.eye[2]);
    this.camera.up.set(cam.up[0], cam.up[1], cam.up[2]);
    this.camera.lookAt(
      new THREE.Vector3(cam.center[0], cam.center[1], cam.center[2])
    );
    // Projection matrix can be copied directly
    this.camera.projectionMatrix.fromArray(cam.projectionMatrix);
    // update lighting
    /////////////////////////////////////////////////////////////////////////////////////////////////////
    // view.environment.lighting.date = Date.now();
    var l = context.sunLight;
    this.sun.position.set(l.direction[0], l.direction[1], l.direction[2]);
    this.sun.intensity = l.diffuse.intensity;
    this.sun.color = new THREE.Color(
      l.diffuse.color[0],
      l.diffuse.color[1],
      l.diffuse.color[2]
    );
    this.ambient.intensity = l.ambient.intensity;
    this.ambient.color = new THREE.Color(
      l.ambient.color[0],
      l.ambient.color[1],
      l.ambient.color[2]
    );

    //动画效果
    if (this._uniforms) {
      this._uniforms.time.value = this._uniforms.time.value + 0.005;
    }

    if (this.renderObject.length > 0) {
      for (let i = 0; i < this.renderObject.length; i++) {
        const item = this.renderObject[i];
        if (item.isUp) {
          item.currentStep += 1;
          item.translateOnAxis(new THREE.Vector3(0, 0, 1), -4);
          if (item.currentStep > 200 / 4) {
            item.isUp = false;
          }
        } else {
          item.currentStep -= 1;
          item.translateOnAxis(new THREE.Vector3(0, 0, 1), 4);
          if (item.currentStep < 1) {
            item.isUp = true;
          }
        }
      }
    }
    if (this.angle) {
      this.angle = this.angle + 0.05;
    }

    // draw the scene
    /////////////////////////////////////////////////////////////////////////////////////////////////////
    this.renderer.state.reset();
    this.renderer.render(this.scene, this.camera);
    // as we want to smoothly animate the ISS movement, immediately request a re-render
    externalRenderers.requestRender(this.view);
    // cleanup
    context.resetWebGLState();
  }
  getGeometry({ position, radius, height, edges = { show: true,  } }) {
    var cenP = [];
    externalRenderers.toRenderCoordinates(
      this.view,
      [position[0], position[1], position[2] + height / 2],
      0,
      this.view.spatialReference,
      cenP,
      0,
      1
    );
    let geometry = new THREE.CylinderGeometry(0, radius, height, 4);
    geometry.rotateX(Math.PI / 2);
    geometry.computeBoundingSphere();

    const renderObject = new THREE.Object3D();
    const material = new THREE.MeshBasicMaterial({
      color: new THREE.Color(this.color),
      side: THREE.DoubleSide,
      transparent: true,
      // opacity: 0.2,
    });

    var mesh = new THREE.Mesh(geometry, material);
    renderObject.add(mesh);
    renderObject.position.set(cenP[0], cenP[1], cenP[2]);
    if (edges.show) {
      let cubeEdges = new THREE.EdgesGeometry(geometry, edges.size||1);
      let edgesMtl = new THREE.LineBasicMaterial({ color: edges.color||0x000000 });
      let cubeLine = new THREE.LineSegments(cubeEdges, edgesMtl);
      renderObject.add(cubeLine)
    }
    this.scene.add(renderObject);

    // 调整柱体位置
    const length = Math.sqrt(
      cenP[0] * cenP[0] + cenP[1] * cenP[1] + cenP[2] * cenP[2]
    );
    var stt = new THREE.Vector3(...cenP.map((e) => e / length));
    renderObject.lookAt(stt);
    renderObject.initZ = cenP[2];
    renderObject.currentStep = 0;
    renderObject.isUp = true;
    return renderObject;
  }
  getMaterial() {
    this._uniforms = {
      dtPyramidTexture: {
        value: new THREE.TextureLoader().load(`${libRoot}img/test-4.png`),
      },
      time: {
        value: 0.0,
      },
      uColor: {
        value: new THREE.Color("#5588aa"),
      },
    };
    let shader = this.getShaderStr();
    let material = new THREE.ShaderMaterial({
      uniforms: this._uniforms,
      vertexShader: shader.vs,
      fragmentShader: shader.fs,
      transparent: true,
      side: THREE.DoubleSide,
    });
    return material;
  }
  getShaderStr() {
    let shader = { vs: "", fs: "" };

    shader.vs =
      "varying vec2 vUv;\n" +
      "void main(){\n" +
      "vUv = uv;\n" +
      "gl_Position = projectionMatrix*viewMatrix*modelMatrix*vec4( position, 1.0 );\n" +
      "}\n";

    shader.fs =
      "uniform float time;\n" +
      "varying vec2 vUv;\n" +
      "uniform sampler2D dtPyramidTexture;\n" +
      "uniform vec3 uColor;\n" +
      "void main() {\n" +
      " vec2 st = vUv;\n" +
      " vec4 colorImage = texture2D(dtPyramidTexture, vec2(vUv.x,fract(vUv.y-time)));\n" +
      //'float alpha=mix(0.1,1.0,clamp((1.0-vUv.y) * uColor.a,0.0,1.0)) +(1.0-sign(vUv.y-time*0.001))*0.2*(1.0-colorImage.r);\n'+
      "vec3 diffuse =(1.0-colorImage.a)*vec3(0.8,1.0,0.0)+colorImage.rgb*vec3(0.8,1.0,0);\n" +
      "gl_FragColor = vec4(diffuse,0.7);\n" +
      "}\n";
    return shader;
  }
}

export default PyramidLayer;
