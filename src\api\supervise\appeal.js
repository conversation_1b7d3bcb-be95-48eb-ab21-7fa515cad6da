import {request} from '@/utils/request'

// 查询申诉列表
export function listAppeal(query) {
  return request({
    url: '/business/appeal/record/list',
    method: 'get',
    params: query
  })
}

// 查询申诉详细
export function getAppeal(id) {
  return request({
    url: '/business/appeal/record/' + id,
    method: 'get'
  })
}

// 新增申诉
export function addAppeal(data) {
  return request({
    url: '/business/appeal/record/add',
    method: 'post',
    data: data
  })
}

// 修改申诉
export function updateAppeal(data) {
  return request({
    url: '/business/appeal/record/edit',
    method: 'post',
    data: data
  })
}

// 删除申诉
export function delAppeal(id) {
  return request({
    url: '/business/appeal/record/remove/' + id,
    method: 'post'
  })
}

// 导出申诉
export function exportAppeal(query) {
  return request({
    url: '/business/appeal/record/export',
    method: 'get',
    params: query
  })
}

