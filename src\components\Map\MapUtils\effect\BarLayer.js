// 动态扫描

import * as externalRenderers from "@arcgis/core/views/3d/externalRenderers.js";
var THREE = window.THREE_r116;

class BarLayer {
  constructor({ view, data }) {
    this.view = view;
    this.data = data;
  }

  setup(context) {
    this.renderer = new THREE.WebGLRenderer({
      context: context.gl, // 可用于将渲染器附加到已有的渲染环境(RenderingContext)中
      premultipliedAlpha: false, // renderer是否假设颜色有 premultiplied alpha. 默认为true
    });
    this.renderer.setPixelRatio(window.devicePixelRatio); // 设置设备像素比。通常用于避免HiDPI设备上绘图模糊
    this.renderer.setViewport(0, 0, view.width, view.height); // 视口大小设置

    // 防止Three.js清除ArcGIS JS API提供的缓冲区。
    this.renderer.autoClearDepth = false; // 定义renderer是否清除深度缓存
    this.renderer.autoClearStencil = false; // 定义renderer是否清除模板缓存
    this.renderer.autoClearColor = false; // 定义renderer是否清除颜色缓存

    // ArcGIS JS API渲染自定义离屏缓冲区，而不是默认的帧缓冲区。
    // 我们必须将这段代码注入到three.js运行时中，以便绑定这些缓冲区而不是默认的缓冲区。
    const originalSetRenderTarget = this.renderer.setRenderTarget.bind(
      this.renderer
    );
    this.renderer.setRenderTarget = function (target) {
      originalSetRenderTarget(target);
      if (target == null) {
        // 绑定外部渲染器应该渲染到的颜色和深度缓冲区
        context.bindRenderTarget();
      }
    };

    this.scene = new THREE.Scene(); // 场景
    this.camera = new THREE.PerspectiveCamera(); // 相机

    // 添加坐标轴辅助工具
    const axesHelper = new THREE.AxesHelper(6371000);
    this.scene.add(axesHelper);

    // setup scene lighting
    this.ambient = new THREE.AmbientLight(0xffffff, 0.5);
    this.scene.add(this.ambient);
    this.sun = new THREE.DirectionalLight(0xffffff, 0.5);
    this.sun.position.set(-600, 300, 60000);
    this.scene.add(this.sun);

    // 添加柱体
    for (let i = 0; i < this.data.length; i++) {
      const item = this.data[i];
      this._addBarToMap(item);
    }

    context.resetWebGLState();
  }
  _addBarToMap({ position, size, color }) {
    const geometry = new THREE.BoxGeometry(size[0], size[1], size[2]); // 柱体的[宽度，高度，深度]
    const material = new THREE.MeshBasicMaterial({
      color: new THREE.Color(color),
    });
    const cube = new THREE.Mesh(geometry, material);
    var cenP = [0, 0, 0];
    externalRenderers.toRenderCoordinates(
      this.view,
      [position[0], position[1], position[2] + size[2]/2],
      0,
      this.view.spatialReference,
      cenP,
      0,
      1
    );
    cube.position.set(cenP[0], cenP[1], cenP[2]);
    this.cube = cube;
    this.scene.add(cube);

    // 调整柱体位置
    const length = Math.sqrt(
      cenP[0] * cenP[0] + cenP[1] * cenP[1] + cenP[2] * cenP[2]
    );
    var stt = new THREE.Vector3(...cenP.map((e) => e / length));
    this.cube.lookAt(stt);
  }
  render(context) {
    // 更新相机参数
    const cam = context.camera;
    this.camera.position.set(cam.eye[0], cam.eye[1], cam.eye[2]);
    this.camera.up.set(cam.up[0], cam.up[1], cam.up[2]);
    this.camera.lookAt(
      new THREE.Vector3(cam.center[0], cam.center[1], cam.center[2])
    );
    // 投影矩阵可以直接复制
    this.camera.projectionMatrix.fromArray(cam.projectionMatrix);
    // update lighting
    /////////////////////////////////////////////////////////////////////////////////////////////////////
    // view.environment.lighting.date = Date.now();
    var l = context.sunLight;
    this.sun.position.set(l.direction[0], l.direction[1], l.direction[2]);
    this.sun.intensity = l.diffuse.intensity;
    this.sun.color = new THREE.Color(
      l.diffuse.color[0],
      l.diffuse.color[1],
      l.diffuse.color[2]
    );
    this.ambient.intensity = l.ambient.intensity;
    this.ambient.color = new THREE.Color(
      l.ambient.color[0],
      l.ambient.color[1],
      l.ambient.color[2]
    );

    this.renderer.state.reset();
    this.renderer.render(this.scene, this.camera);

    // 请求重绘视图。
    externalRenderers.requestRender(view);
    // cleanup
    context.resetWebGLState();
  }
}

export default BarLayer;
