<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" :inline="true" label-width="100px">
      <el-form-item label="代班日期">
        <el-date-picker
          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px;"
          value-format="yyyy-MM-dd hh:mm:ss"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item label="被代班人部门">
        <el-select v-model="queryParams.substituteDeptName" placeholder="请选择被代班人部门" clearable size="small">
          <el-option
            v-for="(deptItem, deptIndex) in deptOptions"
            :key="deptIndex"
            :label="deptItem"
            :value="deptItem"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="关键词">
        <el-input v-model="queryParams.searchValue" type="text" placeholder="请输入内容" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:record:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >
          新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:record:edit']"
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >
          修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:record:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-col> -->
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="recordList">
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <!-- <el-table-column label="申请理由" align="center" prop="title" /> -->
      <el-table-column label="代班人" align="center" prop="userName" />
      <el-table-column label="被代班人" align="center" prop="substituteUserName" />
      <el-table-column label="被代班人部门" align="center" prop="substituteDeptName" />
      <el-table-column label="代班日期" align="center" prop="happenTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.happenTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="审批人名称" align="center" prop="approveUserName" />
      <el-table-column label="审批意见" align="center" prop="approveType">
        <template slot-scope="scope">
          <span>{{ scope.row.approveType==0?'拒绝':'同意' }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="审批理由" align="center" prop="approveReason" /> -->
      <!-- 1-新建 2-审批中 9-完结 -->
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <span>{{ scope.row | statusName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['business:record:edit']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >
            审批
          </el-button>
          <!-- <el-button
            v-hasPermi="['business:record:remove']"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button> -->
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改代班申请记录对话框 -->
    <div>
      <el-dialog class="m-dialog" :title="title" :visible.sync="open">
        <el-scrollbar style="height: 100%;">
          <el-row :gutter="15" style="margin-right: 10px;">
            <el-form ref="form" v-loading="formLoading" :model="form" :rules="rules" label-width="100px" :disabled="formDisabled">
              <h3 class="title">代班信息</h3>
              <el-col :span="12">
                <el-form-item label="代班人" prop="userName">
                  <el-input v-model="form.userName" disabled placeholder="请输入代班人" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="被代班人" prop="substituteUserName">
                  <el-input v-model="form.substituteUserName" disabled placeholder="请输入被代班人" />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="被代班人部门" prop="substituteDeptName">
                  <el-input v-model="form.substituteDeptName" disabled placeholder="被代班人部门" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="代班日期" prop="happenTime">
                  <el-date-picker
                    v-model="form.happenTime"
                    clearable
                    size="small"
                    type="date"
                    disabled
                    value-format="yyyy-MM-dd"
                    placeholder="选择代班日期"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="申请理由" prop="title">
                  <el-input v-model="form.title" type="textarea" disabled placeholder="请输入申请理由" />
                </el-form-item>
              </el-col>

              <h3 class="title">审批信息</h3>
              <el-col :span="24">
                <el-form-item label="审批人名称" prop="approveUserName">
                  <el-input v-model="form.approveUserName" disabled placeholder="请输入审批人名称" />
                </el-form-item>
              </el-col>
              <!-- <el-col :span="12">
                <el-form-item label="类型 0-拒绝 1-通过" prop="approveType">
                  <el-select v-model="form.approveType" placeholder="请选择类型 0-拒绝 1-通过" :style="{ width: '100%' }">
                    <el-option label="请选择字典生成" value="" />
                  </el-select>
                </el-form-item>
              </el-col> -->
              <el-col :span="24">
                <el-form-item label="审批理由" prop="approveReason">
                  <el-input v-model="form.approveReason" type="textarea" placeholder="请输入审批理由" />
                </el-form-item>
              </el-col>
            </el-form>
          </el-row>
        </el-scrollbar>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" :loading="formLoading" @click="submitForm(0)">驳 回</el-button>
          <el-button type="primary" :loading="formLoading" @click="submitForm(1)">同 意</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { listRecord, getRecord, delRecord, addRecord, updateRecord, exportRecord } from '@/api/supervise/substituteApproval'
import { userList as deptList} from '@/api/system/dict/type'

export default {
  name: 'Record',
  components: {
  },
  filters: {
    statusName(item) {
      if (item.status == 1) return '新建'
      if (item.status == 2) return '待审批'
      const statusObj = { 0: '驳回', 1: '同意' }
      if (item.approveType) return statusObj[item.approveType]
    }

  },
  data() {
    return {
      formLoading: false,
      formDisabled: false,

      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 代班申请记录表格数据
      recordList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        searchValue: '',
        dateRange: [],
        substituteDeptId: null,
        substituteDeptName: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        // happenTime: [
        //   { required: true, message: '发生时间不能为空', trigger: 'blur' }
        // ],

      },
      deptOptions: [],
      typeList: 1
    }
  },
  created() {
    this.getList()
    this.getDeptNameOptions()
  },
  methods: {
    // 网格小组选项
    getDeptNameOptions() {
      deptList({ typeList: this.typeList }).then(res => {
        this.deptOptions = res.data.map(item => item.label)
      })
    },
    /** 查询代班申请记录列表 */
    getList() {
      this.loading = true
      const { dateRange, searchValue, pageNum, pageSize, substituteDeptName } = this.queryParams
      let params = { pageNum, pageSize, status: 2 }
      if (dateRange && dateRange.length) params = { ...params, searchStartTime: dateRange[0], searchEndTime: dateRange[1] }
      if (searchValue) params = { ...params, searchValue }
      if (substituteDeptName) params = { ...params, substituteDeptName }

      listRecord(params).then(response => {
        this.recordList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        title: null,
        userId: null,
        userName: null,
        substituteUserId: null,
        substituteUserName: null,
        substituteDeptId: null,
        substituteDeptName: null,
        happenTime: null,
        startTime: null,
        endTime: null,
        approveUserId: this.$store.getters.uid,
        approveUserName: this.$store.getters.nickName,
        approveType: null,
        approveReason: null,
        type: null,
        status: '0',
        delFlag: null,
        deptId: null,
        deptName: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        searchValue: '',
        dateRange: [],
        substituteDeptId: null,
        substituteDeptName: null
      },
      this.handleQuery()
    },
    // 多选框选中数据
    // handleSelectionChange(selection) {
    //   this.ids = selection.map(item => item.id)
    //   this.single = selection.length !== 1
    //   this.multiple = !selection.length
    // },
    /** 新增按钮操作 */
    // handleAdd() {
    //   this.reset()
    //   this.open = true
    //   this.title = '添加代班申请记录'
    // },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      this.formLoading = true
      getRecord(id).then(response => {
        this.form = {...this.form, ...response.data}
        this.open = true
        this.title = '代班审批'
        this.formLoading = false
      }).catch(() => this.formLoading = false)
    },
    /** 提交按钮 */
    submitForm(type) {
      if (this.$store.getters.postKeys.includes('dckkz') || this.$store.getters.postKeys.includes('dczl') || this.$store.getters.postKeys.includes('dckzy')) {
        const name = {1: '通过', 0: '驳回'}
        this.$confirm(`是否${name[type]}此申请？`, '提示', { type: 'warning' }).then(() => {
          this.$refs['form'].validate(valid => {
            if (valid) {
              let params = {...this.form, approveType: type, status: 9}
              this.formLoading = true
              if (this.form.id != null) {
                updateRecord(params).then(() => {
                  this.msgSuccess('修改成功')
                  this.open = false
                  this.getList()
                }).catch(() => this.formLoading = false)
              } else {
                addRecord(params).then(() => {
                  this.msgSuccess('新增成功')
                  this.open = false
                  this.getList()
                }).catch(() => this.formLoading = false)
              }
            }
          })
        }).catch(() => {})
      } else {
        return this.msgError('您不是督查人，没有操作权限！')
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$confirm('是否确认删除代班申请记录编号为"' + ids + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return delRecord(ids)
      }).then(() => {
        this.getList()
        this.msgSuccess('删除成功')
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams
      this.$confirm('是否确认导出所有代班申请记录数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return exportRecord(queryParams)
      }).then(response => {
        this.download(response.msg)
      })
    }
  }
}
</script>
