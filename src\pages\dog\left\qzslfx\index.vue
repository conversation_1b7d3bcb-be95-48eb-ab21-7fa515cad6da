<template>
  <div>
    <CommonTitle text='犬只数量分析'>
      <TabSwitch
        :tabList="list"
        :activeIndex="index"
        @tab-change="handleTabChange"
      />
    </CommonTitle>
    <div class='wrap-container'>
      <TabSwitch
        class='tab-wraper'
        :type='2'
        :tabList="list2"
        :activeIndex="index2"
        @tab-change="handleTabChange2"
      />
      <div class='slfxChart' id='slfxChart'></div>
    </div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import TabSwitch from '@/components/TabSwitch'
import * as echarts from 'echarts'

export default {
  name: 'index',
  components: {
    CommonTitle,
    TabSwitch
  },
  data() {
    return {
      index: 0,
      index2: 0,
      list: [{name:"本月",value:"1"},{name:"本季",value:"2"},{name:"本年",value:"3"}],
      list2: [{name:"登记数",value:"1"},{name:"办证数",value:"2"},{name:"收容数",value:"3"},{name:"免疫到期",value:"4"}],
      chartsData: [
        {
          name: "9月",
          value: 2800
        },
        {
          name: "10月",
          value: 3800
        },
        {
          name: "11月",
          value: 2000
        },
        {
          name: "12月",
          value: 3000
        }
      ]
    }
  },
  computed: {},
  mounted() {
    setTimeout(() => {
      this.initCharts()
    }, 300)
  },
  methods: {
    handleTabChange(i) {
      this.index = i
    },
    handleTabChange2(i) {
      this.index2 = i
    },
    initCharts() {
      const chartDom = document.getElementById('slfxChart')
      const myChart = echarts.init(chartDom)

      const option = {
        grid: {
          left: '7%',
          right: '7%',
          bottom: '5%',
          top: '14%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.chartsData.map(item => item.name),
          axisLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.6)'
            }
          },
          axisLabel: {
            textStyle: {
              fontSize: 24
            }
          },
        },
        yAxis: {
          name: "单位(只)",
          nameTextStyle: {
            color: 'rgba(255,255,255,0.6)',
            padding: [20,10,10,10],
            fontSize: 24
          },
          type: 'value',
          axisLabel: {
            textStyle: {
              fontSize: 24
            }
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.6)'
            }
          },
          splitLine: {
            lineStyle: {
              color: '#4A6C89'
            }
          }
        },
        series: [{
          data: this.chartsData.map(item => item.value),
          type: 'line',
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0,
                color: 'rgba(0,255,255,0.3)'
              }, {
                offset: 1,
                color: 'rgba(0,255,255,0)'
              }]
            }
          },
          lineStyle: {
            color: '#00ffff'
          },
          smooth: true,
          symbol: 'none'
        }]
      }

      myChart.setOption(option)

      window.addEventListener('resize', () => {
        myChart.resize()
      })
    }
  },
  watch: {}
}
</script>

<style scoped lang='less'>
  .wrap-container {
    width: 100%;
    height: 600px;
    .tab-wraper {
      margin: 40px;
    }
    .slfxChart {
      width: 100%;
      height: 480px;
    }
  }
</style>