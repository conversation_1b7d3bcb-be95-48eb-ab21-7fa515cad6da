<template>
  <div class="rightBtn">
    <!-- 收缩 -->
    <p class="middleBtn066" title="收缩" @click="hideAndShowIcon">
      <img
        :src="
          showHide ? require('@/assets/tcgl/main_mapIcon/展开.png') : require('@/assets/tcgl/main_mapIcon/收起.png')
        "
        alt=""
      />
    </p>
    <!-- 初始视角 -->
    <p class="middleBtn066" @click="mapNow" title="初始视角">
      <img src="@/assets/tcgl/main_mapIcon/地图复位b.png" alt="" />
    </p>
    <!-- 切换底图 -->
    <!-- <p class="middleBtn066" @click="openIframeMap" title="切换底图">
      <img
        :src="
          trunMapkuai
            ? require('@/assets/tcgl/main_mapIcon/地图切换b选中.png')
            : require('@/assets/tcgl/main_mapIcon/地图切换b.png')
        "
        alt=""
      />
    </p> -->
    <!-- 路况信息 -->
    <p class="middleBtn066" @click="openWid" title="路况信息" v-show="hideOther">
      <img
        :src="
          showRoad
            ? require('@/assets/tcgl/main_mapIcon/路况信息b选中.png')
            : require('@/assets/tcgl/main_mapIcon/路况信息b.png')
        "
        alt=""
      />
    </p>
    <!-- 区域查询 -->
    <!-- <p class="middleBtn066" @click="openCount" title="区域查询">
      <img
        :src="
          showCount
            ? require('@/assets/tcgl/main_mapIcon/区域查询b选中.png')
            : require('@/assets/tcgl/main_mapIcon/区域查询b.png')
        "
        alt=""
      />
    </p> -->
    <!-- 云查地 -->
    <!-- <p class="middleBtn066" @click="openYCD" title="云查地" v-show="hideOther">
      <img src="@/assets/tcgl/main_mapIcon/云查地b.png" alt="" />
    </p> -->
    <!-- 搜索 -->
    <!-- <p class="middleBtn066" @click="searchFun" title="搜索">
      <img
        :src="
          searchClick
            ? require('@/assets/tcgl/main_mapIcon/搜索b选中.png')
            : require('@/assets/tcgl/main_mapIcon/搜索b.png')
        "
        alt=""
      />
    </p> -->
    <!-- 路径规划 -->
    <!-- <p class="middleBtn066" @click="openRoute" title="路径规划" v-show="hideOther">
      <img
        :src="
          routeClick
            ? require('@/assets/tcgl/main_mapIcon/route_a.png')
            : require('@/assets/tcgl/main_mapIcon/route.png')
        "
        alt=""
      />
    </p> -->
    <!-- 测量 -->
    <!-- <p class="middleBtn066" @click="openMapTools('测量')" title="测量" v-show="hideOther">
      <img
        :src="
          showMapToolIndex == '测量'
            ? require('@/assets/tcgl/main_mapIcon/测量工具b选中.png')
            : require('@/assets/tcgl/main_mapIcon/测量工具b.png')
        "
        alt=""
      />
    </p> -->
    <!-- 标绘 -->
    <!-- <p class="middleBtn066" @click="openMapTools('标绘')" title="标绘">
      <img
        :src="
          showMapToolIndex == '标绘'
            ? require('@/assets/tcgl/main_mapIcon/绘图工具b选中.png')
            : require('@/assets/tcgl/main_mapIcon/绘图工具b.png')
        "
        alt=""
      />
    </p> -->
    <!-- 空间分析 -->
    <!-- <p class="middleBtn066" @click="openMapTools('空间分析')" title="空间分析" v-show="hideOther">
      <img
        :src="
          showMapToolIndex == '空间分析'
            ? require('@/assets/tcgl/main_mapIcon/空间分析b选中.png')
            : require('@/assets/tcgl/main_mapIcon/空间分析b.png')
        "
        alt=""
      />
    </p> -->
  </div>
</template>

<script>
import mapService from '../index.js'
import { indexApi } from '@/api/indexApi'
import coordtransform from '@/utils/coordtransform.js'
export default {
  name: 'rightIndex',
  components: { },
  data() {
    return {
      hideOther: true,
      distance: null,
      // mapin_change: 7, //卫星影像1 矢量地图7
      showHide: false,
      trunMapkuai: false,
      click2D: false,
      showRoad: false,
      showHot: false,
      showCount: false,
      routeClick: false,
      roadData: [],
      searchClick: false,
      url: window.location.href,
      showToolbar: false,
      showMapToolIndex: null,
      videoMapId: [],
      timeOut: null,
      moveRight: null,
      cameraList: [
        {
          name: '枪机在线',
          code: 'camera-zx-qiangji',
        },
        {
          name: '枪机离线',
          code: 'camera-lx-qiangji',
        },
        {
          name: '球机在线',
          code: 'camera-zx-qiuji',
        },
        {
          name: '球机离线',
          code: 'camera-lx-qiuji',
        },
        {
          name: '半球机在线',
          code: 'camera-zx-banqiu',
        },
        {
          name: '半球机离线',
          code: 'camera-lx-banqiu',
        },
        {
          name: '高点在线',
          code: 'camera-zx-gaodian',
        },
        {
          name: '高点离线',
          code: 'camera-lx-gaodian',
        },
      ],
    }
  },
  mounted() {

  },
  methods: {
    hideAndShowIcon() {
      this.showHide = !this.showHide
      if (!this.showHide) {
        document.getElementsByClassName('rightBtn')[0].style.height = '50px'
      } else {
        document.getElementsByClassName('rightBtn')[0].style.height = '660px'
      }
    },
    // 路径规划 弹窗
    openRoute() {
      // if (this.showToolbar) this.openTool()
      if (this.showCount) this.openCount()
      if (this.showMapTools) this.openMapTools()
      if (this.searchClick) this.searchFun()
      this.routeClick = !this.routeClick
      let moveLeft = '1870px'
      if (
        window.parent.document
          .getElementsByClassName('index_main_mapIcon')[0]
          .getAttribute('class')
          .indexOf('map_mapIcon_move') > -1
      ) {
        moveLeft = '2885px'
      }
      if (this.routeClick) {
        // this.hideIframe();
        window.parent.lay.openIframe({
          type: 'openIframe',
          name: 'best_route',
          src: baseURL.url + '/static/citybrain/tcgl/commont/best_route.html',
          width: '801px',
          height: '970px',
          left: moveLeft,
          top: '405px',
          zIndex: 999,
        })
      } else {
        window.parent.postMessage(
          JSON.stringify({
            type: 'closeIframe',
            name: 'best_route',
          }),
          '*'
        )
        window.parent.lay.closeIframeByNames(['best_route'])
        try {
          window.parent.frames['best_route'].postMessage({ clear_route: '清除路线' }, '*')
        } catch {}
      }
    },
    openMapTools(toolName) {
      if (this.searchClick) this.searchFun()
      // if (this.showToolbar) this.openTool()
      if (this.showCount) this.openCount()
      if (this.routeClick) this.openRoute()
      // this.showMapTools = !this.showMapTools
      let close = JSON.stringify({
        type: 'closeIframe',
        name: 'map_toolbar',
      })
      let moveLeft = '2170px'
      if (
        window.parent.document
          .getElementsByClassName('index_main_mapIcon')[0]
          .getAttribute('class')
          .indexOf('map_mapIcon_move') > -1
      ) {
        moveLeft = '3185px'
      }
      if (!toolName) {
        this.showMapToolIndex = null
        this.showMapTools = false
        window.parent.postMessage(close, '*')
        window.parent.lay.closeIframeByNames(['map_toolbar'])
        return
      }
      if (this.showMapToolIndex == toolName) {
        this.showMapToolIndex = null
        this.showMapTools = false
        window.parent.postMessage(close, '*')
        window.parent.lay.closeIframeByNames(['map_toolbar'])
      } else if (toolName) {
        this.showMapToolIndex = toolName
        this.showMapTools = true
        let iframe1 = {
          type: 'openIframe',
          name: 'map_toolbar',
          src: baseURL.url + '/static/citybrain/tcgl/commont/map_toolbar.html',
          width: '500px',
          height: '1020px',
          left: moveLeft,
          top: '405px',
          zIndex: 997,
          argument: {
            type: 'map_toolbar',
            data: { toolName },
          },
        }
        window.parent.postMessage(close, '*')
        window.parent.lay.closeIframeByNames(['map_toolbar'])
        setTimeout(() => {
          window.parent.lay.openIframe(iframe1)
        }, 400)
      }
    },
    // 绘制区域查询人数
    openCount() {
      let that = this
      if (that.searchClick) that.searchFun()
      // if (that.showToolbar) that.openTool()
      if (this.routeClick) this.openRoute()
      if (this.showMapTools) this.openMapTools()
      that.showCount = !that.showCount
      if (that.showCount) {
        // this.hideIframe();
        // mapService.plotTool.active('polygon', (oncomplete) => {
        //   that.createWLGZ(oncomplete)
        //   // that.getCode(oncomplete)
        // })
        // 绘制面
        mapService.drawGeometry('polygon', (oncomplete) => {
          that.createWLGZ(oncomplete)
          // that.getCode(oncomplete)
        });
      } else {
        // 清除绘制
        mapService.cleardrawGeometry();
      }
    },
    //打开工具栏
    openTool() {
      if (this.searchClick) this.searchFun()
      if (this.showCount) this.openCount()
      if (this.routeClick) this.openRoute()
      if (this.showMapTools) this.openMapTools()
      this.showToolbar = !this.showToolbar
      let moveLeft = '2170px'
      if (
        window.parent.document
          .getElementsByClassName('index_main_mapIcon')[0]
          .getAttribute('class')
          .indexOf('map_mapIcon_move') > -1
      ) {
        moveLeft = '3185px'
      }
      let moveLeftNum = this.moveRight ? this.moveRight - 500 + 'px' : moveLeft
      if (this.showToolbar) {
        let iframe1 = {
          type: 'openIframe',
          name: 'main_toolbar',
          src: baseURL.url + '/static/citybrain/tcgl/commont/index_toolbar.html',
          width: '500px',
          height: '600px',
          left: moveLeftNum,
          top: '405px',
          zIndex: 997,
        }
        window.parent.lay.openIframe(iframe1)
      } else {
        window.parent.postMessage(
          JSON.stringify({
            type: 'closeIframe',
            name: 'main_toolbar',
          }),
          '*'
        )
        window.parent.lay.closeIframeByNames(['main_toolbar'])
      }
    },
    // 打开搜索弹窗
    searchFun() {
      // if (this.showToolbar) this.openTool()
      if (this.showCount) this.openCount()
      if (this.routeClick) this.openRoute()
      if (this.showMapTools) this.openMapTools()
      this.searchClick = !this.searchClick
      let left = '2420px'
      // 判断左右页面是否是收起和展开的状态
      if (
        window.parent.document
          .getElementsByClassName('index_main_mapIcon')[0]
          .getAttribute('class')
          .indexOf('map_mapIcon_move') > -1
      ) {
        left = '2420px'
      } else {
        left = '1400px'
      }
      if (this.searchClick) {
        // this.hideIframe();
        window.parent.lay.openIframe({
          type: 'openIframe',
          name: 'index_name_sou',
          src: baseURL.url + '/static/citybrain/tcgl/commont/index_name_sou.html',
          width: '1279px',
          height: '240px',
          left: left,
          top: '250px',
          zIndex: 997,
        })
      } else {
        window.parent.postMessage(
          JSON.stringify({
            type: 'closeIframe',
            name: 'index_name_sou',
            // name: "videoManage",
          }),
          '*'
        )
        window.parent.lay.closeIframeByNames(['index_name_sou'])
      }
    },
    // 打开云查地 弹窗
    openYCD() {
      let nowData = Date.parse(new Date())
      let formData = new FormData()
      let userId = null
      let nickName = null

      if (
        window.location.ancestorOrigins.length != 0 &&
        (JSON.stringify(window.location.ancestorOrigins).includes('https://csdn.dsjj.jinhua.gov.cn:8102') ||
          JSON.stringify(window.location.ancestorOrigins).includes('https://csdn.dsjj.jinhua.gov.cn:9601'))
      ) {
        let url = this.getUrlParams(
          'https://csdn.dsjj.jinhua.gov.cn:8101/static/citybrain/csrk_3840/bsczt_index.html?userId=310&nickName=李贇'
        )
        userId = url['userId']
        nickName = url['nickName']
      } else {
        //   userId = top.commonObj.userId;
        //   nickName = top.commonObj.userInfo.nickName;
        userId = window.parent.loginUserInfo.userId
        nickName = window.parent.loginUserInfo.nickName
      }
      formData.append('appKey', '66f1608e-c30e-4f47-984e-dde2d29c682b')
      formData.append(
        'sign',
        hex_md5('66f1608e-c30e-4f47-984e-dde2d29c682b' + 'a323e590-3731-490d-ab52-82878d55760c' + nowData)
      )
      formData.append('time', nowData)
      formData.append(
        'source',
        JSON.stringify({
          rings: [
            [
              [122.06377843006963, 30.071171396012677],
              [122.06377843006963, 30.070334208577542],
              [122.06428074253071, 30.070769546043813],
              [122.06377843006963, 30.071171396012677],
            ],
          ],
        })
      )
      formData.append(
        'sourceType',
        JSON.stringify({
          rings: [
            [
              [122.06377843006963, 30.071171396012677],
              [122.06377843006963, 30.070334208577542],
              [122.06428074253071, 30.070769546043813],
              [122.06377843006963, 30.071171396012677],
            ],
          ],
        })
      )
      formData.append('userId', userId)
      formData.append('userName', nickName)
      axios({
        method: 'post',
        url: '/ycd/zjgt89/f7b706e9b59344bcae9058d58a231340/open/dcy/v1/submitTask',
        data: formData,
      }).then((res) => {
        // console.log(res)
        // debugger
        if (res.data.success) {
          // window.open(res.data.result)

          //第一种打开方式
          window.open(
            res.data.result,
            '项目接入系统',
            'directories=no, location=no, toolbar=no,scrollbars=yes, resizable=yes, height=' +
              2160 +
              ', width=' +
              3840 +
              ', top=' +
              0 +
              ', left=' +
              1920 +
              ''
          )
        } else {
          if (
            window.location.ancestorOrigins.length != 0 &&
            (JSON.stringify(window.location.ancestorOrigins).includes('https://csdn.dsjj.jinhua.gov.cn:8102') ||
              JSON.stringify(window.location.ancestorOrigins).includes('https://csdn.dsjj.jinhua.gov.cn:9601'))
          ) {
            window.parent.lay.toast('对不起，没有权限')
          } else {
            top.commonObj.Toast('对不起，没有权限')
          }
        }
      })
    },
    // 将url转成对象
    getUrlParams(url) {
      let result = {}
      let queryString = url // 拿到url参数
      let newSearch = url.split('?')[1].split('&')
      if (newSearch) {
        newSearch.forEach((item, index) => {
          let temp = item.split('=')
          let key = temp[0]
          let value = temp[1]
          result[key] = value
        })
      }
      return result
    },

    // 上热力图
    openHot() {
      this.showHot = !this.showHot
      if (this.showHot) {
        // this.hideIframe();
        // indexApi('/cstz_qxrlt_new').then((res) => {
        //   let hotMapData = []
        //   let heatArr = []
        //   let len = res[0].heatmap.length
        //   let sumLen = 20000 - len
        //   if (len >= 20000) {
        //     heatArr = res[0].heatmap.slice(0, 20000)
        //   } else {
        //     heatArr = res[0].heatmap
        //     for (let j = 0; j < sumLen; j++) {
        //       let a = {
        //         count: 0,
        //         geohash: 0,
        //         lat: 0,
        //         lng: 0,
        //       }
        //       heatArr.push(a)
        //     }
        //   }
        //   heatArr.map((item) => {
        //     // 画热力图的数据
        //     let pointArr = []
        //     pointArr[0] = item.lng
        //     pointArr[1] = item.lat
        //     pointArr[2] = item.count
        //     pointArr[3] = item.geohash
        //     hotMapData.push(pointArr)
        //   })
        //   const mapData = {
        //     layerid: 'rkztHot0',
        //     data: hotMapData,
        //     distance: 800,
        //     alpha: 0.3,
        //     threshold: 6000,
        //   }

        //  mapService.loadHeatmapLayer(mapData)
        // })
        // } else {
        //   this.rmLayer('rkztHot0')
        // }
        const mapData = {
          layerid: 'rkztHot0',
          type: 'dynamic',
        }
        mapService.loadHeatmapLayer(mapData)
      } else {
        this.rmLayer('rkztHot0')
      }
    },
    // 打开路况
    async openWid(isHide) {
      let that = this
      this.showRoad = !this.showRoad
      // 畅通 缓行 拥堵 严重拥堵
      // 由于调用了地图绿色的路况了所以只要除了畅通以外的数据
      let hxData = []
      let ydData = []
      let yzydData = []
      // 清除路况
      let mapIdArr = [
        'hxRoad_index',
        'ydRoad_index',
        'yzydRoad_index',
        '拥堵视频',
        '拥堵点1',
        'icon_road_2',
        'camera-index-3840',
      ]
      this.rmAllLayer(mapIdArr)
      mapService.removeLayer('mouseente01')
      mapService.removeLayer('mouseente02')
      //  清除所有视频
      for (let i = 0; i < this.videoMapId.length; i++) {
        let id = this.videoMapId[i]
        this.rmLayer('拥堵视频' + id)
        this.rmLayer('roadText' + id)
      }
      // 清除定时器
      let end = setInterval(function () {}, 3)
      for (let i = 1; i <= end; i++) {
        clearInterval(i)
      }
      clearTimeout(this.timeOut)

      if (this.showRoad) {
        if (isHide == 'hide') {
          // this.hideIframe();
        }
        // 新地图添加路况
        mapService.loadTrafficLayer({
          layerid: 'icon_road_2',
        })
        this.pointRoadFun()
        let time = setInterval(() => {
          that.timeOut = setTimeout(that.pointRoadFun, 0)
        }, 1000 * 120)
      }
    },
    // 给拥堵上点
    pointRoadFun() {
      let that = this
      let str = "'婺城区','金东区','武义县','浦江县','磐安县','兰溪市','义乌市','东阳市','永康市','开发区'"

      // let mapIdArr = ['hxRoad_index', 'ydRoad_index', 'yzydRoad_index', '拥堵视频', '拥堵点1']
      // this.rmAllLayer(mapIdArr)
      // //  清除所有视频
      // for (let i = 0; i < this.videoMapId.length; i++) {
      //   let id = this.videoMapId[i]
      //   this.rmLayer(id)
      // }
      indexApi('/cstz_baiduydd', { addressName: str }).then((res) => {
        for (let i = 0; i < res.length; i++) {
          let time = new Date(res[i].insert_time)
          let h = time.getHours(),
            m = time.getMinutes()
          res[i].insert_time = h.toString().padStart(2, '0') + ':' + m.toString().padStart(2, '0')
          res[i].idx = Number(res[i].idx)
        }
        var roadPointData = []
        res.data.map((ele) => {
          let roadName = `${ele.roadName}`
          let address = ele.description ? ele.description + ele.direction : '--'
          let arr = ele.location.split(',')
          let pointArr = that.transTo4490(arr)
          let point = pointArr[0] + ',' + pointArr[1]
          let str = {
            data: {
              item: ele,
              title: roadName,
              linkStates: ele.linkStates,
              key: ['指数', '时速', '拥堵距离', '持续时间'],
              value: [ele.idx, ele.speed, ele.distance, ele.durationMin],
              distance: ele.distance,
            },
            address: address,
            point: point,
            lng: pointArr[0],
            lat: pointArr[1],
            type: '路况拥堵点',
            idx: ele.idx,
            location: ele.location,
            linkStates: ele.linkStates,
          }
          roadPointData.push(str)
        })
        mapService.loadPointLayer({
          data: roadPointData,
          layerid: '拥堵点1',
          iconcfg: { image: `./pointAssets/icon/spritesImage/拥堵.png`, iconSize: 40 },
          onclick: this.onclick,
        })
        clearTimeout(this.timeOut)
      })
    },
    onclick(e, list) {
      mapService.removeLayer('mouseente01')
      mapService.removeLayer('mouseente02')
      if (e.data.chn_code) {
        mapService.flyTo({
          destination: [e.data.gps_x, e.data.gps_y],
          // zoom: 15,
          offset: [0, -666],
        })
        let item = {
          obj: {
            // chn_name: e.data.chn_name,
            chn_name: e.data.video_name,
            pointList: list,
          },
          video_code: e.data.chn_code,
          csrk: true,
        }
        let iframe1 = {
          type: 'openIframe',
          name: 'video_main_code',
          src: baseURL.url + '/static/citybrain/tcgl/commont/video_main_code.html',
          width: '100%',
          height: '100%',
          left: '0',
          top: '0',
          zIndex: '1000',
          argument: item,
        }
        window.parent.lay.openIframe(iframe1)
      } else if (e.type == '路况拥堵点') {
        let coor = [e.lng, e.lat]
        let arr = {
          name: e.data.key,
          value: e.data.value,
        }
        let countStr = ''
        for (let index = 0; index < arr.name.length; index++) {
          countStr += `<div style="margin:0 10px">${arr.name[index]}：${arr.value[index]}</div>`
        }
        let str = `
                    <div
                    style="
                        position: relative;
                        background: url('./pointAssets/du_bg2.png') no-repeat;
                        background-size: 100% 100%;
                        width: max-content;
                        min-height: 320px;
                    "
                    >
                        <nav  style="display: flex; justify-content: space-between; margin: 0 20px">
                        <h2  style="
                            margin-top: 20px;
                            white-space: nowrap;
                            font-size: 38px;
                            display: flex;
                            background: linear-gradient(to bottom, #df5151, #f4f1ff, #ff4949, #e03e3e);
                            -webkit-background-clip: text;
                            color: transparent;">
                            <img src="/static/citybrain/csdn/img/dupoint.png" width="50px" alt="" />
                            ${e.data.title}${e.data.item.eventSource}（${e.data.item.insert_time}）
                            <span style="font-size: 32px !important;font-style: italic;">${e.data.item.congestAffectArea}</span>
                        </h2>
                        <span style="cursor: pointer; margin-left: 20px; font-size: 40px;width:34px;color: #fff" onclick="this.parentNode.parentNode.style.display = 'none'">x</span>
                        </nav>
                        <header style="font-size: 32px;margin: 20px 20px 10px 20px; display: flex; justify-content: space-between;padding:10px;box-sizing:border-box;">
                        <div style="width: 80%; overflow: hidden;display:flex">
                            <img src="/static/citybrain/csdn/img/address.png" width="40px" height="50px" style="margin-right:10px" />
                            <div>
                            <p  style="background: linear-gradient(to bottom, #ffebce, #ffffff, #ffc559, #ffffff);
                            -webkit-background-clip: text;
                            color: transparent;font-size:36px !important;
                            width: 500px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis
                            "
                                title="${e.address}">${e.address}</p>
                            <p  style="color: #fff;width: 500px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis" title="${e.data.item.congestTailDesc}">源头：${e.data.item.congestTailDesc}</p>
                            </div>
                        </div>
                        <div style="font-size: 32px;color: #fff; background: rgba(216, 81, 81, 0.897);padding: 2px 5px;border-radius: 5px;height:40px;line-height:40px;">${e.data.item.extraEventStatus}</div>
                        </header>
                        <footer style="color: #05BE94;white-space: nowrap;display: flex;justify-content: space-around;margin: 0 20px;font-size: 32px;">
                        ${countStr}
                        </footer>
                    </div> `

        let objData = {
          layerid: e.layerid,
          position: coor,
          offset: [55, -40],
          closeButton: true,
          content: str,
        }

        mapService._createPopup(objData)
        this.roadMapFun(e)
      }
    },
    onblur(e) {
      //onblur
      let info = e.data
      let str = ''
      if (e.status == 0) {
        str = `<div onclick=" this.style.display = 'none'"
                                style="
                                width: 300px;
                                position: absolute;
                                border-radius: 5px;
                                background-color: rgba(10, 31, 53, 0.8);
                                z-index: 999999;
                                box-shadow: inset 0 0 40px 0 #5ba3fa;
                                padding: 24px;">
                            <div class="container1" style="font-size: 30px;color: white;text-align: center;">
                            设备离线中...
                            </div>
                        </div>`
        let objData = {
          layerid: 'mouseente01',
          position: [e.lng, e.lat],
          content: str,
          offset: [50, 100],
        }
        mapService._createPopup(objData)
      } else {
        indexApi('xxwh_bqcx_name', { chnCode: info.chn_code }).then((res) => {
          indexApi('xxwh_dwzl_video_path', { chnCode: info.chn_code }).then((el) => {
            let url = ''
            let lable = ''
            let des = ''

            if (el[0] && el[0].path != null) {
              url = baseURL.url + '/imgPath/' + el[0].path.split('fileServer/')[1]
            } else {
              url = '/static/citybrain/tckz/img/video/404.png'
            }
            if (res[0]) {
              let labelName = res[0].lableName.split(',').slice(0, 3)
              let description = res[0].description.split('，')
              labelName.forEach((item) => {
                lable += `<div style="color: #dbdee2;
                                                    font-size: 28px;
                                                    height: 40px;
                                                    line-height: 40px;
                                                    padding: 0 20px;
                                                    box-sizing: border-box;
                                                    border-radius: 10px;
                                                    margin-right: 10px;
                                                    margin-bottom: 10px;
                                                    background-color: #393967;">${item}
                                    </div>`
              })
              description.forEach((item) => {
                des += `<div style="color: #dbdee2;
                                                    font-size: 28px;
                                                    height: 40px;
                                                    line-height: 40px;
                                                    padding: 0 20px;
                                                    box-sizing: border-box;
                                                    border-radius: 10px;
                                                    margin-right: 10px;
                                                    margin-bottom: 10px;
                                                    background-color: #393967;">${item}
                                    </div>`
              })
            } else {
              lable = `<div style="color: #dbdee2;
                                                    font-size: 28px;
                                                    height: 40px;
                                                    line-height: 40px;
                                                    padding: 0 20px;
                                                    box-sizing: border-box;
                                                    border-radius: 10px;
                                                    margin-right: 10px;
                                                    margin-bottom: 10px;">暂无
                                    </div>`
              des = `<div style="color: #dbdee2;
                                        font-size: 28px;
                                        height: 40px;
                                        line-height: 40px;
                                        padding: 0 20px;
                                        box-sizing: border-box;
                                        border-radius: 10px;
                                        margin-right: 10px;
                                        margin-bottom: 10px;">暂无
                                </div>`
            }

            str = `<div onclick=" this.style.display = 'none'"
                                style="
                                    width: 800px;
                                    position: absolute;
                                    border-radius: 5px;
                                    background-color: rgba(10, 31, 53, 0.8);
                                    z-index: 999999;
                                    box-shadow: inset 0 0 40px 0 #5ba3fa;
                                    padding: 24px;">

                            <div class="container1">
                                <div style="display:flex;justify-content: space-between;">
                                <p title='${
                                  info.video_name
                                }' style='height: 30px;line-height: 30px;color: #fff;font-size: 30px;
                                        white-space: nowrap;overflow: hidden;text-overflow: ellipsis;'>${
                                          info.video_name
                                        }</p>
                                <img style='width:30px;height:30px;'
                                    src="${
                                      info.is_collection == 0
                                        ? '/static/citybrain/tckz/img/not-col.png'
                                        : '/static/citybrain/tckz/img/col.png'
                                    }">
                                </div>
                                <div style="width:100%;display:flex;flex-wrap: wrap;margin-top: 10px;">
                                <span style="font-size:30px;color:#fff;line-height:40px;">标签：</span>
                                ${lable}
                                </div>
                                <div style="width:100%;display:flex;flex-wrap: wrap;margin-top: 0px;">
                                <span style="font-size:30px;color:#fff;line-height:40px;">视频内容：</span>
                                ${des}
                                </div>
                                <img src="${url}" alt="" style='width:100%;height:400px;margin-top: 10px;'>
                            </div>
                            </div>`
            let objData = {
              layerid: 'mouseente02',
              position: [e.lng, e.lat],
              content: str,
              offset: [50, 100],
            }
            mapService._createPopup(objData)
          })
        })
      }
    },
    roadMapFun(obj) {
      let that = this
      let arrLngLats = obj.data.linkStates
      // 畅通 缓行 拥堵 严重拥堵
      // 由于调用了地图绿色的路况了所以只要除了畅通以外的数据
      this.rmAllLayer(['hxRoad_index', 'ydRoad_index', 'yzydRoad_index'])
      mapService.removeAllLayers(['camera-index-3840'])
      //  清除所有视频
      for (let i = 0; i < this.videoMapId.length; i++) {
        let id = this.videoMapId[i]
        this.rmLayer('拥堵视频' + id)
        this.rmLayer('roadText' + id)
      }
      let hxData = []
      let ydData = []
      let yzydData = []
      let point = obj.location.split(',')
      let pointArr = that.transTo4490(point)
      this.videoMapId = []
      let arrList = []
      for (let key of Object.keys(arrLngLats)) {
        let line = arrLngLats[key]
        let arrData = line.split(';')
        let lineArr = []
        let lineStr = ''
        for (let i = 0; i < arrData.length; i++) {
          // 排序
          // let str=that.roadSort(arrData[i])
          // 不排序
          let str = arrData[i]
          let arr = str.split(/[,;]/)
          let coords = that.transTo4490(arr)
          let coordsStr = coords.join(',')
          lineArr.push(coordsStr)
          obj.idx >= 1.5 && obj.idx < 2
            ? hxData.push(coords)
            : obj.idx >= 2 && obj.idx < 4
            ? ydData.push(coords)
            : obj.idx >= 4
            ? yzydData.push(coords)
            : ''
        }
        lineStr = lineArr.join(';')

        let len = Math.ceil(lineArr.length / 2 - 1)
        let linePoint = lineArr[len].split(',')

        that.videoMapId.push(key)
        mapService.loadTextLayer({
          layerid: 'roadText' + key,
          data: [
            {
              pos: [linePoint[0], linePoint[1]], //上文字经纬度
              //内容
              text: obj.data.distance,
            },
          ], //数据
          style: {
            size: 40, //文字大小
            color: [252, 198, 42, 1], //文字颜色
          },
        })
        mapService.loadRoadVideo({
          layerid: '拥堵视频' + key,
          videoType: '拥堵视频' + key,
          distance: 30,
          lineStr: lineStr,
          // onclick: this.onclick,
          callback: (e) => {
            e.forEach((item) => {
              arrList.push(item)
            })
          },
        })
      }
      that.addPoint(arrList)
    },
    async addPoint(arrList) {
      // console.log('arrList=>',arrList)
      //去重
      let forData = []
      for (let i = 0; i < arrList.length; i++) {
        if (!forData.some((e) => e.id == arrList[i].id)) forData.push(arrList[i])
      }
      // console.log('forData=>',forData)
      let arr = []
      for (let i = 0; i < forData.length; i++) {
        let res = await indexApi('/xxwh_dwzlmore', {
          code: forData[i].chn_code,
        }).then((res) => {
          return {
            code: res[0].chn_code,
            pointId: 'camera-index-3840',
            data: res[0],
            point: res[0].gps_x + ',' + res[0].gps_y,
            lng: res[0].gps_x,
            lat: res[0].gps_y,
            status: res[0].is_online,
            cameraType: res[0].cameraType,
            pointType: this.getPointType(res[0].is_online, res[0].cameraType),
          }
        })
        arr.push(res)
      }
      console.log(arr)
      // this.cameraList.forEach((item) => {
      //   this.getManyPoint(this.filterData(arr, item.name), item.code)
      // })

      // 加载视频点位的先隐藏
      // this.getManyPoint(arr)
    },
    getPointType(is_online, cameraType) {
      let arr = is_online + '-' + cameraType
      let obj = {
        枪机在线: '1-1',
        枪机离线: '0-1',
        球机在线: '1-2',
        球机离线: '0-2',
        半球机在线: '1-3',
        半球机离线: '0-3',
        高点在线: '1-4',
        高点离线: '0-4',
      }
      for (key in obj) {
        if (obj[key] == arr) {
          return key
        }
      }
    },
    // filterData(mapObj, name) {
    //   let obj = {
    //     枪机在线: [1, '1'],
    //     枪机离线: [0, '1'],
    //     球机在线: [1, '2'],
    //     球机离线: [0, '2'],
    //     半球机在线: [1, '3'],
    //     半球机离线: [0, '3'],
    //     高点在线: [1, '4'],
    //     高点离线: [0, '4'],
    //   }
    //   return mapObj.filter((item) => {
    //     return item.status == obj[name][0] && item.cameraType == obj[name][1]
    //   })
    // },
    //一次绘制多种不同类型的点
    getManyPoint(pointData, pointId) {
      // if (pointData.length > 0) {
      //   mapService.loadPointLayer({
      //     data: pointData,
      //     layerid: pointId,
      //     iconcfg: {
      //       image: pointId,
      //       iconSize: 0.5,
      //     }, //图标
      //     onclick: this.onclick,
      //     onblur: this.onblur,
      //   })
      // }
      mapService.loadPointLayer({
        layerid: 'camera-index-3840',
        data: pointData,
        onclick: this.onclick,
        onblur: this.onblur,
        cluster: true, //是否定义为聚合点位：true/false
        iconcfg: {
          image: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-qiangji.png`,
          iconSize: 0.5,
          iconlist: {
            field: 'pointType',
            list: [
              {
                value: '枪机在线',
                size: '50',
                src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-qiangji.png`,
              },
              {
                value: '枪机离线',
                size: '50',
                src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-qiangji.png`,
              },
              {
                value: '球机在线',
                size: '50',
                src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-qiuji.png`,
              },
              {
                value: '球机离线',
                size: '50',
                src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-qiuji.png`,
              },
              {
                value: '半球机在线',
                size: '50',
                src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-banqiu.png`,
              },
              {
                value: '半球机离线',
                size: '50',
                src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-banqiu.png`,
              },
              {
                value: '高点在线',
                size: '50',
                src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-gaodian.png`,
              },
              {
                value: '高点离线',
                size: '50',
                src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-gaodian.png`,
              },
            ],
          },
        },
      })
    },
    // 处理路况的排序
    roadSort(item) {
      let allArr = []
      let ccc = item.split(',')
      let a, b
      let aIndex = 0
      let bIndex = 1
      let arrData = []
      ccc.forEach((str, index) => {
        if (index % 2 === 0) {
          a = str
          aIndex += 1
        } else {
          b = str
          bIndex += 1
        }
        if (a && b && bIndex - aIndex === 1) {
          let d = a + ',' + b
          arrData.push(d)
        }
      })
      let sortData = arrData.sort()
      allArr.push(...sortData)
      let strData = allArr.toString()
      return strData
    },
    // 处理路况的经纬度
    transTo4490(arr) {
      const length = arr.length / 2
      const pointArr = []
      for (let i = 1; i <= length; i++) {
        const index = i * 2
        let jwd1 = coordtransform.bd09togcj02(arr[index - 2], arr[index - 1])
        let jwd2 = coordtransform.gcj02towgs84(jwd1[0], jwd1[1])
        let str = [jwd2[0], jwd2[1]].concat(0)
        pointArr.push(str)
      }
      return pointArr.flat()
    },
    // 打开地图场景盒子
    openIframeMap() {
      this.trunMapkuai = !this.trunMapkuai
      let moveLeft = '1990px'
      let moveTop = '20px'
      // 判断左右页面是否是收起和展开的状态
      if (
        window.parent.document
          .getElementsByClassName('index_main_mapIcon')[0]
          .getAttribute('class')
          .indexOf('map_mapIcon_move') > -1
      ) {
        moveLeft = '3000px'
        moveTop = '90px'
      }
      let moveLeftNum = this.moveRight ? this.moveRight - 700 + 'px' : moveLeft
      if (this.trunMapkuai) {
        window.parent.lay.openIframe({
          type: 'openIframe',
          name: 'main_changeMap3840',
          src: baseURL.url + '/static/citybrain/tcgl/commont/main_changeMap.html',
          width: '700px',
          // height: '160px',
          left: moveLeftNum,
          top: '300px',
          zIndex: 997,
        })
      } else {
        window.parent.lay.closeIframeByNames(['main_changeMap3840'])
        // mapService.removeLayer('bmLayer') //移除
        // mapService.removeLayer('qxLayer') //移除
        // window.parent.frames['main_changeMap3840'].changeMap.mapin_changes = ''
        // mapService.tool.changeBaseMap('black')
      }
    },
    mapNow() {
      this.click2D = false
      //回到初始位置
      mapService.tool.backHome()
    },
    map2Dor() {
      //切换2D或3D
      let num = this.click2D ? 3 : 2
      mapService.tool.changeMode(num)
      this.click2D = !this.click2D
      if (this.click2D) {
        mapService.map.addLayers({
          id: '3DRoom',
          type: 'fill-extrusion',
          source: {
            type: 'geojson',
            data: extrusionJson,
          },
          layout: {},
          paint: {
            'fill-extrusion-color': '#fff',
            'fill-extrusion-opacity': 1,
            'fill-extrusion-height': ['/', ['get', 'Shape_Leng'], 10],
            // "fill-extrusion-translate":[1,10],
          },
        })
        mapService.tool.change3D(10)
        mapService.tool.changeScene()
      } else {
        this.rmLayer('3DRoom')
        // 7是深色地图
        mapService.tool.change3D(1)
      }
    },
    mapMix() {
      //缩大地图
      mapService.tool.zoomIn()
    },
    mapMis() {
      //缩小地图
      mapService.tool.zoomOut()
    },
    rmLayer(id) {
      mapService.removeLayer(id)
    },
    // 清除地图
    rmAllLayer(id) {
      mapService.removeAllLayers(id)
    },
    createWLGZ(faceMap) {
      let this_ = this
      // 创建区域
      if (faceMap.geometry.coordinates[0] != []) {
        let str = ''
        faceMap.geometry.coordinates[0].forEach((ele) => {
          str += ele[0] + ',' + ele[1] + '|'
        })
        let faceStr = str.slice(0, str.length - 1)
        axios({
          method: 'post',
          url: '/typeq/api/getu/project/create',
          data: {
            shape: faceStr,
            precision: 7,
          },
        }).then(function (res) {
          this_.getCount(res.data, faceMap)
        })
      }
    },
    getCount(id, faceMap) {
      let this_ = this
      let d = new Date()
      let start = `${d.getFullYear()}-${d.getMonth() + 1}-${d.getDate()} ${d.getHours().toString().padStart(2, '0')}:${d
        .getMinutes()
        .toString()
        .padStart(2, '0')}:${d.getSeconds().toString().padStart(2, '0')}`
      var frontOneHour = new Date(d.getTime() - 3 * 60 * 60 * 1000)
      let end = `${frontOneHour.getFullYear()}-${frontOneHour.getMonth() + 1}-${frontOneHour.getDate()} ${frontOneHour
        .getHours()
        .toString()
        .padStart(2, '0')}:${frontOneHour.getMinutes().toString().padStart(2, '0')}:${frontOneHour
        .getSeconds()
        .toString()
        .padStart(2, '0')}`

      axios({
        method: 'post',
        url: baseURL.url + 'https://webapi.getui.com/typeq/api/getu/project/get',
        data: {
          id: id.data,
          type: 2,
          start_time: end,
          end_time: start,
        },
      }).then(function (res) {
        if (res.data.data) {
          let left = '1900px'
          // 判断左右页面是否是收起和展开的状态
          if (
            window.parent.document
              .getElementsByClassName('index_main_mapIcon')[0]
              .getAttribute('class')
              .indexOf('map_mapIcon_move') > -1
          ) {
            left = '2930px'
          } else {
            left = '1900px'
          }
          window.parent.lay.openIframe({
            type: 'openIframe',
            name: 'person_count',
            src: baseURL.url + '/static/citybrain/tcgl/commont/person_count.html',
            width: '750px',
            height: '580px',
            left: left,
            top: '500px',
            zIndex: 666,
            argument: {
              person_count_data: {
                mapGeoJson: faceMap,
                countAll: res.data.data,
              },
            },
          })
        }
      })
    },
    // 查询人数
    getCode(faceMap) {
      let this_ = this
      if (faceMap.geometry.coordinates[0] != []) {
        let str = ''
        faceMap.geometry.coordinates[0].forEach((ele) => {
          str += ele[0] + ',' + ele[1] + '|'
        })
        let faceStr = str.slice(0, str.length - 1)
        let d = new Date()
        let start = `${d.getFullYear()}-${d.getMonth() + 1}-${d.getDate()} ${d
          .getHours()
          .toString()
          .padStart(2, '0')}:${d.getMinutes().toString().padStart(2, '0')}:${d
          .getSeconds()
          .toString()
          .padStart(2, '0')}`
        var frontOneHour = new Date(d.getTime() - 2 * 60 * 60 * 1000)
        let end = `${frontOneHour.getFullYear()}-${frontOneHour.getMonth() + 1}-${frontOneHour.getDate()} ${frontOneHour
          .getHours()
          .toString()
          .padStart(2, '0')}:${frontOneHour.getMinutes().toString().padStart(2, '0')}:${frontOneHour
          .getSeconds()
          .toString()
          .padStart(2, '0')}`

        axios({
          method: 'post',
          url: baseURL.url + '/typeq/api/getu/project/getByGeohash',
          data: {
            shape: faceStr,
            type: 2,
            start_time: end,
            end_time: start,
          },
        }).then(function (res) {
          if (res.data.data) {
            let left = '2930px'
            // 判断左右页面是否是收起和展开的状态
            if (
              window.parent.document
                .getElementsByClassName('index_main_mapIcon')[0]
                .getAttribute('class')
                .indexOf('map_mapIcon_move') > -1
            ) {
              left = '1900px'
            } else {
              left = '2930px'
            }
            window.parent.lay.openIframe({
              type: 'openIframe',
              name: 'person_count',
              src: baseURL.url + '/static/citybrain/tcgl/commont/person_count.html',
              width: '750px',
              height: '580px',
              left: left,
              top: '500px',
              zIndex: 666,
              argument: {
                person_count_data: {
                  mapGeoJson: faceMap,
                  countAll: res.data.data,
                },
              },
            })
          }
        })
      }
    },
    /*
     * 鉴权
     */
    creditAuth() {
      var appKey = 'zj_jh-API',
        masterSecret = 'vqVINnikQoL8fHbfgLzdzC62'

      var timestamp = Number(Math.round(new Date().getTime() / 1000).toString())

      var time_md5 = CryptoJS.MD5(timestamp.toString()).toString()

      var o_str = appKey + time_md5 + masterSecret,
        sha256_str = CryptoJS.SHA256(o_str).toString()

      var sign = sha256_str + masterSecret

      const reqParams = {
        appKey: appKey,
        sign: sign,
        timestamp: timestamp,
        version: 'v1.0',
      }

      return axios({
        method: 'post',
        url: baseURL.url + '/typeq/api/auth/creditAuth',
        data: reqParams,
      }).then(function (res) {
        if (res.data.errno === 0) {
          window.accessToken = res.data.data.accessToken

          axios.defaults.headers.common['Access-Token'] = res.data.data.accessToken
        }
      })
    },
    // 收缩两边
    hideIframe() {
      let lefthideCss = window.parent.document.getElementsByClassName('page_right')[0]
      if (lefthideCss != undefined && lefthideCss.className.indexOf('fadeInRight') != -1) {
        window.parent.lrFrameSHClick ? window.parent.lrFrameSHClick() : ''
      }
    },
  },
}
</script>

<style scoped lang="scss">
[v-cloak] {
  display: none;
}
body {
  margin: 0;
}

.rightBtn {
  position: absolute;
  width: 127px;
  height: 50px;
  display: flex;
  flex-direction: column;
  /* justify-content: space-around; */
  align-items: center;
  overflow: hidden;
  top: 240px;
  right: 1000px;
  z-index: 99;
}

.rightBottom {
  position: absolute;
  right: 60px;
  top: 450px;
  /* width: 202px; */
  height: 250px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}

.rightBtn > div {
  margin-top: 5px;
}
.rightBtn p {
  margin: 0;
  cursor: pointer;
}
.rightBtn > p {
  margin-top: 5px !important;
}
.rightBtn > p:first-child {
  margin-top: 0 !important;
}

.rightBtn .middleBtn01 {
  /* width: 95px;
            height: 95px; */
  width: 50px;
  height: 50px;
  background: url('@/assets/tcgl/main_mapIcon/home.png') 100% 100% no-repeat;
  background-size: 100% 100%;
  background-position: center;
}

.rightBtn .middleBtn01:hover {
  background: url('@/assets/tcgl/main_mapIcon/home-active.png') 100% 100% no-repeat;
  background-size: 100% 100%;
  background-position: center;
}

.rightBtn .middleBtn02 {
  width: 50px;
  height: 50px;
  background-size: 100% 100%;
}

.rightBtn .middleBtn03 {
  width: 50px;
  height: 50px;
  background: url('@/assets/tcgl/main_mapIcon/middle003.png') 100% 100% no-repeat;
  border: 1px solid #67b6f9;
}

.rightBtn .middleBtn03:hover {
  background: url('@/assets/tcgl/main_mapIcon/middle003-hover.png') 100% 100% no-repeat;
}

.rightBtn .middleBtn04 {
  width: 50px;
  height: 50px;
  background: url('@/assets/tcgl/main_mapIcon/middle004.png') 100% 100% no-repeat;
}

.rightBtn .middleBtn04:hover {
  background: url('@/assets/tcgl/main_mapIcon/middle004-hover.png') 100% 100% no-repeat;
}

.rightBtn .middleBtn055,
.rightBtn .middleBtn06 {
  width: 50px;
  height: 50px;
  font-size: 30px;
  font-weight: bold;
  color: #d6e7f9;
  text-align: center;
  line-height: 45px;
  border: 1px solid #67b6f9;
  box-sizing: border-box;
  background-color: #1c2a47;
  border-radius: 2px;
}

.middleBtn066 {
  width: 50px;
  height: 50px;
  background-color: #1c2a47;
  border-radius: 8px;
}
.rightBtn .middleBtn033 {
  width: 50px;
  height: 50px;
  cursor: pointer;
  border: 1px solid #67b6f9;
  box-sizing: border-box;
  background-image: url('@/assets/tcgl/main_mapIcon/middleBtn033.png');
  background-size: 103% 103%;
  background-repeat: no-repeat;
  background-position: -1px;
}

.rightBtn .middleBtn033:hover {
  background-image: url('@/assets/tcgl/main_mapIcon/middleBtn033-hover.png');
  background-size: 104% 104%;
  background-repeat: no-repeat;
}

.middleBtn033-active {
  background-image: url('@/assets/tcgl/main_mapIcon/middleBtn033-hover.png') !important;
  background-size: 104% 104%;
  background-repeat: no-repeat;
}

.rightBtn .mapClick {
  position: absolute;
  top: 184px;
  right: 133px;
  width: 244px;
  display: flex;
  height: 76px;
  border-radius: 10px;
  background-image: linear-gradient(to bottom, rgb(111 133 228), rgb(14, 64, 109), rgb(113 125 203));
  color: #ccc;
  font-size: 22px;
  justify-content: space-evenly;
  align-items: center;
  cursor: pointer;
}

.rightBtn .mapClick .mapClick_img1,
.rightBtn .mapClick .mapClick_img2 {
  position: relative;
  width: 110px;
  height: 66px;
}

.rightBtn .mapClick .mapClick_img1:hover span,
.rightBtn .mapClick .mapClick_img2:hover span {
  color: #fff;
  background-color: #3c76de;
}

.rightBtn .mapClick .mapClick_img1 span,
.rightBtn .mapClick .mapClick_img2 span {
  position: absolute;
  right: 0;
  top: 36px;
  /* padding: 0 0; */
  width: 100%;
  height: 30px;
  line-height: 30px;
  text-align: center;
}

/* .rightBtn .mapClick .mapClick_img1 span .active,
    .rightBtn .mapClick .mapClick_img2 span .active {
    background-color: #3c76de;
    } */
.rightBtn .mapClick .mapClick_img1 {
  background: url('@/assets/tcgl/commont/qianse.png') no-repeat;
}

.rightBtn .mapClick .mapClick_img2 {
  background: url('@/assets/tcgl/commont/shense.png') no-repeat;
}

.search-box {
  width: 250px;
  position: absolute;
  top: 330px;
  right: 133px;
  color: #fff;
  background-image: linear-gradient(to bottom, rgb(111 133 228), rgb(14, 64, 109), rgb(113 125 203));
  border-radius: 10px;
  font-size: 30px;
  padding: 5px 20px;
  box-sizing: border-box;
}

.search-box > div {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.search-box > div > i {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #7387ff;
}

.search-box > div > span {
  display: inline-block;
  width: 140px;
  margin-left: 10px;
}

.search-box > div:last-child {
  margin-bottom: 0;
}

.sou-el > .el-button {
  border: 0 !important;
  color: #fff;
  width: 50px;
  height: 50px;
  border-radius: 12px;
  padding: 0;
  font-size: 24px;
  border: 1px solid #fff !important;
}

.middle02_2D {
  background-image: url('@/assets/tcgl/main_mapIcon/middle002D.png') !important;
}

.middle02_3D {
  background-image: url('@/assets/tcgl/main_mapIcon/middle002.png') !important;
}

.rightBtn .middleBtn06:hover {
  color: #fff;
  background: #1b63ae !important;
}

.mapPopup {
  box-shadow: none !important;
}
.ifont-30 {
  font-size: 30px !important;
}
.t-r-180 {
  display: inline-block;
  transform: rotateZ(180deg);
}
</style>
