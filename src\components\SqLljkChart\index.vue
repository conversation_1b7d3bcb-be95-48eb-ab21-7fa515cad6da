<template>
  <div ref="sqLljk<PERSON>hart" style='width: 100%;height: 100%'>
  </div>
</template>

<script>
import 'echarts-liquidfill'
export default {
  name: 'SqLljk<PERSON>hart',

  props: {
    chartData: {
      required: true
    }
  },

  data() {
    return {
      chartInstance: null
    }
  },

  watch: {
    chartData() {
      setTimeout(() => {
        this.updateChart()
      }, 300)
    }
  },

  methods: {
    initChart() {
      if (this.chartInstance) {
        this.chartInstance.dispose()
      }
      this.chartInstance = this.$echarts.init(this.$refs.sqLljkChart, 'macarons')

      const color = ['#2693fe', '#33b4fb']

      const option = {
        graphic: [
          {
            type: 'text',
            top: '38%',
            left: 'center',
            z: 2,
            style: {
              text: this.chartData,
              fill: '#fff',
              fontSize: '1.125rem',
              fontWeight: 'bold',
            },
          },
          {
            type: 'text',
            left: 'center',
            z: 2,
            top: '18%',
            style: {
              text: '',
              fill: '#fff',
              fontSize: '1.125rem',
            },
          },
        ],
        series: [
          {
            type: 'liquidFill',
            data: [0.4, 0.5],
            shape: 'circle',
            radius: '90%',
            center: ['50%', '50%'],
            color,
            amplitude: 15,
            outline: {
              show: true,
              borderDistance: 0,
              itemStyle: {
                borderColor: '#0b56a5',
                borderWidth: 1,
              },
            },
            backgroundStyle: {
              color: '#153a86',
            },
            itemStyle: {
              shadowBlur: 0,
            },
            label: {
              show: false,
            },
          },
        ],
      }

      this.chartInstance.setOption(option)
    },

    updateChart() {
      if (this.chartInstance) {
        this.chartInstance.setOption({
          graphic: [
            {
              type: 'text',
              top: '38%',
              left: 'center',
              z: 2,
              style: {
                text: this.chartData,
                fill: '#fff',
                fontSize: '1.125rem',
                fontWeight: 'bold',
              },
            },
          ],
        })
      }
    },

    handleResize() {
      if (this.chartInstance) {
        this.chartInstance.resize()
      }
    }
  },

  mounted() {
    this.initChart()
    window.addEventListener('resize', this.handleResize)
  },

  beforeDestroy() {
    if (this.chartInstance) {
      this.chartInstance.dispose()
      this.chartInstance = null
    }
    window.removeEventListener('resize', this.handleResize)
  }
}
</script>

<style lang="scss" scoped></style>
