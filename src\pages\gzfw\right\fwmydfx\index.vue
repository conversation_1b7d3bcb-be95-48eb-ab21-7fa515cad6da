<template>
  <div>
    <CommonTitle text="服务满意度分析"></CommonTitle>
    <div class="wrap-container">
      <!-- <TabSwitch class="tab-wraper" :type="2" :tabList="list" :activeIndex="index" @tab-change="changeTab" /> -->
      <div id="pieChart" style="width: 100%; height: 500px"></div>
    </div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import TabSwitch from '@/components/TabSwitch'
import * as echarts from 'echarts'
import { getSatisService } from '@/api/gzfw/index.js'
export default {
  name: 'index',
  components: {
    CommonTitle,
    TabSwitch,
  },
  data() {
    return {
      list: [
        { name: '个人服务', value: '1' },
        { name: '法人服务', value: '2' },
      ],
      index: 0,
      //3d饼图
      pieData: [],
      pieColors: ['#22E197', '#00EAFF', '#98DC3E', '#007BFF', '#A23EDC'],
      graphicCunstom: {
        left: '29%',
        top: '26%',
        z: -10,
        rotation: 0, //旋转
        origin: [50, 50], //中心点
        scale: [0.8, 0.8], //缩放
      },
    }
  },
  computed: {},
  mounted() {
    getSatisService().then((res) => {
      this.pieData = res.data.map((item) => {
        return {
          name: item.key,
          value: item.value,
          percent:item.percent
        }
      })
      this.getPie3D('pieChart', this.pieData, this.pieColors)
    })
  },
  methods: {
    changeTab(i) {
      this.index = i
    },
    getPie3D(id, echartsData, colors) {
      let myChart = this.$echarts.init(document.getElementById(id))
      const imgUrl = require('@/assets/wlwsb/pie-bg.png')
      let that = this
      let total = 0
      echartsData.forEach((item) => {
        total += parseFloat(item.value)
      })
      console.log('echartsData', echartsData)
      let option = getPie3D(echartsData, 0.59)
      // 生成扇形的曲面参数方程
      function getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, h) {
        // 计算
        const midRatio = (startRatio + endRatio) / 2

        const startRadian = startRatio * Math.PI * 2
        const endRadian = endRatio * Math.PI * 2
        const midRadian = midRatio * Math.PI * 2

        // 如果只有一个扇形，则不实现选中效果。
        if (startRatio === 0 && endRatio === 1) {
          // eslint-disable-next-line no-param-reassign
          isSelected = false
        }

        // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
        // eslint-disable-next-line no-param-reassign
        k = typeof k !== 'undefined' ? k : 1 / 3

        // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
        const offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0
        const offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0

        // 计算高亮效果的放大比例（未高亮，则比例为 1）
        const hoverRate = isHovered ? 1.05 : 1

        // 返回曲面参数方程
        return {
          u: {
            min: -Math.PI,
            max: Math.PI * 3,
            step: Math.PI / 32,
          },

          v: {
            min: 0,
            max: Math.PI * 2,
            step: Math.PI / 20,
          },

          x(u, v) {
            if (u < startRadian) {
              return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate
            }
            if (u > endRadian) {
              return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate
            }
            return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate
          },

          y(u, v) {
            if (u < startRadian) {
              return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate
            }
            if (u > endRadian) {
              return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate
            }
            return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate
          },

          z(u, v) {
            if (u < -Math.PI * 0.5) {
              return Math.sin(u)
            }
            if (u > Math.PI * 2.5) {
              return Math.sin(u) * h * 0.1
            }
            // 当前图形的高度是Z根据h（每个value的值决定的）
            return Math.sin(v) > 0 ? 1 * h * 0.1 : -1
          },
        }
      }
      // 生成模拟 3D 饼图的配置项
      function getPie3D(pieData, internalDiameterRatio) {
        const series = []
        // 总和
        let sumValue = 0
        let startValue = 0
        let endValue = 0
        const legendData = []
        let legend = []
        const k =
          typeof internalDiameterRatio !== 'undefined'
            ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio)
            : 1 / 3

        // 为每一个饼图数据，生成一个 series-surface 配置
        for (let i = 0; i < pieData.length; i += 1) {
          sumValue += pieData[i].value

          const seriesItem = {
            name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,
            type: 'surface',
            parametric: true,
            wireframe: {
              show: false,
            },
            pieData: pieData[i],
            pieStatus: {
              selected: false,
              hovered: false,
              k,
            },
          }
          
          if (typeof pieData[i].itemStyle !== 'undefined') {
            const { itemStyle } = pieData[i]

            // eslint-disable-next-line no-unused-expressions
            typeof pieData[i].itemStyle.color !== 'undefined' ? (itemStyle.color = pieData[i].itemStyle.color) : null
            // eslint-disable-next-line no-unused-expressions
            typeof pieData[i].itemStyle.opacity !== 'undefined'
              ? (itemStyle.opacity = pieData[i].itemStyle.opacity)
              : null

            seriesItem.itemStyle = itemStyle
          }
          series.push(seriesItem)
        }
        // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
        // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
        for (let i = 0; i < series.length; i += 1) {
          endValue = startValue + series[i].pieData.value

          series[i].pieData.startRatio = startValue / sumValue
          series[i].pieData.endRatio = endValue / sumValue
          series[i].parametricEquation = getParametricEquation(
            series[i].pieData.startRatio,
            series[i].pieData.endRatio,
            false,
            false,
            k,
            // 我这里做了一个处理，使除了第一个之外的值都是10
            series[i].pieData.value === series[0].pieData.value ? 35 : 10
          )

          startValue = endValue

          legendData.push(series[i].name)
        }
        // 准备待返回的配置项，把准备好的 legendData、series 传入。
        const option = {
          color: colors,
          // animation: false,
          tooltip: {
            formatter: (params) => {
              if (params.seriesName !== 'mouseoutSeries') {
                return `${
                  params.seriesName
                }<br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                  params.color
                };"></span>${option.series[params.seriesIndex].pieData.value} ${(
                  option.series[params.seriesIndex].pieData.percent
                ).toFixed(2)}%`
              }
              return ''
            },
            textStyle: {
              color: '#ffff',
              fontSize: 24,
            },
            borderWidth: 0,
            backgroundColor: 'rgba(51, 51, 51, 0.7)',
          },
          legend: {
            right: 'center',
            bottom: '8%',
            orient: 'horizontal',
            width: 800,
            // height:300,
            itemGap: 26,
            textStyle: {
              rich: {
                name: {
                  fontSize: 26,
                  color: '#ffffff',
                  padding: [0, 0, 0, 15],
                },
                value: {
                  fontSize: 26,
                  color: '#e9d0ab',
                  padding: [10, 5, 0, 15],
                },
                value1: {
                  fontSize: 26,
                  color: '#e9d0ab',
                  padding: [10, 5, 0, 15],
                },
              },
            },
            formatter: function (name) {
              var data = option.series //获取series中的data
              var tarValue
              var zbValue
              for (var i = 0, l = data.length; i < l; i++) {
                if (data[i].pieData.name == name) {
                  tarValue = data[i].pieData.value
                  zbValue = data[i].pieData.percent.toFixed(2)
                }
              }
              // return '{name|' + name + '}\n{value|' +p+"%  "+ tarValue + '件'+'}'
              return '{name|' + name + '}{value1|' + tarValue + '}{value|' + zbValue + '%}'
            },
            // padding: [0, 600, 0, 200],
          },
          xAxis3D: {
            min: -1,
            max: 1,
          },
          yAxis3D: {
            min: -1,
            max: 1,
          },
          zAxis3D: {
            min: -1,
            max: 1,
          },
          grid3D: {
            show: false,
            z: 1,
            boxHeight: 10,
            top: '-20%',
            left: '0%',
            viewControl: {
              // 3d效果可以放大、旋转等，请自己去查看官方配置
              alpha: 25,
              // beta: 30,
              rotateSensitivity: 1,
              zoomSensitivity: 0,
              panSensitivity: 0,
              autoRotate: true,
              distance: 240,
            },
            // 后处理特效可以为画面添加高光、景深、环境光遮蔽（SSAO）、调色等效果。可以让整个画面更富有质感。
            postEffect: {
              // 配置这项会出现锯齿，请自己去查看官方配置有办法解决
              enable: false,
              bloom: {
                enable: true,
                bloomIntensity: 0.1,
              },
              SSAO: {
                enable: true,
                quality: 'medium',
                radius: 2,
              },
              // temporalSuperSampling: {
              //   enable: true,
              // },
            },
          },
          graphic: [
            {
              type: 'image',
              id: 'logo',
              bounding: 'raw',
              ...that.graphicCunstom,
              style: {
                image: imgUrl,
                opacity: 0.5,
              },
            },
          ],
          series,
        }
        return option
      }
      myChart.setOption(option)
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
.wrap-container {
  width: 100%;
  .tab-wraper {
    width: fit-content;
    margin: 10px;
    float: right;
    z-index: 100;
    position: relative;
  }
  .lineChart {
    width: 100%;
    height: 480px;
  }
}
</style>