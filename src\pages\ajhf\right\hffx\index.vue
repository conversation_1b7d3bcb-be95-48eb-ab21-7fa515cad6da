<template>
  <div>
    <CommonTitle text='回访分析' @click.native='titleClick'></CommonTitle>
    <div class="hffx-container">
      <div style="position: absolute; top: 40px; left: 680px; z-index: 2" v-show="year == $currentYear" class='yearChange'>
        <el-date-picker
          v-model="value1"
          type="monthrange"
          @change="(range) => initAllChart(range,city,year)"
          range-separator="-"
          start-placeholder="开始月份"
          value-format="yyyy-MM"
          end-placeholder="结束月份"
          :append-to-body='false'>
        </el-date-picker>
      </div>
      <div class="hffx-container-charts">
        <div class="hffx-container-chart">
          <div class="chart2" id="lingyuChart"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import moment from 'moment'
import { getHffxList } from '@/api/ajhf'
import { registerCityYearChangeEvents } from '@/utils'
export default {
  name: 'index',
  components: {
    CommonTitle
  },
  data() {
    return {
      year: localStorage.getItem("year"),
      city: localStorage.getItem("city"),
      value1: [
        new Date().getFullYear() + "-01-01",
        moment(new Date()).format("YYYY-MM-DD"),
      ],
      questionList: [
        {
          id:"1",
          name:"需要了解信用信息",
          value:0
        },
        {
          id:"2",
          name:"按期履行缴纳困难",
          value:0
        },
        {
          id:"3",
          name:"未告知裁量理由",
          value:0
        },
        {
          id:"4",
          name:"执法人员存在廉洁问题",
          value:0
        },
        {
          id:"5",
          name:"未告知并保障相应权利",
          value:0
        },
        {
          id:"6",
          name:"不当行为侵害权利",
          value:0
        },
        {
          id:"7",
          name:"需进一步法律释疑",
          value:0
        },
        {
          id:"8",
          name:"需要法律帮扶",
          value:0
        },
        {
          id:"9",
          name:"需要了解相关审批事项",
          value:0
        },
        {
          id:"10",
          name:"执法工作不规范",
          value:0
        },
        {
          id:"11",
          name:"有意见建议",
          value:0
        }
      ],
    }
  },
  computed: {},
  mounted() {
    registerCityYearChangeEvents(this,this.initApi)
  },
  methods: {
    initApi(city,year) {
      this.initAllChart(this.value1, city)
    },
    //获取理由维度，领域维度图表信息
    initAllChart(range, city) {
      this.questionListMap = this.questionList.reduce((map, obj) => {
        map[obj.id] = obj;
        return map;
      }, {});
      getHffxList({
        xsq: city,
        startTime: range[0],
        endTime: range[1],
      }).then(res => {
        console.log(res,"right")
        if (res.data && res.code === 200) {
          let resdata = res.data.map(item => {
            const { name, bfb } = item;
            return {
              name: this.questionListMap[name]?.name || '', // 添加了防御性编程，如果找不到name，则提供一个默认值
              value: bfb.toFixed(0)
            };
          });
          this.initChartLINGYU(resdata)
        } else {
          console.error("请求失败，错误代码:", res.code);
        }
      })
    },
    initChartLINGYU(resdata) {
      let that = this
      let myChart = this.$echarts.init(document.getElementById("lingyuChart"));
      let imgUrl = require('@/assets/ajhf/chartBg3.png')
      let option = {
        color: ['#00C0FF', '#22E8E8', '#FFD461', '#A9DB52', '#B76FD8', '#FD852E', '#FF4949', '#0594C3',
          '#009D9D', '#A47905'
        ],

        tooltip: {
          trigger: "item",
          backgroundColor: "rgba(51, 51, 51, 0.7)",
          borderWidth: 0,
          axisPointer: {
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
          textStyle: {
            color: "white",
            fontSize: "24",
          },
        },
        legend: {
          orient: 'vertical',
          left: '0',
          top: '65%',
          bottom: '0%',
          icon: 'circle',
          itemGap: 10,
          textStyle: {
            rich: {
              name: {
                fontSize: 25,
                color: '#ffffff',
                padding: [0, 20, 0, 15]
              },
              value: {
                fontSize: 25,
                color: '#2CC6F9',
                // padding: [10, 0, 0, 15]
              },
            }
          },
          formatter: function (name) {
            var data = option.series[0].data //获取series中的data
            var total = 0
            var tarValue
            for (var i = 0, l = data.length; i < l; i++) {
              total += Number(data[i].value)
              if (data[i].name == name) {
                tarValue = data[i].value
              }
            }
            that.serverNum = total;
            // var p = ((tarValue / total) * 100).toFixed(2)
            // return '{name|' + name + '}{value|' + Number.isNaN(p)?0:p + '%}'
            if (total === 0) {
              return `{name|${name}}{value|0%}`; // 直接返回0%，同时保持了字符串格式不变
            }

            // 计算百分比并确保其为数字类型
            let percentage = ((tarValue / total) * 100).toFixed(2);
            percentage = Number(percentage);

            // 使用模板字符串和Number.isNaN来提高代码的可读性和准确性
            return `{name|${name}}{value|${Number.isNaN(percentage) ? 0 : percentage + '%'}}`;
          },
        },
        graphic: [{
          type: "image",
          id: "logo2",
          left: "28.4%",
          top: "2.5%",
          z: -10,
          bounding: "raw",
          rotation: 0, //旋转
          origin: [50, 50], //中心点
          scale: [0.8, 0.8], //缩放
          style: {
            image: imgUrl,
            opacity: 1,
          },
        }, ],
        series: [{
          name: '',
          type: 'pie',
          radius: ['30%', '45%'],
          center: ['50%', '30%'],
          roseType: '',
          itemStyle: {
            borderRadius: 0,
          },
          label: {
            show: true,
            color: "#FFFFFF",
            formatter: function (params) {
              return params.percent + "%"
            },
            fontSize: 20
          },
          labelLine: {
            length: 15,
            length2: 30,
            maxSurfaceAngle: 80
          },
          data: resdata,
        }, ],
      }
      myChart.setOption(option)
      myChart.getZr().on('mousemove', param => {
        myChart.getZr().setCursorStyle('default')
      })
    },
    titleClick() {
      window.location.href = '/ygf/command/ajhf/ProblemStatistics'
    }
  },
  watch: {}
}
</script>

<style scoped lang='less'>
.hffx-container {
  width: 100%;
  margin-bottom: 53px;
  /deep/ .yearChange {
    .el-input__inner {
      height: 48px !important;
      background-color: #132c4e !important;
      border: 2px solid #afdcfb !important;
      color: #fff !important;
      border-radius: 15px !important;
    }
    .el-picker-panel {
      left: -300px !important;
    }
  }
}

.hffx-container-charts {
  width: 100%;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  margin-top: 48px;
}

.hffx-container-chart {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
}

.chart2 {
  width: 800px;
  height: 616px;
}
</style>