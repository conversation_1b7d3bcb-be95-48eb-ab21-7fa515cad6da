import initSceneView from './MapUtils/initSceneView'
import initMapView from './MapUtils/initMapView'
import { loadArcgisLayer } from './MapUtils/loadArcgisLayer'
import mapClickEventHandle1 from './MapUtils/MapClickEventHandle'
import mapPopupWidget1 from './MapUtils/MapPopupWidget'
import addGeojsonToMap from './MapUtils/addGeojsonToMap'
import { load3DTextLayer } from './MapUtils/load3DTextLayer'
import HeatmapRenderer from '@arcgis/core/renderers/HeatmapRenderer.js'
import FeatureLayer from '@arcgis/core/layers/FeatureLayer.js'
import loadHeatmapLayer from './MapUtils/loadHeatmapLayer.js'
import { addRoadLayer, removeRoadLayer } from './MapUtils/loadRoadLayer'
import ClusterLayer from "./MapUtils/esriClusterLayer.js";
import Draw from "./MapUtils/Draw.js";
import DrawGeometry from './MapUtils/DrawGeometry';
import coordtransform from '@/utils/coordtransform.js'
import {
    converPathToPoints,
    geodesic4490Areas,
  } from "./MapUtils/geometryUtils.js";
import addArrowLineLayer from "./MapUtils/addArrowLineLayer.js";
import addLineLayer from "./MapUtils/addLineLayer.js";
import {
    addHeatMap,
    removeHeatmap,
    removeDynamicWall,
    addMix3dHeatmap,
    removeMix3dHeatmap,
  } from "./MapUtils/effect/index.js";
export class MapService {
    constructor() {
        this.events = {}
        this.popups = {}
        this.mapclick = null
        this.drawTool1 = null
        this.layerstate = {}
        this.map = null
        this.mapview = null
        this.layers = []
        this._clickEvtPoint = ""
        this._onload()
        this.tool = {
            zoomOut: () => {
                if (mapUtil.mapview) {
                    let zm = mapUtil.mapview.zoom - 1
                    mapUtil.mapview.goTo({
                        target: mapUtil.mapview.center,
                        zoom: zm
                    });
                }
            },
            zoomIn() {
                if (mapUtil.mapview) {
                    let zm = mapUtil.mapview.zoom + 1
                    mapUtil.mapview.goTo({
                        target: mapUtil.mapview.center,
                        zoom: zm
                    });
                }
            },
            changeBaseMap(type = "black") {
                if (!(window.frames['map']?window.frames['map']:window.parent.frames['map']).view) return
                const typeEnum = {
                    "black": "vector",
                    "img": "image",
                    "gfimg": "newImage"
                }
                if (!(window.frames['map']?window.frames['map']:window.parent.frames['map']).view)
                    return;
                let view = (window.frames['map']?window.frames['map']:window.parent.frames['map']).view;
                let code = localStorage.getItem("QRCode")
                "gfimg" == type ? (window.frames['map']?window.frames['map']:window.parent.frames['map']).gis.exchangeMap(view, typeEnum[type], code) : (window.frames['map']?window.frames['map']:window.parent.frames['map']).gis.exchangeMap(view, typeEnum[type])
                mapUtil._baseMapType = type
            },
            pickPoint(callback) {
                if (!this._pickEvent) {
                    this._pickEvent = (e) => {
                        if (callback) callback(e)
                    }
                    (window.frames['map']?window.frames['map']:window.parent.frames['map']).ArcGisUtils.mapClickEventHandle.addCoordinateListener((point) => {
                        const { x, y } = point;
                        if (this._pickEvent) this._pickEvent({ "lng": x, "lat": y })
                    });
                }
            },
            _startPick() {
    
            },
            stopPick() {
                if (this._pickEvent) {
                    (window.frames['map']?window.frames['map']:window.parent.frames['map']).ArcGisUtils.mapClickEventHandle._coordinateClientList = []
                    this._pickEvent = null
                }
            },
            backHome:() => {
                if (this.mapview) this.mapview.goHome()
            },
            changeMode(mode) {
                if (!(window.frames['map']?window.frames['map']:window.parent.frames['map']).view) return
                (window.frames['map']?window.frames['map']:window.parent.frames['map']).gis.change2DOr3D((window.frames['map']?window.frames['map']:window.parent.frames['map']).view, mode == 2 ? '2D' : '3D')
            },
            change3D(type) {
                if (!(window.frames['map']?window.frames['map']:window.parent.frames['map']).view)
                    return
                if (1 == type) { // 切换到二维
                    (window.frames['map']?window.frames['map']:window.parent.frames['map']).view.goTo({
                        target: (window.frames['map']?window.frames['map']:window.parent.frames['map']).view.center,
                        tilt: 0,
                    });
                }
                else if (10 == type) {// 切换到3d
                    (window.frames['map']?window.frames['map']:window.parent.frames['map']).view.goTo({
                        target: (window.frames['map']?window.frames['map']:window.parent.frames['map']).view.center,
                        tilt: 47.03419627587757,
                    });
                }
                //首屏（没有左右侧面板）初始化视角，加精模
                else if (11 == type) {
                    (window.frames['map']?window.frames['map']:window.parent.frames['map']).view.goTo({
                        position: {
                            spatialReference: {
                                wkid: 4490,
                            },
                            x: 119.62357755062095,
                            y: 29.059796274571504,
                            z: 1851.2331084022298,
                        },
                        heading: 0,//350.3785610052847,
                        tilt: 59.95894739193048,
                    });
                    (window.frames['map']?window.frames['map']:window.parent.frames['map']).mapUtil.loadModelLayer({
                        layerid: "精模",
                        type: "jm"
                    })
                    // mapUtil._weatherSwitch = true
                    // mapUtil.loadWeatherEffect()
                }
                //切换回原来大脑首页（有左右侧面板）初始化视角，去精模
                else if (12 == type) {
                    (window.frames['map']?window.frames['map']:window.parent.frames['map']).view.goTo({
                        position: {
                            spatialReference: {
                                wkid: 4490,
                            },
                            x: 119.65842342884746,
                            y: 28.97890877935061,
                            z: 10280.48295974452,
                        },
                        heading: 0,// 354.2661149152386,
                        tilt: 47.902020858006175,
                    });
                    (window.frames['map']?window.frames['map']:window.parent.frames['map']).mapUtil.removeLayer("精模")
                    (window.frames['map']?window.frames['map']:window.parent.frames['map']).mapUtil._weatherSwitch = false
                }
            },
            changeScene(type = 'day') {
    
            },
            addEffect(type) {
                switch (type) {
                    case "flowline":
                        (window.frames['map']?window.frames['map']:window.parent.frames['map']).gis.addOdLineEffect({
                            queryUrl: 'https://geoplat.geoscene.cn/server/rest/services/daluzizao/MapServer/41',
                            color: '#16D3D8',
                            size: 3,
                            length: 0.4,
                            speed: 0.2,
                            isShow: false,
                        })
                        break;
    
                    default:
                        break;
                }
            },
            removeEffect(type) {
                switch (type) {
                    case "flowline":
                        (window.frames['map']?window.frames['map']:window.parent.frames['map']).gis.removeOdLineEffect()
                        break;
    
                    default:
                        break;
                }
            }
        }
        this.searchTool = {
            _layerlist: null,
            _style: {},
            results: [],
            promiseObjs: [],
            _init() {
                mapUtil._ajaxQuery('http://localhost:38400/jhgt/jhgt107/32a9c9a2bec34f6f9478288e32de3314/arcgis/rest/services/GCS330700_3004_CLDJ/GCS330700_3004_CLDJ_DRKQSDJ/MapServer/0/query?outfields=*&f=geojson&where=kqmc%20like%20%27%25%E5%AE%89%E5%9C%B0%E6%B0%B4%E5%BA%93%25%27&resultRecordCount=3', {}, res => {
                    mapUtil.logInfo("连接jhgt服务")
                })
            },
            _getLayerlist(cb) {
                this._init()
                if (this._layerlist) return this._layerlist
                mapUtil._ajaxQuery('https://www.fastmock.site/mock/f2ecc6c32f4a68dfaedf6c429d8e24be/jhcsdn/map/searchtool/config', {}, res => {
                    this._layerlist = res.data.layerlist
                    this._style = res.data.searchTool.style
                    if (cb) cb(res)
                })
            },
            _getLayercfg(id) {
                if (!this._layerlist) return null
                for (let i = 0; i < this._layerlist.length; i++) {
                    if (this._layerlist[i].id == id) {
                        return this._layerlist[i]
                    }
                }
                return null
            },
            _getQueryPromise(key, selectedLayer) {
                let layercfg = this._getLayercfg(selectedLayer.id)
                let obj = {}
                if (layercfg) {
                    let p1 = new Promise((resolve, reject) => {
                        // mapUtil._ajaxQuery(layercfg.resturl + `/0/query?where=${layercfg.keyfield} like '%${key}%'&outfields=*&resultRecordCount=10&f=json`, {}, res => {
                        mapUtil._ajaxQuery(layercfg.resturl + `/0/query?outfields=*&f=geojson`, {
                            where: `${layercfg.keyfield} like '%${key}%'`,
                            resultRecordCount: 3
                        }, res => {
                            res.layerid = "searchTool_result_" + selectedLayer.id
                            res.id = selectedLayer.id
                            res.keyfield = layercfg.keyfield
                            resolve(res)
                            obj.abort = reject
                        })
                    });
                    obj.promise = p1;
                    return obj;
                }
                return null
            },
            _getPromiseList(key, selectedLayers) {
                let promises = []
                selectedLayers.forEach(layer => {
                    let promise = this._getQueryPromise(key, layer)
                    if (promise) {
                        promises.push(promise.promise)
                        this.promiseObjs.push(promise)
                    }
                })
                return promises
            },
            _setResult(item) {
                if (item.features.length > 0) {
                    item.features.forEach((f, idx) => {
                        let geojson = { ...item }
                        geojson.features = [f]
                        geojson.label = f.properties[item.keyfield]
                        geojson.id = item.layerid + "_" + idx
                        this.results.push(geojson)
                    })
                }
            },
            searchByKey(key, selectedLayers, callback) {
                this.clear()
                if (this.promiseObjs) {
                    this.promiseObjs.forEach(item => {
                        item.abort()
                    })
                }
                let promises = this._getPromiseList(key, selectedLayers)
                if (promises.length < 1) {
                    console.warn("当前勾选的图层尚未配置搜索，请勾选【2022年生态保护红线（202210版）-面】来测试")
                    return
                }
                Promise.all(promises).then(res => {
                    mapUtil.logInfo(res)
                    res.forEach((item, idx) => {
                        if (item.features.length > 0) {
                            const type = item.features[0].geometry.type
                            if (type == "Polygon") {
                                mapUtil.loadPolygonLayer({
                                    layerid: item.layerid,
                                    data: item,
                                    style: this._style.polygon,
                                    onclick: (e) => {
                                        mapUtil.logInfo(e)
                                    }
                                })
                            }
                            else if (type == "Point") {
                                //todo:
                            }
                            else if (type == "LineString") {
                                mapUtil.loadPolylineLayer({
                                    layerid: item.layerid,
                                    data: item,
                                    style: this._style.polyline,
                                    onclick: (e) => {
                                        mapUtil.logInfo(e)
                                    }
                                })
                            }
                            this._setResult(item, key)
                        }
                    })
                    if (callback) callback(this.results)
                })
            },
            zoomTo(id) {
                this.results.forEach((item) => {
                    if (item.id == id) {
                        mapUtil.fitBounds(item)
                    }
                })
            },
            clear() {
                Object.keys(mapUtil.layers).forEach(key => {
                    if (key.startsWith('searchTool_result_')) {
                        mapUtil.removeLayer(key)
                    }
                })
                this.results = []
            }
        }
        this.plotTool = {
            _tool: null,
            _currType: "",
            _oncomplete: null,
            _typeEnum: {
                "point": "point",
                "polygon": "polygon",
                "line": "polyline",
                "circle": "circle",
                "rect": "rectangle",
            },
            active: (type = "polygon", oncomplete, once = true) => {
                if (!this.plotTool._tool) { //这里this指向的是mapservice 箭头函数没有this
                    this.plotTool._tool = new Draw({ view: this.mapview })
                }
                this.plotTool._tool.draw(this.plotTool._typeEnum[type]).then(e => {
                    let res = ArcgisToGeojsonUtils.arcgisToGeoJSON(e)
                    if (!res.properties) res.properties = {}
                    if (this.plotTool._typeEnum[type] == "circle") {
                        //计算半径
                        let la1 = e.geometry.centroid.latitude
                        let ln1 = e.geometry.centroid.longitude
                        let la2 = e.geometry.rings[0][0][1]
                        let ln2 = e.geometry.rings[0][0][0]
    
                        function GetDistance(lat1, lng1, lat2, lng2) {
                            let radLat1 = lat1 * Math.PI / 180.0;
                            let radLat2 = lat2 * Math.PI / 180.0;
                            let a = radLat1 - radLat2;
                            let b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
                            let s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) +
                                Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
                            s = s * 6378.137;// EARTH_RADIUS;
                            s = Math.round(s * 10000) / 10000;
                            return s;
                        }
                        // 调用 return的距离单位为km
    
                        let radiu = GetDistance(la1, ln1, la2, ln2)
                        let cent = { lat: la1, lng: ln1 }
                        let are = Math.PI * Math.pow(radiu, 2)
                        let circleresult = { radius: radiu, center: cent, area: are, coordinates: e.geometry.rings }
                        if (oncomplete) {
                            oncomplete(circleresult)
                            if (!once) {
                                this.plotTool.active(type, oncomplete)
                            } else {
                                this.plotTool._tool.cancel()
                            }
                        }
                    } else {
                        if (this.plotTool._typeEnum[type] == "polygon") {
                            res.properties.area = turf.area(res)
                        }
                        if (oncomplete) {
                            oncomplete(res)
                            if (!once) {
                                this.plotTool.active(type, oncomplete)
                            } else {
                                this.plotTool._tool.cancel()
                            }
                        }
                    }
    
                })
    
            },
            close: () => {
                if (this._tool) {
                    this._tool.clear()
                    this._tool.destroy()
                    this._tool = null
                }
            },
            clear: () => {
                if (this._tool) {
                    this._tool.clear()
                }
            }
        }
        this.drawTool = {
            layers: {},
            draw: (type, opts) => {
                let params = null
                let layerid = opts.layerid || 'drawtool_circle'
                switch (type) {
                    case "circle":
                        this.clear([layerid])
                        let _circlelist = opts.circles
                        params = {
                            "funcName": "drawCircle",
                            "circleData": []
                        }
                        this.drawTool.layers[layerid] = {
                            funcName: "drawCircle",
                            subs: [],
                            clear: function () {
                                if (this.subs) this.subs.forEach(item => {
                                    item.remove()
                                })
                            }
                        }
                        _circlelist.forEach((item, idx) => {
                            item.center.push(1000) //来自易利，不知道第三个参数是干嘛的，可能多了个高度
                            let _strokeColor = item.strokeColor || [0, 255, 255, 1]
                            const _outlineColor = `rgba(${_strokeColor[0]},${_strokeColor[1]},${_strokeColor[2]},${_strokeColor[3]})`
                            let _circledata = {
                                coords: item.center,
                                r: item.radius,
                                color: item.fillColor || [0, 255, 255, 1],
                                colorOutline: _outlineColor,
                                name: `${layerid}_${idx}`
                            }
                            params.circleData.push(_circledata)
                            const _circleGra = gisHelper.createCircle({
                                center: item.center,
                                radius: item.radius ? item.radius / 1000 : 5,
                                strokeColor: _strokeColor,
                                color: item.fillColor || [0, 255, 255, 1],
                                view: (window.frames['map']?window.frames['map']:window.parent.frames['map']).view,
                                layer: gisHelper.createGraphicsLayer((window.frames['map']?window.frames['map']:window.parent.frames['map']).view)
                            })
                            this.drawTool.layers[layerid].subs.push(_circleGra)
                        })
                        break;
                    case "polygon":
                        break;
                    default:
                        break;
                }
            },
            clear: (ids) => {
                if (ids && ids.length) {
                    ids.forEach(item => {
                        this.drawTool.layers[item] && this.drawTool.layers[item].clear()
                        this.drawTool.layers[item] = null
                    })
                } else {
                    Object.keys(this.drawTool.layers).forEach(layerid => {
                        this.drawTool.layers[layerid] && this.drawTool.layers[layerid].clear()
                    })
                    this.drawTool.layers = {}
                }
            }
        }
        this.measureTool = {
            listener: null,
            active(type = "distance", opts = {}) {
                let data = null
                switch (type) {
                    case "distance":
                        data = {
                            funcName: 'getDistance',
                            color: opts.color || [255, 0, 0, 1],
                            pointAndTextStatus: false // 是否展示文字和点位
                        }
                        break;
                    case "area":
                        data = {
                            funcName: 'startDrawPolygon',
                            color: opts.color || [0, 0, 255, 0.4],//绘制区域颜色
                            pointAndTextStatus: false, // 是否展示文字和点位
                            getSectionDatas: false,//是否查询区域数据，默认false
                        }
                        break;
                    default:
                        break;
                }
                if (data) {
                    mapUtil._postData(data)
                    if (!this.listener) {
                        this.listener = function (e) {
                            let result = { action: "", distance: 0, area: 0 }
                            let data = e.data
                            switch (data.type) {
                                case "line":
                                    result.action = "测距"
                                    result.distance = data.getDistance
                                    break;
                                case "Polygon":
                                    result.action = "测面"
                                    result.area = data.getDistance
                                    break;
                                default:
                                    break;
                            }
                            mapUtil.logInfo(result)
                        }
                        window.addEventListener('message', this.listener)
                    }
                }
            },
            close() {
                if (this.listener) window.removeEventListener('message', this.listener)
                mapUtil._postData({
                    "funcName": "rmLine", //结束绘制多边形
                })
                mapUtil._postData({
                    "funcName": "rmDrawPolygon", //结束绘制多边形
                })
            }
        }
    }

    initMap(container) {
       this.mapview = initSceneView({div: container, basemap: "image", viewingModeExtend: "local"})
    //    this.mapview = initMapView({divId: container, basemap: "image"})
       
       // 初始化 mapClickEventHandle和mapPopupWidget
       window.mapClickEventHandle = new mapClickEventHandle1(this.mapview)
       window.mapPopupWidget = new mapPopupWidget1(this.mapview)

       // 添加鼠标滚轮事件监听 移除默认缩放 
       this.mapview.on("mouse-wheel", (event) => {
           // 阻止默认行为
           event.stopPropagation();
           
           // 获取当前缩放比例
           const scaleHeight1 = 1.0 / window.scaleHeight;
           const scaleWidth1 = 1.0 / window.scaleWidth
           
           // 获取鼠标位置并修正坐标
           const point = {
               x: event.x * scaleWidth1,
               y: event.y * scaleHeight1
           };
           
           // 计算新的缩放级别
           const zoomChange = event.deltaY > 0 ? -0.5 : 0.5;
           const newZoom = this.mapview.zoom + zoomChange;
           
           // 将屏幕坐标转换为地图坐标
           const mapPoint = this.mapview.toMap(point);
           
           // 执行缩放动画
           this.mapview.goTo({
               center: mapPoint,
               zoom: newZoom
           }, {
               duration: 200,
               easing: "ease-out"
           });
       });

       return this.mapview
    }
    _sortClickEvts() {
        // 事件排序
        let pointlyr = [], polygonlyr = []
        for (let i = 0; i < mapClickEventHandle._callbackEvent.length; i++) {
            const __id = mapClickEventHandle._callbackEvent[i].layerId
            if (this.mapview.map.findLayerById(__id)) {
                if (this.mapview.map.findLayerById(__id)?.geometryType == "polygon") polygonlyr.push(mapClickEventHandle._callbackEvent[i])
                else {
                    pointlyr.push(mapClickEventHandle._callbackEvent[i])
                }
            }
        }
        mapClickEventHandle._callbackEvent = [...pointlyr, ...polygonlyr]
    }
    // 通用打点
    loadPointLayer(opts) {
        let datacfg = opts.datacfg || {};
        let iconcfg = opts.iconcfg || { image: "" };
        console.log(iconcfg)
        let labelcfg = opts.labelcfg || {};
        let layercfg = opts.layercfg || {};
        let popcfg = opts.popcfg || {}
        let onclick = opts.onclick
        let layerid = opts.layerid || layercfg.layerid
        let data = opts.data
        let iconlist = iconcfg.iconlist || {}
        const { cluster = true, viewer = this.mapview } = opts
        if (!data || data.length == 0) {
            console.error('上图数据不可为空！', layercfg.layerid)
            return
        };
        if (!this._checkBeforeLoad(layerid)) {
            return
        }
        const _data = []
        const _attr = {}
        data.forEach((item, idx) => {
            item.layerid = layerid
            item.guid = this.guid()
            item.objid = idx
            // if (item.longitude != '' &&
            //     item.longitude != 0.0 &&
            //     item.longitude != null &&
            //     item.longitude != undefined &&
            //     !isNaN(item.longitude)) {
            //     item.code = 3
            //     // item.esX = item.longitude
            //     // item.esY = item.latitude
            //     _data.push(item)
            //     _attr[item.guid] = item.data
            // }
            // if (!item.name) item.name = item.guid   //聚合功能data必传name字段
            // if (cluster) item.id = item.guid       //聚合功能data必传id字段
            let lng = item[datacfg.lngfield] * 1 || item.lng * 1;
            let lat = item[datacfg.latfield] * 1 || item.lat * 1;
            if (lng != '' &&
                lng != 0.0 &&
                lng != null &&
                lng != undefined &&
                !isNaN(lng)) {
                item.code = 3
                item.longitude = lng
                item.latitude = lat
                item.esX = lng
                item.esY = lat
                _data.push(item)
                _attr[item.guid] = item.data
            }
            if (!item.name) item.name = item.guid   //聚合功能data必传name字段
            if (cluster) item.id = item.guid     
        })
        const imgUrl = (iconcfg.image.indexOf(".png") >= 0 || iconcfg.image.indexOf(".gif") >= 0) ? iconcfg.image : null
        if (data.length > 1 && cluster) {//如果cluster为true则进行聚合，默认采用聚合
            let uniqueCfg = { valueArr: [], imgArr: [], sizeArr: [] }
            if (iconlist.list && iconlist.field) {
                iconlist.list.forEach(item => {
                    uniqueCfg.valueArr.push(item.value)
                    uniqueCfg.imgArr.push(item.src)
                    uniqueCfg.sizeArr.push(item.size)
                })
                uniqueCfg.field = iconlist.field
            }
            this.loadClusterLayer({
                layerid: layerid,
                data: data,
                lyrCfg: {
                    field: "id", // 接口返回值：唯一的字段
                    clusterImg: imgUrl || `https://csdn.dsjj.jinhua.gov.cn:8101/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/${iconcfg.image || "bus"}.png` || '', // 聚合图标地址
                    iconImg: imgUrl || `https://csdn.dsjj.jinhua.gov.cn:8101/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/${iconcfg.image || "bus"}.png`, // 默认图标地址
                    criticalZoom: 17,
                },
                popCfg: {
                    title: popcfg.title,//标题
                    dict: popcfg.dict,
                    attr: _attr,
                    onclick
                },
                uniqueCfg,
            });
            return;
        }
        let rendererIcon = {
            size: iconcfg.size || 64, // 图片大小
            src: imgUrl || `https://csdn.dsjj.jinhua.gov.cn:8101/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/${iconcfg.image || "bus"}.png`, // 图片src
        }
        console.log(rendererIcon)
        if (iconlist) {
            rendererIcon.field = iconlist.field
            rendererIcon.uniqueValueInfos = iconlist.list
            // 解决数据只有一条的时候，uniqueValueInfos不起作用的问题，mod 2023年5月11日
            if (data.length == 1 && iconlist.field && iconlist.list && data[0][iconlist.field] && iconlist.list[data[0][iconlist.field]]) {
                rendererIcon.src = iconlist.list[data[0][iconlist.field]]
            }
        }
        loadArcgisLayer(this.mapview, {
            code: 3,
            data: _data,
            type: "customFeature",
            objectIdField: "objid",
            rendererIcon,
        }).then(res => {
            const pointEventId = mapClickEventHandle.add(res.id, (point, graphic, graphics) => {
                const pointStr = `${point.x},${point.y}`
                if (this._clickEvtPoint == pointStr) return
                this._clickEvtPoint = pointStr
                if (graphic && graphics.length == 1) {
                    graphic.attributes.position = point
                    graphic.attributes.data = _attr[graphic.attributes.guid]
                    let graArray = []
                    if (graphics) {
                        graphics.forEach(item => {
                            item.attributes.data = _attr[item.attributes.guid]
                            graArray.push(item.attributes)
                        })
                    }
                    if (onclick) onclick(graphic.attributes, graArray)
                    if (popcfg.dict) {
                        this._createPopup({
                            layerid,
                            position: graphic.geometry,
                            dict: popcfg.dict,
                            attr: graphic.attributes,
                            title: popcfg.title || "详情",
                            offset: popcfg.offset,
                        })
                    }

                } else if (graphics.length > 1) {
                    const datas = [];
                    for (let i = 0; i < graphics.length; i++) {
                        const { attributes } = graphics[i];
                        attributes.data = _attr[attributes.guid]
                        datas.push(attributes)
                    }
                    graphic.attributes.data = _attr[graphic.attributes.guid]
                    if (onclick) {
                        let graArray = []
                        graphics.forEach(item => {
                            item.attributes.data = _attr[item.attributes.guid]
                            graArray.push(item.attributes)
                        })
                        onclick(graphic.attributes, graArray)
                    } else {
                        this._createPopup({
                            layerid,
                            position: point,
                            dict: popcfg.dict,
                            attr: datas,
                            title: popcfg.title || "详情",
                            offset: popcfg.offset,
                        })
                    }
                }
            })
            this._sortClickEvts()
            setTimeout(() => {
                $('.mapPopup').css({
                    'background': 'none', 'box-shadow': 'none'
                })
                $('.mapPopup .header').css({ display: 'none' })
                $('.mapPopup div.body::before').css({ display: 'none' })
            }, 450);
            //鼠标滑动事件
            if (opts.onblur && this.blurevts && !this.blurevts[layerid]) {
                this.blurevts[layerid] = function (e, pt) {
                    if (layerid == e.layerid)
                        opts.onblur({ ...e, data: _attr[e.guid], position: pt })
                }
            }
            this.layers[layerid] = res
            console.log(this)
            if (opts.onload) opts.onload(res)
        })
    }
    // 图层管理用到了
    loadPointLayer1(opts) {
        let datacfg = opts.datacfg || {};
        let iconcfg = opts.iconcfg || { image: "" };
        console.log(iconcfg)
        let labelcfg = opts.labelcfg || {};
        let layercfg = opts.layercfg || {};
        let popcfg = opts.popcfg || {}
        let onclick = opts.onclick
        let layerid = opts.layerid || layercfg.layerid
        let data = opts.data
        let iconlist = iconcfg.iconlist || {}
        const { cluster = true, viewer = this.mapview } = opts
        if (!data || data.length == 0) {
            console.error('上图数据不可为空！', layercfg.layerid)
            return
        };
        if (!this._checkBeforeLoad(layerid)) {
            return
        }
        const _data = []
        const _attr = {}
        data.forEach((item, idx) => {
            item.layerid = layerid
            item.guid = this.guid()
            item.objid = idx
            let lng = item[datacfg.lngfield] * 1 || item.lng * 1;
            let lat = item[datacfg.latfield] * 1 || item.lat * 1;
            if (lng != '' &&
                lng != 0.0 &&
                lng != null &&
                lng != undefined &&
                !isNaN(lng)) {
                item.code = 3
                item.longitude = lng
                item.latitude = lat
                item.esX = lng
                item.esY = lat
                _data.push(item)
                _attr[item.guid] = item.data
            }
            if (!item.name) item.name = item.guid   //聚合功能data必传name字段
            if (cluster) item.id = item.guid       //聚合功能data必传id字段
        })
        const imgUrl = (iconcfg.image.indexOf(".png") >= 0 || iconcfg.image.indexOf(".gif") >= 0) ? iconcfg.image : null
        if (data.length > 1 && cluster) {//如果cluster为true则进行聚合，默认采用聚合
            let uniqueCfg = { valueArr: [], imgArr: [], sizeArr: [] }
            if (iconlist.list && iconlist.field) {
                iconlist.list.forEach(item => {
                    uniqueCfg.valueArr.push(item.value)
                    uniqueCfg.imgArr.push(item.src)
                    uniqueCfg.sizeArr.push(item.size)
                })
                uniqueCfg.field = iconlist.field
            }
            this.loadClusterLayer({
                layerid: layerid,
                data: data,
                lyrCfg: {
                    field: "id", // 接口返回值：唯一的字段
                    clusterImg: imgUrl || `https://csdn.dsjj.jinhua.gov.cn:8101/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/${iconcfg.image || "bus"}.png` || '', // 聚合图标地址
                    iconImg: imgUrl || `https://csdn.dsjj.jinhua.gov.cn:8101/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/${iconcfg.image || "bus"}.png`, // 默认图标地址
                    criticalZoom: 17,
                },
                popCfg: {
                    title: popcfg.title,//标题
                    dict: popcfg.dict,
                    attr: _attr,
                    onclick
                },
                uniqueCfg,
            });
            return;
        }
        let rendererIcon = {
            size: iconcfg.iconSize || 64, // 图片大小
            src: imgUrl || `https://csdn.dsjj.jinhua.gov.cn:8101/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/${iconcfg.image || "bus"}.png`, // 图片src
        }
        console.log(rendererIcon)
        if (iconlist) {
            rendererIcon.field = iconlist.field
            rendererIcon.uniqueValueInfos = iconlist.list
            // 解决数据只有一条的时候，uniqueValueInfos不起作用的问题，mod 2023年5月11日
            if (data.length == 1 && iconlist.field && iconlist.list && data[0][iconlist.field] && iconlist.list[data[0][iconlist.field]]) {
                rendererIcon.src = iconlist.list[data[0][iconlist.field]]
            }
        }
        loadArcgisLayer(this.mapview, {
            code: 3,
            data: _data,
            type: "customFeature",
            objectIdField: "objid",
            rendererIcon,
        }).then(res => {
            const pointEventId = mapClickEventHandle.add(res.id, (point, graphic, graphics) => {
                const pointStr = `${point.x},${point.y}`
                if (this._clickEvtPoint == pointStr) return
                this._clickEvtPoint = pointStr
                if (graphic && graphics.length == 1) {
                    graphic.attributes.position = point
                    graphic.attributes.data = _attr[graphic.attributes.guid]
                    let graArray = []
                    if (graphics) {
                        graphics.forEach(item => {
                            item.attributes.data = _attr[item.attributes.guid]
                            graArray.push(item.attributes)
                        })
                    }
                    if (onclick) onclick(graphic.attributes, graArray)
                    if (popcfg.dict) {
                        this._createPopup({
                            layerid,
                            position: graphic.geometry,
                            dict: popcfg.dict,
                            attr: graphic.attributes,
                            title: popcfg.title || "详情",
                            offset: popcfg.offset,
                        })
                    }

                } else if (graphics.length > 1) {
                    const datas = [];
                    for (let i = 0; i < graphics.length; i++) {
                        const { attributes } = graphics[i];
                        attributes.data = _attr[attributes.guid]
                        datas.push(attributes)
                    }
                    graphic.attributes.data = _attr[graphic.attributes.guid]
                    if (onclick) {
                        let graArray = []
                        graphics.forEach(item => {
                            item.attributes.data = _attr[item.attributes.guid]
                            graArray.push(item.attributes)
                        })
                        onclick(graphic.attributes, graArray)
                    } else {
                        this._createPopup({
                            layerid,
                            position: point,
                            dict: popcfg.dict,
                            attr: datas,
                            title: popcfg.title || "详情",
                            offset: popcfg.offset,
                        })
                    }
                }
            })
            this._sortClickEvts()
            setTimeout(() => {
                $('.mapPopup').css({
                    'background': 'none', 'box-shadow': 'none'
                })
                $('.mapPopup .header').css({ display: 'none' })
                $('.mapPopup div.body::before').css({ display: 'none' })
            }, 450);
            //鼠标滑动事件
            if (opts.onblur && this.blurevts && !this.blurevts[layerid]) {
                this.blurevts[layerid] = function (e, pt) {
                    if (layerid == e.layerid)
                        opts.onblur({ ...e, data: _attr[e.guid], position: pt })
                }
            }
            this.layers[layerid] = res
            if (opts.onload) opts.onload(res)
        })
    }
    // 站前一张图用到了
    loadPointLayer2(opts) {
        let datacfg = opts.datacfg || {};
        let iconcfg = opts.iconcfg || { image: "" };
        console.log(iconcfg)
        let labelcfg = opts.labelcfg || {};
        let layercfg = opts.layercfg || {};
        let popcfg = opts.popcfg || {}
        let onclick = opts.onclick
        let layerid = opts.layerid || layercfg.layerid
        let data = opts.data
        let iconlist = iconcfg.iconlist || {}
        const { cluster = true, viewer = this.mapview } = opts
        if (!data || data.length == 0) {
            console.error('上图数据不可为空！', layercfg.layerid)
            return
        };
        if (!this._checkBeforeLoad(layerid)) {
            return
        }
        const _data = []
        const _attr = {}
        data.forEach((item, idx) => {
            item.layerid = layerid
            item.guid = this.guid()
            item.objid = idx
            if (item.longitude != '' &&
                item.longitude != 0.0 &&
                item.longitude != null &&
                item.longitude != undefined &&
                !isNaN(item.longitude)) {
                item.code = 3
                // item.esX = item.longitude
                // item.esY = item.latitude
                _data.push(item)
                _attr[item.guid] = item.data
            }
            if (!item.name) item.name = item.guid   //聚合功能data必传name字段
            if (cluster) item.id = item.guid       //聚合功能data必传id字段
        })
        const imgUrl = (iconcfg.image.indexOf(".png") >= 0 || iconcfg.image.indexOf(".gif") >= 0) ? iconcfg.image : null
        if (data.length > 1 && cluster) {//如果cluster为true则进行聚合，默认采用聚合
            let uniqueCfg = { valueArr: [], imgArr: [], sizeArr: [] }
            if (iconlist.list && iconlist.field) {
                iconlist.list.forEach(item => {
                    uniqueCfg.valueArr.push(item.value)
                    uniqueCfg.imgArr.push(item.src)
                    uniqueCfg.sizeArr.push(item.size)
                })
                uniqueCfg.field = iconlist.field
            }
            this.loadClusterLayer({
                layerid: layerid,
                data: data,
                lyrCfg: {
                    field: "id", // 接口返回值：唯一的字段
                    clusterImg: imgUrl || `https://csdn.dsjj.jinhua.gov.cn:8101/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/${iconcfg.image || "bus"}.png` || '', // 聚合图标地址
                    iconImg: imgUrl || `https://csdn.dsjj.jinhua.gov.cn:8101/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/${iconcfg.image || "bus"}.png`, // 默认图标地址
                    criticalZoom: 17,
                },
                popCfg: {
                    title: popcfg.title,//标题
                    dict: popcfg.dict,
                    attr: _attr,
                    onclick
                },
                uniqueCfg,
            });
            return;
        }
        let rendererIcon = {
            size: iconcfg.size || 64, // 图片大小
            src: imgUrl || `https://csdn.dsjj.jinhua.gov.cn:8101/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/${iconcfg.image || "bus"}.png`, // 图片src
        }
        console.log(rendererIcon)
        if (iconlist) {
            rendererIcon.field = iconlist.field
            rendererIcon.uniqueValueInfos = iconlist.list
            // 解决数据只有一条的时候，uniqueValueInfos不起作用的问题，mod 2023年5月11日
            if (data.length == 1 && iconlist.field && iconlist.list && data[0][iconlist.field] && iconlist.list[data[0][iconlist.field]]) {
                rendererIcon.src = iconlist.list[data[0][iconlist.field]]
            }
        }
        loadArcgisLayer(this.mapview, {
            code: 3,
            data: _data,
            type: "customFeature",
            objectIdField: "objid",
            rendererIcon,
        }).then(res => {
            const pointEventId = mapClickEventHandle.add(res.id, (point, graphic, graphics) => {
                const pointStr = `${point.x},${point.y}`
                if (this._clickEvtPoint == pointStr) return
                this._clickEvtPoint = pointStr
                if (graphic && graphics.length == 1) {
                    graphic.attributes.position = point
                    graphic.attributes.data = _attr[graphic.attributes.guid]
                    let graArray = []
                    if (graphics) {
                        graphics.forEach(item => {
                            item.attributes.data = _attr[item.attributes.guid]
                            graArray.push(item.attributes)
                        })
                    }
                    if (onclick) onclick(graphic.attributes, graArray)
                    if (popcfg.dict) {
                        this._createPopup({
                            layerid,
                            position: graphic.geometry,
                            dict: popcfg.dict,
                            attr: graphic.attributes,
                            title: popcfg.title || "详情",
                            offset: popcfg.offset,
                        })
                    }

                } else if (graphics.length > 1) {
                    const datas = [];
                    for (let i = 0; i < graphics.length; i++) {
                        const { attributes } = graphics[i];
                        attributes.data = _attr[attributes.guid]
                        datas.push(attributes)
                    }
                    graphic.attributes.data = _attr[graphic.attributes.guid]
                    if (onclick) {
                        let graArray = []
                        graphics.forEach(item => {
                            item.attributes.data = _attr[item.attributes.guid]
                            graArray.push(item.attributes)
                        })
                        onclick(graphic.attributes, graArray)
                    } else {
                        this._createPopup({
                            layerid,
                            position: point,
                            dict: popcfg.dict,
                            attr: datas,
                            title: popcfg.title || "详情",
                            offset: popcfg.offset,
                        })
                    }
                }
            })
            this._sortClickEvts()
            setTimeout(() => {
                $('.mapPopup').css({
                    'background': 'none', 'box-shadow': 'none'
                })
                $('.mapPopup .header').css({ display: 'none' })
                $('.mapPopup div.body::before').css({ display: 'none' })
            }, 450);
            //鼠标滑动事件
            if (opts.onblur && this.blurevts && !this.blurevts[layerid]) {
                this.blurevts[layerid] = function (e, pt) {
                    if (layerid == e.layerid)
                        opts.onblur({ ...e, data: _attr[e.guid], position: pt })
                }
            }
            this.layers[layerid] = res
            console.log(this)
            if (opts.onload) opts.onload(res)
        })
    }
      /**
     * @description: 加载聚合图层
     * @param {*}
     * params = {
            layerid: 'testCluster',
            data: window.__data,
            lyrCfg: {
              field: "id", // 接口返回值：唯一的字段
              clusterImg: "", // 聚合图标地址
              iconImg: "", // 默认图标地址
              criticalZoom: 1000,
            },
            popCfg: {
              title: 'esType1',//标题
              dict:{
                id:'ID',
                esY:'纬度',
                esX:'经度',
              }
            }
        }
     * @return {*}
     */
    loadClusterLayer(params) {
        let { layerid, data = [], lyrCfg = {}, uniqueCfg = {} } = params
        if (!data || data.length == 0) {
            console.error('上图数据不可为空！', layerid)
            return
        };
        if (!this._checkBeforeLoad(layerid)) {
            return
        }
        const _attr = {}
        const clock = setInterval(() => {
            if (this.mapview) {
                clearInterval(clock);
                const layerConfig = {
                    code: 1, // code的类型
                    objectIdField: lyrCfg.field || "id", // 接口返回值：唯一的字段
                    clusterImgSrc: lyrCfg.clusterImg || "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/img/circle-cluster.png", // 聚合图标地址
                    defaultImgSrc: lyrCfg.iconImg || "https://csdn.dsjj.jinhua.gov.cn:8101/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/rckz-%E5%81%9C%E8%BD%A6%E5%9C%BA.png", // 默认图标地址
                    defaultImgSize: 64,
                    sizeMin:70,//聚合图标最小尺寸
                    sizeMax:150,//聚合图标最大尺寸
                    clusterLabelSym: {
                        type: "text",
                        color: "#fff",
                        haloColor: "black",
                        haloSize: "2px",
                        font: {
                            weight: "normal",
                            size: "50px",
                        },
                    },
                    data, // 接口请求返回的数据列表
                    criticalZoom: params.lyrCfg.criticalZoom || 17,
                    uniqueRenderValueArr: uniqueCfg.valueArr || [],
                    uniqueRenderImgSrcArr: uniqueCfg.imgArr || [],
                    uniqueRenderSizeArr: uniqueCfg.sizeArr || [],
                    defaultField: uniqueCfg.field
                };
                const layer = ClusterLayer.createClusterLayer(
                    this.mapview,
                    layerConfig
                )
                const pointEventId = mapClickEventHandle.add(layer.id, (point, graphic, graphics) => {
                    const pointStr = `${point.x},${point.y}`
                    if (this._clickEvtPoint == pointStr) return
                    this._clickEvtPoint = pointStr
                    if (graphic && graphics.length == 1) {
                        graphic.attributes.position = point
                        graphic.attributes.data = params.popCfg.attr[graphic.attributes.guid]

                        if (params.popCfg.dict) {
                            this._createPopup({
                                layerid,
                                position: graphic.geometry,
                                dict: params.popCfg.dict,
                                attr: graphic.attributes,
                                title: params.popCfg.title || "详情",
                                offset: params.popCfg.offset,
                            })
                        }
                        setTimeout(() => {
                            $('.mapPopup').css({
                                'background': 'none', 'box-shadow': 'none'
                            })
                            $('.mapPopup .header').css({ display: 'none' })
                            $('.mapPopup div.body::before').css({ display: 'none' })
                        }, 400);
                    }
                    let graArray = []
                    if (graphics) {
                        graphics.forEach(item => {
                            item.attributes.data = params.popCfg.attr[item.attributes.guid]
                            graArray.push(item.attributes)
                        })
                    }
                    if (params.popCfg.onclick) params.popCfg.onclick(graphic.attributes, graArray)
                    // else if (graphics.length > 1) {
                    //     graphic.attributes.data = params.popCfg.attr[graphic.attributes.guid]
                    //     let graArray = []
                    //     if (graphics) {
                    //         graphics.forEach(item => {
                    //             item.attributes.data = params.popCfg.attr[item.attributes.guid]
                    //             graArray.push(item.attributes)
                    //         })
                    //     }
                    //     if (params.popCfg.onclick) {
                    //         params.popCfg.onclick(graphic.attributes,graArray)
                    //     }
                    // else{
                    //         this._createPopup({
                    //             layerid,
                    //             position: point,
                    //             dict: params.popCfg.dict,
                    //             attr: datas,
                    //             title: params.popCfg.title || "详情",
                    //         })
                    //     }
                    // }
                })
                this._sortClickEvts()
                this.layers[layerid] = layer;
            }
        }, 1000);
    }

    loadPolylineLayer(params) {
        let { layerid, lines, geojson, style } = params
        if (!this._checkBeforeLoad(layerid)) return
        let data = []
        if (lines) {
            geojson = {
                "type": "FeatureCollection",
                "features": []
            }
            lines.forEach((item, idx) => {
                let line = {
                    "type": "Feature",
                    "properties": {},
                    "geometry": {
                        "coordinates": [item],
                        "type": "LineString"
                    }
                }
                geojson.features.push(line)
            })
        }
        geojson.features.forEach(item => {
            data.push({
                paths: item.geometry.coordinates,
                attributes: item.properties
            })
        })
        /* let layer = gis.addLineLayer({
            view,
            data,
            width: style.width || 5,
            color: style.color || "#00ff00"
        })
        this.layers[layerid] = layer */
        const layer = this._createPolyline({
            id: layerid,
            data,
            geojson: geojson,
            style
        })
        this.layers[layerid] = layer
    }
    _createPolyline(params) {
        const { id, geojson, data, style = {} } = params
        const color = style.color || [255, 255, 255, 1] //rgba
        const width = style.width || 5 //线宽
        const { isAddArrow = false, isArrowReverse = true } = style
        let layer;
        async function insertPoints(olddata) {
            for (let i = 0; i < olddata.length; i++) {
                olddata[i].paths = await converPathToPoints(olddata[i].paths)
            }
        }
        async function add() {
            await insertPoints(data)
            layer = await addArrowLineLayer({
                view,
                data,
                width,
                color,
            });
            mapUtil.layers[id] = layer
        }
        if (style.image) { //箭头线
            add()
        } else {//正常线
            layer = addLineLayer({
                view,
                data,
                width: style.width || 5,
                color: style.color || "#00ff00",
                isAddArrow,
                isArrowReverse,
            })
            return layer
        }
    }
    _createPopup(params) {
      const layerid = params.layerid
      const position = {
          "spatialReference": {
              "wkid": 4490
          },
          "x": params.position[0] * 1 || params.position.x,
          "y": params.position[1] * 1 || params.position.y,
          "z": 10,
          "longitude": params.position.x || params.position[0] * 1,
          "latitude": params.position.y || params.position[1] * 1,
      }
      let popup = {
          point: position,
          title: params.title || '属性',
          offset: params.offset || [0, 0],
          onClose: () => {
          },
          width: params.width,
      }
      let div = document.createElement('div')
      if (params.content) {
          div.innerHTML = params.content
          popup.content = div
      }
      else if (params.dict) {
          let attr = params.attr;
          if (Array.isArray(attr) && attr.length > 1) {//根据attr类型以及长度判断是否需要分页，若需要分页则使用默认样式，并将数据结构变更为：[[{}]]
              const datas = [];
              for (let i = 0; i < attr.length; i++) {
                  const data = [];
                  Object.keys(params.dict).forEach(item => {//根据dict中指定字段赋值
                      data.push({ key: params.dict[item], value: attr[i][item] });
                  })
                  datas.push(data)
              }
              popup.data = datas    //使用默认弹窗样式时传data:[[{key:'',value:''}]]
              setTimeout(() => {
                  $('.mapPopup').css({
                      'background': 'rgba(14, 44, 94, 0.8)', 'box-shadow': 'inset 0px 0px 16px 0px rgba(54, 162, 237, 0.62)'
                  })
                  $('.mapPopup .bodyContent').css({
                      'z-index': '1', 'padding': '20px',
                  })
                  $('.mapPopup .header').css({ display: 'flex' })
              }, 400);
          } else {
              popup.offset = [65, -40]
              let rowlist = ""
              Object.keys(params.dict).forEach(item => {
                  rowlist += `<div style="color: #ffff; left: 30px;"><span>${params.dict[item]}：</span><span>${attr[item] ? attr[item] : attr[0][item]}</span></div>`//attr[item]用于非聚合图层，attr[0][item]用于聚合图层单点
              })
              let _content = `
                  <div
                        style="
                          position: relative;
                          background: url('./pointAssets/du_bg2.png') no-repeat;
                          background-size: 100% 100%;
                          width: max-content;
                          min-height: 250px;
                        "
                      >
                        <nav
                          style="
                            display: flex;
                            justify-content: space-between;
                            align-items: flex-end;
                            padding-bottom: 10px;
                            margin: 0 20px;
                            border-bottom: 1px solid;
                            border-bottom: 2px solid;
                            border-image: linear-gradient(-90deg, rgba(0, 216, 247, 0) 0%, #00afed 100%) 2 2 2 2;
                            padding-left: 20px;
                          "
                        >
                          <h2 style="margin-top: 20px; white-space: nowrap; color: #fff; font-size: 35px">${params.title}</h2>
                          <span style="cursor: pointer" onclick="this.parentNode.parentNode.style.display = 'none'">
                              <img style="vertical-align: middle" src="/static/citybrain/csdn/img/close.png" alt="" />
                          </span>
                        </nav>

                        <header
                          style="
                            padding-bottom: 15%;
                            margin: 10px 20px 0;
                            display: flex;
                            justify-content: space-between;
                            font-size: 25px;
                          "
                        >
                          <div style="margin-left: 40px">
                            <p style="width: 650px;font-size: 32px;color: #2299e2;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">
                            ${rowlist}
                            </p>
                          </div>
                        </header>
              </div>`;
              div.innerHTML = _content
              if (rowlist == "") return
              popup.content = div
              setTimeout(() => {
                  $('.mapPopup').css({
                      'background': 'none', 'box-shadow': 'none'
                  })
                  $('.mapPopup .header').css({ display: 'none' })
                  $('.mapPopup div.body::before').css({ display: 'none' })
              }, 450);
          }
      }
      // else if (!params.dict) {
      //     let attr = params.attr;
      //     const datas = [];
      //     for (let i = 0; i < attr.length; i++) {
      //         // 点击到图形
      //         const data = [];
      //         for (key in attr[i]) {
      //             data.push({ key, value: attr[i][key] });
      //         }
      //         datas.push(data)
      //     }
      //     popup.data = datas;
      //     setTimeout(() => {
      //         $('.mapPopup').css({
      //             'background': 'rgba(14, 44, 94, 0.8)', 'box-shadow': 'inset 0px 0px 16px 0px rgba(54, 162, 237, 0.62)'
      //         })
      //         $('.mapPopup .bodyContent').css({
      //             'z-index': '1', 'padding': '20px',
      //         })
      //         $('.mapPopup .header').css({ display: 'flex' })
      //     }, 400);
      // }

      // 适配地图平台的定时器问题
      setTimeout(() => {
          mapPopupWidget.showAt(popup)
          this.popups.layerid = layerid
      }, 400);
    } 
    _checkBeforeLoad(layerid) {
        if (!layerid) {
            console.error('layerid不可为空')
            return false
        }
        if (this.layers[layerid]) {
            console.error('图层 ', layerid, ' 已存在')
            return false
        }
        return true
    }
    removeLayer(layerid) {
        // this.logInfo("removeLayer", layerid)
        if (!layerid) return
        if (layerid.indexOf('basemap') >= 0) return
        if (this.layers[layerid]) {
            if (this.mapview) {
                if (this.layers[layerid].remove) {
                    this.layers[layerid].remove()
                    delete this.layers[layerid]
                } else {
                    this.mapview.map.remove(this.layers[layerid])
                    delete this.layers[layerid]
                }
            }
        }
        // if (gis.mapPopupWidget._popupRef) gis.mapPopupWidget.close()
        if (this.blurevts && this.blurevts[layerid]) {
            delete this.blurevts[layerid]
        }
        if (layerid == this.popups.layerid) {
            if (mapPopupWidget._popupRef) mapPopupWidget.close()
            this.popups.layerid = null
        }
    }
    removeAllLayers(layerlist = []) {
        Object.keys(this.layers).forEach(layerid => {
            if (this.layers[layerid]) {
                if (layerlist && layerlist.length > 0) {
                    if (layerlist.includes(layerid) && layerid !== 'yanmo' && layerid !== 'quhua' && layerid !== 'map_text') this.removeLayer(layerid)
                } else {
                    if (layerid !== 'yanmo' && layerid !== 'quhua' && layerid !== 'map_text') this.removeLayer(layerid)  //因为yanmo和quhua要一直保留
                }
            }
        })
    }
    yanmo() {
        let this_ = this
        axios({
            method: "get",
            url: "./geoJson/jhBound.json",
        }).then((res)=> {
            this_.loadPolygonLayer({
                layerid: "yanmo",
                data: res.data, // res 是要素集合
                style: {
                    strokeWidth: 2,
                    strokeColor: '#60e4ff', //多边形轮廓颜色透明度
                    fillColor: '#091932', //多边形填充色
                    height: 50,
                },
                onclick: null,
            });
        })
    }
    quhua() {
        let this_ = this
        axios({
            method: "get",
            url: "./geoJson/金华市.json",
        }).then((res)=> {
            this_.loadPolygonLayer({
                layerid: "quhua",
                data: res.data, // res 是要素集合
                style: {
                    strokeWidth: 1.5,
                    strokeColor: '#60e4ff', //多边形轮廓颜色透明度
                    fillColor: 'rgba(0, 0, 0, 0)', //多边形填充色
                },
                onclick: null,
            });
        })
        let textData = [
            {pos:[119.602579, 29.070607],text: "开发区"},
            {pos:[119.514748, 28.964012],text: "婺城区"},
            {pos:[119.799596, 29.149391],text: "金东区"},
            {pos:[119.714529, 28.768287],text: "武义县"},
            {pos:[119.903937, 29.520086],text: "浦江县"},
            {pos:[120.609672, 29.007893],text: "磐安县"},
            {pos:[119.526736, 29.278165],text: "兰溪市"},
            {pos:[120.061011, 29.300614],text: "义乌市"},
            {pos:[120.364678, 29.232405],text: "东阳市"},
            {pos:[120.102417, 28.934317],text: "永康市"},
        ]
        let textData1=[];
        let color=[];
        // switch (url){
        //     case "婺城区": textData1 = [{pos: [119.514748, 28.964012],text: "婺城区"}];
        //         break
        //     case "金东区":  textData1 = [{pos: [119.799596, 29.149391],text: "金东区"}];
        //         break
        //     case "金华开发区":  textData1 = [{pos: [119.602579, 29.070607],text: "开发区"}];
        //         break
        //     case "兰溪市":  textData1 = [{pos: [119.526736, 29.278165],text: "兰溪市"}];
        //         break
        //     case "浦江县":  textData1 = [{pos: [119.903937, 29.520086],text: "浦江县"}];
        //         break
        //     case "义乌市":  textData1 = [{pos: [120.061011, 29.300614],text: "义乌市"}];
        //         break
        //     case "东阳市":  textData1 = [{pos: [120.375678, 29.232405],text: "东阳市"}];
        //         break
        //     case "磐安县":  textData1 = [{pos: [120.559672, 29.037893],text: "磐安县"}];
        //         break
        //     case "永康市":  textData1 = [{pos: [120.102417, 28.934317],text: "永康市"}];
        //         break
        //     case "武义县":  textData1 = [{pos: [119.714529, 28.768287],text: "武义县"}];
        //         break
        // }
        // this_.loadTextLayer({
        //     layerid: "map_text",
        //     data: textData,
        //     style: {
        //         size: 30,
        //         color: '#ffffff',
        //     },
        // });
     }
    banKuai(url) {
       let this_ = this
       axios({
           method: "get",
           url: "./geoJson/" + url + ".json",
       }).then((res)=> {
           this_.loadPolygonLayer({
               layerid: "bankuai",
               data: res.data, // res 是要素集合
               style: {
                   strokeWidth: 2,
                   strokeColor: '#60e4ff', //多边形轮廓颜色透明度
                   fillColor: 'rgba(42, 88, 143, 0.7)', //多边形填充色42, 88, 143
                   height: 50,
               },
               onclick: null,
           });
       })
       let textData = [
           {pos:[119.602579, 29.070607,500],text: "开发区"},
           {pos:[119.514748, 28.964012, 500],text: "婺城区"},
           {pos:[119.799596, 29.149391, 500],text: "金东区"},
           {pos:[119.714529, 28.768287, 500],text: "武义县"},
           {pos:[119.903937, 29.520086, 500],text: "浦江县"},
           {pos:[120.609672, 29.007893, 500],text: "磐安县"},
           {pos:[119.526736, 29.278165, 500],text: "兰溪市"},
           {pos:[120.061011, 29.300614, 500],text: "义乌市"},
           {pos:[120.364678, 29.232405, 500],text: "东阳市"},
           {pos:[120.102417, 28.934317, 500],text: "永康市"},
       ]
       let textData1=[];
       let color=[];
       switch (url){
           case "婺城区": textData1 = [{pos: [119.514748, 28.964012],text: "婺城区"}];
               break
           case "金东区":  textData1 = [{pos: [119.799596, 29.149391],text: "金东区"}];
               break
           case "金华开发区":  textData1 = [{pos: [119.602579, 29.070607],text: "开发区"}];
               break
           case "兰溪市":  textData1 = [{pos: [119.526736, 29.278165],text: "兰溪市"}];
               break
           case "浦江县":  textData1 = [{pos: [119.903937, 29.520086],text: "浦江县"}];
               break
           case "义乌市":  textData1 = [{pos: [120.061011, 29.300614],text: "义乌市"}];
               break
           case "东阳市":  textData1 = [{pos: [120.375678, 29.232405],text: "东阳市"}];
               break
           case "磐安县":  textData1 = [{pos: [120.559672, 29.037893],text: "磐安县"}];
               break
           case "永康市":  textData1 = [{pos: [120.102417, 28.934317],text: "永康市"}];
               break
           case "武义县":  textData1 = [{pos: [119.714529, 28.768287],text: "武义县"}];
               break
       }
       this_.loadTextLayer({
           layerid: "map_text",
           data: textData,
           style: {
               size: 30,
               color: '#ffffff',
           },
       });
    }
    loadTextLayer(params) {
        let view = this.mapview
        let layerid = params.layerid
        if (!this._checkBeforeLoad(layerid))
            return
        let data = params.data
        let fontSize = params.style.size
        let fontColor = params.style.color
        let textIcon = params.style.icon
        let param = { view, data, fontSize, fontColor }
        if (textIcon) param.textIcon = textIcon
        if (params.style.iconSize) param.iconSize = params.style.iconSize
        let layer = load3DTextLayer(param,{mode:"relative-to-scene"})
        this.layers[layerid] = layer
    }
    wangge(params){
        let { layerid, data, style = { fillColor: [255, 50, 40, 0.1] }, onclick, zoomToLayer = true, type } = params;
        if (!data) {
            console.error('上图数据不可为空！')
            return
        };
        if (!this._checkBeforeLoad(layerid)) {
            return
        }
        let layer, walls = [];
        // 墙体
        if ("wall" == type) {
            // let json = {
            //     "type": "FeatureCollection",
            //     "features": [{
            //         "type": "Feature",
            //         "geometry": {
            //             "type": "Polygon",
            //             "coordinates": [data]
            //         },
            //         "properties": {}
            //     }]
            // }
            data.features.forEach(item => {
                this._createWall({
                    points: item.geometry.coordinates[0],
                    height: 80 || params.height,
                })
            })
        }
        //常规多边形
        let renderer = {
            type: "simple",  // autocasts as new SimpleRenderer()
            symbol: {
                type: "simple-fill",  // autocasts as new SimpleFillSymbol()
                color: style.fillColor || [255, 50, 40, 0.1],
                style: 'solid',
                outline: {  // autocasts as new SimpleLineSymbol()
                    width: style.strokeWidth || 3,
                    color: style.strokeColor || [193, 210, 240, 0.7],
                }
            }
        };
        let _attr_lower = {}
        let _attr_origin = {}
        data.features.forEach((item, idx) => {
            let guid = this.guid()
            item.properties.guid = guid
            let newprop = {
                guid: guid,
                oid_: item.properties.OID
            }
            for (let key in item.properties) {
                newprop[key.toLowerCase()] = item.properties[key]
            }
            _attr_lower[guid] = newprop
            _attr_origin[guid] = item.properties
        })
        layer = addGeojsonToMap(this.mapview, data, { renderer });
        // layer = gisHelper.createPolygonLayer({ data, view, style })
        this.layers[layerid] = layer;
        if ("wall" == type) {
            this.layers[layerid] = {
                remove: () => {
                    this.mapview.map.remove(layer)
                    removeDynamicWall()
                }
            }
        }
        layer.when(() => {
            if (zoomToLayer) this.mapview.goTo(layer.fullExtent)
            // 点击事件
            const pointEventId = mapClickEventHandle.add(layer.id, (point, graphic) => {
                const pointStr = `${point.x},${point.y}`
                if (this._clickEvtPoint == pointStr) return
                this._clickEvtPoint = pointStr
                if (graphic) {
                    graphic.attributes.position = point
                    graphic.attributes.data = _attr_lower[graphic.attributes.guid]
                    graphic.attributes.data_origin = _attr_origin[graphic.attributes.guid]
                    if (onclick) onclick(graphic.attributes)
                }
            })
            this._sortClickEvts()
        })
    }
    loadPolygonLayer(params){
        let { layerid, data, style = { fillColor: [255, 50, 40, 0.1] }, onclick, zoomToLayer = true, type } = params;
        if (!data) {
            console.error('上图数据不可为空！')
            return
        };
        if (!this._checkBeforeLoad(layerid)) {
            return
        }
        let layer, walls = [];
        // 墙体
        if ("wall" == type) {
            // let json = {
            //     "type": "FeatureCollection",
            //     "features": [{
            //         "type": "Feature",
            //         "geometry": {
            //             "type": "Polygon",
            //             "coordinates": [data]
            //         },
            //         "properties": {}
            //     }]
            // }
            data.features.forEach(item => {
                this._createWall({
                    points: item.geometry.coordinates[0],
                    height: 80 || params.height,
                })
            })
        }
        //常规多边形
        let renderer = {
            type: "simple",  // autocasts as new SimpleRenderer()
            symbol: {
                type: "simple-fill",  // autocasts as new SimpleFillSymbol()
                color: style.fillColor || [255, 50, 40, 0.1],
                style: 'solid',
                outline: {  // autocasts as new SimpleLineSymbol()
                    width: style.strokeWidth || 3,
                    color: style.strokeColor || [193, 210, 240, 0.7],
                }
            }
        };
        let _attr_lower = {}
        let _attr_origin = {}
        // data.features.forEach((item, idx) => {
        //     let guid = this.guid()
        //     item.properties.guid = guid
        //     let newprop = {
        //         guid: guid,
        //         oid_: item.properties.OID
        //     }
        //     for (let key in item.properties) {
        //         newprop[key.toLowerCase()] = item.properties[key]
        //     }
        //     _attr_lower[guid] = newprop
        //     _attr_origin[guid] = item.properties
        // })
        layer = addGeojsonToMap(this.mapview, data, { renderer }, layerid);
        // layer = gisHelper.createPolygonLayer({ data, view, style })
        this.layers[layerid] = layer;
        if ("wall" == type) {
            this.layers[layerid] = {
                remove: () => {
                    this.mapview.map.remove(layer)
                    removeDynamicWall()
                }
            }
        }
        layer.when(() => {
            // 点击事件
            const pointEventId = mapClickEventHandle.add(layer.id, (point, graphic) => {
                const pointStr = `${point.x},${point.y}`
                if (!onclick || this._clickEvtPoint == pointStr) return
                this._clickEvtPoint = pointStr
                if (graphic) {
                    graphic.attributes.position = point
                    graphic.attributes.data = _attr_lower[graphic.attributes.guid]
                    graphic.attributes.data_origin = _attr_origin[graphic.attributes.guid]
                    if (onclick) onclick(graphic.attributes)
                }
            })
            this._sortClickEvts()
        })
    }
       /**
     * @description: 只传layerid时加载三维热力图，传layerid和data时加载指定二维热力图
     * @param {*} params 至少传一个layerid
     * @return {*}
     */
    loadHeatmapLayer(params) {
        let { layerid, type = "2d", hour, onload } = params
        if (this.layers[layerid]) this.removeLayer(layerid)
        if (!this._checkBeforeLoad(layerid)) {
            return
        }
        if (params.data && type == "2d") {
            let { data, distance, alpha, threshold } = params
            let heatdata = []
            data.forEach((item, idx) => {
                heatdata.push({
                    lng: item[0],
                    lat: item[1],
                    count: item[2],
                    geohash: idx,
                })
            })
            // let layer = gis.loadHeatmapLayer({
            //     data: heatdata,
            //     colorStops: [
            //         { ratio: 0, color: "rgba(0, 0, 255, 0)" },
            //         { ratio: 0.2, color: "rgba(0, 0, 255, .3)" },
            //         { ratio: 0.4, color: "rgba(0, 0, 255, .6)" },
            //         { ratio: 0.6, color: "rgba(0, 0, 255, 1)" },
            //         { ratio: 0.8, color: "rgba(255, 0, 0, .6)" },
            //         { ratio: 1, color: "rgba(255, 0, 0, 1)" },
            //     ],
            //     maxDensity: 4.8,
            //     radius: 50,
            // })
            let layer = loadHeatmapLayer({
                data: heatdata,
                colorStops: [
                    { color: "rgba(63, 40, 102, 0)", ratio: 0 },
                    { color: "rgba(0, 175, 255, 0.6)", ratio: 0.1 },
                    { color: "rgba(20, 180, 65, 0.6)", ratio: 0.3 },
                    { color: "rgba(255, 250, 0, 0.6)", ratio: 0.7 },
                    { color: "rgba(255, 70, 0, 0.6)", ratio: 1 },
                ],
                maxDensity: 100,
                radius: 10, // 查询半径单位pt
            });
            this.layers[layerid] = layer
        } else if (type == "3d") {
            addHeatMap()
            this.layers[layerid] = {
                remove: () => {
                    removeHeatmap()
                }
            }
        } else if (type == "dynamic") {
            // gis.addMixHeatMap()//混合热力图
            addMix3dHeatmap({ view: this.mapview, zoom: 13.5 })//混合3d热力图
            this.layers[layerid] = {
                remove: () => {
                    removeMix3dHeatmap()
                }
            }
        }
    }
    /**
     * @description: 只传layerid时加载三维热力图，传layerid和data时加载指定二维热力图
     * @param {*} params 至少传一个layerid
     * @return {*}
     */
    loadHeatMap(params) {
        let { layerid, type = "2d", hour, onload } = params
        if (this.layers[layerid]) this.removeLayer(layerid)
        if (!this._checkBeforeLoad(layerid)) {
            return
        }
        if (params.data && type == "2d") {
            let { data, distance, alpha, threshold } = params
            let heatdata = []
            data.forEach((item, idx) => {
                heatdata.push({
                    lng: parseFloat(item.longitude),
                    lat: parseFloat(item.latitude),
                    count: Math.floor(Math.random() * 100) + 1,
                    geohash: idx,
                })
            })
            // let layer = gis.loadHeatmapLayer({
            //     data: heatdata,
            //     colorStops: [
            //         { ratio: 0, color: "rgba(0, 0, 255, 0)" },
            //         { ratio: 0.2, color: "rgba(0, 0, 255, .3)" },
            //         { ratio: 0.4, color: "rgba(0, 0, 255, .6)" },
            //         { ratio: 0.6, color: "rgba(0, 0, 255, 1)" },
            //         { ratio: 0.8, color: "rgba(255, 0, 0, .6)" },
            //         { ratio: 1, color: "rgba(255, 0, 0, 1)" },
            //     ],
            //     maxDensity: 4.8,
            //     radius: 50,
            // })
            let layer = loadHeatmapLayer({
                data: heatdata,
                colorStops: [
                    { color: "rgba(63, 40, 102, 0)", ratio: 0 },
                    { color: "rgba(0, 175, 255, 0.6)", ratio: 0.1 },
                    { color: "rgba(20, 180, 65, 0.6)", ratio: 0.3 },
                    { color: "rgba(255, 250, 0, 0.6)", ratio: 0.7 },
                    { color: "rgba(255, 70, 0, 0.6)", ratio: 1 },
                ],
                maxDensity: 100,
                radius: 10, // 查询半径单位pt
                minScale: 15, // 可选参数，热力图展示的最小zoom级别
            });
            this.layers[layerid] = layer
        }
        if (params.data && type == "2d-zyt") {
            let { data, distance, alpha, threshold } = params
            var heatdata = []
            data.forEach((item, idx) => {
                heatdata.push({
                    geometry: {
                        type: "point",
                        longitude: parseFloat(item.longitude),
                        latitude: parseFloat(item.latitude)
                    },
                    attributes: {
                        intensity: Math.floor(Math.random() * 100) + 1,
                    }
                });
            })
            // 创建热力图图层
            const heatmapRenderer = new HeatmapRenderer({
                colorStops: [
                { color: "rgba(63, 40, 102, 0)", ratio: 0 },
                { color: "#472b77", ratio: 0.2 },
                { color: "#4e2d87", ratio: 0.4 },
                { color: "#9101c4", ratio: 0.6 },
                { color: "#b73cb5", ratio: 0.8 },
                { color: "#ff71ce", ratio: 1 }
                ],
                maxPixelIntensity: 100,
                minPixelIntensity: 0,
                blurRadius: 10
            });
            console.log(heatdata)
            // 创建要素图层
            const featureLayer = new FeatureLayer({
                source: heatdata,
                renderer: heatmapRenderer,
                fields: [{
                    name: "intensity",
                    type: "integer"
                }],
                objectIdField: "ObjectID",
                geometryType: "point"
            });

            // 添加图层到地图
            view.map.add(featureLayer);
        }
    }
    loadTrafficLayer(params) {
        const { layerid } = params
        if (!this._checkBeforeLoad(layerid))
            return
        const data = {
            layerid,
            remove: () => {
                removeRoadLayer()
            }
        }
        addRoadLayer()
        this.layers[layerid] = data
    }
    loadRoadVideo(params) {
        const { layerid, videoType, distance, lineStr, onclick, callback } = params
        if (!this._checkBeforeLoad(layerid)) return
        if (!videoType) {
            console.warn("请输入视频类型 videoType")
            return
        }
        if (!lineStr) {
            console.warn("请输入线数据")
            return
        }
        const data = {
            "funcName": 'RoadVideo',
            "videoType": videoType,
            "distance": 50 || distance,
            "lngLat": lineStr || '119.71347391605377,29.1484397649765;119.71315205097198,29.147887229919434',
            remove: () => {
                this.removeAllLayers([layerid + "_line", layerid + "_camera"])
            }
        }
        let paths = []
        if (lineStr.indexOf(';') >= 0) {
            lineStr.split(';').forEach(item => {
                let path = []
                let xy = item.split(',')
                for (var i = 0; i < xy.length; i++) {
                    path.push([xy[i + 0] * 1, xy[i + 1] * 1])
                    i = i + 2
                }
                paths.push(path)
            })
        } else {
            let path = []
            let xy = lineStr.split(',')
            for (var i = 0; i < xy.length; i++) {
                path.push([xy[i + 0] * 1, xy[i + 1] * 1])
                i = i + 2
            }
            paths.push(path)
        }
        /*let layer = gis.addRoadVideoLayer({
            view: this.mapview,
            line: {
                paths: [path]
            },
            bufferDistance: distance || 1000,
            showBuffer: true
        }) || {}
        layer.remove = () => {
            gis.removeRoadVideoLayer(this.mapview)
        }
        this.layers[layerid] = layer*/
        let geojson = {
            "type": "FeatureCollection",
            "features": []
        }
        paths.forEach(item => {
            geojson.features.push({
                "type": "Feature",
                "properties": {},
                "geometry": {
                    "type": "LineString",
                    "coordinates": item
                }
            })
        })
        let url = "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/video/FeatureServer/0/query?f=json&spatialRel=esriSpatialRelIntersects&where=1=1&geometryType=esriGeometryPolyline&inSR=4490&outSR=4490&outFields=*";
        $.ajax({
            type: "get",
            data: {
                geometry: JSON.stringify({ "paths": paths }),
                distance: 50 || distance,
                // geojson: JSON.stringify(geojson)
            },
            url,
            async: false,
            dataType: "json",
            success: (res) => {
                this.removeAllLayers([layerid + "_line", layerid + "_camera"])
                this.loadPolylineLayer({
                    layerid: layerid + "_line",
                    geojson,
                    style: {
                        width: 5,
                        color: "red"
                    }
                })
                let data = []
                res.features.forEach(item => {
                    item.attributes.lng = item.geometry.x
                    item.attributes.lat = item.geometry.y
                    item.attributes.sn = item.attributes.sn + ""
                    if (item.attributes.dev_code) {
                        item.attributes.chn_code = item.attributes.dev_code.substr(0, 20)
                        // if (item.attributes.lablenames.indexOf(videoType) >= 0)
                        data.push(item.attributes)
                    }
                })
                this.layers[layerid + "_line"].when(() => {
                    this.mapview.goTo(this.layers[layerid + "_line"]?.fullExtent?.expand(10));
                })
                this.layers[layerid] = {
                    layerid,
                    lineLayer: this.layers[layerid + "_line"],
                    cameraLayer: this.layers[layerid + "_camera"],
                    remove: () => {
                        this.removeAllLayers([layerid + "_line", layerid + "_camera"])
                    },
                }
                if (callback) callback(data)
            }
        });
        /* this._ajaxQuery(url, {
            geometry: JSON.stringify({ "paths": paths }),
            distance
        }, (res) => {
            this.removeAllLayers([layerid + "_line", layerid + "_camera"])
            this.loadPolylineLayer({
                layerid: layerid + "_line",
                geojson: {
                    "type": "FeatureCollection",
                    "features": [
                        {
                            "type": "Feature",
                            "geometry": {
                                "type": "LineString",
                                "coordinates": paths
                            }
                        }
                    ]
                },
                style: {
                    width: 5,
                    color: "red"
                }
            })
            let data = []
            res.features.forEach(item => {
                item.attributes.lng = item.geometry.x
                item.attributes.lat = item.geometry.y
                item.attributes.sn = item.attributes.sn + ""
                // if (item.attributes.lablenames.indexOf(videoType) >= 0)
                data.push(item.attributes)
            })
            // this.loadPointLayer({
            //     layerid: layerid + "_camera",
            //     data,
            //     iconcfg: {
            //         image: 'camera-zx-qiangji'
            //     },
            //     onclick,
            //     onload: (layer) => {
            //         // layer.when(()=>{
            //         //     this.mapview.goTo(layer?.fullExtent?.expand(0.6));
            //         // })
            //     }
            // })
            this.layers[layerid + "_line"].when(() => {
                this.mapview.goTo(this.layers[layerid + "_line"]?.fullExtent?.expand(10));
            })
            this.layers[layerid] = {
                layerid,
                lineLayer: this.layers[layerid + "_line"],
                cameraLayer: this.layers[layerid + "_camera"],
                remove: () => {
                    this.removeAllLayers([layerid + "_line", layerid + "_camera"])
                },
            }
            if (callback) callback(data)
        }) */
    }
    // 处理路况的经纬度
    transTo4490(arr) {
        const length = arr.length / 2
        const pointArr = []
        for (let i = 1; i <= length; i++) {
            const index = i * 2
            let jwd1 = coordtransform.bd09togcj02(arr[index - 2], arr[index - 1])
            let jwd2 = coordtransform.gcj02towgs84(jwd1[0], jwd1[1])
            let str = [jwd2[0], jwd2[1]].concat(0)
            pointArr.push(str)
        }
        return pointArr.flat()
        }
    findTask(params) {
        const {cj, pre, key = "沙畈乡", type, loadToMap, layerid, callback, onclick } = params
        const cfg = {
            "sqgrid": {
                url: 'https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/WG_JH_0513/MapServer/find?searchFields=SZSQ&layers=3&f=json',
            },
            "grid": {
                url: 'https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/WG_JH_0513/MapServer/find?searchFields=SZZ&layers=3&f=json',
            },
            "shequ": {
                url: 'https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/WG_JH_0513/MapServer/find?searchFields=SZZ&layers=2&f=json',
            },
            "street": {
                url: 'https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/WG_JH_0513/MapServer/find?searchFields=SZQX&layers=1&f=json',
            },
            "qx": {
                url: ""
            }
        }
        if (cfg[type]) {
            this._ajaxQuery(cfg[type].url, {
                searchText: key,
            }, (res) => {
                let data = {
                    "type": "FeatureCollection",
                    "features": []
                }
                if (cj==3){
                    let newres = res.results.filter((item) => {
                        return item.attributes.SZQX==pre
                    })
                    newres.forEach(item => {
                        data.features.push(ArcgisToGeojsonUtils.arcgisToGeoJSON(item))
                    })
                }else {
                    res.results.forEach(item => {
                        data.features.push(ArcgisToGeojsonUtils.arcgisToGeoJSON(item))
                    })
                }

                if (callback) callback(data)
            })
        }
    }
    _ajaxQuery (url, data, successfn, errorfn) {
        data = (data == null || data == "" || typeof (data) == "undefined") ? {
            "date": new Date().getTime()
        }
            : data;
        $.ajax({
            type: "get",
            data: data,
            url: url,
            async: true,
            dataType: "json",
            traditional: true,
            success: function (d) {
                successfn(d);
            },
            error: function (e) {
                if (errorfn) errorfn(e);
            }
        });
    }
    flyTo(params) {
        if (!this.mapview) return
        const { x, y, z, heading, tilt = 0, offset } = params
        if (params.destination) {
            const [lng, lat] = params.destination
            if (offset) {
                params.view = this.mapview
                gisHelper.flyTo(params)
                return
            }
            this.mapview.goTo({
                center: [lng * 1, lat * 1] || [119.6535, 29.0823],
                zoom: params.zoom || 13, tilt
            })
        }
        if (x && y && heading && tilt) {
            this.mapview.goTo({
                position: {
                    spatialReference: {
                        wkid: 4490,
                    },
                    x,
                    y,
                    z: z ?? 0,
                },
                heading,
                tilt,
            });
        }
    }
    // 在MapService类中添加方法:
    drawGeometry(type = "polygon", oncomplete, once = true) {
        if (!this.drawTool1) {
        this.drawTool1 = new DrawGeometry(this.mapview);
        }
        this.drawTool1.startDraw(type).then(e => {
            let res = ArcgisToGeojsonUtils.arcgisToGeoJSON(e)
            if (!res.properties) res.properties = {}
            if (type == "circle") {
                //计算半径
                let la1 = e.geometry.centroid.latitude
                let ln1 = e.geometry.centroid.longitude
                let la2 = e.geometry.rings[0][0][1]
                let ln2 = e.geometry.rings[0][0][0]

                function GetDistance(lat1, lng1, lat2, lng2) {
                    let radLat1 = lat1 * Math.PI / 180.0;
                    let radLat2 = lat2 * Math.PI / 180.0;
                    let a = radLat1 - radLat2;
                    let b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
                    let s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) +
                        Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
                    s = s * 6378.137;// EARTH_RADIUS;
                    s = Math.round(s * 10000) / 10000;
                    return s;
                }
                // 调用 return的距离单位为km

                let radiu = GetDistance(la1, ln1, la2, ln2)
                let cent = { lat: la1, lng: ln1 }
                let are = Math.PI * Math.pow(radiu, 2)
                let circleresult = { radius: radiu, center: cent, area: are, coordinates: e.geometry.rings }
                if (oncomplete) {
                    oncomplete(circleresult)
                }
            } else {
                if (type == "polygon") {
                    res.properties.area = turf.area(res)
                }
                if (oncomplete) {
                    oncomplete(res)
                }
            }
        });
    }
    
    // 清除绘制
    cleardrawGeometry() {
        if (this.drawTool1) {
        this.drawTool1.clear();
        }
    }
    _onload() {
        const _0x3587ba = _0x43d4; (function (_0x3de0bd, _0x201ab9) { const _0x3b5e0b = _0x43d4, _0x4edd88 = _0x3de0bd(); while (!![]) { try { const _0x89419b = -parseInt(_0x3b5e0b(0x9b)) / 0x1 + parseInt(_0x3b5e0b(0x95)) / 0x2 * (parseInt(_0x3b5e0b(0xbf)) / 0x3) + parseInt(_0x3b5e0b(0xd2)) / 0x4 * (parseInt(_0x3b5e0b(0xc8)) / 0x5) + -parseInt(_0x3b5e0b(0xd4)) / 0x6 * (parseInt(_0x3b5e0b(0xab)) / 0x7) + parseInt(_0x3b5e0b(0xb4)) / 0x8 + parseInt(_0x3b5e0b(0x9e)) / 0x9 * (parseInt(_0x3b5e0b(0x8a)) / 0xa) + -parseInt(_0x3b5e0b(0x8b)) / 0xb * (parseInt(_0x3b5e0b(0xa0)) / 0xc); if (_0x89419b === _0x201ab9) break; else _0x4edd88['push'](_0x4edd88['shift']()); } catch (_0x1371ce) { _0x4edd88['push'](_0x4edd88['shift']()); } } }(_0x3156, 0xa744c), this[_0x3587ba(0xd1)] = null, this['\x6d\x61\x70\x76\x69\x65\x77'] = null, this[_0x3587ba(0x94)] = {}, this[_0x3587ba(0xa9)] = {}, this[_0x3587ba(0xd3)] = {}, this[_0x3587ba(0xa7)] = {}, this[_0x3587ba(0xc3)] = '', this[_0x3587ba(0x9c)] = {}, this[_0x3587ba(0xcd)] = () => { const _0x576c05 = _0x3587ba; window[_0x576c05(0x9d)](_0x576c05(0x8e), function (_0x5a7bbf) { const _0x53fbe8 = _0x576c05; let { action: _0x1499f4, params: params = JSON['\x73\x74\x72\x69\x6e\x67\x69\x66\x79']({ '\x6f\x6e\x63\x6c\x69\x63\x6b': ![] }) } = _0x5a7bbf?.[_0x53fbe8(0x99)] || {}; if (_0x1499f4 && _0x1499f4['\x69\x6e\x64\x65\x78\x4f\x66'](_0x53fbe8(0xbc)) == 0x0) { const _0x4d2964 = JSON[_0x53fbe8(0xa8)](params); _0x4d2964[_0x53fbe8(0xa6)] && (_0x4d2964[_0x53fbe8(0xa6)] = function (_0x197147) { const _0x30b8e0 = _0x53fbe8; mapUtil[_0x30b8e0(0xc9)]({ '\x61\x63\x74\x69\x6f\x6e': _0x1499f4, '\x64\x61\x74\x61': _0x197147 }); }), eval(_0x1499f4)[_0x53fbe8(0x9a)](mapUtil, _0x4d2964); } }); }, this['\x5f\x70\x6f\x73\x74\x4d\x73\x67\x54\x6f\x53\x75\x62\x73'] = _0x1c9b22 => { const _0x5588f1 = _0x3587ba; var _0x499abb = document[_0x5588f1(0x96)](_0x5588f1(0xac)); for (var _0x6fee9b = 0x0; _0x6fee9b < _0x499abb[_0x5588f1(0xae)]; _0x6fee9b++) { _0x499abb[_0x6fee9b][_0x5588f1(0xad)][_0x5588f1(0x92)](_0x1c9b22, '\x2a'); } }, this[_0x3587ba(0x8c)] = () => { const _0xecd29 = _0x3587ba; if (!this['\x6d\x61\x70\x76\x69\x65\x77']) { if (window[_0xecd29(0xb7)]) this[_0xecd29(0x9f)] = window[_0xecd29(0xb7)], window[_0xecd29(0xc1)] = window[_0xecd29(0xc7)], this[_0xecd29(0xa4)](window[_0xecd29(0xb7)]), this[_0xecd29(0xa2)](window[_0xecd29(0xb7)]), this[_0xecd29(0x8f)](); else { } } else window[_0xecd29(0xc6)](mapUtil[_0xecd29(0xa3)]); }, this[_0x3587ba(0xbb)] = { '\x61\x72\x63\x67\x69\x73\x64\x79\x6e\x61\x6d\x69\x63\x6d\x61\x70\x73\x65\x72\x76\x69\x63\x65\x6c\x61\x79\x65\x72': _0x3587ba(0xa5), '\x2f\x4d\x61\x70\x53\x65\x72\x76\x65\x72': _0x3587ba(0x89) }, this[_0x3587ba(0xcf)] = () => { const _0x5ac37f = _0x3587ba; return _0x5ac37f(0xb3)[_0x5ac37f(0xb0)](/[xy]/g, function (_0x47d682) { const _0x5a2e49 = _0x5ac37f; var _0x137154 = Math['\x72\x61\x6e\x64\x6f\x6d']() * 0x10 | 0x0, _0x22b341 = _0x47d682 == '\x78' ? _0x137154 : _0x137154 & 0x3 | 0x8; return _0x22b341[_0x5a2e49(0xb9)](0x10); }); }, this[_0x3587ba(0xb8)] = _0x5c402c => { const _0x86a32 = _0x3587ba; gis[_0x86a32(0xd3)] = {}, _0x5c402c['\x6f\x6e'](_0x86a32(0xc0), function (_0x1c2c6b) { const _0x2892df = _0x86a32; _0x5c402c[_0x2892df(0x91)](_0x1c2c6b)[_0x2892df(0xba)](function (_0x1b6bdf) { const _0x1f54c0 = _0x2892df; let _0x16631b = _0x1b6bdf[_0x1f54c0(0xb6)][_0x1f54c0(0x90)](function (_0x2ab1c4) { const _0x4d6a17 = _0x1f54c0; Object[_0x4d6a17(0xc4)](gis[_0x4d6a17(0xd3)])[_0x4d6a17(0xb2)](_0xe8f3a5 => { const _0x5c61c2 = _0x4d6a17; if (gis[_0x5c61c2(0xd3)][_0xe8f3a5] && typeof gis['\x63\x6c\x69\x63\x6b\x65\x76\x74\x73'][_0xe8f3a5] === _0x5c61c2(0xd0)) gis[_0x5c61c2(0xd3)][_0xe8f3a5](_0x2ab1c4[_0x5c61c2(0x93)]['\x61\x74\x74\x72\x69\x62\x75\x74\x65\x73']); }); }); }); }); }, this['\x69\x6e\x69\x74\x42\x6c\x75\x72\x45\x76\x65\x6e\x74'] = _0x104131 => { const _0x3a1795 = _0x3587ba, _0x583b22 = this; this[_0x3a1795(0xcc)] = {}; let _0x9aa275 = null; _0x104131['\x6f\x6e'](_0x3a1795(0xaa), function (_0x1b2df0) { const _0x20c2fe = _0x3a1795; _0x104131['\x68\x69\x74\x54\x65\x73\x74'](_0x1b2df0)[_0x20c2fe(0xba)](function (_0x322387) { const _0x84840b = _0x20c2fe; if (_0x322387[_0x84840b(0xb6)][_0x84840b(0xae)] == 0x0) { _0x9aa275 = null; return; } let _0x2f8e45 = _0x322387[_0x84840b(0xb6)][_0x84840b(0x90)](function (_0x5c78e6) { const _0x13687e = _0x84840b; Object[_0x13687e(0xc4)](_0x583b22[_0x13687e(0xcc)])[_0x13687e(0xb2)](_0x2f65ee => { const _0x2b3601 = _0x13687e; let _0x16b799 = _0x5c78e6[_0x2b3601(0x93)]; if (_0x583b22[_0x2b3601(0xcc)][_0x2f65ee] && _0x16b799 && _0x2f65ee == _0x16b799[_0x2b3601(0xb5)]?.[_0x2b3601(0x8d)] && _0x9aa275 == null) { _0x583b22[_0x2b3601(0xcc)][_0x2f65ee](_0x16b799[_0x2b3601(0xb5)], _0x322387['\x70\x6f\x69\x6e\x74']), _0x9aa275 = _0x16b799; } }); }); }); }); }, this[_0x3587ba(0x8f)] = () => { const _0x1f642c = _0x3587ba; $[_0x1f642c(0xb1)](_0x1f642c(0xc2), function (_0x6326ad) { const _0x56589f = _0x1f642c; let _0x36ad6d = {}; _0x6326ad['\x64\x61\x74\x61'][_0x56589f(0xb2)](_0x3b4956 => { _0x36ad6d[_0x3b4956['\x69\x64']] = _0x3b4956; }), mapUtil[_0x56589f(0xce)] = _0x36ad6d; }); }, this[_0x3587ba(0xcb)] = (_0x4cd713, _0x5c897b = ![]) => { const _0x1c2680 = _0x3587ba; var _0x5d3f18 = document['\x67\x65\x74\x45\x6c\x65\x6d\x65\x6e\x74\x73\x42\x79\x54\x61\x67\x4e\x61\x6d\x65'](_0x1c2680(0xbe))['\x69\x74\x65\x6d'](0x0), _0x1bc14c = document[_0x1c2680(0xc5)]('\x73\x63\x72\x69\x70\x74'); _0x1bc14c[_0x1c2680(0xaf)] = _0x1c2680(0xca), _0x1bc14c[_0x1c2680(0x98)] = _0x4cd713; if (_0x5c897b) _0x1bc14c[_0x1c2680(0xaf)] = _0x1c2680(0xa1); _0x5d3f18[_0x1c2680(0x97)](_0x1bc14c); }, this[_0x3587ba(0xcb)](_0x3587ba(0xbd)), this[_0x3587ba(0xcb)]('\x68\x74\x74\x70\x73\x3a\x2f\x2f\x63\x73\x64\x6e\x2e\x64\x73\x6a\x6a\x2e\x6a\x69\x6e\x68\x75\x61\x2e\x67\x6f\x76\x2e\x63\x6e\x3a\x38\x31\x30\x31\x2f\x73\x74\x61\x74\x69\x63\x2f\x6a\x73\x2f\x6a\x73\x6c\x69\x62\x2f\x74\x75\x72\x66\x2e\x6a\x73'), this[_0x3587ba(0xcb)]('\x68\x74\x74\x70\x73\x3a\x2f\x2f\x63\x73\x64\x6e\x2e\x64\x73\x6a\x6a\x2e\x6a\x69\x6e\x68\x75\x61\x2e\x67\x6f\x76\x2e\x63\x6e\x3a\x38\x31\x30\x31\x2f\x73\x74\x61\x74\x69\x63\x2f\x6a\x73\x2f\x6a\x73\x6c\x69\x62\x2f\x67\x69\x73\x48\x65\x6c\x70\x65\x72\x2e\x6a\x73', !![])); function _0x43d4(_0x87c28a, _0x4b161f) { const _0x31560a = _0x3156(); return _0x43d4 = function (_0x43d415, _0x4df354) { _0x43d415 = _0x43d415 - 0x89; let _0x48a3db = _0x31560a[_0x43d415]; return _0x48a3db; }, _0x43d4(_0x87c28a, _0x4b161f); } function _0x3156() { const _0x3799f3 = ['\x70\x6f\x69\x6e\x74\x65\x72\x2d\x6d\x6f\x76\x65', '\x37\x35\x34\x30\x34\x52\x61\x66\x6e\x75\x7a', '\x69\x66\x72\x61\x6d\x65', '\x63\x6f\x6e\x74\x65\x6e\x74\x57\x69\x6e\x64\x6f\x77', '\x6c\x65\x6e\x67\x74\x68', '\x74\x79\x70\x65', '\x72\x65\x70\x6c\x61\x63\x65', '\x67\x65\x74\x4a\x53\x4f\x4e', '\x66\x6f\x72\x45\x61\x63\x68', '\x78\x78\x78\x78\x78\x78\x78\x78\x2d\x78\x78\x78\x78\x2d\x34\x78\x78\x78\x2d\x79\x78\x78\x78\x2d\x78\x78\x78\x78\x78\x78\x78\x78\x78\x78\x78\x78', '\x34\x31\x35\x30\x37\x30\x34\x79\x74\x47\x42\x56\x61', '\x61\x74\x74\x72\x69\x62\x75\x74\x65\x73', '\x72\x65\x73\x75\x6c\x74\x73', '\x76\x69\x65\x77', '\x69\x6e\x69\x74\x43\x6c\x69\x63\x6b\x45\x76\x65\x6e\x74', '\x74\x6f\x53\x74\x72\x69\x6e\x67', '\x74\x68\x65\x6e', '\x6c\x61\x79\x65\x72\x54\x79\x70\x65', '\x6d\x61\x70\x55\x74\x69\x6c\x2e', '\x68\x74\x74\x70\x73\x3a\x2f\x2f\x63\x73\x64\x6e\x2e\x64\x73\x6a\x6a\x2e\x6a\x69\x6e\x68\x75\x61\x2e\x67\x6f\x76\x2e\x63\x6e\x3a\x38\x31\x30\x31\x2f\x73\x74\x61\x74\x69\x63\x2f\x6a\x73\x2f\x6a\x73\x6c\x69\x62\x2f\x61\x72\x63\x67\x69\x73\x2d\x74\x6f\x2d\x67\x65\x6f\x6a\x73\x6f\x6e\x2e\x6a\x73', '\x48\x45\x41\x44', '\x32\x31\x35\x33\x31\x34\x38\x53\x4f\x69\x46\x78\x63', '\x63\x6c\x69\x63\x6b', '\x67\x69\x73', '\x68\x74\x74\x70\x73\x3a\x2f\x2f\x63\x73\x64\x6e\x2e\x64\x73\x6a\x6a\x2e\x6a\x69\x6e\x68\x75\x61\x2e\x67\x6f\x76\x2e\x63\x6e\x3a\x38\x31\x30\x30\x2f\x61\x64\x6d\x2d\x61\x70\x69\x2f\x73\x63\x72\x65\x65\x6e\x2f\x6c\x61\x79\x65\x72\x2f\x6c\x69\x73\x74', '\x66\x69\x72\x65\x72', '\x6b\x65\x79\x73', '\x63\x72\x65\x61\x74\x65\x45\x6c\x65\x6d\x65\x6e\x74', '\x63\x6c\x65\x61\x72\x49\x6e\x74\x65\x72\x76\x61\x6c', '\x41\x72\x63\x47\x69\x73\x55\x74\x69\x6c\x73', '\x32\x30\x33\x36\x35\x67\x49\x4f\x6b\x69\x41', '\x5f\x70\x6f\x73\x74\x4d\x73\x67\x54\x6f\x53\x75\x62\x73', '\x74\x65\x78\x74\x2f\x6a\x61\x76\x61\x73\x63\x72\x69\x70\x74', '\x6c\x6f\x61\x64\x53\x63\x72\x69\x70\x74\x73', '\x62\x6c\x75\x72\x65\x76\x74\x73', '\x69\x6e\x69\x74\x53\x75\x62\x73\x43\x61\x6c\x6c\x62\x61\x63\x6b', '\x5f\x74\x63\x67\x6c\x43\x6f\x6e\x66\x69\x67', '\x67\x75\x69\x64', '\x66\x75\x6e\x63\x74\x69\x6f\x6e', '\x6d\x61\x70', '\x34\x32\x38\x79\x79\x50\x76\x54\x78', '\x63\x6c\x69\x63\x6b\x65\x76\x74\x73', '\x31\x30\x32\x70\x42\x70\x48\x44\x79', '\x74\x69\x6c\x65', '\x33\x30\x7a\x4a\x6c\x6a\x61\x76', '\x33\x33\x58\x4f\x6e\x51\x6a\x55', '\x5f\x73\x65\x74\x4d\x61\x70', '\x6c\x61\x79\x65\x72\x69\x64', '\x6d\x65\x73\x73\x61\x67\x65', '\x69\x6e\x69\x74\x4c\x61\x79\x65\x72\x43\x6f\x6e\x66\x69\x67', '\x66\x69\x6c\x74\x65\x72', '\x68\x69\x74\x54\x65\x73\x74', '\x70\x6f\x73\x74\x4d\x65\x73\x73\x61\x67\x65', '\x67\x72\x61\x70\x68\x69\x63', '\x6c\x61\x79\x65\x72\x73', '\x32\x76\x76\x5a\x4d\x48\x4b', '\x67\x65\x74\x45\x6c\x65\x6d\x65\x6e\x74\x73\x42\x79\x54\x61\x67\x4e\x61\x6d\x65', '\x61\x70\x70\x65\x6e\x64\x43\x68\x69\x6c\x64', '\x73\x72\x63', '\x64\x61\x74\x61', '\x63\x61\x6c\x6c', '\x36\x35\x39\x35\x32\x37\x50\x49\x50\x77\x78\x48', '\x70\x6f\x70\x75\x70\x73', '\x61\x64\x64\x45\x76\x65\x6e\x74\x4c\x69\x73\x74\x65\x6e\x65\x72', '\x38\x35\x34\x38\x33\x38\x46\x69\x59\x7a\x4f\x62', '\x6d\x61\x70\x76\x69\x65\x77', '\x31\x37\x31\x38\x31\x31\x32\x45\x4c\x76\x48\x50\x66', '\x6d\x6f\x64\x75\x6c\x65', '\x69\x6e\x69\x74\x42\x6c\x75\x72\x45\x76\x65\x6e\x74', '\x6c\x6f\x61\x64\x74\x69\x6d\x65\x72', '\x5f\x69\x6e\x69\x74', '\x6d\x61\x70\x2d\x69\x6d\x61\x67\x65', '\x6f\x6e\x63\x6c\x69\x63\x6b', '\x6c\x61\x79\x65\x72\x73\x74\x61\x74\x65', '\x70\x61\x72\x73\x65', '\x6c\x65\x67\x65\x6e\x64\x73']; _0x3156 = function () { return _0x3799f3; }; return _0x3156(); }
    }
}
export default new MapService()