<template>
  <div class="wrapper">
    <div :id="videoConfig.domId" :style="{ height: height, width: width }"></div>
  </div>
</template>

<script>
import DHWs from './DHWs.js'
const DHWsInstance = DHWs.getInstance({
  reConnectCount: 2,
  connectionTimeout: 30 * 1000000,
  messageEvents: {},
})

export default {
  name: 'venueVideo',
  components: {},
  props: {
    width: {
      type: String,
      default: '',
    },
    height: {
      type: String,
      default: '',
    },
    visible: {
      type: Boolean,
      default: true,
    },
    videoConfig: {
      type: Object,
      default: function () {
        return {}
      },
    },
    code: {
      type: String,
      default: '',
    },
    createflag: {
      type: Boolean,
      default: false,
    },
    destoryflag: {
      type: Boolean,
      default: false,
    },
    isLogin: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      ws: DHWsInstance,
      loginStatus: false, // 登录状态标志
      currentVideoCode: '', // 当前视频编码
    }
  },
  created() {
    // 监听全局登录状态变化
    this.ws.on('loginState', (res) => {
      if (res) {
        console.log('登录成功')
        this.loginStatus = true
        // 如果有待播放的视频，立即创建
        if (this.videoConfig && this.videoConfig.ctrlCode) {
          this.createMinitor()
        }
      } else {
        console.log('登录失败')
        this.loginStatus = false
      }
    })
  },
  methods: {
    // 创建视频监控窗口
    createMinitor() {
      if (!this.videoConfig.ctrlCode) {
        console.warn('无效的视频配置')
        return
      }

      // 如果当前播放的就是这个视频，且窗口已创建，只需切换可见性
      if (this.currentVideoCode === this.videoConfig.ctrlCode) {
        this.changeVisible()
        return
      }

      // 更新当前视频编码
      this.currentVideoCode = this.videoConfig.ctrlCode

      // 创建新的控制窗口
      console.log(this.videoConfig,"videoConfig")
      this.ws.createCtrl([this.videoConfig])

      // 检查是否为回放模式
      if (this.videoConfig.ctrlProperty && this.videoConfig.ctrlProperty.displayMode === 2) {
        // 如果是回放模式，播放录像
        this.playRecording()
      }
    },

    // 切换视频窗口可见性
    changeVisible() {
      if (!this.videoConfig.ctrlCode) return
      this.ws.setCtrlVisible([
        {
          ctrlCode: this.videoConfig.ctrlCode,
          visible: this.videoConfig.visible,
        },
      ])
    },

    // 视频登录
    loginVideo() {
      // 如果已经登录，直接创建视频窗口
      if (this.loginStatus) {
        console.log('已登录，直接创建视频窗口')
        this.createMinitor()
        return
      }

      // 否则，检测连接并登录
      this.ws.detectConnectQt().then((res) => {
        if (res) {
          // 连接客户端成功
          console.log('连接大华客户端成功，开始登录')
          this.ws.login({
            loginIp: '*************',
            loginPort: '7902',
            userName: 'zfj',
            userPwd: 'JHzfj2025',
            token: '',
            https: 1
          })
          // 登录状态会通过全局监听器处理
        } else {
          // 连接客户端失败
          console.error('连接大华客户端失败')
          this.$message.error('连接视频客户端失败，请确保客户端已启动')
        }
      }).catch(error => {
        console.error('检测连接出错:', error)
        this.$message.error('视频客户端连接异常')
      })
    },

    // 视频登出
    logOut() {
      this.ws.logout({
        loginIp: '*************',
      })
      this.loginStatus = false
      this.currentVideoCode = ''
      console.log("登出")
    },

    // 销毁时清理资源
    destroyVideo() {
      if (this.currentVideoCode) {
        this.ws.destroyCtrl([
          {
            ctrlCode: this.currentVideoCode
          }
        ])
        this.currentVideoCode = ''
      }
    },

    // 播放录像
    playRecording() {
      if (!this.loginStatus) {
        this.$message.warning('请先登录视频系统')
        this.loginVideo()
        return
      }

      if (!this.videoConfig.ctrlCode) {
        this.$message.warning('无效的视频配置')
        return
      }

      // 确保控件已创建
      if (!this.currentVideoCode) {
        this.createMinitor()
        return // 创建后会再次调用此方法
      }

      // 从videoConfig中获取时间范围
      if (!this.videoConfig.ctrlProperty ||
          !this.videoConfig.ctrlProperty.startTime ||
          !this.videoConfig.ctrlProperty.endTime) {
        console.warn('未设置回放时间范围')
        return
      }

      // 格式化时间
      const startDate = this.videoConfig.ctrlProperty.startTime
      const endDate = this.videoConfig.ctrlProperty.endTime

      // 添加时间部分，如果只有日期
      const beginTime = startDate.includes(' ') ? startDate : `${startDate} 00:00:00`
      const endTime = endDate.includes(' ') ? endDate : `${endDate} 23:59:59`

      console.log('播放录像', {
        ctrlCode: this.videoConfig.ctrlCode,
        beginTime,
        endTime
      })

      // 调用大华SDK的录像回放方法
      this.ws.openCtrlRecord([
        {
          ctrlCode: this.videoConfig.ctrlCode,
          array: [
            {
              beginTime: beginTime,
              channelId: this.videoConfig.channelId ||
                        (this.videoConfig.ctrlProperty.channelList &&
                         this.videoConfig.ctrlProperty.channelList.length > 0 ?
                         this.videoConfig.ctrlProperty.channelList[0].channelId :
                         this.videoConfig.ctrlCode),
              endTime: endTime
            }
          ]
        }
      ]).then(() => {
        this.$message.success('录像回放请求已发送')
      }).catch(error => {
        console.error('录像回放失败:', error)
        this.$message.error('录像回放失败，请检查时间范围或通道是否有录像')
      })
    }
  },
  watch: {
    createflag(v) {
      if (v) {
        if (this.loginStatus) {
          this.createMinitor()
        } else {
          this.loginVideo()
        }
      }
    },
    destoryflag(v) {
      if (v) {
        this.changeVisible()
      }
    },
    'videoConfig.ctrlCode': {
      handler(newVal, oldVal) {
        if (newVal && newVal !== oldVal && this.loginStatus) {
          // 视频源变化，且已登录，创建新的视频窗口
          this.createMinitor()
        }
      },
      immediate: false
    },
    'videoConfig.visible': {
      handler() {
        if (this.currentVideoCode) {
          // 如果已经创建了视频窗口，切换可见性
          this.changeVisible()
        }
      },
      immediate: false
    }
  },
  beforeDestroy() {
    // 组件销毁前清理资源
    this.destroyVideo()
  }
}
</script>

<style lang="less" scoped>
// .wrapper>div {
//   width: 1670px;
//   height: 739px;
// }
.wrapper {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  position: relative;
}
</style>
