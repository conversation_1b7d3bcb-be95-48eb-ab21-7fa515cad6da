<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      :inline="true"
    >
      <el-form-item label="巡查对象">
        <el-dropdown trigger="click" @command="handleCommand">
          <span class="el-dropdown-link">

            <el-input
              v-model="queryParams.carTypeName"
              placeholder="请选择巡查对象"
              readonly="readonly"
              clearable
              size="small"
              style="width: 240px; margin: 0 5px;"
              @keyup.enter.native="handleQuery"
            />
          </span>
          <el-dropdown-menu slot="dropdown" style="width: 240px;">
            <el-dropdown-item v-for="v in typeData" :key="v.id" :command="v">{{ v.dictLabel }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form-item>
      <el-form-item label="巡查时间">
        <el-date-picker
          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px;"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item label="关键字检索">
        <el-input v-model="queryParams.Keyword" size="small" style="width: 240px; margin: 0 5px;" placeholder="请输入内容" />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:role:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >
          新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:role:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="dataList"
      :cell-style="cellStyle"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="标题" align="center" prop="title" width="150" show-overflow-tooltip /> -->
      <el-table-column label="车辆类型" align="center" prop="carTypeName" width="100" />
      <el-table-column label="车牌号" prop="carNo" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="违规类型" align="center" prop="inspectionTypeName" width="150" show-overflow-tooltip />
      <el-table-column label="上报人员" prop="userName" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="辅助人员" prop="userNames" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="网格小组" prop="deptName" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="上报时间" align="center" prop="inspectionTime" width="150">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.inspectionTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="地址" prop="address" show-overflow-tooltip />
      <el-table-column label="违规内容" prop="content" show-overflow-tooltip />
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <span :style="{ color: circleColor[scope.row.status] }">{{ scope.row.status | statusName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button v-if="scope.row.status == 2" size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button v-if="scope.row.status == 9" size="mini" type="text" icon="el-icon-edit" @click="handleQueryData(scope.row)">详情</el-button>
          <el-button v-if="scope.row.status == 9" size="mini" type="text" icon="el-icon-printer" style="color: #9e9e9e;" @click="handlePrint(scope.row.transportId)">打印</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- <el-pagination
      :page-size="queryParams.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      style="margin-top: 20px;"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    /> -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 详情弹窗 -->
    <from-list :visible.sync="open" :title="title" :detail-id="detailId" :form-disabled="formDisabled" :car-type-options="typeData" @reLoad="handleQuery" />
  </div>
</template>

<script>
import { transportList} from '@/api/case/pipe/dayCheck'
import { removeTransport } from '@/api/case/pipe/dayCheck'
import fromList from '@/pages/case/views/pipe/illegal/components/fromlist.vue'
export default {
  name: 'DailyCheck',
  components: {fromList},
  filters: {
    statusName(status) {
      const statusObj = { 2: '处理中', 9: '已办结' }
      return statusObj[status]
    }
  },
  data() {
    return {
      // 巡查车类型
      typeData: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      dataList: [],
      // 弹出层标题
      title: '新增违规处置',
      // 详情ID
      detailId: null,
      // 是否可编辑表单
      formDisabled: false,
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        carType: '',
        carTypeName: undefined,
        Keyword: '',
        // 日期范围
        dateRange: []
      },
      // 表单参数
      form: {},
      circleColor: {
        2: '#FAB71C',
        9: '#bdc3bf'
      }
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    handlePrint(id) {
      let routeUrl = this.$router.resolve({ path: '/case/print/illegal', query: { id } })
      window.open(routeUrl.href, '_blank')
    },
    // 类型选择
    handleCommand(command) {
      this.queryParams = {...this.queryParams, carType: command.dictValue, carTypeName: command.dictLabel }
    },
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.queryParams.pageNum = 1
      this.getList()
    },
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.getList()
    },
    // 状态颜色
    cellStyle(row) {
      if (row.column.label == '状态') return `color: ${row.row.statusColor}`
    },
    // 已下为模板
    /** 查询列表 */
    getList() {
      this.loading = true
      let { pageNum, pageSize, carTypeName, status, dateRange, Keyword } = this.queryParams
      let params = { pageNum, pageSize, type: 1 }
      if (carTypeName) params.carTypeName = carTypeName
      if (status || status == 0) params.status = status
      if (dateRange && !dateRange.length == 0) {
        params.searchStartTime = dateRange[0]
        params.searchEndTime = dateRange[1]
      }
      if (Keyword) params.searchValue = Keyword
      Promise.all([
        this.getDicts('transport_car_type'),
        transportList(params)
      ]).then(resAry => {
        const [ carTypeList, listData ] = resAry
        /* cartype */
        this.typeData = carTypeList.data
        /* dataList */
        this.dataList = listData.rows
        this.total = listData.total
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {dateRange: [], pageNum: 1, pageSize: 10, carTypeName: '', carType: undefined, statusName: '', status: undefined, Keyword: ''}
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.transportId)
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.open = true
      this.title = '新增违规处置'
      this.detailId = 0
      this.formDisabled = false
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.open = true
      this.title = '修改违规处置'
      this.detailId = row.transportId
      this.formDisabled = false
    },
    handleQueryData(row) {
      this.open = true
      this.title = '查看违规处置'
      this.detailId = row.transportId
      this.formDisabled = true
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const transportIds = row.transportId || this.ids
      if (Array.isArray(transportIds) && !transportIds.length) {
        this.$message.warning('请选择需要删除的数据')
        return
      }
      this.$confirm('是否确认删除?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          removeTransport(transportIds).then(() => {
            this.getList()
            this.msgSuccess('删除成功')
          })
        })
        .catch(() => {})
    }
  }
}
</script>
