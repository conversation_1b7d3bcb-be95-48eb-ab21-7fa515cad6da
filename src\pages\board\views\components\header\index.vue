<template>
  <div class="header">
    <h1>
      <!-- <img class="logo-img" src="@/assets/logo/logo.png" alt="金华火车站交通枢纽联合执法管理应用"> -->
      <div class="title" @click="handleClick">
        <span class="c-title">金华火车站交通枢纽联合执法管理应用</span>
        <!-- <span class="e-title">Jinhua Railway Station Front Area Integrated Management Information Command Platform</span> -->
      </div>
    </h1>
    <div class="left">
      <span class="f-s-22">{{ dataTime }}</span>
      <span class="week">{{ week }}</span>
      <div class="line" />
      <span>{{ date }}</span>
    </div>
    <div class="right">
      <span>气温{{ temperature }}</span>
      <div class="line" />
      <span class="weather">{{ weather }}</span>
      <span class="logout" style="cursor: pointer;" @click="logout">退出登录</span>
    </div>
  </div>
</template>

<script>
import { loadMap } from '@/utils/amapLoad'

export default {
  data() {
    return {
      date: '',
      dataTime: '',
      week: '',
      timer: null,
      weather: '',
      temperature: ''
    }
  },
  async mounted() {
    this.getDateTime()
    if (this.timer) clearInterval(this.timer)
    this.timer = setInterval(() => this.getDateTime(), 1000)
    // 查询天气
    const AMap = await loadMap(['AMap.Weather'])
    const amapWeather = new AMap.Weather()
    amapWeather.getLive('金华市', (err, data) => {
      if (err) {
        this.weather = '天气查询失败'
      } else {
        console.log(data)
        this.weather = data.weather
        this.temperature = `${data.temperature}℃`
      }
    })
  },
  destroyed() {
    clearInterval(this.timer)
  },
  methods: {
    handleClick() {
      window.location.href = '/'
    },
    getDateTime() {
      const nDate = new Date()
      this.date = this.parseTime(nDate, '{y}-{m}-{d}')
      this.dataTime = this.parseTime(nDate, '{h}:{i}:{s}')
      this.week = this.parseTime(nDate, '星期{a}')
    },
    async logout() {
      this.$confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('LogOut').then(() => {
          location.href = '/'
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
.header {
  width: 100%;
  height: pxtorem(150);
  background: url(@/assets/images/header.png) no-repeat center center / 100% 100%;
  position: fixed;
  z-index: 500;
  color: #fff;
  font-size: pxtorem(18);
  letter-spacing: 2px;
  // font-weight: 700;
  h1 {
    height: pxtorem(88);
    text-align: center;
    font-weight: normal;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0;
    .logo-img {
      width: pxtorem(60);
      height: pxtorem(60);
      margin-right: pxtorem(20);
      cursor: pointer;
    }
    span {
      text-shadow: 0 0 20px rgba(0, 247, 255, 0.8);
    }
    .title {
      display: flex;
      flex-direction: column;
      cursor: pointer;
    }
    .c-title {
      font-size: pxtorem(34);
    }
    .e-title {
      font-size: pxtorem(12);
      letter-spacing: pxtorem(1.2);
    }
  }
  .left,
  .right {
    position: absolute;
    top: pxtorem(20);
    color: #fff;
    .f-s-22 {
      font-size: pxtorem(22);
    }
    .line {
      width: 2px;
      height: pxtorem(16);
      background: #7cd0ec;
      margin: 0 pxtorem(20);
      display: inline-block;
    }
  }
  .left {
    left: pxtorem(25);
    .week {
      margin-left: pxtorem(70);
    }
  }
  .right {
    right: pxtorem(25);
    .logout {
      font-size: pxtorem(20);
      padding-left: pxtorem(30);
      background: url(@/assets/images/logout.png) no-repeat left center / pxtorem(25) pxtorem(20);
    }
    .weather {
      margin-right: pxtorem(60);
    }
  }
}
</style>
