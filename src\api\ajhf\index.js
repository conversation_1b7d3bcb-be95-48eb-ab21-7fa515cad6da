import {request} from '@/utils/request'

//获取回访概况信息
export function getHfgkList(params) {
  return request({
    url: `/ajhf/zftsZfrw/dpHfgk`,
    method: 'get',
    params
  })
}

//获取回访进度信息
export function getHfjdList(params) {
  return request({
    url: `/ajhf/zftsZfrw/hfjd`,
    method: 'get',
    params
  })
}

//获取回访分析图表
export function getHffxList(params) {
  return request({
    url: `/ajhf/zftsZfrw/hffx`,
    method: 'get',
    params
  })
}

//获取交办跟踪列表
export function getjbgzList(params) {
  return request({
    url: `/ajhf/zftsZfrw/jbgz`,
    method: 'get',
    params
  })
}

//获取回访跟踪列表
export function gethfgzList(params) {
  return request({
    url: `/ajhf/zftsZfrw/qlScreenList`,
    method: 'get',
    params
  })
}

//获取回访排名
export function gethfpmList(params) {
  return request({
    url: `/ajhf/zftsZfrw/hfpm`,
    method: 'get',
    params
  })
}

//获取回访概况详情
export function gethfgkList(params) {
  return request({
    url: `/ajhf/zftsZfrw/caseTotalList`,
    method: 'get',
    params
  })
}

//获取案件列表
export function getAjList(params) {
  return request({
    url: `/ajhf/zftsZfrw/qlList`,
    method: 'get',
    params
  })
}

//案件详情
export function getAjDetail(params) {
  return request({
    url: `/ajhf/zftsZfrw/getDetails`,
    method: 'get',
    params
  })
}