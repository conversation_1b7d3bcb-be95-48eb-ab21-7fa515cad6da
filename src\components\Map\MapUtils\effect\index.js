// import { heatMapData } from "./heatMapData.js";
import Heatmap3D from "./heatmap.js";
import EsriODLineLayer from "./esriODLineLayer.js";
import * as externalRenderers from "@arcgis/core/views/3d/externalRenderers.js";
import * as reactiveUtils from "@arcgis/core/core/reactiveUtils.js";
import { getLayerConfigById } from "../layerConfig.js";
import WallLayer from "./WallLayer.js";
import ScanRadar from "./ScanRadar.js";
import FireEmergencyTHREERenderer from "./Fire/FireEmergencyTHREERenderer.js";
import FireSmokeTHREERenderer from "./FireSmoke/FireSmokeEffect.js";
import FountainTHREERenderer from "./FountainTHREERenderer.js";
import CylinderLayer from "./CylinderLayer.js";
import BarLayer from "./BarLayer.js";
import { contains } from "@arcgis/core/geometry/geometryEngine.js";
import PyramidLayer from "./PyramidLayer.js";
import RadarMask from "./RadarMask.js";
import RouteLayer from "./RouteLayer.js";
import ranges from "../rangeHeatMapConfig.js";
import loadHeatmapLayer, { addJinHuaCoverLayer } from "../loadHeatmapLayer.js";
let heat3dEffect = null;
let heat3dMixEffect = null;
let heat3dEffectDynamic = [];
let lineLayerRenders = null;
let fireRenders = [];
let fireSmokeRenders = null;
let fountainRenders = [];
let dynamicWallRenders = []; //
let scanRadarRenders = []; //
let cylinderLayerRender = null; // 扩散圈
let barLayerRender = null; // 柱体
let pyramidRenderer = null;
let radarMaskRender = null;
let routeLayerRender = null;
let odLineLayerRenders = null;
let mix3dHeatHandler = null;
let heat3dEffect2 = null;
let zheshidaHeatmap = null;
let bottomLayer = null; //热力图下叠加的全金华范围面图层
const minScale = 15;
const colorList = [
  { color: [0, 255, 255, 150], percent: 0 }, // 在 0% 时 颜色为 [0, 0, 0, 0]透明
  { color: [0, 255, 255, 255], percent: 4 }, // 在 0% 时 颜色为 [0, 0, 0, 0]透明
  { color: [0, 255, 0, 255], percent: 10 }, // 在 30% 时 颜色为绿色 （10 ~ 30%区间则从蓝色过渡到绿色
  { color: [255, 255, 0, 255], percent: 30 }, // 在 30% 时 颜色为绿色 （10 ~ 30%区间则从蓝色过渡到绿色
  { color: [255, 0, 0, 255], percent: 100 }, // 在 100% 时 颜色为红色 （40 ~ 100%区间则从黄色过渡到红色
];

let rangeHandler = null;
// const zoom = 15;
let lastHeatMapInfo = {
  effect: null,
  range: null,
};

//全金华全量融合热力，zoom控制
export async function addMix3dHeatmap({
  view = window.view,
  zoom = 12.5,
  zoom2 = 15,
  heatmapCfg = {},
  bicyclicRingHeatmapcfg = {},
}) {
  //如果处于第三级goto到第一级
  if (view.zoom > zoom2) {
    await view.goTo(
      {
        position: {
          spatialReference: {
            latestWkid: 4490,
            wkid: 4490,
          },
          x: 120.01306886115333,
          y: 28.366580301417745,
          z: 139408.97898597643,
        },
        heading: 352.21529454461495,
        tilt: 31.867727913680415,
      },
      {
        speedFactor: 2,
      }
    );
  }
  // bottomLayer = addJinHuaCoverLayer({ view });
  if (heat3dEffect || heat3dEffect2) return;
  //加载第二级热力
  addheatMap2(
    bicyclicRingHeatmapcfg.colorList || colorList,
    bicyclicRingHeatmapcfg.overstate || 1.5,
    // true,
    view.zoom > zoom && view.zoom < zoom2,
    bicyclicRingHeatmapcfg.extent || {
      xmax: 120.780266,
      xmin: 119.21623,
      ymax: 29.684145,
      ymin: 28.516192,
    }
  );
  //加载第一级热力
  addHeatMap(
    heatmapCfg.colorList || colorList,
    heatmapCfg.overstate || 3,
    //  true,
    view.zoom < zoom,
    heatmapCfg.extent || {
      xmax: 120.780266,
      xmin: 119.21623,
      ymax: 29.684145,
      ymin: 28.516192,
    }
  );
  //加载范围热力
  addRangeHeatMap({ view, zoom: zoom2 });
  //开始监听
  mix3dHeatHandler = reactiveUtils.watch(
    () => view.zoom,
    (value) => {
      if (heat3dEffect && heat3dEffect2) {
        if (value < zoom) {
          heat3dEffect.setVisible(true);
          heat3dEffect2.setVisible(false);
          // bottomLayer.visible = true;
        } else if (value > zoom && value < zoom2) {
          heat3dEffect.setVisible(false);
          heat3dEffect2.setVisible(true);
          // bottomLayer.visible = false;
        } else {
          heat3dEffect.setVisible(false);
          heat3dEffect2.setVisible(false);
          // bottomLayer.visible = false;
        }
      }
    }
  );
  view.zoom = view.zoom + 0.001;
}

export function removeMix3dHeatmap(view = window.view) {
  if (mix3dHeatHandler) {
    mix3dHeatHandler.remove();
    mix3dHeatHandler = null;
  }
  if (heat3dEffect2) {
    externalRenderers.remove(view, heat3dEffect2);
    heat3dEffect2 = null;
  }
  if (heat3dEffect) {
    externalRenderers.remove(view, heat3dEffect);
    heat3dEffect = null;
  }
  removeRangeHeatMap(view);
  if (bottomLayer) {
    view.map.remove(bottomLayer);
    bottomLayer = null;
  }
}

//全金华第二级3d热力图
export const addheatMap2 = async function (
  colorList = [
    { color: [0, 255, 255, 0], percent: 0 }, // 在 0% 时 颜色为 [0, 0, 0, 0]透明
    { color: [0, 255, 255, 255], percent: 4 }, // 在 0% 时 颜色为 [0, 0, 0, 0]透明
    { color: [0, 255, 0, 255], percent: 10 }, // 在 30% 时 颜色为绿色 （10 ~ 30%区间则从蓝色过渡到绿色
    { color: [255, 255, 0, 255], percent: 30 }, // 在 30% 时 颜色为绿色 （10 ~ 30%区间则从蓝色过渡到绿色
    { color: [255, 0, 0, 255], percent: 100 }, // 在 100% 时 颜色为红色 （40 ~ 100%区间则从黄色过渡到红色
  ],
  overstate = 5,
  visible = true,
  extent = {
    xmax: 119.548264,
    xmin: 119.742262,
    ymax: 29.150306,
    ymin: 29.026034,
  }
) {
  if (!heat3dEffect2) {
    // 从服务器获取数据

    let hour = new Date().getHours();
    if (hour == 0) {
      hour = "24";
    } else if (hour < 10 && hour > 0) {
      hour = `0${hour}`;
    }

    const url = `https://csdnwlgz.dsjj.jinhua.gov.cn:82/city-brain-service/attachments/files/heatmap_m.json`;
    const response = await fetch(url);
    const { data } = await response.json();

    heat3dEffect2 = new Heatmap3D(window.view, {
      externalRenderers,
      pixelValueArray: data,
      extent,

      // 热力图高度夸张倍率
      overstate,
      /**
       * 热力图颜色映射表
       * @type {Array<{ color: Array<number>[4], percent: number }>}
       * color为该节点阈值的颜色 值在 0 ~ 255
       * percent为该节点值在数据[最小值 ~ 最大值]的百分比 值在 0 ~ 100
       *
       * 按百分比 percent 由小到大排序（必须
       */
      colorList,
      // 热力图透明度 值在 0 ~ 1
      opacity: 0.5,
      visible,
      offsetZ: 800,
    });
    externalRenderers.add(window.view, heat3dEffect2);
  } else {
    //如果存在，只需重新赋值参数
    heat3dEffect2.colorList = colorList;
    heat3dEffect2.overstate = overstate;
    heat3dEffect2.ground.geometry = heat3dEffect2.changeCoordinate(
      heat3dEffect2.pixelValueArray
    );
    heat3dEffect2.ground.material.map = heat3dEffect2.produceCanvas(
      heat3dEffect2.pixelValueArray,
      heat3dEffect2.pixelValueObj
    );
  }
};

//浙师大3d热力
export const addzhishidaHeatmap = async function (
  colorList = [
    { color: [0, 255, 255, 0], percent: 0 }, // 在 0% 时 颜色为 [0, 0, 0, 0]透明
    { color: [0, 255, 255, 255], percent: 4 }, // 在 0% 时 颜色为 [0, 0, 0, 0]透明
    { color: [0, 255, 0, 255], percent: 10 }, // 在 30% 时 颜色为绿色 （10 ~ 30%区间则从蓝色过渡到绿色
    { color: [255, 255, 0, 255], percent: 30 }, // 在 30% 时 颜色为绿色 （10 ~ 30%区间则从蓝色过渡到绿色
    { color: [255, 0, 0, 255], percent: 100 }, // 在 100% 时 颜色为红色 （40 ~ 100%区间则从黄色过渡到红色
  ],
  overstate = 4,
  visible = true,
  extent = {
    xmin: 119.606153,
    ymin: 29.117142,
    xmax: 119.68555,
    ymax: 29.150106,
  }
) {
  if (!zheshidaHeatmap) {
    // 从服务器获取数据
    const url = `https://csdnwlgz.dsjj.jinhua.gov.cn/heatmap/popu_zheshida0511.json`;
    const response = await fetch(url);
    const { data } = await response.json();

    zheshidaHeatmap = new Heatmap3D(window.view, {
      externalRenderers,
      pixelValueArray: data,
      extent,

      // 热力图高度夸张倍率
      overstate,
      /**
       * 热力图颜色映射表
       * @type {Array<{ color: Array<number>[4], percent: number }>}
       * color为该节点阈值的颜色 值在 0 ~ 255
       * percent为该节点值在数据[最小值 ~ 最大值]的百分比 值在 0 ~ 100
       *
       * 按百分比 percent 由小到大排序（必须
       */
      colorList,
      // 热力图透明度 值在 0 ~ 1
      opacity: 0.5,
      visible,
    });
    externalRenderers.add(window.view, zheshidaHeatmap);
  } else {
    //如果存在，只需重新赋值参数
    zheshidaHeatmap.colorList = colorList;
    zheshidaHeatmap.overstate = overstate;
    zheshidaHeatmap.ground.geometry = zheshidaHeatmap.changeCoordinate(
      zheshidaHeatmap.pixelValueArray
    );
    zheshidaHeatmap.ground.material.map = zheshidaHeatmap.produceCanvas(
      zheshidaHeatmap.pixelValueArray,
      zheshidaHeatmap.pixelValueObj
    );
  }
};
export const removezheshidaHeatmap = function () {
  if (zheshidaHeatmap) {
    externalRenderers.remove(window.view, zheshidaHeatmap);
    zheshidaHeatmap = null;
  }
};
export const removeHeatMap2 = function () {
  if (heat3dEffect2) {
    externalRenderers.remove(window.view, heat3dEffect2);
    heat3dEffect2 = null;
  }
};
//第三级热力按范围控制（判断view中心点是否在区县范围内，在哪个区县就加哪个热力）
export async function addRangeHeatMap({ view = window.view, zoom }) {
  const rangesClone = JSON.parse(JSON.stringify(ranges));
  //请求各个区县范围
  const res = await Promise.all(
    rangesClone.map(({ serverUrl }) => fetch(serverUrl))
  );

  const datas = await Promise.all(res.map((v) => v.json()));
  // console.log(datas);

  rangesClone.forEach((v, i) => {
    v.geometry = {
      ...datas[i].features[0].geometry,
      spatialReference: { wkid: 4490 },
    };
  });
  console.log(rangesClone);
  //做一个异步存储
  Promise.all(rangesClone.map(({ url }) => fetch(url))).then((res) => {
    res.forEach((v, i) => {
      v.json().then(({ data }) => {
        rangesClone[i].data = data;
      });
    });
  });
  rangeHandler = reactiveUtils.watch(
    () => view.center,
    (center) => {
      if (view.zoom < zoom) {
        if (lastHeatMapInfo.effect) {
          externalRenderers.remove(view, lastHeatMapInfo.effect);
          lastHeatMapInfo.effect = null;
          lastHeatMapInfo.range = null;
        }
        return;
      }
      //定位到是哪个区县
      const range = rangesClone.find(({ geometry }) =>
        contains(geometry, center.clone())
      );

      if (range && range.data) {
        if (range !== lastHeatMapInfo.range) {
          if (lastHeatMapInfo.effect) {
            externalRenderers.remove(view, lastHeatMapInfo.effect);
            lastHeatMapInfo.effect = null;
          }
          const effect = new Heatmap3D(view, {
            externalRenderers,
            pixelValueArray: range.data,
            extent: range.extent,

            // 热力图高度夸张倍率
            overstate: 2,
            /**
             * 热力图颜色映射表
             * @type {Array<{ color: Array<number>[4], percent: number }>}
             * color为该节点阈值的颜色 值在 0 ~ 255
             * percent为该节点值在数据[最小值 ~ 最大值]的百分比 值在 0 ~ 100
             *
             * 按百分比 percent 由小到大排序（必须
             */
            colorList: [
              { color: [0, 255, 255, 0], percent: 0 }, // 在 0% 时 颜色为 [0, 0, 0, 0]透明
              { color: [0, 255, 255, 255], percent: 4 }, // 在 0% 时 颜色为 [0, 0, 0, 0]透明
              { color: [0, 255, 0, 255], percent: 10 }, // 在 30% 时 颜色为绿色 （10 ~ 30%区间则从蓝色过渡到绿色
              { color: [255, 255, 0, 255], percent: 30 }, // 在 30% 时 颜色为绿色 （10 ~ 30%区间则从蓝色过渡到绿色
              { color: [255, 0, 0, 255], percent: 100 }, // 在 100% 时 颜色为红色 （40 ~ 100%区间则从黄色过渡到红色
            ],
            // 热力图透明度 值在 0 ~ 1
            opacity: 0.5,
            visible: true,
            offsetZ: 80, //高层偏移量
          });
          lastHeatMapInfo.effect = effect;
          lastHeatMapInfo.range = range;
          externalRenderers.add(view, effect);
        }
      } else {
        if (lastHeatMapInfo.effect) {
          externalRenderers.remove(view, lastHeatMapInfo.effect);
          lastHeatMapInfo.effect = null;
          lastHeatMapInfo.range = null;
        }
      }
    }
  );
  //触发监听
  view.zoom = view.zoom + 0.01;
}

export function removeRangeHeatMap(view) {
  if (rangeHandler) {
    rangeHandler.remove();
    rangeHandler = null;
  }
  if (lastHeatMapInfo.effect) {
    externalRenderers.remove(view, lastHeatMapInfo.effect);
    lastHeatMapInfo.effect = null;
    lastHeatMapInfo.range = null;
  }
}
//全金华3d热力图第一级
export const addHeatMap = async function (
  colorList = [
    { color: [0, 255, 255, 255], percent: 0 }, // 在 0% 时 颜色为 [0, 0, 0, 0]透明
    { color: [0, 255, 255, 255], percent: 4 }, // 在 0% 时 颜色为 [0, 0, 0, 0]透明
    { color: [0, 255, 0, 255], percent: 10 }, // 在 30% 时 颜色为绿色 （10 ~ 30%区间则从蓝色过渡到绿色
    { color: [255, 255, 0, 255], percent: 30 }, // 在 30% 时 颜色为绿色 （10 ~ 30%区间则从蓝色过渡到绿色
    { color: [255, 0, 0, 255], percent: 100 }, // 在 100% 时 颜色为红色 （40 ~ 100%区间则从黄色过渡到红色
  ],
  overstate = 1,
  visible = true,
  extent = {
    xmax: 120.780266,
    xmin: 119.21623,
    ymax: 29.684145,
    ymin: 28.516192,
  }
) {
  if (!heat3dEffect) {
    // 从服务器获取数据

    let hour = new Date().getHours();
    if (hour == 0) {
      hour = "24";
    } else if (hour < 10 && hour > 0) {
      hour = `0${hour}`;
    }

    const url = `https://csdnwlgz.dsjj.jinhua.gov.cn:82/city-brain-service/attachments/files/heatmap_m.json`;
    const response = await fetch(url);
    const { data } = await response.json();

    heat3dEffect = new Heatmap3D(window.view, {
      externalRenderers,
      pixelValueArray: data,
      extent,
      // 热力图高度夸张倍率
      overstate,
      /**
       * 热力图颜色映射表
       * @type {Array<{ color: Array<number>[4], percent: number }>}
       * color为该节点阈值的颜色 值在 0 ~ 255
       * percent为该节点值在数据[最小值 ~ 最大值]的百分比 值在 0 ~ 100
       *
       * 按百分比 percent 由小到大排序（必须
       */
      colorList,
      // 热力图透明度 值在 0 ~ 1
      opacity: 0.5,
      visible,
      offsetZ: 800,
    });
    externalRenderers.add(window.view, heat3dEffect);
  } else {
    //如果存在，只需重新赋值参数
    heat3dEffect.colorList = colorList;
    heat3dEffect.overstate = overstate;
    heat3dEffect.ground.geometry = heat3dEffect.changeCoordinate(
      heat3dEffect.pixelValueArray
    );
    heat3dEffect.ground.material.map = heat3dEffect.produceCanvas(
      heat3dEffect.pixelValueArray,
      heat3dEffect.pixelValueObj
    );
  }
};

//二三位混合热力，zoom控制显隐
export async function addMixHeatMap(
  colorList = [
    { color: [0, 255, 255, 0], percent: 0 }, // 在 0% 时 颜色为 [0, 0, 0, 0]透明
    { color: [0, 255, 255, 255], percent: 4 }, // 在 4% 时 颜色为 [0, 0, 255, 255]蓝色 （0 ~ 4%区间则从透明过渡到蓝色
    { color: [0, 255, 0, 255], percent: 10 }, // 在 10% 时 颜色为绿色 （4 ~ 10%区间则从蓝色过渡到绿色
    { color: [255, 255, 0, 255], percent: 30 }, // 在 30% 时 颜色为黄色 （10 ~ 30%区间则从绿色过渡到黄色
    { color: [255, 0, 0, 255], percent: 100 }, // 在 100% 时 颜色为红色 （30 ~ 100%区间则从黄色过渡到红色
  ],
  overstate = 1
) {
  if (!heat3dMixEffect) {
    // 从服务器获取数据
    let hour = new Date().getHours();
    if (hour == 0) {
      hour = "24";
    } else if (hour < 10 && hour > 0) {
      hour = `0${hour}`;
    }

    const url = `https://csdnwlgz.dsjj.jinhua.gov.cn/heatmap/${hour}.json`;
    const url2d = `https://csdnwlgz.dsjj.jinhua.gov.cn/heatmap2d/${hour}.json`;
    const [res, heat2dData] = await Promise.all([fetch(url), fetch(url2d)]);
    const { data } = await res.json();
    const data2d = await heat2dData.json();

    loadHeatmapLayer({
      data: data2d.map((item) => ({
        ...item,
        lan: +item.lng,
        lat: +item.lat,
        count: +item.count,
      })),
      colorStops: colorList.map((colorObj) => ({
        color: `rgba(${colorObj.color.join(",")})`,
        ratio: colorObj.percent * 0.01,
      })),
      maxDensity: 0.035,
      referenceScale: null,
      radius: 350, // 查询半径单位pt
      minScale, // 可添参数，热力图展示的最小zoom级别
      visible2d: false,
      opacity2d: 0.7,
    });
    heat3dMixEffect = new Heatmap3D(window.view, {
      externalRenderers,
      pixelValueArray: data,
      extent: {
        xmax: 120.780266,
        xmin: 119.21623,
        ymax: 29.684145,
        ymin: 28.516192,
      },
      // 热力图高度夸张倍率
      overstate,
      /**
       * 热力图颜色映射表
       * @type {Array<{ color: Array<number>[4], percent: number }>}
       * color为该节点阈值的颜色 值在 0 ~ 255
       * percent为该节点值在数据[最小值 ~ 最大值]的百分比 值在 0 ~ 100
       *
       * 按百分比 percent 由小到大排序（必须
       */
      colorList,
      // 热力图透明度 值在 0 ~ 1
      opacity: 0.5,
    });
    view.watch("zoom", (e) => {
      if (minScale) {
        if (e < minScale) {
          heat3dMixEffect.setVisible(true);
        } else {
          heat3dMixEffect.setVisible(false);
        }
      }
    });
    view.zoom = view.zoom + 0.01;
    externalRenderers.add(window.view, heat3dMixEffect);
  } else {
    //如果存在，只需重新赋值参数
    heat3dMixEffect.colorList = colorList;
    heat3dMixEffect.overstate = overstate;
    heat3dMixEffect.ground.geometry = heat3dMixEffect.changeCoordinate(
      heat3dMixEffect.pixelValueArray
    );
    heat3dMixEffect.ground.material.map = heat3dMixEffect.produceCanvas(
      heat3dMixEffect.pixelValueArray,
      heat3dMixEffect.pixelValueObj
    );
  }
}

export function removeMixHeatMap() {
  if (heat3dMixEffect) {
    externalRenderers.remove(window.view, heat3dMixEffect);
    heat3dMixEffect = null;
  }
  const layer = view.map.findLayerById("heatmap2d");
  if (layer) {
    view.map.remove(layer);
  }
}

export function removeHeatmap() {
  if (heat3dEffect) {
    externalRenderers.remove(window.view, heat3dEffect);
    heat3dEffect = null;
  }
}

//  ------------------------------------------交通流光图--------------------------------------------------------

export function addLineEffect(url = getLayerConfigById("RailwayLine").url) {
  if (lineLayerRenders === null) {
    lineLayerRenders = new EsriODLineLayer(window.view, {
      queryUrl: url,
      color: "#ffb506",
      size: 5, //宽度
      length: 0.2, //<1
      speed: 0.1, //<1
      isShow: true, //是否可见道路线
    });

    externalRenderers.add(view, lineLayerRenders);
  } else {
    return;
  }
}

export function removeLineEffect() {
  if (lineLayerRenders) {
    externalRenderers.remove(window.view, lineLayerRenders);
    lineLayerRenders = null;
  }
}

//  ------------------------------------------自定义交通流光图--------------------------------------------------------

export function addOdLineEffect(option) {
  if (odLineLayerRenders === null) {
    odLineLayerRenders = new EsriODLineLayer(window.view, {
      ...option,
    });

    externalRenderers.add(view, odLineLayerRenders);
  } else {
    return;
  }
}

export function removeOdLineEffect() {
  if (odLineLayerRenders) {
    externalRenderers.remove(window.view, odLineLayerRenders);
    odLineLayerRenders = null;
  }
}
//自定义paths
export function addPathsLineEffect(view, option) {
  const odLineLayerRender = new EsriODLineLayer(view, {
    ...option,
  });
  externalRenderers.add(view, odLineLayerRender);

  return odLineLayerRender;
}

export function removePathsLineEffect(view, renderer) {
  if (renderer) {
    externalRenderers.remove(view, renderer);
  }
}
//  ------------------------------------------火焰特效--------------------------------------------------------
export function addFireEffect(locations) {
  if (fireRenders.length === 0) {
    fireRenders = locations.map((v) => {
      const fireRender = new FireEmergencyTHREERenderer();
      fireRender.view = view;
      fireRender.locations = [v];
      fireRender.externalRenderers = externalRenderers;
      externalRenderers.add(window.view, fireRender);
      return fireRender;
    });
  } else {
    return;
  }
}

export function removeFireEffect() {
  if (fireRenders.length) {
    fireRenders.forEach((v) => {
      externalRenderers.remove(window.view, v);
    });
    fireRenders.length = 0;
  }
}

//  ------------------------------------------烟雾特效--------------------------------------------------------
export function addFireSmokeEffect(locations) {
  if (fireSmokeRenders === null) {
    fireSmokeRenders = new FireSmokeTHREERenderer();
    fireSmokeRenders.view = view;
    fireSmokeRenders.locations = locations;
    fireSmokeRenders.externalRenderers = externalRenderers;
    externalRenderers.add(window.view, fireSmokeRenders);
  } else {
    return;
  }
}

export function removeFireSmokeEffect() {
  if (fireSmokeRenders) {
    externalRenderers.remove(window.view, fireSmokeRenders);
    fireSmokeRenders = null;
  }
}

//  ------------------------------------------喷泉特效--------------------------------------------------------
export function addFountainEffect(location) {
  if (fountainRenders.length === 0) {
    location.forEach((loc) => {
      const fountainRender = new FountainTHREERenderer(window.view, {
        center: [loc.x, loc.y, loc.z || 50],
        color: loc.color || 0xccccff,
        heightLevel: loc.heightLevel || 1,
        sizeLevel: loc.sizeLevel || 0.5,
        particleCount: loc.particleCount || 3000,
      });
      fountainRender.externalRenderers = externalRenderers;
      externalRenderers.add(window.view, fountainRender);
      fountainRenders.push(fountainRender);
    });
  } else {
    return;
  }
}

export function removeFountainEffect() {
  if (fountainRenders.length > 0) {
    fountainRenders.forEach((fountainRender) => {
      externalRenderers.remove(window.view, fountainRender);
    });
    fountainRenders = [];
  }
}

//#region 动态墙体
export function addDynamicWall({ view, points, height, color, isDynamic }) {
  for (var i = 1; i < points.length; i++) {
    const line = [points[i - 1], points[i]];
    const itemWallRender = new WallLayer({
      view,
      points,
      height,
      color,
      isDynamic,
    });
    externalRenderers.add(window.view, itemWallRender);
    dynamicWallRenders.push(itemWallRender);
  }
}

export function removeDynamicWall() {
  for (let i = 0, len = dynamicWallRenders.length; i < len; i++) {
    const item = dynamicWallRenders[i];
    externalRenderers.remove(window.view, item);
  }
  dynamicWallRenders = [];
}
//#endregion

//#region 动态扫描
export const addScanRadar = function ({ view, data }) {
  for (var i = 0; i < data.length; i++) {
    const item = data[i];
    const itemScanRadarRender = new ScanRadar({
      view,
      ...item,
      // view,
      // height: 5000,
      // radius: 10000,
      // position: [119.6242823104002, 29.05841639892346, 100],
    });
    externalRenderers.add(view, itemScanRadarRender);
    scanRadarRenders.push(itemScanRadarRender);
  }
};

export function removeScanRadar(view) {
  for (let i = 0, len = scanRadarRenders.length; i < len; i++) {
    const item = scanRadarRenders[i];
    externalRenderers.remove(view, item);
  }
  scanRadarRenders = [];
}
//#endregion

//#region 扩散圈
export const addCylinderLayer = function ({ view, data }) {
  const layerRender = new CylinderLayer({
    view,
    data,
  });
  externalRenderers.add(view, layerRender);
  cylinderLayerRender = layerRender;
};

export function removeCylinderLayer(view) {
  if (cylinderLayerRender) {
    externalRenderers.remove(view, cylinderLayerRender);
    cylinderLayerRender = null;
  }
}
//#endregion

//#region 添加柱状体
export const addBarLayer = function ({ view, data }) {
  barLayerRender = new BarLayer({
    view,
    data,
  });
  externalRenderers.add(view, barLayerRender);
};

export function removeBarLayer(view) {
  if (barLayerRender) {
    externalRenderers.remove(view, barLayerRender);
    barLayerRender = null;
  }
}
//#endregion

//#region 金字塔点位图层
export const addPyramidLayer = function ({ view, data, color }) {
  pyramidRenderer = new PyramidLayer(view, data, color);
  externalRenderers.add(view, pyramidRenderer);
};

export function removePyramidLayer(view) {
  if (pyramidRenderer) {
    externalRenderers.remove(view, pyramidRenderer);
    pyramidRenderer = null;
  }
}
//#endregion

//#region 雷达遮罩
export const addRadarMask = function ({ view, data }) {
  const layerRender = new RadarMask({
    view,
    data,
  });
  externalRenderers.add(view, layerRender);
  radarMaskRender = layerRender;
};

export function removeRadarMask(view) {
  if (radarMaskRender) {
    externalRenderers.remove(view, radarMaskRender);
    radarMaskRender = null;
  }
}
//#endregion

//#region 路径图层
export const addRouteLayer = function ({
  view,
  data,
  routeColor,
  isLock,
  speed,
}) {
  routeLayerRender = new RouteLayer({
    view,
    data,
    routeColor,
    isLock,
    speed,
  });
  externalRenderers.add(view, routeLayerRender);
};

export function removeRouteLayer(view) {
  if (routeLayerRender) {
    view.map.remove(routeLayerRender.routeLayer);
    externalRenderers.remove(view, routeLayerRender);
    routeLayerRender = null;
  }
}
//#endregion
