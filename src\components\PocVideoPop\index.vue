<template>
  <ygfDialog :visible='visible' width='1500px' style='margin-top: 240px;'>
    <div id="zfy_video" class="rwgz-tc">
    <div class="rw-title flex-between">
      <div class="fs-44 font-syBold dialogTitle" id="rwTitle"></div>
      <div class="close" style="z-index: 9999" @click="closeWin"></div>
    </div>
    <video
      id="videoPlay"
      controls="controls"
      autoplay="autoplay"
      muted="false"
      name="media"
      style="
          width: 94%;
          height: 90%;
          position: absolute;
          top: 75px;
          left: 50px;
        "
      ref="video"
    ></video>
    <div
      style="
          z-index: 1000;
          position: absolute;
          top: 50px;
          left: 50px;
          display: flex;
        "
    >
      <button
        style="width: 70px; height: 30px; margin-bottom: 10px; display: none"
        onclick=""
      >
        登录
      </button>
      <button
        style="width: 70px; height: 30px; margin-bottom: 10px; display: none"
        onclick=""
      >
        打开视频
      </button>
      <button
        style="width: 70px; height: 30px; margin-bottom: 10px; display: none"
        onclick=""
      >
        呼叫
      </button>
      <button
        id="pttButton"
        style="
            width: 180px;
            height: 40px;
            margin-bottom: 10px;
            font-size: 24px;
          "
        @click="ptt()"
      >
        *
      </button>
    </div>
  </div>
  </ygfDialog>
</template>

<script>
// const poc = require('@/utils/poc_sdk.js')
import ygfDialog from '@/components/ygfDialog'
export default {
  name: 'index',
  props: ['visible','gVideoUser','gUID','gPWD'],
  components: {
    ygfDialog
  },
  data() {
    return {
      pttButtonOnTalk: " 结束讲话 ",
      pttButtonOnIdle: " 点击开始讲话 ",
      pttButtonOnFail: " 连接失败 ",

      videoButtonMute: " Mute ",
      videoButtonUnmute: " Unmute ",
      videoButtonPause: " Pause ",
      videoButtonResume: " Resume ",

      gVideoSession: 0,
      // gVideoUser: "1440641527868",
      gVideoOwner: "",

      personList: [],
      dataList: [],
    }
  },
  computed: {},
  mounted() {
    this.check()
  },
  methods: {
    check() {
      let that = this;
      //==========检测webrtc/wasm
      if (poc.isWebRTCSupported) {
        console.log("[POC] Info: WebRTC is supported!");
      } else {
        console.log("[POC] Warning: WebRTC is unsupported!");
      }

      if (poc.isWebAssemblySupported) {
        console.log("[POC] Info: WebAssembly is supported!");
      } else {
        console.log("[POC] Warning: WebAssembly is unsupported!");
      }
      if (!poc.isWebRTCSupported || !poc.isWebAssemblySupported) {
        console.log("WebRTC or WebAssembly unsupported!");
      } else {
        poc.ptt.init(function (err) {
          if (err != null) {
            if (err.name == "NotFoundError") {
              console.log(err + ". PTT Listen Only!");
            } else {
              console.log(
                err +
                ". POC PTT init has error! Some functions may not be available."
              );
            }
          }
          poc.ptt.setLog(true);
          that.init();
        });
      }
    },
    init() {
      this.login();

      // 生成会话
      setTimeout(() => {
        this.dialogCall();
      }, 2000);

      //生成视频画面
      setTimeout(() => {
        this.onVideoShare();
      }, 4000);

      //callback
      this.onContactPresence();
      this.onLocation();
    },
    closeWin() {
      console.log("关闭视频");
      this.dialogBye();
      this.closeDialog()
    },

    login() {
      let that = this;
      var addr = "https://112.33.0.176:4010/dm-http-interface";
      poc.ptt.doLogin(addr, this.gUID, this.gPWD);
      poc.ptt.onLogin = function (result, secret) {
        if (result == 0) {
          //登陆成功,记住用户名密码
          document.cookie = "uid=" + that.gUID;
          document.cookie = "pwd=" + that.gPWD;
        }
        console.log("onLogin: result=" + result + " secret=" + secret);
      };
    },
    logout() {
      poc.ptt.doLoginOut();
      poc.ptt.onLogout = function (result, reason) {
        console.log("onLogout: result=" + result + " reason=" + reason);
      };
    },
    dialogCall() {
      let that = this;
      var inputStr = this.gVideoUser;
      if (inputStr == null || inputStr.length <= 0) {
        return;
      }
      var t = "tempuser";
      if (t == "sid" || t == "tempsid") {
        poc.ptt.doSessionTempCall("", inputStr, 0, function (session) {
          console.log(
            "doSessionTempCall: sid=" + inputStr + " session=" + session
          );
        });
      } else if (t == "tempuser") {
        var users = inputStr.split(",");
        poc.ptt.doSessionTempCall(users, "", 0, function (session) {
          console.log(
            "doSessionTempCall: users=" + users + " session=" + session
          );
          that.gVideoSession = session;

          // callback
          that.onSessionDialogPresence();
          that.onSessionRelease();
        });
      } else if (t == "tempinvite") {
        var users = inputStr.split(",");
        that.gVideoSession = "";
        poc.ptt.doSessionCallInvite(session, users);
        console.log(
          "doSessionCallInvite: users=" + users + " session=" + session
        );
      }
    },
    dialogBye() {
      this.closeDialog()
      var session = this.gVideoSession;
      poc.ptt.doLeaveCall(session);
      console.log("dialogBye: session=" + session);
    },
    ptt() {
      var session = this.gVideoSession;
      var users = this.gVideoUser;
      if (
        document.getElementById("pttButton").innerHTML ==
        this.pttButtonOnIdle
      ) {
        if (users.length > 0) {
          //广播带用户
          document.getElementById("pttButton").innerHTML =
            this.pttButtonOnTalk;
          poc.ptt.doTalkRequestWithUserlist(session, users.split(","));
        } else {
          //out queue or release
          document.getElementById("pttButton").innerHTML =
            this.pttButtonOnTalk;
          poc.ptt.doTalkRequest(session);
        }
      } else {
        document.getElementById("pttButton").innerHTML =
          this.pttButtonOnIdle;
        poc.ptt.doTalkRelease(session);
      }
    },
    onVideoShare() {
      let that = this;
      poc.video.onVideoShare = function (session, sid, ipocid, valid) {
        console.log(session, sid, ipocid, valid);
        var r = false;
        if (valid) {
          r = confirm("onVideoShare: sid =" + sid + "ipocid=" + ipocid);
          if (r == true) {
            that.gVideoOwner = ipocid;
            that.gVideoSession = session;
            poc.video.play(session, document.getElementById("videoPlay"));
            that.videoDup();
          }
        } else {
          //清理多余标签
          that.cleanVideoDup();
        }

        console.log(
          "onVideoShare:  session=" +
          session +
          " accept=" +
          r +
          " sid=" +
          sid +
          " ipocid=" +
          ipocid +
          " valid=" +
          valid
        );
      };
    },
    videoDup() {
      var v = document.getElementById("videoPlay");
      v.autoplay = true;
      v.className = "videoDup";
      document.getElementById("videoPlay").after(v);
      poc.video.play(this.gVideoSession, v);
    },

    // callback
    onContactPresence() {
      poc.ptt.onContactPresence = function (userList) {
        console.log("userList", userList);
      };
    },

    //callback
    onLocation() {
      poc.ptt.onLocation = function (loc) {
        console.log("onLocation:  loc=" + JSON.stringify(loc));
      };
    },

    //callback
    onSessionDialogPresence() {
      let that = this;
      poc.ptt.onSessionDialogPresence = function (userList) {
        document.getElementById("pttButton").innerHTML =
          that.pttButtonOnIdle;
        console.log("onSessionDialogPresence", userList);
      };
    },

    //callback
    onSessionRelease() {
      let that = this;
      poc.ptt.onSessionRelease = function (session, reason) {
        document.getElementById("pttButton").innerHTML =
          that.pttButtonOnFail;
        console.log("onSessionRelease: " + session + " reason: " + reason);
      };
    },

    cleanVideoDup() {
      //清理多余标签
      var tags = null;
      do {
        tags = document.getElementsByClassName("videoDup");
        console.log("remove " + tags.length);
        for (i = 0; i < tags.length; i++) {
          tags[i].removeAttribute("src");
          tags[i].removeAttribute("srcObject");
          console.log(tags[i]);
          tags[i].parentNode.removeChild(tags[i]);
        }
      } while (tags != null && tags.length > 0);
    },

    closeDialog() {
      this.$emit('close')
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.check()
      } else {
        this.logout()
      }
    }
  }
}
</script>

<style scoped>
.rwgz-tc {
  position: relative;
  width: 1500px;
  height: 1000px;
  background-image: url("@/assets/zhdd/bg_panel.png");
  background-size: 100% 100%;
  /* border: 2px solid #3a9ff8;
  background: rgba(3,24,39,.8); */
  border-radius: 57px;
}

.rw-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: absolute;
  top: 50px;
  z-index: 888;
  /* background: linear-gradient(#00aae2, #064b65); */
  border-radius: 57px 57px 0 0;
  width: 100%;
  height: 60px;
  line-height: 60px;
  padding: 1% 3%;
  box-sizing: border-box;
}

.close {
  background: url("@/assets/zhdd/close.png") no-repeat;
  width: 34px;
  height: 34px;
  cursor: pointer;
  float: right;
}
</style>