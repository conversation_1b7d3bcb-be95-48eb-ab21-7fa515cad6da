import {request} from '@/utils/request'

// 查询服务预约列表
export function listAppointment(query) {
  return request({
    url: '/business/vol/appointment/list',
    method: 'get',
    params: query
  })
}

// 查询服务预约详细
export function getAppointment(id) {
  return request({
    url: '/business/vol/appointment/' + id,
    method: 'get'
  })
}

// 新增服务预约
export function addAppointment(data) {
  return request({
    url: '/business/vol/appointment/add',
    method: 'post',
    data: data
  })
}

// 修改服务预约
export function updateAppointment(data) {
  return request({
    url: '/business/vol/appointment/edit',
    method: 'post',
    data: data
  })
}

// 删除服务预约
export function delAppointment(id) {
  return request({
    url: '/business/vol/appointment/remove/' + id,
    method: 'post'
  })
}

// 导出服务预约
export function exportAppointment(query) {
  return request({
    url: '/business/vol/appointment/export',
    method: 'get',
    params: query
  })
}
