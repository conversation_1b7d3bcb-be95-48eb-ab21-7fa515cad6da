<template>
  <div class='pageContainer-noMap'>
    <div class='monitor-left'>
      <videoList></videoList>
    </div>
    <div class='monitor-right'>
      <videoView></videoView>
<!--      <videoHistory></videoHistory>-->
    </div>
  </div>
</template>

<script>
import videoList from './videoList'
import videoView from './videoView'
import videoHistory from './videoHistory'
export default {
  name: 'index',
  components: {
    videoList,
    videoView,
    videoHistory
  },
  data() {
    return {}
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang='less'>
  .monitor-left{
    padding: 50px 0;
    box-sizing: border-box;
    width: 1030px;
    height: 1960px;
    background: url('@/assets/index/left-bg.png') no-repeat;
    background-size: 100% 100%;
    z-index: 2;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-content: center;
    margin-top: 220px;
  }
  .monitor-right {
    width: 2800px;
    height: 1960px;
    z-index: 2;
    margin-top: 220px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
  }
</style>