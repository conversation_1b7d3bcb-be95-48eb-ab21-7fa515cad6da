import Map from "@arcgis/core/Map.js";
import MapView from "@arcgis/core/views/MapView.js";
import TileLayer from "@arcgis/core/layers/TileLayer.js";

import {
  getIRSLayer,
  getIRSlabelLayer,
  getCustomBasemp,
} from "./basemap.js";
import layerCreatAsync from "./layerCreatAsync.js";
import { getLayerConfigById } from "./layerConfig.js";

const basemapFactor = {
  vector: {
    id: "vectorMap",
    url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/BLUEGrey_noPOI_newdata/MapServer",
  },
  tiandituVector: {
    id: "tiandituVector",
    url: "https://mps.arcgisonline.cn/tianditu/xmap2/Jinhua/vea/c/dark/MapServer",
  },
};


let catchBaseMap = null;
catchBaseMap = getIRSLayer();
/**
 * 加载MapView
 * @param {*} divId
 */
function initMapView({ divId, basemap = "vector" }) {
  const map = new Map({
    basemap: catchBaseMap,
  });
  // map.ground.surfaceColor = "#08294a"; // 设置球体颜色
  const viewProps = {
    container: divId || "viewDiv",
    map: map,
    extent: {
      spatialReference: {
        latestWkid: 4490,
        wkid: 4490,
      },
      xmin: 118.58367919918683,
      ymin: 28.454589843742458,
      xmax: 121.22039794918786,
      ymax: 29.75509643553983,
    },
  };
  const view = new MapView(viewProps);
  window.view = view;
  // goHome方法
  view.goHome = () => {
    view.goTo({
      spatialReference: {
        latestWkid: 4490,
        wkid: 4490,
      },
      xmin: 118.58367919918683,
      ymin: 28.454589843742458,
      xmax: 121.22039794918786,
      ymax: 29.75509643553983,
    });
  };

  // 添加行政区切片
  // const rangeConfig = getLayerConfigById("shiliangqiepian");
  // const rangeConfigLayer = await layerCreatAsync(rangeConfig);
  // view.map.add(rangeConfigLayer);
  view.qualityProfile = "high";
  return view;
}

export default initMapView;
