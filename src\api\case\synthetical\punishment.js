import {request} from '@/utils/request'
/** 简易处罚 */
// 列表
export function punishmentList(params) {
  return request({
    url: '/business/punishment/list',
    method: 'get',
    params
  })
}
// 列表
export function punishmentOne(params) {
  return request({
    url: '/business/punishment/' + params,
    method: 'get'
  })
}
export function myPunishmentList(params) {
  return request({
    url: '/business/punishment/myCases',
    method: 'get',
    params
  })
}
// 修改
export function editPunishment(data) {
  return request({
    url: '/business/punishment/edit',
    method: 'post',
    data
  })
}
// 删除
export function removePunishment(data) {
  return request({
    url: '/business/punishment/remove/' + data,
    method: 'post'
  })
}
