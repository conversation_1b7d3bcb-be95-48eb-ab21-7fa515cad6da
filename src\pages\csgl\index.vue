<template>
  <div class='pageContainer-Map'>
    <left></left>
    <center></center>
    <right></right>
  </div>
</template>

<script>
import left from './left'
import right from './right'
import center from './center'
export default {
  name: 'index',
  components: {
    left,
    right,
    center,
  },
  data() {
    return {}
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped>

</style>