import {request} from '@/utils/request'

//预警情况数据区域
export function getYjqkChartqy(params) {
  return request({
    url: `/statistics/warnings/statistics-by-district`,
    method: 'get',
    params
  })
}

//预警情况数据领域
export function getYjqkChartly(params) {
  return request({
    url: `/statistics/warnings/sz/statistics-by-domain`,
    method: 'get',
    params
  })
}

//三书一函数据
export function getSsyh(params) {
  return request({
    url: `/statistics/letters/statistics-by-type`,
    method: 'get',
    params
  })
}

//永康系统
export function getYklink(params) {
  return request({
    url: `/misApi/mis/system/linkdetails/tempAuthCode`,
    method: 'get',
    dataType: "json",
    headers: {
      Authorization: sessionStorage
        .getItem("Authorization")
        .replaceAll('"', ""),
    },
    params
  })
}

//磐安系统
export function getPalink(data) {
  return request({
    url: `/paxmd/sso/getToken`,
    method: 'post',
    headers: {
      "Content-Type": "application/json"
    },
    dataType: "json",
    data
  })
}

//获取本项目url
export function getUrl(city) {
  return request({
    url: `/xzzfj/mdUser/getUrl?area=` + city,
    method: 'post'
  })
}

export function getPwdNow(params) {
  return request({
    url: `/xzzfj/hdjjUpdateLog/getPwdNow`,
    method: 'get',
    params
  })
}

//获取新平台东阳市执法仪列表
export function getDyPocList(params) {
  return request({
    url: `/screen/dongyang/getAllOnlineUserTracks`,
    method: 'get',
    params
  })
}
