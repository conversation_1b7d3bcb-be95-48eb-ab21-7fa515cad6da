import {request} from '@/utils/request'
let dzzp = {
  list(params) {
    return request({
      url: '/business/traffic/capture/list',
      method: 'get',
      params
    })
  },
  oneList(params) {
    return request({
      url: '/business/traffic/capture/' + params,
      method: 'get'
    })
  },
  add(data) {
    return request({
      url: '/business/traffic/capture/add',
      method: 'post',
      data
    })
  },
  edit(data) {
    return request({
      url: '/business/traffic/capture/edit',
      method: 'post',
      data
    })
  },
  remove(data) {
    return request({
      url: '/business/traffic/capture/remove/' + data,
      method: 'post'
    })
  }
}

export default dzzp
