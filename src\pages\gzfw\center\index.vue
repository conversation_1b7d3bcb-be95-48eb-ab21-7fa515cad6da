<template>
  <div class="center-Map">
    <div class="top1 item">
      <div class="info">
        <div class="name">当日服务人次</div>
        <div class="flex-e">
          <div class="num">{{ value1 }}</div>
          <div class="rate flex-e">
            <div>环比 </div>
            <div class="num2"> {{ todayPercent || '-'}}%</div>
          </div>
        </div>
      </div>
    </div>
    <div class="top2 item">
      <div class="info">
        <div class="name">累计服务人次</div>
        <div class="flex-e">
          <div class="num">{{ value2 }}</div>
          <!-- <div class="rate flex-e">
            <div>环比</div>
            <div class="num2">+16.8%</div>
          </div> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { midMap } from '@/api/gzfw/index.js'
export default {
  name: 'index',
  components: {},
  data() {
    return {
      value1: 0,
      p1: 0,
      value2: 0,
    }
  },
  computed: {},
  mounted() {
    let area = localStorage.getItem('city')
    this.init(area)
    // this.$bus.$on('cityChange', (city) => {
    //   area = city
    //   this.init(area)
    // })
  },
  methods: {
    init(area) {
      midMap().then((res) => {
        this.value1 = res.data.todayCount
        this.p1 = res.data.todayPercent
        this.value2 = res.data.totalCount
      })
    },
  },
  watch: {},
}
</script>

<style scoped lang="scss">
.item {
  width: 606px;
  height: 161px;

  .info {
    padding-left: 180px;
    box-sizing: border-box;
    width: 100%;
    color: #ffffff;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 500;
    font-size: 32px;
    .name {
      margin-top: 24px;
    }
    .rate {
      margin-left: 30px;
      margin-bottom: 10px;
    }
    .num {
      font-size: 60px;
      font-weight: 700;
      background: linear-gradient(180deg, #ffeccb 0%, #ffffff 47%, #ffc460 50%, #ffffff 100%);
      -webkit-background-clip: text;
      color: transparent;
    }
    .num2 {
      margin-right: 10px;
      font-size: 32px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      background: linear-gradient(180deg, #ffffff 32%, #52c3f7 100%);
      -webkit-background-clip: text;
      color: transparent;
    }
  }
}
.top1 {
  position: fixed;
  top: 220px;
  left: 1230px;
  background: url(@/assets/gzfw/itemBg1.png) no-repeat;
  background-size: 100% 100%;
}
.top2 {
  position: fixed;
  top: 220px;
  left: 2000px;
  background: url(@/assets/gzfw/itemBg2.png) no-repeat;
  background-size: 100% 100%;
}
.flex-e {
  display: flex;
  align-items: flex-end;
}
</style>