<template>
  <div v-if="visible" class="largeWindowInfo">
    <!-- 左侧部分 -->
    <div v-loading="leftLoading" class="left">
      <el-image class="img" :src="fImgSrc" fit="cover">
        <div slot="placeholder" class="image-slot">
          <span class="dot">加载中...</span>
        </div>
      </el-image>
      <div class="content">
        <h2>出租车信息</h2>
        <p class="icon-cphm">车牌号码：{{ dataDetail.plateNo }}</p>
        <p class="icon-cllx">车辆类型：{{ dataDetail.taxiModel || '未知' }}</p>
        <p class="icon-xm">驾驶员姓名：{{ dataDetail.driver || '未知' }}</p>
        <p class="icon-dh">电话：{{ dataDetail.phone || '未知' }}</p>
      </div>
      <div class="btns">
        <el-button class="phone" @click="handleOpenVideo">
          <i class="btn-phone" />
          <span>查看视频</span>
        </el-button>
        <!-- <div style="margin-top: 15px;" />
        <el-button class="voice">
          <i class="btn-voice" />
          <span>语音呼叫</span>
        </el-button> -->
      </div>
    </div>
    <!-- 右侧部分 -->
    <div v-loading="rightLoading" class="right">
      <div class="search-top">
        <!-- <el-date-picker v-model="gpsDate" size="mini" type="date" placeholder="轨迹查询" value-format="yyyy-MM-dd 23:59:59" @change="handleChange" /> -->
        <el-date-picker
          v-model="gpsDate"
          type="datetimerange"
          size="mini"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          @change="handleChange"
        />
        <span class="close" @click="handleClose">×</span>
      </div>
      <!-- 地图 -->
      <div id="window-map" ref="map" />
      <!-- 控制按钮 -->
      <div v-if="showCarTrack" class="ctrl-btn">
        <el-button type="primary" plain round @click="handleCarstart">开始</el-button>
        <el-button type="primary" plain round @click="handleCarpause">暂停</el-button>
        <el-button type="primary" plain round @click="handleCarstop">结束</el-button>
      </div>
    </div>
    <!-- 海康视频控件 -->
    <windowVideo v-model="hkVideoVisible" :video-name="winVideoName" :video-id="hkVideoId" />
  </div>
</template>

<script>
import taxiImg from '@/assets/images/windowInfo/taxi.jpg'
import { getTaxiInfo, getTaxiGps } from '@/api/board/map/index.js'
import windowVideo from '../../windowVideo/taxi_index.vue'

let carTrack = null

export default {
  components: {
    windowVideo
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    detailId: {
      type: String
    },
    videoVisible: Boolean
  },
  data() {
    return {
      dataDetail: {},
      fImgSrc: taxiImg,
      fSrcList: [],
      gpsDate: [],
      $wMap: null,
      leftLoading: false,
      rightLoading: false,
      terminalNo: null,
      showCarTrack: false,
      hkVideoVisible: false,
      winVideoName: '',
      hkVideoId: ''
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.fetchData().then(() => {
          // this.gpsDate = new Date()
          // this.handleChange()
        })
        this.mapInit()
      }
    }
  },
  methods: {
    mapInit() {
      this.$nextTick(() => {
        this.$wMap = new window.T.Map(this.$refs.map)
        this.$wMap.centerAndZoom(new window.T.LngLat(119.636153, 29.108149), 18)
      })
    },
    fetchData() {
      return new Promise(resolve => {
        this.leftLoading = false
        getTaxiInfo({ vehicleIndexCode: this.detailId }).then(res => {
          this.dataDetail = res.data
          this.leftLoading = false
          resolve()
        }).catch(() => {
          this.leftLoading = false
        })
      })
    },
    handleOpenVideo() {
      this.hkVideoVisible = true
      this.winVideoName = this.dataDetail.plateNo
      this.hkVideoId = this.dataDetail.subDeviceIndexCode
    },
    handleChange() {
      if (!this.gpsDate || !this.gpsDate.length) {
        return
      }
      // 缺少查询条件，重置地图
      if (!this.detailId) {
        this.$message.error('未获取到出租车终端号，请重试')
        return
      }

      this.rightLoading = true
      this.carTrackClear()
      let params = { terminalNo: this.detailId }
      if (this.gpsDate[0]) params.searchStartTime = this.gpsDate[0]
      if (this.gpsDate[1]) params.searchEndTime = this.gpsDate[1]
      getTaxiGps(params).then(res => {
        this.creatCarTrack(res.rows)
        this.rightLoading = false
      }).catch(() => {
        this.rightLoading = false
      })
    },
    creatCarTrack(path) {
      // 创建轨迹
      if (Array.isArray(path) && path.length) {
        let datas = []
        path.forEach(item => {
          datas.push(new window.T.LngLat(item.longitude, item.latitude))
        })
        carTrack = new window.T.CarTrack(this.$wMap, {
          interval: 10,
          speed: 1,
          dynamicLine: true,
          polylinestyle: {color: '#2C64A7', weight: 5, opacity: 0.9},
          Datas: datas
        })
        this.showCarTrack = true
        this.$wMap.setViewport(datas)
        this.handleCarstop() // 先调用一下结束，然行动轨迹出现
      } else {
        this.$message.warning('无轨迹数据')
        this.carTrackClear()
      }
    },
    carTrackClear() {
      if (carTrack) carTrack.clear()
      carTrack = null
      this.showCarTrack = false
    },
    handleCarstart() {
      carTrack.start()
    },
    handleCarpause() {
      carTrack.pause()
    },
    handleCarstop() {
      carTrack.stop()
    },
    handleClose() {
      this.gpsDate = []
      this.winVideoName = ''
      this.hkVideoId = ''
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style scoped lang="scss">
.largeWindowInfo {
  width: pxtorem(1500);
  height: pxtorem(790);
  position: absolute;
  top: pxtorem(100);
  left: pxtorem(20);
  background: #fff;
  z-index: 1000;
  box-shadow: 0 4px 14px rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  .left {
    width: pxtorem(200);
    background: #f2fafe;
    position: absolute;
    top: pxtorem(20);
    bottom: pxtorem(20);
    left: pxtorem(20);
    .img {
      width: pxtorem(200);
      height: pxtorem(180);
    }
    .content {
      font-size: pxtorem(14);
      padding: 0 15px;
      h2 {
        font-size: pxtorem(20);
      }
      p {
        padding-left: pxtorem(20);
        &.icon-cphm {
          background: url(@/assets/images/windowInfo/cphm.png) no-repeat left center / pxtorem(14);
        }
        &.icon-cllx {
          background: url(@/assets/images/windowInfo/cllx.png) no-repeat left center / pxtorem(14);
        }
        &.icon-xm {
          background: url(@/assets/images/windowInfo/fulb.png) no-repeat left center / pxtorem(14);
        }
        &.icon-dh {
          background: url(@/assets/images/windowInfo/dh.png) no-repeat left center / pxtorem(14);
        }
      }
    }
    .btns {
      text-align: center;
      padding: 0 15px;
      margin-top: pxtorem(35);
      button {
        width: 100%;
        height: pxtorem(40);
        line-height: pxtorem(40);
        color: #fff;
        padding: 0;
        border-radius: 10px;
        i {
          width: pxtorem(25);
          height: pxtorem(25);
          display: inline-block;
          vertical-align: middle;
          margin-right: 10px;
        }
        .btn-phone {
          background: url(@/assets/images/windowInfo/btn-phone.png) no-repeat center center / 100%;
        }
        .btn-voice {
          background: url(@/assets/images/windowInfo/btn-voice.png) no-repeat center center / 100%;
        }
      }
      .phone {
        background: #229add;
        border-color: #229add;
      }
      .voice {
        background: #ffba00;
        border-color: #ffba00;
      }
    }
  }
  .right {
    position: absolute;
    left: pxtorem(230);
    top: pxtorem(20);
    bottom: pxtorem(20);
    right: pxtorem(20);
    .search-top {
      height: pxtorem(30);
      .el-date-editor {
        width: 320px;
        ::v-deep .el-input__inner {
          background: #eee;
        }
      }
      .close {
        position: absolute;
        right: 0;
        font-size: 20px;
        color: #c3c3c3;
        cursor: pointer;
        &:hover {
          color: #999;
        }
      }
    }
    #window-map {
      position: absolute;
      top: pxtorem(40);
      left: 0;
      right: 0;
      bottom: 0;
      border: 1px solid #e9e9e9;
    }
    .ctrl-btn {
      position: absolute;
      top: pxtorem(60);
      right: pxtorem(20);
      background: #fff;
      box-shadow: 0 2px 6px 0 rgb(114 124 245 / 50%);
      z-index: 500;
      padding: 10px;
      border-radius: 5px;
    }
  }
}
</style>
