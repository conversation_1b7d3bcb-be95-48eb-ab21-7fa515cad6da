<template>
  <div>
    <CommonTitle text='疫苗接种数量分析'>
      <TabSwitch
        :tabList="list"
        :activeIndex="index"
        @tab-change="handleTabChange"
      />
    </CommonTitle>
    <div class='wrap-container' id='chartsymjzslfx'></div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import TabSwitch from '@/components/TabSwitch'
export default {
  name: 'index',
  components: {
    CommonTitle,
    TabSwitch
  },
  data() {
    return {
      index: 0,
      list: [{name:"本月",value:"1"},{name:"本季",value:"2"},{name:"本年",value:"3"}],
      chartsData: [
        {
          name:"婺城",
          current:70,
          before:58
        },
        {
          name:"婺城",
          current:70,
          before:58
        },
        {
          name:"婺城",
          current:70,
          before:58
        },
        {
          name:"婺城",
          current:70,
          before:58
        },
        {
          name:"婺城",
          current:70,
          before:58
        },
        {
          name:"婺城",
          current:70,
          before:58
        },
        {
          name:"婺城",
          current:70,
          before:58
        }
      ]
    }
  },
  computed: {},
  mounted() {
    setTimeout(() => {
      this.initChart()
    }, 300)
  },
  methods: {
    handleTabChange(i) {
      this.index = i
    },
    initChart() {
      let myChart = this.$echarts.init(document.getElementById("chartsymjzslfx"));
      let option = {
        tooltip: {
          trigger: "axis",
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        legend: {
          orient: "horizontal",
          // icon: "circle",
          padding: [30,10,10,10],
          itemGap: 45,
          textStyle: {
            color: "#D6E7F9",
            fontSize: 28,
          },
        },
        grid: {
          left: "8%",
          right: "6%",
          top: "22%",
          bottom: "1%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: this.chartsData.map(item => item.name),
            axisLine: {
              lineStyle: {
                color: "rgb(119,179,241,.4)", // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              textStyle: {
                color: "#D6E7F9",
                fontSize: 28,
              },
            },
          },
        ],
        yAxis: [
          {
            name: "单位：%",
            type: "value",
            nameTextStyle: {
              fontSize: 24,
              color: "#D6E7F9",
              padding: 20,
            },
            splitLine: {
              lineStyle: {
                color: "rgb(119,179,241,.4)",
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 28,
                color: "#D6E7F9",
              },
            },
          },
        ],
        series: [
          {
            name: "本期疫苗接种数",
            type: "bar",
            barWidth: "20%",
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#00C0FF",
                  },
                  {
                    offset: 1,
                    color: "rgba(0,192,255,0)",
                  },
                ]),
                barBorderRadius: 4,
              },
            },
            data: this.chartsData.map(item => item.current),
          }, {
            name: "上期疫苗接种数",
            type: "bar",
            barWidth: "20%",
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#22E8E8",
                  },
                  {
                    offset: 1,
                    color: "rgba(0,192,255,0)",
                  },
                ]),
                barBorderRadius: 4,
              },
            },
            data: this.chartsData.map(item => item.before),
          }
        ],
      };
      myChart.setOption(option)
      myChart.getZr().on('mousemove', param => {
        myChart.getZr().setCursorStyle('default')
      })
    }
  },
  watch: {}
}
</script>

<style scoped lang='less'>
  .wrap-container {
    width: 100%;
    height: 460px;
    margin-bottom: 40px;
  }
</style>