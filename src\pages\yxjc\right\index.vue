<template>
  <div class="right-Map">
    <fxyj></fxyj>
    <fssjqs></fssjqs>
    <sjlb @openDialog="openDialog"></sjlb>
  </div>
</template>

<script>
import fxyj from './fxyj'
import fssjqs from './fssjqs'
import sjlb from './sjlb'
export default {
  name: 'index',
  components: {
    fxyj,
    fssjqs,
    sjlb,
  },
  data() {
    return {}
  },
  computed: {},
  mounted() {},
  methods: {
    openDialog() {
      this.$emit('openDialog')
    },
  },
}
</script>

<style scoped lang="scss">
</style>