<template>
  <div class="attention-btn">
    <div @click="handleClick">
      <i class="t-icon" :class="icon" />
      <span>{{ isAdd ? '已关注' : '关注' }}</span>
    </div>
  </div>
</template>

<script>
import { listAttentione, addAttentione, delAttentione } from '@/api/case/attentione/attentione'

export default {
  props: {
    caseId: {
      type: Number,
      default: 0
    },
    caseType: {
      type: String,
      default: ''
    },
    caseContent: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      icon: 'el-icon-star-off',
      isAdd: false,
      attId: null
    }
  },
  watch: {
    caseId(nval) {
      console.log(nval)
      if (nval && this.caseType) {
        this.getStatus()
      }
    }
  },
  methods: {
    handleClick() {
      if (!this.isAdd && this.caseId && this.icon != 'el-icon-loading') {
        const caseTypeNames = {
          four: '四位一体',
          capture: '监控抓拍',
          inspection: '巡查发现',
          punish: '简易案件',
          tout: '黄牛处置',
          trafficCapture: '电子抓拍',
          transport: '违规处置'
        }
        this.icon = 'el-icon-loading'
        addAttentione({
          userId: this.$store.getters.uid,
          userName: this.$store.getters.name,
          caseId: this.caseId,
          caseType: this.caseType,
          caseTypeName: caseTypeNames[this.caseType] || '',
          caseContent: this.caseContent
        }).then(() => {
          this.icon = 'el-icon-star-on'
          this.isAdd = true
          this.$message.success('关注成功')
        }).catch(() => {
          this.icon = 'el-icon-star-off'
        })
      } else if (this.isAdd && this.attId && this.icon != 'el-icon-loading') {
        this.icon = 'el-icon-loading'
        delAttentione(this.attId).then(() => {
          this.icon = 'el-icon-star-off'
          this.isAdd = false
          this.$message.success('取消关注成功')
        }).catch(() => {
          this.icon = 'el-icon-star-on'
        })
      } else {
        this.$message.error('信息错误，请联系管理员')
      }
    },
    getStatus() {
      listAttentione({ caseId: this.caseId, caseType: this.caseType }).then(res => {
        this.isAdd = !!(Array.isArray(res.rows) && res.rows.length)
        this.icon = this.isAdd ? 'el-icon-star-on' : 'el-icon-star-off'
        if (this.isAdd) this.attId = res.rows[0].attentionId
      }).catch(() => {
        this.isAdd = false
        this.icon = 'el-icon-star-off'
      })
    }
  }
}
</script>

<style scoped>
.attention-btn {
  position: absolute;
  right: 0;
  top: 0;
  font-size: 16px;
  color: #333;
  font-weight: normal;
  cursor: pointer;
  user-select: none;
}
.t-icon {
  color: rgb(206, 206, 3);
  margin-right: 5px;
}
</style>
