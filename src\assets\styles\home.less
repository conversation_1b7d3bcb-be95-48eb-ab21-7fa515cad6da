/* home模块公共样式 */

/* 通用标题样式 */
.home-title {
  font-size: 32px;
  font-weight: bold;
  color: #fff;
  margin-bottom: 20px;
}

/* 数值显示样式 */
.home-number {
  font-family: DINCondensed;
  font-size: 48px;
  font-style: italic;
  font-weight: bold;
}

.home-number-blue {
  color: #1dfcfe;
}

.home-number-yellow {
  color: #eed252;
}

.home-number-green {
  color: #45f95e;
}

/* 单位样式 */
.home-unit {
  font-size: 20px;
  margin-left: 10px;
}

/* 容器样式 */
.home-container {
  width: 100%;
  height: fit-content;
  margin-bottom: 40px;
  box-sizing: border-box;
  position: relative;
}

/* 盒子样式 */
.home-box {
  padding: 20px 40px;
  box-sizing: border-box;
  position: relative;
  margin-bottom: 20px;
}

/* 数字背景样式 */
.home-number-bg {
  display: inline-block;
  width: 20px;
  height: 48px;
  font-weight: 700;
  line-height: 48px;
  text-align: center;
  margin: 0 4px;
  border-radius: 8px;
  background: url('../common/sz-bg.png');
  background-size: 100% 100%;
}

/* 图表容器样式 */
.home-chart-container {
  width: 100%;
  height: 300px;
  position: relative;
}

/* 无数据提示样式 */
.home-no-data {
  width: 100%;
  height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 30px;
  color: #fff;
}

/* 图标样式 */
.home-icon {
  display: inline-block;
  width: 32px;
  height: 32px;
  background-size: contain;
  background-repeat: no-repeat;
  cursor: pointer;
}

.home-icon-phone {
  background-image: url('../common/phone.png');
}

.home-icon-video {
  background-image: url('../common/video.png');
}

/* 日期选择器样式 */
.home-date-picker {
  position: absolute;
  right: 20px;
  top: 20px;
  z-index: 10;
}

.home-date-picker ::v-deep .el-input__inner {
  background-color: rgba(0, 0, 0, 0.3);
  border: 1px solid #0e5c9e;
  color: #fff;
}

/* 资源项样式 */
.home-resource-item {
  display: flex;
  width: 30%;
  align-items: center;
  margin-bottom: 20px;
}

.home-resource-icon {
  width: 89px;
  height: 100px;
  margin-right: 20px;
}

.home-resource-content {
  margin-top: 20px;
}

.home-resource-name {
  width: 200px;
  height: 35px;
  font-size: 28px;
  line-height: 35px;
  color: #d1d6df;
}

.home-resource-value {
  display: flex;
  align-items: center;
}

/* 值班人员样式 */
.home-duty-box {
  padding: 10px 40px;
}

.home-duty-leader {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.home-duty-position {
  font-size: 32px;
  color: #fff;
  margin-bottom: 10px;
}

.home-duty-info {
  display: flex;
  flex-direction: column;
  font-size: 28px;
  color: #fff;
}

.home-duty-staff {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
}

.home-duty-person {
  display: flex;
  width: 475px;
}

.home-duty-avatar {
  width: 185px;
  height: 190px;
  margin-right: 15px;
}

.home-duty-contact {
  white-space: nowrap;
}

/* 分隔线样式 */
.home-divider {
  width: 100%;
  height: 1px;
  background-color: rgba(255, 255, 255, 0.1);
  margin: 20px 0;
}

/* 行政检查样式 */
.home-inspection-top {
  display: flex;
  justify-content: space-around;
  margin-bottom: 30px;
}

.home-inspection-item {
  display: flex;
  align-items: center;
  font-size: 32px;
  color: #fff;
}

.home-number-container {
  display: flex;
  margin: 0 10px;
}

.home-inspection-charts {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
}

.home-chart-item {
  width: 30%;
  margin-bottom: 20px;
  text-align: center;
}

.home-chart-title {
  font-size: 28px;
  color: #fff;
  white-space: nowrap;
}