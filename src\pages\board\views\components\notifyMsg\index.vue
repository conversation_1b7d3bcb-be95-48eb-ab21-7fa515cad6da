<template>
  <div>
    <el-button type="success" @click="handleCallIn">接听</el-button>
    <el-button type="danger" @click="handleCallRel">挂断</el-button>
  </div>
</template>

<script>
import { Notification } from 'element-ui'
import { fn_rel } from '@/assets/idt/Idt'
import store from '@/store/index'

export default {
  methods: {
    handleCallIn() {
      store.commit('board/SET_VIDEO_VISIBLE', true)
      Notification.closeAll()
    },
    handleCallRel() {
      fn_rel()
      Notification.closeAll()
    }
  }
}
</script>
