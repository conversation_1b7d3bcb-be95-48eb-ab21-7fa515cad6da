<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>

export default {
  name: 'App'
}
</script>

<style lang="scss">
@font-face {
  font-family: 'Conv_GemunuLibre-ExtraBold';
  src: url('../../assets/font/GemunuLibre-ExtraBold.eot');
  src: local('☺'), url('../../assets/font/GemunuLibre-ExtraBold.woff') format('woff'), url('../../assets/font/GemunuLibre-ExtraBold.ttf') format('truetype'), url('../../assets/font/GemunuLibre-ExtraBold.svg') format('svg');
  font-weight: normal;
  font-style: normal;
}
body {
  width: 100%;
  height: 100%;
  min-width: 1280px;
  min-height: 720px;
  #app {
    width: inherit;
    height: inherit;
  }
}
</style>
