import {request} from '@/utils/request'

// 查询流动书吧书籍分类列表
export function listCategory(query) {
  return request({
    url: '/business/vol/book/category/list',
    method: 'get',
    params: query
  })
}

// 查询流动书吧书籍分类详细
export function getCategory(id) {
  return request({
    url: '/business/vol/book/category/' + id,
    method: 'get'
  })
}

// 新增流动书吧书籍分类
export function addCategory(data) {
  return request({
    url: '/business/vol/book/category/add',
    method: 'post',
    data: data
  })
}

// 修改流动书吧书籍分类
export function updateCategory(data) {
  return request({
    url: '/business/vol/book/category/edit',
    method: 'post',
    data: data
  })
}

// 删除流动书吧书籍分类
export function delCategory(id) {
  return request({
    url: '/business/vol/book/category/remove/' + id,
    method: 'post'
  })
}

// 导出流动书吧书籍分类
export function exportCategory(query) {
  return request({
    url: '/business/vol/book/category/export',
    method: 'get',
    params: query
  })
}
