<template>
  <div class="mgt48">
    <CommonTitle text="系统运行状态"></CommonTitle>
    <div class="yxzt_box">
      <div class="yxzt_item">
        <div class="num blue_linear">{{info.cpu.used}}%</div>
        <div class="title blue_linear">CPU使用率</div>
        <img src="@/assets/csyxgl/yxzt_img1.png" alt="" />
      </div>
      <div class="yxzt_item">
        <div class="num blue_linear">{{info.mem.used}}%</div>
        <div class="title blue_linear">内存使用率</div>
        <img src="@/assets/csyxgl/yxzt_img2.png" alt="" />
      </div>
      <div class="yxzt_item">
        <div class="num blue_linear">{{info.sysFiles[0].usage}}%</div>
        <div class="title blue_linear">硬盘使用率</div>
        <img src="@/assets/csyxgl/yxzt_img3.png" alt="" />
      </div>
    </div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import { getServer } from '@/api/csyxgl/index.js'
export default {
  name: 'index',
  components: {
    CommonTitle,
  },
  data() {
    return {
      info:{}
    }
  },
  computed: {},
  mounted() {
    this.getData()
  },
  methods: {
    getData() {
      getServer().then((res) => {
        this.info = res.data
      })
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
.yxzt_box {
  display: flex;
  justify-content: space-around;
  .yxzt_item {
    text-align: center;
    margin-top: 80px;
    .num {
      font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
      font-weight: 400;
      font-size: 49px;
    }
    .title {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 34px;
      color: #c1d3e3;
    }
    img {
      width: 157px;
      height: 185px;
    }
  }
}
</style>