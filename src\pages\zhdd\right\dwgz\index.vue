<template>
  <div>
    <CommonTitle text="多维感知">
      <i
        class="pz_icon"
        :class="showVideoFind ? 'pz_active' : ''"
        @click="openVideoFind()"
        :style="{ cursor: sgVideoList.length == 0 ? 'no-drop' : '' }"
      ></i>
    </CommonTitle>
    <VideoChoosePop :show="showVideoFind" @close="showVideoFind = false" @changeVideo="changeVideo"></VideoChoosePop>
    <VideoContainer :show="showVideoIframe" :list="sgVideoList"></VideoContainer>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import VideoChoosePop from './VideoChoosePop'
import VideoContainer from './VideoContainer'
import { getDwgzVideoByPoint } from '@/api/zhdd'
export default {
  name: 'index',
  components: {
    CommonTitle,
    VideoChoosePop,
    VideoContainer,
  },
  data() {
    return {
      year: localStorage.getItem('year'),
      city: localStorage.getItem('city'),
      showVideoFind: false,
      showVideoIframe: true,
      sgVideoList: [],
      pointCenter: [
        { name: '金华市', esX: 119.642632, esY: 29.082182 },
        { name: '婺城区', esX: 119.509748, esY: 28.977012 },
        { name: '义乌市', esX: 120.061011, esY: 29.300614 },
        { name: '金东区', esX: 119.799596, esY: 29.149391 },
        { name: '东阳市', esX: 120.375678, esY: 29.232405 },
        { name: '永康市', esX: 120.102417, esY: 28.934317 },
        { name: '兰溪市', esX: 119.526736, esY: 29.278165 },
        { name: '武义县', esX: 119.714529, esY: 28.768287 },
        { name: '磐安县', esX: 120.559672, esY: 29.037893 },
        { name: '浦江县', esX: 119.903937, esY: 29.520086 },
        { name: '金华开发区', esX: 119.63356, esY: 29.089116 },
      ],
      sgId: null,
      video_point: '119.642632, 29.082182',
    }
  },
  computed: {},
  mounted() {
    this.$bus.$on('cityChange', (city) => {
      this.city = city
      this.initVideo(city, localStorage.getItem('year'))
    })
    this.$bus.$on('yearChange', (year) => {
      this.year = year
      this.initVideo(localStorage.getItem('city'), year)
    })
    this.initVideo(localStorage.getItem('city'), localStorage.getItem('year'))
  },
  methods: {
    initVideo(city, year) {
      this.video_point =
        this.pointCenter.find((item) => item.name == city).esX +
        ', ' +
        this.pointCenter.find((item) => item.name == city).esY
      this.findVideoByPoint(this.getCityPointCenter(city))
    },
    changeVideo(val) {
      this.sgVideoList = val
    },
    getCityPointCenter(city) {
      let point = ''
      this.pointCenter.forEach((item, i) => {
        if (item.name == city) {
          point = item.esX + ', ' + item.esY
        }
      })
      return point
    },
    findVideoByPoint(point_center) {
      let this_ = this
      getDwgzVideoByPoint({
        type: 'type=zbjk',
        distance: 3, //1km=0.1  2km=0.2  3km=0.3
        point: point_center,
      }).then(function (data) {
        let datas = data.data.pointData
        if (datas.length != 0) {
          this_.showVideoIframe = true
          this_.sgVideoList = datas.map((a) => {
            let addinfo = JSON.parse(a.addinfo)
            return {
              name: a.name || '-',
              code: addinfo.chncode || '-',
              bq: addinfo.labels + '|' + addinfo.hymc || '-',
              jl: a.jl || '-',
            }
          })
          this_.videoIframe()
        } else {
          window.parent.mapUtil.removeLayer('camera-load-icon')
          this_.showVideoIframe = false
          frames['zhddzxRightVideo'].vm.etVisible(false)
        }
      })
    },
    videoIframe() {
      this.sgVideoList = this.sgVideoList.length >= 4 ? this.sgVideoList.slice(0, 4) : this.sgVideoList

      console.log(this.sgVideoList,'video');
    },
    openVideoFind() {
      this.showVideoFind = !this.showVideoFind
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
.pz_icon {
  cursor: pointer;
  position: absolute;
  top: 1350px;
  right: 18px;
  display: inline-block;
  width: 68px;
  height: 68px;
  background: url('@/assets/zhdd/pz_btn.png') no-repeat;
  border-radius: 10px;
}

.pz_active {
  background-color: #7995b8;
}
</style>