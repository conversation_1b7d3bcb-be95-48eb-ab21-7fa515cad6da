import Graphic from "@arcgis/core/Graphic.js";
import FeatureLayer from "@arcgis/core/layers/FeatureLayer.js";
import Point from "@arcgis/core/geometry/Point.js";
import { getLayerConfigById } from "./layerConfig.js";
function _getFields(objectId, attributes) {
  const fields = [{ name: objectId, alias: "OBJECTID", type: "oid" }];
  for (let key in attributes) {
    if (key.toUpperCase() !== objectId) {
      if (typeof attributes[key] === "string") {
        fields.push({
          name: key,
          alias: key,
          type: "string",
        });
      } else if (typeof attributes[key] === "number") {
        if (attributes[key] % 1 == 0) {
          fields.push({
            name: key,
            alias: key,
            type: "integer",
          });
        } else {
          fields.push({
            name: key,
            alias: key,
            type: "double",
          });
        }
      }
      // 日期格式设置为Date报错？
      else if (attributes[key] instanceof Date) {
        fields.push({
          name: key,
          alias: key,
          type: "string",
        });
      }
    }
  }

  return fields;
}

function loadHeatmapLayer({
  data,
  maxDensity,
  radius,
  colorStops,
  view = window.view,
  referenceScale = null,
  maxScale = undefined,
  minScale = undefined,
  visible2d = true,
  opacity2d = 1,
}) {
  const graphics = [];
  for (let i = 0; i < data.length; i++) {
    const item = data[i];
    const { lat, lng } = item;
    const point = new Point({
      latitude: lat,
      longitude: lng,
      spatialReference: view.spatialReference,
    });
    const graphic = new Graphic({
      geometry: point,
      attributes: {
        OBJECTID: `point_${i}`,
        ...item,
      },
    });
    graphics.push(graphic);
  }

  let renderer = {
    type: "heatmap",
    field: "count",
    colorStops,
    referenceScale,
    maxDensity,
    radius,
    minDensity: 0,
  };

  const fields = _getFields("OBJECTID", graphics[0].attributes);
  let featureCfg = {
    id: "heatmap2d",
    objectIdField: "OBJECTID",
    outFields: ["*"],
    fields,
    source: graphics, // 使用自定义的数据源
    // url: "http://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/point_ppl/FeatureServer/0",
    renderer: renderer,
    elevationInfo: {
      mode: "on-the-ground",
    },
    visible: true,
    opacity: opacity2d,
  };

  const layer = new FeatureLayer(featureCfg);
  view.map.add(layer);
  return layer;
}

export default loadHeatmapLayer;

//热力全金华市覆盖底图
export function addJinHuaCoverLayer({ view, color = [0, 255, 255, 0.58] }) {
  const cfg = getLayerConfigById("JINHUA_FEATURE");
  const layer = new FeatureLayer({
    ...cfg,
    renderer: {
      type: "simple",
      symbol: {
        type: "simple-fill", // autocasts as new SimpleFillSymbol()
        color,
        outline: {
          // autocasts as new SimpleLineSymbol()
          color: [128, 128, 128, 0],
          width: "0.5px",
        },
      },
    },
  });
  view.map.add(layer);
  return layer;
}
