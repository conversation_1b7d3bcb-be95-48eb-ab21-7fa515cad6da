<template>
  <div>
    <CommonTitle text='疫苗超期提醒'>
      <TabSwitch
        :tabList="list"
        :activeIndex="index"
        @tab-change="handleTabChange"
      />
    </CommonTitle>
    <div class='wrap-container'>
      <div class='wrap-container-left'>
        <div class='ball'>
          <div class='ballDesc'>65%</div>
          <SqLljkChart :chartData="''" />
        </div>
        <div class='ballText'>超期未接种占比率</div>
      </div>
      <div class='wrap-container-right'>
        <div class='wrap-container-right-item' v-for='(item,i) in rightList' :key='i'>
            <div class='wrap-container-right-item-left' :style="{background: 'url(' + item.img + ') no-repeat'}"></div>
            <div class='wrap-container-right-item-right'>
              <div class='wrap-container-right-item-right-name'>{{item.name}}</div>
              <div class='wrap-container-right-item-right-value'>{{item.value}}</div>
            </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import TabSwitch from '@/components/TabSwitch'
import SqLljkChart from '@/components/SqLljkChart'
export default {
  name: 'index',
  components: {
    CommonTitle,
    TabSwitch,
    SqLljkChart
  },
  data() {
    return {
      index: 0,
      list: [{name:"本月",value:"1"},{name:"本季",value:"2"},{name:"本年",value:"3"}],
      rightList: [
        {
          name:"超期未接种数量",
          value:5548,
          img: require('@/assets/dog/rimg1.png')
        },
        {
          name:"提醒后还未接种数量",
          value:4352,
          img: require('@/assets/dog/rimg2.png')
        },
        {
          name:"已接种数量",
          value:1220,
          img: require('@/assets/dog/rimg3.png')
        }
      ]
    }
  },
  computed: {},
  mounted() {

  },
  methods: {
    handleTabChange(i) {
      this.index = i
    }
  },
  watch: {}
}
</script>

<style scoped lang='less'>
  .wrap-container {
    width: 100%;
    height: 600px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    margin-top: 40px;
    .wrap-container-left {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      .ball {
        width: 351.36px;
        height: 315.36px;
        position: relative;
        bottom: 60px;
      }
      .ballDesc {
        font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
        font-weight: 400;
        font-size: 86px;
        line-height: 112px;
        letter-spacing: 3px;
        text-align: center;
        font-style: normal;
        text-transform: none;
        background: linear-gradient(to bottom, #FFFFFF 100%, #DCEFFF 100%);
        -webkit-background-clip: text;
        color: transparent;
        position: absolute;
        left: 80px;
        top: 100px;
        z-index: 66;
      }
      .ballText {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 500;
        font-size: 38px;
        color: #DCEFFF;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-top: 10px;
      }
    }
    .wrap-container-right {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;
      .wrap-container-right-item {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-bottom: 70px;
        .wrap-container-right-item-left {
          width: 110px;
          height: 110px;
          background-size: 100% 100%;
        }
        .wrap-container-right-item-right {
          margin-left: 20px;
          .wrap-container-right-item-right-name {
            font-family: Source Han Sans, Source Han Sans;
            font-weight: 400;
            font-size: 32px;
            color: #FFFFFF;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }
          .wrap-container-right-item-right-value {
            font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
            font-weight: 400;
            font-size: 48px;
            text-align: left;
            font-style: normal;
            text-transform: none;
            background: linear-gradient(to bottom, #FFFFFF 32%, #52C3F7 100%);
            -webkit-background-clip: text;
            color: transparent;
          }
        }
      }
    }
  }
</style>