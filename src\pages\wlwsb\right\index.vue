<template>
  <div class="right-Map">
    <sbxl></sbxl>
    <gjjl></gjjl>
    <ssjc></ssjc>
  </div>
</template>

<script>
import sbxl from './sbxl'
import gjjl from './gjjl'
import ssjc from './ssjc'
export default {
  name: 'index',
  components: {
    sbxl,
    gjjl,
    ssjc,
  },
  data() {
    return {}
  },
  computed: {},
  mounted() {},
  methods: {},
}
</script>

<style scoped lang="scss">
</style>