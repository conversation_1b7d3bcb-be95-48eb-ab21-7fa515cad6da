<template>
  <div class="vol-list">
    <el-form ref="queryForm" :model="queryParams" :inline="true">
      <el-form-item label="服务时段">
        <el-date-picker
          v-model="queryParams.dateRange"
          size="mini"
          style="width: 240px;"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item label="是否在岗">
        <el-select v-model="queryParams.status">
          <el-option label="全部" />
          <el-option label="在岗" value="1" />
          <el-option label="离岗" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="getList">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="volList">
      <el-table-column label="姓名" align="center" prop="userName">
        <template slot-scope="scope">{{ scope.row.userName | decryptVal }}</template>
      </el-table-column>
      <el-table-column label="性别" align="center" prop="sex">
        <template slot-scope="scope">{{ scope.row.sex | sexName }}</template>
      </el-table-column>
      <el-table-column label="电话" align="center" prop="phone">
        <template slot-scope="scope">{{ scope.row.phone | decryptVal }}</template>
      </el-table-column>
      <el-table-column label="是否在岗" align="center" prop="status">
        <template slot-scope="scope">{{ scope.row.status == '1' ? '在岗' : '离岗' }}</template>
      </el-table-column>
      <el-table-column label="服务次数" align="center" prop="serviceTimes" />
      <el-table-column label="服务时长(小时)" align="center" prop="hoursOfService" />
      <el-table-column label="发布动态数" align="center" prop="dynamics" />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listVolUser } from '@/api/board/dataView/index.js'
import { AESDecrypt } from '@/utils/aesutil'

export default {
  name: 'VolList',
  filters: {
    sexName(sex) {
      const sexObj = { 1: '男', 2: '女' }
      return sexObj[sex]
    },
    decryptVal(value) {
      if (!value) return
      let res =  AESDecrypt(value, 'ivqpsFQwQqxYUr7f')
      return res || value
    }
  },
  data() {
    return {
      loading: false,
      volList: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dateRange: [],
        status: ''
      }
    }
  },
  mounted() {
    // this.getList()
  },
  methods: {
    reLoadList() {
      this.queryParams.pageNum = 1
      this.queryParams.pageSize = 10
      this.getList()
    },
    getList() {
      const { pageNum, pageSize, dateRange, status } = this.queryParams
      const params = { pageNum, pageSize }
      if (status) params.status = status
      if (dateRange && dateRange.length) {
        params.searchStartTime = this.parseTime(dateRange[0])
        params.searchEndTime = this.parseTime(dateRange[1])
      }
      this.loading = true
      listVolUser(params).then(res => {
        this.volList = res.rows
        this.total = res.total
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        dateRange: [],
        status: ''
      }
      this.getList()
    }
  }
}
</script>
