import { getLayerConfigById } from "./layerConfig.js";
import layerCreatAsync from "./layerCreatAsync.js";
import Graphic from "@arcgis/core/Graphic.js";
import FeatureLayer from "@arcgis/core/layers/FeatureLayer.js";
import Polyline from "@arcgis/core/geometry/Polyline.js";
import gcj02towgs84 from "../../../utils/coordtransform.js";

const LAYER_ID = "TRA_NET_LN";
const BAIDU_WARING_ID = "BAIDU_WARING";
let clock;
async function addRoadLayer() {
  const layerConfig = getLayerConfigById(LAYER_ID);
  const layer = await layerCreatAsync(layerConfig);
  view.map.add(layer);

  // clock = setInterval(async () => {
  //   if (view) {
  //     const list = await _getBaiduApiData(
  //       "https://dev.arcgisonline.cn/baidu-jiaotong/openapi/v2/event/alarmlist",
  //       {
  //         nodeId: 947,
  //         roadType: "1,2,3,4,5",
  //         eventSource: "1,2,3",
  //         ak: "9EY1KFlfi8pZZIol7QnCLlpRfCZTb6Zh",
  //         retCoordType: "gcj02",
  //         returnType: 2,
  //       }
  //     );
  //     if (list.length > 0) _addRoadLayerToMap(view, list);
  //   }
  // }, 8000);
}

function removeRoadLayer() {
  // clearInterval(clock);
  let layer = view.map.findLayerById(LAYER_ID);
  if (layer) {
    view.map.remove(layer);
  }

  // let waringLayer = view.map.findLayerById(BAIDU_WARING_ID);
  // if (waringLayer) {
  //   view.map.remove(waringLayer);
  // }
}

function _addRoadLayerToMap(view, list) {
  const graphics = [];
  for (let i = 0; i < list.length; i++) {
    const item = list[i];
    const { codeId, linkStates } = item;
    const points = _getAllPoints(linkStates);
    console.log(points);

    const keys = Object.keys(points);
    for (let j = 0; j < keys.length; j++) {
      const linkState = points[keys[j]];
      const polyline = new Polyline({
        paths: linkState,
      });
      const graphic = new Graphic({
        geometry: polyline,
        attributes: {
          OBJECTID: `${codeId}_${keys[j]}`,
          ...item,
          linkStatesType: Number(keys[j]),
        },
      });
      graphics.push(graphic);
    }
  }

  let renderer = {
    type: "unique-value",
    field: "linkStatesType",
    defaultSymbol: null,
    uniqueValueInfos: [
      // {
      //   value: "1",
      //   symbol: {
      //     type: "simple-line",
      //     color: "green",
      //     width: "6px",
      //   },
      // },
      {
        value: "2",
        symbol: {
          type: "simple-line",
          color: [255, 208, 69],
          width: "6px",
        },
      },
      {
        value: "3",
        symbol: {
          type: "simple-line",
          width: "6px",
          color: [232, 14, 14, 1],
        },
      },
      {
        value: "4",
        symbol: {
          type: "simple-line",
          width: "6px",
          color: [180, 0, 0, 1],
        },
      },
    ],
  };

  const fields = _getFields("OBJECTID", graphics[0].attributes);
  const preLayer = view.map.findLayerById(BAIDU_WARING_ID);
  if (preLayer) {
    view.map.remove(preLayer);
  }
  const layer = new FeatureLayer({
    id: BAIDU_WARING_ID,
    objectIdField: "OBJECTID",
    outFields: ["*"],
    fields,
    source: graphics, // 使用自定义的数据源
    renderer: renderer,
    elevationInfo: {
      mode: "on-the-ground",
    },
  });
  view.map.add(layer);
}

async function _getBaiduApiData(requestUrl, payload) {
  try {
    const keys = Object.keys(payload);
    const querys = [];
    for (let i = 0; i < keys.length; i++) {
      querys.push(`${keys[i]}=${payload[keys[i]]}`);
    }
    const url =
      querys.length > 0 ? `${requestUrl}?${querys.join("&")}` : requestUrl;
    const response = await fetch(url);
    const responseJson = await response.json();
    return responseJson.result;
  } catch (e) {
    throw new Error("请求百度接口出错！");
  }
}

function _getAllPoints(linkStates) {
  const linkStatesClone = { ...linkStates };
  const keys = Object.keys(linkStatesClone);

  for (let i = 0; i < keys.length; i++) {
    const item = linkStatesClone[keys[i]];
    const roadLines = [];
    const roadPoints = item.split(";");
    for (let j = 0; j < roadPoints.length; j++) {
      roadLines.push(roadPoints[j]);
    }

    const paths = [];
    for (let k = 0; k < roadLines.length; k++) {
      const item = roadLines[k];
      const oneRoadPoints = item.split(",");
      const path = [];
      for (let l = 0; l < oneRoadPoints.length; l += 2) {
        const cordinate = gcj02towgs84(
          Number(oneRoadPoints[l]),
          Number(oneRoadPoints[l + 1])
        );
        path.push(cordinate);
        //path.push([oneRoadPoints[l], oneRoadPoints[l + 1]]);
      }
      // path.push(path[0]);
      paths.push(path);
    }
    linkStatesClone[keys[i]] = paths;
  }

  return linkStatesClone;
}

function _getFields(objectId, attributes) {
  const fields = [{ name: objectId, alias: "OBJECTID", type: "oid" }];
  for (let key in attributes) {
    if (key.toUpperCase() !== objectId) {
      if (typeof attributes[key] === "string") {
        fields.push({
          name: key,
          alias: key,
          type: "string",
        });
      } else if (typeof attributes[key] === "number") {
        if (attributes[key] % 1 == 0) {
          fields.push({
            name: key,
            alias: key,
            type: "integer",
          });
        } else {
          fields.push({
            name: key,
            alias: key,
            type: "double",
          });
        }
      }
      // 日期格式设置为Date报错？
      else if (attributes[key] instanceof Date) {
        fields.push({
          name: key,
          alias: key,
          type: "string",
        });
      }
    }
  }

  return fields;
}

export { addRoadLayer, removeRoadLayer };
