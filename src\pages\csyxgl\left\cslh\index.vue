<template>
  <div>
    <CommonTitle text="城市绿化"></CommonTitle>
    <div class="wrap-container" id="chartcslh"></div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
export default {
  name: 'index',
  components: {
    CommonTitle,
  },
  data() {
    return {
      chartsData: [
        {
          name: '婺城',
          current: 70,
          before: 58,
          value: 70,
        },
        {
          name: '婺城',
          current: 70,
          before: 58,
          value: 60,
        },
        {
          name: '婺城',
          current: 70,
          before: 58,
          value: 50,
        },
        {
          name: '婺城',
          current: 70,
          before: 58,
          value: 60,
        },
        {
          name: '婺城',
          current: 70,
          before: 58,
          value: 40,
        },
        {
          name: '婺城',
          current: 70,
          before: 58,
          value: 30,
        },
        {
          name: '婺城',
          current: 70,
          before: 58,
          value: 70,
        },
      ],
    }
  },
  computed: {},
  mounted() {
    this.initChart()
  },
  methods: {
    initChart() {
      let myChart = this.$echarts.init(document.getElementById('chartcslh'))
      let option = {
        tooltip: {
          trigger: 'axis',
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        legend: {
          orient: 'horizontal',
          // icon: "circle",
          padding: [30, 10, 10, 10],
          itemGap: 45,
          textStyle: {
            color: '#D6E7F9',
            fontSize: 28,
          },
          right: '15%',
          data: [
            {
              name: '绿地面积',
              icon: 'rect',
              textStyle: {
                color: '#CFD7E5', // 第一个图例文字颜色
              },
              itemStyle: {
                color: '#22E097', // 第一个图例文字颜色
              },
            },
            {
              name: '树木数量',
              icon: 'rect', // 折线图使用圆形图标
              textStyle: {
                color: '#CFD7E5', // 第二个图例文字颜色
              },
              itemStyle: {
                color: '#E0D622', // 第一个图例文字颜色
              },
            },
            {
              name: '绿化覆盖率',
              icon: 'circle', // 折线图使用圆形图标
              textStyle: {
                color: '#CFD7E5', // 第二个图例文字颜色
              },
              itemStyle: {
                color: '#3ED7FD', // 第一个图例文字颜色
              },
            },
          ],
        },
        grid: {
          left: '8%',
          right: '6%',
          top: '22%',
          bottom: '1%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            data: this.chartsData.map((item) => item.name),
            axisLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)', // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              textStyle: {
                color: '#D6E7F9',
                fontSize: 28,
              },
            },
          },
        ],
        yAxis: [
          {
            name: '单位：万',
            type: 'value',
            nameTextStyle: {
              fontSize: 24,
              color: '#D6E7F9',
              padding: 20,
            },
            splitLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)',
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 28,
                color: '#D6E7F9',
              },
            },
          },
          {
            name: '单位(%)',
            nameTextStyle: {
              color: 'rgba(255,255,255,0.6)',
              padding: [20, 10, 10, 10],
              fontSize: 24,
            },
            type: 'value',
            axisLabel: {
              textStyle: {
                fontSize: 24,
              },
            },
            axisLine: {
              lineStyle: {
                color: 'rgba(255,255,255,0.6)',
              },
            },
            splitLine: {
              lineStyle: {
                color: '#4A6C89',
              },
            },
          },
        ],
        series: [
          {
            name: '绿地面积',
            type: 'bar',
            barWidth: '10px',
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: '#22E197',
                  },
                  {
                    offset: 1,
                    color: 'rgba(34, 225, 151, 0)',
                  },
                ]),
                barBorderRadius: 4,
              },
            },
            data: this.chartsData.map((item) => item.current),
          },
          {
            name: '树木数量',
            type: 'bar',
            barWidth: '10px',
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: '#E0D722',
                  },
                  {
                    offset: 1,
                    color: 'rgba(224, 215, 34, 0)',
                  },
                ]),
                barBorderRadius: 4,
              },
            },
            data: this.chartsData.map((item) => item.before),
          },
          {
            name:'绿化覆盖率',
            data: this.chartsData.map((item) => item.value),
            type: 'line',
            yAxisIndex: 1, // 添加这行，指定使用第二个y轴
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(62, 215, 253, 0.30)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(62, 215, 253, 0)',
                  },
                ],
              },
            },
            lineStyle: {
              color: '#3ED7FD',
            },
            smooth: true,
            symbol: 'none',
          },
        ],
      }
      myChart.setOption(option)
      myChart.getZr().on('mousemove', (param) => {
        myChart.getZr().setCursorStyle('default')
      })
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
.wrap-container {
  width: 100%;
  height: 400px;
}
</style>