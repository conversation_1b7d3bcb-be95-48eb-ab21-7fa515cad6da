<template>
  <!-- <transition name="el-zoom-in-center"> -->
  <el-dialog
    :close-on-click-modal="false"
    :title="videoName"
    :visible.sync="value"
    :fullscreen="true"
    :before-close="handleClose"
  >
    <div class="videoWindowBg">
      <div style="width: 100%; height: 100%;" @click="$emit('input', false)" />
      <div
        v-loading="loading"
        class="videoWindow"
        element-loading-background="rgba(0, 0, 0, 0.3)"
      >
        <div class="content">
          <div id="playWnd" ref="playWnd" class="video-box">
            <a
              href="http://zqzf.xzzfj.jinhua.gov.cn/zqzfj/system/file/downLoad/VideoWebPlugin.exe"
              >[下载插件]</a
            >
            <span>{{ msg }}</span>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
  <!-- </transition> -->
</template>

<script>
import JSEncrypt from "@/assets/video/jsencrypt.min.js";
const jsWebControl = require("@/assets/video/jsWebControl-1.0.0.min.js");
const WebControl = jsWebControl.WebControl;

function VueDebounce(fnName, time) {
  let timeout = null;
  return function() {
    if (timeout) {
      clearTimeout(timeout);
    }
    timeout = setTimeout(() => {
      this[fnName]();
    }, time);
  };
}

export default {
  props: {
    videoName: {
      type: String,
      default: ""
    },
    videoId: {
      type: String,
      default: ""
    },
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      initCount: 0,
      pubKey: "",
      oWebControl: null,
      msg: "",
      playWndWidth: 0,
      playWndHeight: 0
    };
  },
  watch: {
    value(newVal) {
      if (newVal) {
        this.$nextTick(() => {
          this.playWndWidth = this.$refs.playWnd.clientWidth;
          this.playWndHeight = this.$refs.playWnd.clientHeight;
          this.initPlugin();
        });
      } else {
        this.stopPreview();
        this.unload();
      }
    }
  },
  mounted() {
    window.addEventListener("resize", this.webControlDebounce);
    window.addEventListener("scroll", this.webControlDebounce);
    // this.initPlugin()
  },
  destroyed() {
    window.removeEventListener("resize", this.webControlDebounce);
    window.removeEventListener("scroll", this.webControlDebounce);
    this.unload();
  },
  methods: {
    handleClose() {
      this.$emit("input", false);
    },
    startPreview() {
      this.oWebControl.JS_RequestInterface({
        funcName: "startPreview",
        argument: JSON.stringify({
          cameraIndexCode: this.videoId, // 监控点编号
          streamMode: 0, // 主子码流标识
          transMode: 1, // 传输协议
          gpuMode: 0, // 是否开启GPU硬解
          wndId: -1 // 可指定播放窗口
        })
      });
    },
    stopPreview() {
      this.oWebControl.JS_RequestInterface({
        funcName: "stopAllPreview"
      });
    },
    // 创建播放实例
    initPlugin() {
      this.oWebControl = new WebControl({
        szPluginContainer: "playWnd", // 指定容器id
        iServicePortStart: 15900, // 指定起止端口号，建议使用该值
        iServicePortEnd: 15909,
        szClassId: "23BF3B0A-2C56-4D97-9C03-0CB103AA8F11", // 用于IE10使用ActiveX的clsid
        cbConnectSuccess: () => {
          // 创建WebControl实例成功
          this.oWebControl
            .JS_StartService("window", {
              // WebControl实例创建成功后需要启动服务
              dllPath: "./VideoPluginConnect.dll" // 值"./VideoPluginConnect.dll"写死
            })
            .then(
              () => {
                // 启动插件服务成功
                this.oWebControl.JS_SetWindowControlCallback({
                  // 设置消息回调
                  cbIntegrationCallBack: this.cbIntegrationCallBack
                });

                this.oWebControl
                  .JS_CreateWnd(
                    "playWnd",
                    this.playWndWidth,
                    this.playWndHeight
                  )
                  .then(() => {
                    // JS_CreateWnd创建视频播放窗口，宽高可设定
                    this.init(); // 创建播放实例成功后初始化
                  });
              },
              () => {
                // 启动插件服务失败
                this.$message.error("启动插件服务失败");
              }
            );
        },
        cbConnectError: () => {
          // 创建WebControl实例失败
          this.oWebControl = null;
          this.msg = "插件未启动，正在尝试启动，请稍候...";
          WebControl.JS_WakeUp("VideoWebPlugin://"); // 程序未启动时执行error函数，采用wakeup来启动程序
          this.initCount++;
          if (this.initCount < 3) {
            setTimeout(() => {
              this.initPlugin();
            }, 3000);
          } else {
            this.msg = "插件启动失败，请检查插件是否安装！";
          }
        },
        cbConnectClose: bNormalClose => {
          // 异常断开：bNormalClose = false
          // JS_Disconnect正常断开：bNormalClose = true
          console.log("cbConnectClose", bNormalClose);
          this.oWebControl = null;
        }
      });
    },
    // 设置窗口控制回调
    setCallbacks() {
      this.oWebControl.JS_SetWindowControlCallback({
        cbIntegrationCallBack: this.cbIntegrationCallBack
      });
    },
    // 推送消息
    cbIntegrationCallBack(oData) {
      console.log("oData", oData);
      // showCBInfo(JSON.stringify(oData.responseMsg));
    },
    // 初始化
    init() {
      this.getPubKey(() => {
        // //////////////////////////////// 请自行修改以下变量值	////////////////////////////////////
        let appkey = "24139534"; // 综合安防管理平台提供的appkey，必填
        let secret = this.setEncrypt("9tLWVYd7JdSGAJZRlEdW"); // 综合安防管理平台提供的secret，必填
        let ip = "************"; // 综合安防管理平台IP地址，必填
        let playMode = 0; // 初始播放模式：0-预览，1-回放
        let port = 443; // 综合安防管理平台端口，若启用HTTPS协议，默认443
        let snapDir = "D:\\SnapDir"; // 抓图存储路径
        let videoDir = "D:\\VideoDir"; // 紧急录像或录像剪辑存储路径
        let layout = "1x1"; // playMode指定模式的布局
        let enableHTTPS = 1; // 是否启用HTTPS协议与综合安防管理平台交互，这里总是填1
        let encryptedFields = "secret"; // 加密字段，默认加密领域为secret
        let showToolbar = 1; // 是否显示工具栏，0-不显示，非0-显示
        let showSmart = 1; // 是否显示智能信息（如配置移动侦测后画面上的线框），0-不显示，非0-显示
        let buttonIDs =
          "0,16,256,257,258,259,260,512,513,514,515,516,517,768,769"; // 自定义工具条按钮
        // //////////////////////////////// 请自行修改以上变量值	////////////////////////////////////

        this.oWebControl
          .JS_RequestInterface({
            funcName: "init",
            argument: JSON.stringify({
              appkey: appkey, // API网关提供的appkey
              secret: secret, // API网关提供的secret
              ip: ip, // API网关IP地址
              playMode: playMode, // 播放模式（决定显示预览还是回放界面）
              port: port, // 端口
              snapDir: snapDir, // 抓图存储路径
              videoDir: videoDir, // 紧急录像或录像剪辑存储路径
              layout: layout, // 布局
              enableHTTPS: enableHTTPS, // 是否启用HTTPS协议
              encryptedFields: encryptedFields, // 加密字段
              showToolbar: showToolbar, // 是否显示工具栏
              showSmart: showSmart, // 是否显示智能信息
              buttonIDs: buttonIDs // 自定义工具条按钮
            })
          })
          .then(oData => {
            console.log("oData", oData);
            this.oWebControl.JS_Resize(this.playWndWidth, this.playWndHeight); // 初始化后resize一次，规避firefox下首次显示窗口后插件窗口未与DIV窗口重合问题
            this.startPreview();
          });
      });
    },
    getPubKey(callback) {
      this.oWebControl
        .JS_RequestInterface({
          funcName: "getRSAPubKey",
          argument: JSON.stringify({
            keyLength: 1024
          })
        })
        .then(oData => {
          console.log(oData);
          if (oData.responseMsg.data) {
            this.pubKey = oData.responseMsg.data;
            callback();
          }
        });
    },
    // RSA加密
    setEncrypt(value) {
      var encrypt = new JSEncrypt();
      encrypt.setPublicKey(this.pubKey);
      return encrypt.encrypt(value);
    },
    webControlDebounce: VueDebounce("webControlSize", 1000),
    webControlSize() {
      if (this.oWebControl != null) {
        this.playWndWidth = this.$refs.playWnd.clientWidth;
        this.playWndHeight = this.$refs.playWnd.clientHeight;
        this.oWebControl.JS_Resize(this.playWndWidth, this.playWndHeight);
        this.setWndCover();
      }
    },
    setWndCover() {
      let iWidth = document.body.clientWidth;
      let iHeight = document.body.clientHeight;
      let oDivRect = this.$refs.playWnd.getBoundingClientRect();

      let iCoverLeft = oDivRect.left < 0 ? Math.abs(oDivRect.left) : 0;
      let iCoverTop = oDivRect.top < 0 ? Math.abs(oDivRect.top) : 0;
      let iCoverRight =
        oDivRect.right - iWidth > 0 ? Math.round(oDivRect.right - iWidth) : 0;
      let iCoverBottom =
        oDivRect.bottom - iHeight > 0
          ? Math.round(oDivRect.bottom - iHeight)
          : 0;

      iCoverLeft =
        iCoverLeft > this.playWndWidth ? this.playWndWidth : iCoverLeft;
      iCoverTop =
        iCoverTop > this.playWndHeight ? this.playWndHeight : iCoverTop;
      iCoverRight =
        iCoverRight > this.playWndWidth ? this.playWndWidth : iCoverRight;
      iCoverBottom =
        iCoverBottom > this.playWndHeight ? this.playWndHeight : iCoverBottom;

      this.oWebControl.JS_RepairPartWindow(
        0,
        0,
        this.playWndWidth + 1,
        this.playWndHeight
      ); // 多1个像素点防止还原后边界缺失一个像素条
      if (iCoverLeft != 0) {
        this.oWebControl.JS_CuttingPartWindow(
          0,
          0,
          iCoverLeft,
          this.playWndHeight
        );
      }
      if (iCoverTop != 0) {
        this.oWebControl.JS_CuttingPartWindow(
          0,
          0,
          this.playWndWidth + 1,
          iCoverTop
        ); // 多剪掉一个像素条，防止出现剪掉一部分窗口后出现一个像素条
      }
      if (iCoverRight != 0) {
        this.oWebControl.JS_CuttingPartWindow(
          this.playWndWidth - iCoverRight,
          0,
          iCoverRight,
          this.playWndHeight
        );
      }
      if (iCoverBottom != 0) {
        this.oWebControl.JS_CuttingPartWindow(
          0,
          this.playWndHeight - iCoverBottom,
          this.playWndWidth,
          iCoverBottom
        );
      }
    },
    unload() {
      if (this.oWebControl != null) {
        this.oWebControl.JS_HideWnd(); // 先让窗口隐藏，规避可能的插件窗口滞后于浏览器消失问题
        this.oWebControl.JS_Disconnect().then(
          () => {
            // 断开与插件服务连接成功
          },
          () => {
            // 断开与插件服务连接失败
            this.$message.error("断开与插件服务连接失败");
          }
        );
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.videoWindowBg {
  position: relative;
  height: calc(100vh - 136px);
}
.videoWindow {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  .el-icon-circle-close {
    color: #fff;
  }
  .content {
    position: relative;
    // padding: 15px;
    height: 100%;
    box-sizing: border-box;
    z-index: 10;
    overflow: hidden;
    .title {
      height: 45px;
      padding-top: 15px;
      font-size: 18px;
      // background: url(../../assets/images/box-title-bg2.png) no-repeat left bottom / contain;
    }
    .video-box {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
    }
  }
  .bg {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    .bg-top {
      height: 30px;
      // background: url(../../assets/images/windowInfo-top1.png) no-repeat 0 0 / cover;
    }
    .bg-center {
      width: 100%;
      position: absolute;
      top: 30px;
      bottom: 30px;
      // background: url(../../assets/images/windowInfo-center1.png) repeat-y 0 0;
      background-size: 100%;
    }
    .bg-bottom {
      position: absolute;
      bottom: 0;
      width: 100%;
      height: 30px;
      // background: url(../../assets/images/windowInfo-bottom1.png) no-repeat left bottom / cover;
    }
  }
}
</style>
