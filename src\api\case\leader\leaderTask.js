import {request} from '@/utils/request'

// 查询领导交办列表
export function listTask(query) {
  return request({
    url: '/oa/leader/task/list',
    method: 'get',
    params: query
  })
}

// 查询领导交办详细
export function getTask(id) {
  return request({
    url: '/oa/leader/task/' + id,
    method: 'get'
  })
}

// 新增领导交办
export function addTask(data) {
  return request({
    url: '/oa/leader/task/add',
    method: 'post',
    data: data
  })
}

// 修改领导交办
export function updateTask(data) {
  return request({
    url: '/oa/leader/task/edit',
    method: 'post',
    data: data
  })
}

// 删除领导交办
export function delTask(id) {
  return request({
    url: '/oa/leader/task/remove/' + id,
    method: 'post'
  })
}

// 导出领导交办
export function exportTask(query) {
  return request({
    url: '/oa/leader/task/export',
    method: 'get',
    params: query
  })
}
