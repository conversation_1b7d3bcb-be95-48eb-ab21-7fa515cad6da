<template>
  <div class="container">
    <el-form ref="form" v-loading="loading" :model="form" label-width="110px">
      <el-row>
        <el-col :span="24">
          <h3 class="title">
            <span>基本信息</span>
          </h3>
        </el-col>
        <el-col :span="12">
          <el-form-item label="当事人" prop="contact">
            <span>{{ form.contact }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系号码" prop="phone">
            <span>{{ form.phone }}</span>
          </el-form-item>
        </el-col>
        <div v-if="form.businessNo">
          <el-col :span="12">
            <el-form-item label="店铺名称" prop="businessName">
              <span>{{ form.businessName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="营业执照号码" prop="businessNo">
              <span>{{ form.businessNo }}</span>
            </el-form-item>
          </el-col>
        </div>
        <el-col :span="24">
          <el-form-item label="性别" prop="sex">
            <span>{{ form.sex | sexName }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="发生时间" prop="happenDate">
            <span>{{ form.happenDate }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="案件地址" prop="address">
            <span>{{ form.address }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="现场图片" prop="files">
            <el-image v-for="(src,idx) in fileList" :key="idx" style="width: 200px; height: 200px;" fit="cover" :src="src" @load="() => handleLoadImg(idx)" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <h3 class="title">警情信息</h3>
        </el-col>
        <el-col :span="12">
          <el-form-item label="巡查人名称" prop="userId">
            <span>{{ form.userName }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="辅助人员名称" prop="userIds">
            <span>{{ form.userNames }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="是否有问题" prop="isProblem">
            <span>{{ form.isProblem | problemName }}</span>
          </el-form-item>
        </el-col>
        <div v-if="form.isProblem == 1">
          <el-col :span="12">
            <el-form-item label="警情类型" prop="policeCategory">
              <span>{{ policeCategoryDataName(form.policeCategory) }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="警情类别" prop="policeType">
              <span>{{ policeTypeDataName(form.policeType) }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="案由名称" prop="summaryName">
              <span>{{ form.summaryName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="处理类型" prop="handleType">
              <span>{{ form.handleType }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="案情内容" prop="policeContent">
              <span>{{ form.policeContent }}</span>
            </el-form-item>
          </el-col>
        </div>
        <el-col :span="24">
          <el-form-item label="是否联合执法" prop="isUnion">
            <span>{{ form.isUnion | unionNames }}</span>
          </el-form-item>
        </el-col>
        <el-col v-if="form.isUnion == 1" :span="24">
          <el-form-item label="联合执法" prop="unionName">
            <span>{{ form.unionName }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <h3 class="title">警情信息</h3>
        </el-col>
        <el-col :span="24">
          <el-form-item label="处理结果" prop="inspectionContent">
            <span>{{ form.inspectionContent }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24" style="border-bottom: 1px solid #ccc;">
          <el-form-item label="处理后图片" prop="files">
            <el-image v-for="(src,idx) in sFileList" :key="idx" style="width: 200px; height: 200px;" fit="cover" :src="src" @load="() => handleLoadImg(idx)" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import {inspectionOne} from '@/api/case/synthetical/patrol'
import { getFiles } from '@/api/supervise/swit'
import {listData} from '@/api/system/dict/type'

export default {
  filters: {
    sexName(type) {
      const names = { 1: '男', 2: '女' }
      return names[type]
    },
    problemName(type) {
      const names = { 1: '有', 2: '无' }
      return names[type]
    },
    unionNames(type) {
      const names = { 1: '是', 0: '否' }
      return names[type]
    }
  },
  data() {
    return {
      loading: false,
      form: {},
      fileList: [],
      sFileList: [],
      fileIdx: 0,
      policeTypeData: [],
      policeCategoryData: []
    }
  },
  mounted() {
    const params = this.$route.query
    if (params.id) {
      this.loading = true
      Promise.all([
        listData({dictType: 'case_alert_type'}),
        listData({dictType: 'case_call_type'}),
        inspectionOne(params.id),
        getFiles({businessId: params.id, tableName: 'case_inspection'})
      ])
        .then(resAry => {
          const [policeTypeData, policeCategoryData, formData, fileData] = resAry
          this.policeTypeData = policeTypeData.rows
          this.policeCategoryData = policeCategoryData.rows
          this.form = formData.data
          fileData.rows.map(file => {
            const url =  `/zqzfj${file.filePath}`
            if (file.status == 2) {
              this.fileList.push(url)
            } else if (file.status == 9) {
              this.sFileList.push(url)
            }
          })
          this.loading = false
        }).catch(() => {
          this.loading = false
        })
    }
  },
  methods: {
    policeTypeDataName(type) {
      const res = this.policeTypeData.find(item => type == item.dictValue)
      if (res) {
        return res.dictLabel
      } else {
        return ''
      }
    },
    policeCategoryDataName(type) {
      const res = this.policeCategoryData.find(item => type == item.dictValue)
      if (res) {
        return res.dictLabel
      } else {
        return ''
      }
    },
    handleLoadImg() {
      this.fileIdx++
      if (this.fileIdx == this.fileList.length + this.sFileList.length) {
        this.pagePrint()
      }
    },
    pagePrint() {
      this.$nextTick(() => {
        window.print()
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .container {
    .title {
      padding-left: 10px;
    }
    ::v-deep {
      .el-form-item {
        margin-bottom: 0;
      }
      .el-row {
        border-right: 1px solid #ccc;
      }
      [class*=el-col-] {
        border-top: 1px solid #ccc;
        border-left: 1px solid #ccc;
      }
      .el-form-item--medium .el-form-item__label {
        padding-left: 10px;
      }
      .el-form-item--medium .el-form-item__content {
        padding-left: 10px;
        border-left: 1px solid #ccc;
      }
    }
  }
</style>
