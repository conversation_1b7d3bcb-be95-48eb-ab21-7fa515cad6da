import GraphicsLayer from "@arcgis/core/layers/GraphicsLayer.js";
import PlotDrawTool from "./PlotDrawTool.js";

const _getSymbol = (bar, text = "") => {
  switch (bar) {
    case "point":
      if (text) {
        return {
          type: "text", // autocasts as new TextSymbol()
          color: "white",
          haloSize: "1px",
          text: text,
          xoffset: 3,
          yoffset: 3,
          kerning: true,
          font: {
            size: 12,
          },
        };
      } else {
        return {
          type: "simple-marker", // autocasts as new SimpleMarkerSymbol()
          style: "circle",
          color: "blue",
          size: "10px", // pixels
          outline: {
            color: [0, 0, 255],
            width: 3, // points
          },
        };
      }

    case "polyline":
      return {
        type: "simple-line", // autocasts as new SimpleLineSymbol()
        color: "blue",
        width: "3px",
        style: "solid",
      };
    case "polygon":
      return {
        type: "simple-fill", // autocasts as new SimpleFillSymbol()
        color: [255, 0, 0, 0.3],
        style: "solid",
        outline: {
          color: [255, 0, 0, 1],
          width: 1,
        },
      };

    default:
      break;
  }
};

let sketchViewModel = null;
// 绘制箭头
function drawArrow(type) {
  return new Promise(function (resolve, reject) {
    const simpleFillSymbol = _getSymbol("polygon");
    if (!sketchViewModel) {
      sketchViewModel = new PlotDrawTool({
        mapView: view,
        symbol: simpleFillSymbol,
      });
    }
    sketchViewModel.symbol = simpleFillSymbol;
    sketchViewModel.active(type);

    sketchViewModel.onDrawEnd((event) => {
      let layer = view.map.findLayerById("drawLayer");
      if (!layer) {
        layer = new GraphicsLayer({
          id: "drawLayer",
          elevationInfo: { mode: "relative-to-scene" },
        });
        view.map.add(layer);
      }
      layer.add(event.graphic);
      resolve({ graphic: event.graphic, sketchViewModel });
    });
  });
}

export default drawArrow;
