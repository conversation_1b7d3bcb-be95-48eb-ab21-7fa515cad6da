import {request} from '@/utils/request'
/** 监控抓拍 */
// 列表
export function lsrwList(params) {
  return request({
    url: '/business/temporary/list',
    method: 'get',
    params
  })
}
// 获取单个
export function getLsrwList(params) {
  return request({
    url: '/business/temporary/' + params,
    method: 'get'
  })
}
// 新增
export function lsrwAdd(data) {
  return request({
    url: '/business/temporary/add',
    method: 'post',
    data
  })
}
// 修改
export function lsrwEdit(data) {
  return request({
    url: '/business/temporary/edit',
    method: 'post',
    data
  })
}
// 删除
export function lsrwRemove(data) {
  return request({
    url: '/business/temporary/remove/' + data,
    method: 'post'

  })
}
