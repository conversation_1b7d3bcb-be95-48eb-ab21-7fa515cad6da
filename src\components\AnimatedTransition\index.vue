<template functional>
  <transition
    :enter-active-class="`animate__animated animate__${props.enter}`"
    :leave-active-class="`animate__animated animate__${props.leave}`"
    mode="out-in"
  >
    <slot />
  </transition>
</template>

<script>
export default {
  name: 'AnimatedTransition',
  functional: true,
  props: {
    enter: {
      type: String,
      required: true
    },
    leave: {
      type: String,
      required: true
    }
  }
}
</script> 