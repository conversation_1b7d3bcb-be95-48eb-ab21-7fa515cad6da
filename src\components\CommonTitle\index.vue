<template>
  <div class="titleLine hearder_h1">
    <div class='titleText' :style='styles'>{{text}}</div>
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'index',
  props: {
    text: {
      type: String,
      default: ''
    },
    styles: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  computed: {},
  mounted() {

  },
  methods: {

  },
  watch: {}
}
</script>

<style scoped lang='less'>
  .titleLine {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    cursor: pointer;
  }
</style>