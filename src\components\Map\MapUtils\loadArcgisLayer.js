import layerCreatAsync from "./layerCreatAsync.js";
import TileInfo from "@arcgis/core/layers/support/TileInfo.js";
import Extent from "@arcgis/core/geometry/Extent.js";
import * as reactiveUtils from "@arcgis/core/core/reactiveUtils.js";

import SpatialReference from "@arcgis/core/geometry/SpatialReference.js";
const defaultTileInfo = new TileInfo({
  dpi: 90.71428571427429,
  origin: {
    x: -180,
    y: 90,
  },
  spatialReference: {
    wkid: 4490,
  },
  lods: [
    {
      level: 0,
      levelValue: "1",
      resolution: 0.703125,
      scale: 295497593.05875003,
    },
    {
      level: 1,
      levelValue: "2",
      resolution: 0.3515625,
      scale: 147748796.52937502,
    },
    {
      level: 2,
      levelValue: "3",
      resolution: 0.17578125,
      scale: 73874398.264687508,
    },
    {
      level: 3,
      levelValue: "4",
      resolution: 0.087890625,
      scale: 36937199.132343754,
    },
    {
      level: 4,
      levelValue: "5",
      resolution: 0.0439453125,
      scale: 18468599.566171877,
    },
    {
      level: 5,
      levelValue: "6",
      resolution: 0.02197265625,
      scale: 9234299.7830859385,
    },
    {
      level: 6,
      levelValue: "7",
      resolution: 0.010986328125,
      scale: 4617149.8915429693,
    },
    {
      level: 7,
      levelValue: "8",
      resolution: 0.0054931640625,
      scale: 2308574.9457714846,
    },
    {
      level: 8,
      levelValue: "9",
      resolution: 0.00274658203125,
      scale: 1154287.4728857423,
    },
    {
      level: 9,
      levelValue: "10",
      resolution: 0.001373291015625,
      scale: 577143.73644287116,
    },
    {
      level: 10,
      levelValue: "11",
      resolution: 0.0006866455078125,
      scale: 288571.86822143558,
    },
    {
      level: 11,
      levelValue: "12",
      resolution: 0.00034332275390625,
      scale: 144285.93411071779,
    },
    {
      level: 12,
      levelValue: "13",
      resolution: 0.000171661376953125,
      scale: 72142.967055358895,
    },
    {
      level: 13,
      levelValue: "14",
      resolution: 8.58306884765625e-5,
      scale: 36071.483527679447,
    },
    {
      level: 14,
      levelValue: "15",
      resolution: 4.291534423828125e-5,
      scale: 18035.741763839724,
    },
    {
      level: 15,
      levelValue: "16",
      resolution: 2.1457672119140625e-5,
      scale: 9017.8708819198619,
    },
    {
      level: 16,
      levelValue: "17",
      resolution: 1.0728836059570313e-5,
      scale: 4508.9354409599309,
    },
    {
      level: 17,
      levelValue: "18",
      resolution: 5.3644180297851563e-6,
      scale: 2254.4677204799655,
    },
    {
      level: 18,
      levelValue: "19",
      resolution: 2.68220901489257815e-6,
      scale: 1127.23386023998275,
    },
    {
      level: 19,
      levelValue: "20",
      resolution: 1.341104507446289075e-6,
      scale: 563.616930119991375,
    },
  ],
});

let dragHandlerx, dragHandlery;
export async function loadArcgisLayer(view, layerProps) {
  if (layerProps.type === "web-tile") {
    layerProps.tileInfo = defaultTileInfo;
  }
  let layer;
  if (layerProps.id) {
    //用id来判断图层是否存在F
    const _layer = view.map.findLayerById(layerProps.id);
    if (_layer) {
      layer = _layer;
    } else {
      layer = await layerCreatAsync(layerProps);
      view.map.add(layer);
    }
  } else {
    layer = await layerCreatAsync(layerProps);
    view.map.add(layer);
  }

  layer.when(async () => {
    if (layerProps.isGoto) {
      if (layer && layer.fullExtent) await view.goTo(layer.fullExtent);
    }
    if (layerProps.type === "building-scene") {
      //加bim特殊处理
      layer.allSublayers.forEach((layer) => {
        layer.visible = true;
        layer.popupEnabled = false;
      });
    }
  });

  layer.setOpacity = function (opacity) {
    layer.opacity = opacity;
  };
  return layer;
}

//范围限制，限制最大高度，和最大extent
export function addLimitToView(view, extentLimitCfg = {}) {
  const extent = extentLimitCfg.fullExtent;
  const zoomMax = extentLimitCfg.zoomMax;
  if (zoomMax)
    view.constraints.altitude.max = extentLimitCfg.zoomMax || 25512548;

  if (extent) {
    const { xmin, xmax, ymax, ymin } = extent;
    if (dragHandlerx) {
      dragHandlerx.remove();
      dragHandlerx = null;
    }
    if (dragHandlery) {
      dragHandlery.remove();
      dragHandlery = null;
    }
    dragHandlerx = reactiveUtils.watch(
      () => view.camera.position.x,
      (x) => {
        if (x > xmin && x < xmax) {
          console.log(111);
        }
        if (extent.xmin > x) {
          if (extent.ymin > view.camera.position.y) {
            const camera = view.camera.clone();
            camera.position.x = xmin;
            camera.position.y = ymin;
            view.goTo(camera, { animate: false }).then(() => {});
          } else if (extent.ymax < view.camera.position.y) {
            const camera = view.camera.clone();
            camera.position.x = xmin;
            camera.position.y = ymax;
            view.goTo(camera, { animate: false }).then(() => {});
          } else {
            const camera = view.camera.clone();
            camera.position.x = xmin;
            view.goTo(camera, { animate: false }).then(() => {});
          }
        }
        if (extent.xmax < x) {
          if (extent.ymax < view.camera.position.y) {
            const camera = view.camera.clone();
            camera.position.x = xmax;
            camera.position.y = ymax;
            // view.camera = camera;
            view.goTo(camera, { animate: false }).then(() => {});
          } else if (extent.ymin > view.camera.position.y) {
            const camera = view.camera.clone();
            camera.position.x = xmax;
            camera.position.y = ymin;
            // view.camera = camera;
            view.goTo(camera, { animate: false }).then(() => {});
          } else {
            const camera = view.camera.clone();
            camera.position.x = xmax;

            // view.camera = camera;
            view.goTo(camera, { animate: false }).then(() => {});
          }
        }
      }
    );

    dragHandlery = reactiveUtils.watch(
      () => view.camera.position.y,
      (y) => {
        if (extent.ymin > y) {
          if (extent.xmin > view.camera.position.x) {
            const camera = view.camera.clone();
            camera.position.y = ymin;
            camera.position.y = ymin;
            view.goTo(camera, { animate: false }).then(() => {});
          } else if (extent.xmax < view.camera.position.x) {
            const camera = view.camera.clone();
            camera.position.y = ymin;
            camera.position.x = xmax;
            view.goTo(camera, { animate: false }).then(() => {});
          } else {
            const camera = view.camera.clone();
            camera.position.y = ymin;
            view.goTo(camera, { animate: false }).then(() => {});
          }
        }
        if (extent.ymax < y) {
          if (extent.xmax < view.camera.position.x) {
            const camera = view.camera.clone();
            camera.position.x = xmax;
            camera.position.y = ymax;
            // view.camera = camera;
            view.goTo(camera, { animate: false }).then(() => {});
          } else if (extent.xmin > view.camera.position.x) {
            const camera = view.camera.clone();
            camera.position.y = ymax;
            camera.position.x = xmin;
            // view.camera = camera;
            view.goTo(camera, { animate: false }).then(() => {});
          } else {
            const camera = view.camera.clone();
            camera.position.y = ymax;

            // view.camera = camera;
            view.goTo(camera, { animate: false }).then(() => {});
          }
        }
      }
    );
  }
}

export function removeViewLimit(view, type = "all") {
  if (!type === "all") {
    if (type === "extent") {
      if (dragHandlerx) {
        dragHandlerx.remove();
        dragHandlerx = null;
      }
      if (dragHandlery) {
        dragHandlery.remove();
        dragHandlery = null;
      }
    } else {
      view.constraints.altitude.max = 25512548;
    }
  } else {
    if (dragHandlerx) {
      dragHandlerx.remove();
      dragHandlerx = null;
    }
    if (dragHandlery) {
      dragHandlery.remove();
      dragHandlery = null;
    }
    view.constraints.altitude.max = 25512548;
  }
}
