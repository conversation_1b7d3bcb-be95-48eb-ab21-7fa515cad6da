<template>
  <div class='right'>
    <gjxx></gjxx>
    <rwgz ref='rwgz'></rwgz>
    <dwgz></dwgz>
  </div>
</template>

<script>
import gjxx from './gjxx'
import rwgz from './rwgz'
import dwgz from './dwgz'
export default {
  name: 'index',
  components: {
    gjxx,
    rwgz,
    dwgz
  },
  data() {
    return {}
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped>

</style>