<template>
  <div>
    <div class='tabLine' v-if='type == 1'>
      <div class='tab'
           v-for='(item, i) in tabList'
           :key="i"
           :class="{'activeTab': activeIndex == i}"
           @click='handleTabChange(i)'>
        {{item.name}}
      </div>
    </div>
    <div class='tabLine' v-else>
      <div class='tab2'
           v-for='(item, i) in tabList'
           :key="i"
           :class="{'tab2Active': activeIndex == i}"
           @click='handleTabChange(i)'>
        {{item.name}}
      </div>
    </div>
  </div>

</template>

<script>
export default {
  name: 'TabSwitch',
  props: {
    tabList: {
      type: Array,
      required: true
    },
    activeIndex: {
      type: Number,
      default: 0
    },
    type: {
      type: Number,
      default: 1
    }
  },
  methods: {
    handleTabChange(index) {
      this.$emit('tab-change', index)
    }
  }
}
</script>

<style scoped lang='less'>
.tabLine {
  width: fit-content;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  margin-bottom: 40px;
  .tab {
    font-family: Source <PERSON> Sans, Source Han Sans;
    font-weight: 400;
    font-size: 32px;
    color: #9EA7B3;
    font-style: normal;
    text-transform: none;
    margin: 0 20px 0 20px;
    cursor: pointer;
  }
  .tab2 {
    width: 236px;
    height: 60px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 32px;
    color: #7C99B9;
    line-height: 60px;
    text-align: center;
    font-style: normal;
    text-transform: none;
    cursor: pointer;
  }
  .activeTab {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 32px;
    color: #FFFFFF;
    text-shadow: 0px 0px 16px #1677FF;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  .tab2Active {
    width: 236px;
    height: 60px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 32px;
    color: #FFFFFF;
    line-height: 60px;
    text-align: center;
    font-style: normal;
    text-transform: none;
    background: url("@/assets/dog/tabBg.png") no-repeat;
    background-size: 100% 100%;
  }
}
</style>