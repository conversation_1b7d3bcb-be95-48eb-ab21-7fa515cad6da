import {request} from '@/utils/request'

// 查询出租车列表
export function listTaxi(query) {
  return request({
    url: '/business/car/taxi/list',
    method: 'get',
    params: query
  })
}

// 查询出租车详细
export function getTaxi(taxiId) {
  return request({
    url: '/business/car/taxi/' + taxiId,
    method: 'get'
  })
}

// 新增出租车
export function addTaxi(data) {
  return request({
    url: '/business/car/taxi/add',
    method: 'post',
    data: data
  })
}

// 修改出租车
export function updateTaxi(data) {
  return request({
    url: '/business/car/taxi/edit',
    method: 'post',
    data: data
  })
}

// 删除出租车
export function delTaxi(taxiId) {
  return request({
    url: '/business/car/taxi/remove/' + taxiId,
    method: 'post'
  })
}

// 导出出租车
export function exportTaxi(query) {
  return request({
    url: '/business/car/taxi/export',
    method: 'get',
    params: query
  })
}
