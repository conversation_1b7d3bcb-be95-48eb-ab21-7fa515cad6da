<template>
  <div class="m-uplaod" :class="{ hide: isHide }">
    <el-upload
      ref="upload"
      multiple
      :limit="limit"
      list-type="picture-card"
      :accept="accept"
      action="/zqzfj/system/file/upload"
      :auto-upload="false"
      :headers="headers"
      :file-list="formFiles"
      :on-success="handleSuccess"
      :on-error="handleError"
      :data="exData"
      :on-change="handleChange"
      name="files"
      :disabled="disabled"
    >
      <i class="el-icon-plus" />

      <!-- 已上传文件 -->
      <div slot="file" slot-scope="{file}" style="height:100%">
        <!-- 显示图片 -->
        <el-image v-if="show(file)" style="width: 100%;height:100%" :src="file.url" fit="cover" />

        <!-- 显示文件 -->
        <div v-else class="file_content">
          <svg-icon class="file_icon" icon-class="file" />
          <div class="file_title">{{ file.name }}</div>
        </div>
        <!-- 上层小图标 -->
        <span class="el-upload-list__item-actions">
          <!-- 预览 -->
          <span
            v-if="show(file)"
            class="el-upload-list__item-preview"
            @click="handlePictureCardPreview(file)"
          >
            <i class="el-icon-zoom-in" />
          </span>
          <!-- 下载 -->
          <span
            v-if="!disabled"
            class="el-upload-list__item-delete"
            @click="handleDownload(file)"
          >
            <i class="el-icon-download" />
          </span>
          <!-- 删除 -->
          <span
            v-if="!disabled"
            class="el-upload-list__item-delete"
            @click="handleRemove(file)"
          >
            <i class="el-icon-delete" />
          </span>
        </span>
      </div>
      <!-- /已上传文件 -->
    </el-upload>
    <!-- 图片预览 -->
    <!-- <el-dialog :visible.sync="dialogVisible" append-to-body> -->
    <!-- <el-image v-show="false" ref="imgList" style="width: 100%;" :preview-src-list="srcList" /> -->
    <!-- <img width="100%" :src="dialogImageUrl"> -->
    <!-- </el-dialog> -->
    <el-image v-show="false" ref="imgList" :src="dialogImageUrl" fit="cover" :preview-src-list="srcList" />
  </div>
</template>

<script>
import { removeFiles } from '@/api/supervise/swit'
import { getToken } from '@/utils/auth'

export default {
  props: {
    exData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    disabled: {
      type: Boolean,
      default: false
    },
    limit: {
      type: Number
    },
    accept: {
      type: String,
      default: '.jpg,.jpeg,.png,.JPG,.JPEG,.PNG'
    },
    params: {
      type: Object,
      default: () => {
        return {}
      }
    },
    fileList: {
      type: Array,
      default: () => {
        return []
      }
    },
    notUploadMsg: {
      type: String,
      default: '未上传图片'
    },
    required: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      headers: {Authorization: 'Bearer ' + getToken()},
      formFiles: this.fileList,
      dialogVisible: false,
      dialogImageUrl: '',
      isHide: this.fileList.length == this.limit,
      srcList: []
    }
  },
  computed: {

    // srcList() {

    //   if (this.$refs.upload) return this.$refs.upload.uploadFiles.filter(item => {
    //     if (this.show(item)) return item
    //   }).map(value => value.url)
    //   return []

    //   // if (this.$refs.upload) {
    //   //   let a = this.$refs.upload.uploadFiles.filter(item => {
    //   //     if (this.show(item)) return item
    //   //   }).map(value => value.url)
    //   //   return a
    //   // } else {
    //   //   return []
    //   // }
    // }
  },
  watch: {
    fileList: {
      handler: function(nVal) {
        this.formFiles = [...nVal]
        this.isHide = nVal.length == this.limit
      },
      deep: true
    }
  },
  methods: {
    // 判断文件后缀名是否是图片
    show(file) {
      // console.log(file)
      const suffix = file.name.substring(file.name.lastIndexOf('.'))
      const picSuffix = ['.bmp', '.jpg', '.jpeg', '.png', '.JPG', '.JPEG', '.PNG', '.gif']
      if (picSuffix.includes(suffix)) return true
      return false
    },
    // 下载
    handleDownload(file) {
      // console.log(file)
      // window.location.href = file.url
      this.downLoad(file, file.name)
    },
    downLoad(content, fileName) {
      let url = content.url
      if (content.raw) url = window.URL.createObjectURL(content.raw)
      const a = document.createElement('a')
      a.href = url
      a.download = fileName
      a.click()
      window.URL.revokeObjectURL(url)
    },
    handleChange(file, fileList) {
      this.isHide = fileList.length == this.limit
    },
    // handleRemove(file, fileList) {
    //   setTimeout(() => {
    //     this.isHide = fileList.length == this.limit
    //   }, 1000)
    // },
    handlePictureCardPreview(file) {
      this.getSrcList()
      this.dialogImageUrl = file.url
      this.$nextTick(() => {
        this.$refs.imgList.clickHandler()
      })
    },
    // 生成预览图片数组
    getSrcList() {
      if (this.$refs.upload) {
        this.srcList = this.$refs.upload.uploadFiles.filter(item => {
          if (this.show(item)) return item
        }).map(value => value.url)
      }
    },
    handleRemove(file) {
      this.$confirm('是否确认删除？ 删除后将无法恢复', '提示', { type: 'warning' }).then(() => {
        if (file.fileId) {
          removeFiles(file.fileId).then(() => {
            this.$refs.upload.uploadFiles = this.$refs.upload.uploadFiles.filter(item => item.uid != file.uid)
          }).catch(() => {
            this.$message.error('图片删除失败')
          })
        } else {
          this.$refs.upload.uploadFiles = this.$refs.upload.uploadFiles.filter(item => item.uid != file.uid)
        }
      }).catch(() => {})
    },
    handleSuccess() {
      const hasFailFile = this.$refs.upload.uploadFiles.some(item => item.response && item.response.code != 200)
      if (hasFailFile) {
        this.$message.error('文件上传失败，请重新上传')
        this.$emit('error')
      } else {
        this.$emit('uploadSucces')
      }
    },
    handleError() {
      this.$message.error('文件上传失败，请重新上传')
      this.$emit('error')
    },
    isNotUploadFile() {
      if (!this.$refs.upload.uploadFiles.length && !this.formFiles.length && this.required) {
        // 判断是否有文件存在，判断必传的条件
        this.$message.error(this.notUploadMsg)
        return true
      } else {
        return false
      }
    },
    clear() {
      console.log('12121212')
      this.$refs.upload.clearFiles()
      this.isHide = this.fileList.length == this.limit
    },
    submitFile() {
      const uploadFile = this.$refs.upload.uploadFiles.some(item => (typeof item.percentage == 'number' && item.percentage !== 100))
      if (uploadFile) {
        // 存在需要上传的文件，调用上传
        this.$refs.upload.submit()
      } else {
        this.$emit('uploadSucces')
      }
    }
  }
}
</script>

<style scoped lang="scss">
.m-uplaod {
  &.hide {
    ::v-deep .el-upload--picture-card {
      display: none;
    }
  }
}
.file_content{
  display: flex;
  flex-direction:column;
  align-items: center;
  width: 98px;
  height: 98px;
  padding:6px 4px 4px;
  .file_icon{
    width: 50px;
    height: 50px;
  }
  .file_title{
    width: 90px;
    height: 38px;
    line-height: 40px;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

  }
}
</style>
