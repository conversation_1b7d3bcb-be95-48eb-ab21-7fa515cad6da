<template>
  <div class='wrap-bg'>
    <CommonTitle text='播放记录' :styles="{marginLeft: '140px'}"></CommonTitle>
    <div class='wrap-container'>
      <div class='banner-container'>
        <div class='banner-container-left' @click='scrollClick("left")'></div>
        <div class='banner-container-center' id='scrollList'>
          <div class='banner-container-center-item' v-for='(item,i) in videoList' :key='i'>
            <img src='' alt=''>
            <div class='text'>{{item.name}}</div>
            <div class='date'>{{item.date}}</div>
          </div>
        </div>
        <div class='banner-container-right' @click='scrollClick("right")'></div>
      </div>
    </div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
export default {
  name: 'index',
  components: {
    CommonTitle
  },
  data() {
    return {
      videoList: [
        {
          name:"监控名称1",
          date:"2024-07-15 19:00:10",
          img:""
        },
        {
          name:"监控名称2",
          date:"2024-07-15 19:00:10",
          img:""
        },
        {
          name:"监控名称3",
          date:"2024-07-15 19:00:10",
          img:""
        },
        {
          name:"监控名称4",
          date:"2024-07-15 19:00:10",
          img:""
        },
        {
          name:"监控名称5",
          date:"2024-07-15 19:00:10",
          img:""
        },
        {
          name:"监控名称6",
          date:"2024-07-15 19:00:10",
          img:""
        },
        {
          name:"监控名称6",
          date:"2024-07-15 19:00:10",
          img:""
        }
      ]
    }
  },
  computed: {},
  mounted() {

  },
  methods: {
    scrollClick(type) {
      const ScrollElement = document.getElementById('scrollList');
      const scrollStep = 495;
      const maxScrollLeft = ScrollElement.scrollWidth - ScrollElement.clientWidth;

      if (type === 'left') {
        // 向左滚动
        if (ScrollElement.scrollLeft - scrollStep >= 0) {
          ScrollElement.scrollBy({
            left: -scrollStep,
            behavior: 'smooth'
          });
        }
      } else {
        // 向右滚动
        if (ScrollElement.scrollLeft + scrollStep <= maxScrollLeft) {
          ScrollElement.scrollBy({
            left: scrollStep,
            behavior: 'smooth'
          });
        }
      }
    }
  },
  watch: {}
}
</script>

<style scoped lang='less'>
::-webkit-scrollbar {
  width: 0;
  height: 0;
}
.wrap-container {
  width: 100%;
  height: 627px;
  display: flex;
  justify-content: center;
  align-items: center;
  .banner-container {
    width: 100%;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    .banner-container-left {
      width: 102px;
      height: 102px;
      background: url("@/assets/index/arrowLeft.png") no-repeat;
      background-size: 100% 100%;
      cursor: pointer;
    }
    .banner-container-center {
      width: 2396px;
      height: 440px;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      overflow-x: scroll;
      .banner-container-center-item {
        flex-shrink: 0;
        width: 420px;
        height: 440px;
        background: url("@/assets/index/videoBg.png") no-repeat;
        background-size: 100% 100%;
        cursor: pointer;
        margin-right: 74px;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        img {
          width: 380px;
          height: 248px;
          margin-top: 20px;
        }
        .text {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 700;
          font-size: 28px;
          color: #A8D6FF;
          text-align: center;
          font-style: normal;
          text-transform: none;
          margin-top: 30px;
        }
        .date {
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 400;
          font-size: 24px;
          color: #A8D6FF;
          text-align: center;
          font-style: normal;
          text-transform: none;
          margin-top: 20px;
        }
      }
    }
    .banner-container-right {
      width: 102px;
      height: 102px;
      background: url("@/assets/index/arrowRight.png") no-repeat;
      background-size: 100% 100%;
      cursor: pointer;
    }
  }
}
</style>