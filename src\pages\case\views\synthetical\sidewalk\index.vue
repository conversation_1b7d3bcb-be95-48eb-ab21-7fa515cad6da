<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" :inline="true" label-width="100px">
      <el-form-item label="违法时间">
        <el-date-picker
          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px;"
          value-format="yyyy-MM-dd hh:mm:ss"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="getList"
        />
      </el-form-item>
      <el-form-item label="关键词搜索">
        <el-input
          v-model="queryParams.searchValue"
          placeholder="请输入关键词搜索"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" size="mini" :loading="asyncLoading" @click="asyncData">同步数据</el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="sidewalkList" @selection-change="handleSelectionChange">
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column label="抄告单号" align="center" prop="billno" />
      <el-table-column label="采集部门" align="center" prop="cjbmmc" />
      <el-table-column label="采集人员" align="center" prop="cjmj" />
      <!-- <el-table-column label="处理标记" align="center" prop="clbj">
        <template slot-scope="scope">{{ scope.row.clbj | clbjName }}</template>
      </el-table-column> -->
      <el-table-column label="违法时间" align="center" prop="wfsj" />
      <el-table-column label="当事人" align="center" prop="dsr" />
      <el-table-column label="号牌号码" align="center" prop="hphm" />
      <el-table-column label="违法行为" align="center" prop="wfxw" />
      <el-table-column label="违法地址" align="center" prop="wfdz" />
      <el-table-column label="罚款金额(元)" align="center" prop="fkje" />
      <el-table-column label="缴款标记" align="center" prop="jkbj">
        <template slot-scope="scope">{{ scope.row.jkbj | jkbjName }}</template>
      </el-table-column>
      <!-- <el-table-column label="是否结案" align="center" prop="sfja">
        <template slot-scope="scope">{{ scope.row.sfja | sfjaName }}</template>
      </el-table-column> -->
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">{{ scope.row.status | statusName }}</template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['business:record:edit']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            style="color: #e6a23c;"
            @click="handleUpdate(scope.row, 'disabled')"
          >
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改智慧城管人行道违停对话框 -->
    <div>
      <el-dialog class="m-dialog" :title="title" :visible.sync="open">
        <el-scrollbar style="height: 100%;">
          <el-row :gutter="15" style="margin-right: 10px;">
            <el-form ref="form" v-loading="formLoading" :model="form" :rules="rules" label-width="100px">
              <el-col :span="12">
                <el-form-item label="案件编号" prop="ajbh">
                  <el-input v-model="form.ajbh" disabled placeholder="请输入案件编号" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="抄告单号" prop="billno">
                  <el-input v-model="form.billno" disabled placeholder="请输入抄告单号" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="采集部门名称" prop="cjbmmc">
                  <el-input v-model="form.cjbmmc" disabled placeholder="请输入采集部门名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="采集人员" prop="cjmj">
                  <el-input v-model="form.cjmj" disabled placeholder="请输入采集人员" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="处理时间" prop="clsj">
                  <el-input v-model="form.clsj" disabled placeholder="请输入处理时间" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="处理标记" prop="clbj">
                  <el-select v-model="form.clbj" placeholder="请选择处理标记" disabled :style="{ width: '100%' }">
                    <el-option label="未处理" value="0" />
                    <el-option label="已处理" value="1" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="当事人" prop="dsr">
                  <el-input v-model="form.dsr" disabled placeholder="请输入当事人姓名" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="号牌号码" prop="hphm">
                  <el-input v-model="form.hphm" disabled placeholder="请输入号牌号码" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="违法时间" prop="wfsj">
                  <el-input v-model="form.wfsj" disabled placeholder="请输入违法时间" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="违法地址" prop="wfdz">
                  <el-input v-model="form.wfdz" disabled placeholder="请输入违法地址" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="违法行为" prop="wfxw">
                  <el-input v-model="form.wfxw" disabled type="textarea" placeholder="请输入违法行为" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="罚款金额" prop="fkje">
                  <el-input v-model="form.fkje" disabled placeholder="请输入罚款金额" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="缴款日期" prop="jkrq">
                  <el-input v-model="form.jkrq" disabled placeholder="请输入缴款日期" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="缴款标记" prop="jkbj">
                  <el-select v-model="form.jkbj" placeholder="请选择缴款标记" disabled :style="{ width: '100%' }">
                    <el-option label="未缴款" value="0" />
                    <el-option label="已缴款" value="1" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="是否结案" prop="sfja">
                  <el-select v-model="form.sfja" placeholder="请选择是否结案" disabled :style="{ width: '100%' }">
                    <el-option label="未结案" value="0" />
                    <el-option label="已结案" value="1" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="违停图片" prop="files">
                  <MFileUpload ref="mainFile" disabled :file-list="mainFileList" />
                </el-form-item>
              </el-col>
            </el-form>
          </el-row>
        </el-scrollbar>
        <div slot="footer" class="dialog-footer">
          <!-- <el-button type="primary" @click="submitForm">确 定</el-button> -->
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { listSidewalk, getSidewalk, delSidewalk, addSidewalk, updateSidewalk, exportSidewalk, asyncListByCode } from '@/api/case/synthetical/sidewalk'
import { getFiles } from '@/api/supervise/swit'
import MFileUpload from '@/components/MFileUpload/index.vue'

export default {
  name: 'Sidewalk',
  components: {
    MFileUpload
  },
  filters: {
    statusName(status) {
      const statusObj = { 1: '进行中', 9: '已完结'}
      if (statusObj) return statusObj[status]
    },
    // 处理标记
    clbjName(status) {
      const clbjObj = { 0: '未处理', 1: '已处理'}
      if (clbjObj) return clbjObj[status]
    },
    // 缴款标记
    jkbjName(status) {
      const jkbjObj = { 0: '未缴款', 1: '已缴款'}
      if (jkbjObj) return jkbjObj[status]
    },
    // 是否结案
    sfjaName(status) {
      const sfjaObj = { 0: '未结案', 1: '已结案'}
      if (sfjaObj) return sfjaObj[status]
    }
  },
  data() {
    return {
      formLoading: false,
      mainFileList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 智慧城管人行道违停表格数据
      sidewalkList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dateRange: [],
        searchValue: ''
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      asyncLoading: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    asyncData() {
      this.asyncLoading = true
      asyncListByCode().then(() => {
        this.asyncLoading = false
        this.$message.success('同步成功')
        this.getList()
      }).catch(() => this.asyncLoading = false)
    },
    /** 查询智慧城管人行道违停列表 */
    getList() {
      this.loading = true
      const { dateRange,  pageNum, pageSize, searchValue} = this.queryParams
      let params = { pageNum, pageSize }
      if (dateRange && dateRange.length) params = { ...params, searchStartTime: dateRange[0], searchEndTime: dateRange[1] }
      if (searchValue) params = { ...params, searchValue }

      listSidewalk(params).then(response => {
        this.sidewalkList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.mainFileList = []
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        code: null,
        title: null,
        content: null,
        userId: null,
        userName: null,
        userIds: null,
        userNames: null,
        happenTime: null,
        address: null,
        longitude: null,
        latitude: null,
        type: null,
        status: '0',
        delFlag: null,
        deptId: null,
        deptName: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null,
        vioid: null,
        ajbh: null,
        billno: null,
        cjbm: null,
        cjbmmc: null,
        cjmj: null,
        clbj: null,
        clsj: null,
        dsr: null,
        fkje: null,
        hphm: null,
        hpzl: null,
        imgids: null,
        jdsbh: null,
        jkbj: null,
        jkrq: null,
        sfja: null,
        wfdd: null,
        wfdz: null,
        wfsj: null,
        wfxw: null,
        roadcode: null,
        roadname: null,
        xxdz: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        dateRange: [],
        searchValue: ''
      }
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加智慧城管人行道违停'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      this.open = true
      this.formLoading = true
      this.title = '智慧城管人行道违停'
      Promise.all([
        getSidewalk(id),
        getFiles({ businessId: id, tableName: 'case_sidewalk' })
      ]).then(response => {
        const [formData, fileData] = response
        this.form = formData.data
        this.formLoading = false

        // 获取文件
        this.mainFileList = fileData.rows.map(item => {
          return { name: item.displayName, url: `/zqzfj${item.filePath}`, ...item }
        })

      }).catch(() => this.formLoading = false)
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateSidewalk(this.form).then(() => {
              this.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addSidewalk(this.form).then(() => {
              this.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$confirm('是否确认删除智慧城管人行道违停编号为"' + ids + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return delSidewalk(ids)
      }).then(() => {
        this.getList()
        this.msgSuccess('删除成功')
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams
      this.$confirm('是否确认导出所有智慧城管人行道违停数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return exportSidewalk(queryParams)
      }).then(response => {
        this.download(response.msg)
      })
    }
  }
}
</script>
