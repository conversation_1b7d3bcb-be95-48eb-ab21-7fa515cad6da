<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" :inline="true" label-width="100px">
      <el-form-item label="类型">
        <el-select v-model="queryParams.type" style="width: 150px;" placeholder="请选择类型" @change="getList">
          <el-option
            v-for="item in typeData"
            :key="item.id"
            :label="item.value"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="人员名称" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入人员名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="打卡日期" prop="punchTime">
        <el-date-picker v-model="queryParams.punchTime" clearable
                        size="small"
                        type="date"
                        value-format="yyyy-MM-dd"
                        placeholder="选择打卡日期"
        />
      </el-form-item>
      <el-form-item label="内容">
        <el-input v-model="queryParams.searchValue" type="text" placeholder="请输入内容" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:in:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >
          新增
        </el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:in:edit']"
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >
          修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:in:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="inList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="类型" align="center" prop="type">
        <template slot-scope="scope">
          <span>{{ scope.row.type == 0 ? '机关' : '网格' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="人员名称" align="center" prop="userName" />
      <el-table-column label="打卡开始时间" align="center" prop="actualStartTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.actualStartTime) }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="早上打卡地址" align="center" prop="startAddr" /> -->
      <el-table-column label="打卡结束时间" align="center" prop="actualEndTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.actualEndTime) }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="下班打卡地址" align="center" prop="endAddr" /> -->
      <el-table-column label="打卡日期" align="center" prop="punchTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.punchTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="上班打卡状态" align="center" prop="startStatus">
        <template slot-scope="scope">
          <span v-if="scope.row.startStatus == 0">未打卡</span>
          <span v-if="scope.row.startStatus == 1">正常</span>
          <span v-if="scope.row.startStatus == 2">迟到</span>
          <span v-if="scope.row.startStatus == 3">早退</span>
          <span v-if="scope.row.startStatus == 4">定位异常</span>
          <span v-if="scope.row.startStatus == 5">迟到及定位异常</span>
          <span v-if="scope.row.startStatus == 6">早退及定位异常</span>
          <span v-if="scope.row.startStatus == 7">请假</span>
        </template>
      </el-table-column>
      <el-table-column label="下班打卡状态" align="center" prop="endStatus">
        <template slot-scope="scope">
          <span v-if="scope.row.endStatus == 0">未打卡</span>
          <span v-if="scope.row.endStatus == 1">正常</span>
          <span v-if="scope.row.endStatus == 2">迟到</span>
          <span v-if="scope.row.endStatus == 3">早退</span>
          <span v-if="scope.row.endStatus == 4">定位异常</span>
          <span v-if="scope.row.endStatus == 5">迟到及定位异常</span>
          <span v-if="scope.row.endStatus == 6">早退及定位异常</span>
          <span v-if="scope.row.endStatus == 7">请假</span>
        </template>
      </el-table-column>
      <el-table-column label="部门名称" align="center" prop="deptName" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['business:in:edit']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >
            修改
          </el-button>
          <el-button
            v-hasPermi="['business:in:remove']"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改打卡考勤对话框 -->
    <div>
      <el-dialog class="m-dialog" :close-on-click-modal="false" :title="title" :visible.sync="open">
        <el-scrollbar style="height: 100%;">
          <el-row :gutter="15" style="margin-right: 10px;">
            <el-form ref="form" :model="form" :rules="rules" label-width="100px">
              <h3 class="title">基本信息</h3>
              <el-col :span="12">
                <el-form-item label="类型" prop="type">
                  <el-select v-model="form.type" disabled :style="{ width: '100%' }">
                    <el-option label="机关" value="0" />
                    <el-option label="网格" value="1" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="人员名称" prop="userName">
                  <el-input v-model="form.userName" disabled placeholder="请输入人员名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="打卡日期" prop="punchTime">
                  <el-date-picker
                    v-model="form.punchTime"
                    clearable
                    size="small"
                    type="date"
                    value-format="yyyy-MM-dd"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="部门名称" prop="deptName">
                  <el-input v-model="form.deptName" disabled placeholder="请输入部门名称" />
                </el-form-item>
              </el-col>
              <h3 class="title">上班打卡</h3>
              <el-col :span="12">
                <el-form-item label="上班打卡时间" prop="actualStartTime">
                  <el-date-picker
                    v-model="form.actualStartTime"
                    clearable
                    size="small"
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="选择上班打卡时间"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="上班打卡状态" prop="startStatus">
                  <el-select v-model="form.startStatus" :style="{ width: '100%' }">
                    <el-option label="未打卡" value="0" />
                    <el-option label="正常" value="1" />
                    <el-option label="迟到" value="2" />
                    <el-option label="早退" value="3" />
                    <el-option label="定位异常" value="4" />
                    <el-option label="迟到及定位异常" value="5" />
                    <el-option label="早退及定位异常" value="6" />
                    <el-option label="请假" value="7" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="上班打卡地址" prop="startAddr">
                  <el-input v-model="form.startAddr" placeholder="请输入早上打卡地址" />
                </el-form-item>
              </el-col>
              <h3 class="title">下班打卡</h3>
              <el-col :span="12">
                <el-form-item label="打卡结束时间" prop="actualEndTime">
                  <el-date-picker
                    v-model="form.actualEndTime"
                    clearable
                    size="small"
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="选择实际打卡结束时间"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="下班打卡状态" prop="endStatus">
                  <el-select v-model="form.endStatus" :style="{ width: '100%' }">
                    <el-option label="未打卡" value="0" />
                    <el-option label="正常" value="1" />
                    <el-option label="迟到" value="2" />
                    <el-option label="早退" value="3" />
                    <el-option label="定位异常" value="4" />
                    <el-option label="迟到及定位异常" value="5" />
                    <el-option label="早退及定位异常" value="6" />
                    <el-option label="请假" value="7" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="下班打卡地址" prop="endAddr">
                  <el-input v-model="form.endAddr" placeholder="请输入下班打卡地址" />
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="备注" prop="remark">
                  <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
                </el-form-item>
              </el-col>
            </el-form>
          </el-row>
        </el-scrollbar>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { listIn, getIn, delIn, addIn, updateIn, exportIn } from '@/api/supervise/in'

export default {
  name: 'In',
  components: {
  },
  data() {
    return {
      typeData: [{id: '0', value: '机关'}, {id: '1', value: '网格'}],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 打卡考勤表格数据
      inList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        type: null,
        searchValue: '',
        punchTime: ''
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询打卡考勤列表 */
    getList() {
      this.loading = true
      const { punchTime, type, searchValue, pageNum, pageSize } = this.queryParams
      let params = { pageNum, pageSize }
      if (punchTime) params = { ...params, punchTime }
      if (type) params = { ...params, type }
      if (searchValue) params = { ...params, searchValue }
      listIn(params).then(response => {
        this.inList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        punchId: null,
        schedulingDetailId: null,
        title: null,
        type: null,
        userId: null,
        userName: null,
        actualStartTime: null,
        startLongitude: null,
        startLatitude: null,
        startAddr: null,
        actualEndTime: null,
        endLongitude: null,
        endLatitude: null,
        endAddr: null,
        startTime: null,
        endTime: null,
        punchTime: null,
        startStatus: '0',
        endStatus: '0',
        status: '0',
        delFlag: null,
        deptId: null,
        deptName: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        dateRange: [],
        pageNum: 1,
        pageSize: 10,
        type: null,
        searchValue: '',
        punchTime: ''
      }
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.punchId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加打卡考勤'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const punchId = row.punchId || this.ids
      getIn(punchId).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改打卡考勤'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.punchId != null) {
            updateIn(this.form).then(() => {
              this.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addIn(this.form).then(() => {
              this.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const punchIds = row.punchId || this.ids
      this.$confirm('是否确认删除打卡考勤编号为"' + punchIds + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return delIn(punchIds)
      }).then(() => {
        this.getList()
        this.msgSuccess('删除成功')
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams
      this.$confirm('是否确认导出所有打卡考勤数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return exportIn(queryParams)
      }).then(response => {
        this.download(response.msg)
      })
    }
  }
}
</script>
