<template>
  <div>
    <el-dialog class="m-dialog" :close-on-click-modal="false" v-bind="$attrs" :title="title" width="1000px" v-on="$listeners" @close="onClose">
      <el-scrollbar v-loading="loading" style="height: 100%;" :element-loading-text="formLoadingText">
        <el-row :gutter="15" style="margin-right: 10px;">
          <el-form ref="form" :model="form" label-width="120px" disabled>
            <h3 class="title">基本信息</h3>
            <el-row>
              <el-col :span="12">
                <el-form-item label="通讯地址" prop="address">
                  <el-tooltip :disabled="!form.address" class="item" effect="dark" :content="form.address" placement="top">
                    <el-input v-model="form.address" placeholder="请选择通讯地址">
                      <svg-icon slot="suffix" icon-class="map" class="svg-icon" @focus="openMap = true" />
                    </el-input>
                  </el-tooltip>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="申报者证件号码" prop="applyCardNumber">
                  <el-input v-model="form.applyCardNumber" placeholder="请输入申报者证件号码" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="申报者证件类型" prop="applyCardType">
                  <el-input v-model="form.applyCardType" placeholder="请输入申报者证件类型" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="申报来源" prop="applyFrom">
                  <el-input v-model="form.applyFrom" placeholder="请输入申报来源" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="申报者名称" prop="applyName">
                  <el-input v-model="form.applyName" placeholder="请输入申报者名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目性质" prop="applyPropertiy">
                  <el-input v-model="form.applyPropertiy" placeholder="请输入项目性质" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="审批类型" prop="approvetype">
                  <el-input v-model="form.approvetype" placeholder="请输入审批类型" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="区划编码" prop="areacode">
                  <el-input v-model="form.areacode" placeholder="请输入收件部门所属行政区划编码" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="所属系统" prop="belongsystem">
                  <el-input v-model="form.belongsystem" placeholder="请输入所属系统" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目关联号" prop="belongto">
                  <el-input v-model="form.belongto" placeholder="请输入项目关联号" />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="办理方式" prop="busmode">
                  <el-input v-model="form.busmode" placeholder="请输入办理方式" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="办理方式说明" prop="busmodeDesc">
                  <el-input v-model="form.busmodeDesc" placeholder="请输入办理方式说明" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系人" prop="contactMan">
                  <el-input v-model="form.contactMan" placeholder="请输入联系人" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="代理人证件号码" prop="contactManCardNumber">
                  <el-input v-model="form.contactManCardNumber" placeholder="请输入代理人证件号码" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="代理人证件类型" prop="contactManCardType">
                  <el-input v-model="form.contactManCardType" placeholder="请输入代理人证件类型" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="办件类型" prop="infoType">
                  <el-input v-model="form.infoType" placeholder="请输入办件类型" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="是否是在垂管系统中运行的事项" class="sf" prop="isManubrium">
                  <el-radio-group v-model="form.isManubrium">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="0">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="法人代表" prop="legalman">
                  <el-input v-model="form.legalman" placeholder="请输入法人代表" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="邮编" prop="postcode">
                  <el-input v-model="form.postcode" placeholder="请输入邮编" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="申报名称" prop="projectName">
                  <el-input v-model="form.projectName" placeholder="请输入申报名称" />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="申报号" prop="projId">
                  <el-input v-model="form.projId" placeholder="请输入申报号" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="查询密码" prop="projPwd">
                  <el-input v-model="form.projPwd" placeholder="请输入查询密码" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="创建用户名称" prop="receiveName">
                  <el-input v-model="form.receiveName" placeholder="请输入创建用户名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="申报时间" prop="receiveTime">
                  <div class="block">
                    <el-date-picker
                      v-model="form.receiveTime"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      type="datetime"
                      placeholder="选择日期时间"
                      default-time="12:00:00"
                    />
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="收件人ID" prop="receiveUseId">
                  <el-input v-model="form.receiveUseId" placeholder="请输入收件人ID" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="关联业务标识" prop="relbusId">
                  <el-input v-model="form.relbusId" placeholder="请输入关联业务标识" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="权力事项编码" prop="serviceCode">
                  <el-input v-model="form.serviceCode" placeholder="请输入权力事项编码" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="权力事项基本码" prop="serviceCodeId">
                  <el-input v-model="form.serviceCodeId" placeholder="请输入权力事项基本码" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="终审部门编码" prop="serviceDeptId">
                  <el-input v-model="form.serviceDeptId" placeholder="请输入事项终审部门编码" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="实施机构代码" prop="ssorgCode">
                  <el-input v-model="form.ssorgCode" placeholder="请输入实施机构组织机构代码" />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="权力事项版本号" prop="serviceVersion">
                  <el-input v-model="form.serviceVersion" placeholder="请输入权力事项版本号" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="权力事项名称" prop="serviceName">
                  <el-input v-model="form.serviceName" placeholder="请输入权力事项名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="同步状态" prop="syncStatus">
                  <el-input v-model="form.syncStatus" placeholder="请输入同步状态" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系电话" prop="phone">
                  <el-input v-model="form.phone" placeholder="请输入联系电话" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="是否已缴费" prop="payment">
                  <el-radio-group v-model="form.payment">
                    <el-radio :label="1">已缴费</el-radio>
                    <el-radio :label="0">未缴费</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="分类中队名称" prop="cDeptName">
                  <el-input v-model="form.cDeptName" placeholder="请输入分类中队名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="截至时间" prop="endtime">
                  <div class="block">
                    <el-date-picker
                      v-model="form.endtime"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      type="datetime"
                      placeholder="选择日期时间"
                      default-time="12:00:00"
                    />
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="处罚金额" prop="punishment">
                  <el-input v-model="form.punishment" placeholder="请输入处罚金额" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="搜索地址" prop="searchAddress">
                  <el-input v-model="form.searchAddress" placeholder="请输入搜索地址" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <!-- 地图 -->
              <!-- <div v-if="openMap" class="map">
                <div class="bg">
                  <tdtMap ref="bindmap" :map-search="true" :styles="{width:'900px',height:'90vh'}" :dw="form" @onlnglat="onlnglat" />
                </div>
              </div> -->
              <tdtMap ref="bindmap" :visible.sync="openMap" append-to-body :map-search="true" :dw="form" @onlnglat="onlnglat" />
            </el-row>
          </el-form>
        </el-row>
      </el-scrollbar>
      <div slot="footer" class="dialog-footer">
        <!-- <el-button type="primary" @click="primary">确 定</el-button> -->
        <el-button @click="close">取 消 </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import shop from '@/api/case/synthetical/shop'

import tdtMap from '@/components/tdtMap/tdtMap'

export default {
  name: 'Fromlist',
  components: {tdtMap},
  props: {
    detailId: Number,
    title: String,
    getform: Object

  },
  data() {
    return {
      loading: true,
      openMap: false,
      form: {},
      formLoadingText: '数据加载中'
    }
  },
  watch: {
    '$attrs.visible'(nVal) {
      if (nVal && this.getform) {
        this.form = {...this.getform}
        this.loading = false
      }
    }
  },
  created() {
  },
  methods: {
    primary() {
      let params = {...this.form}
      params.status = 1
      shop.xukeAdd(params).then(res => {
        console.log(res)
      })
    },
    onlnglat(lenlat) {      // 经纬度  详细地址
      let {lng: longitude, lat: latitude, address} = lenlat
      this.form = {...this.form, longitude, latitude, address}
      this.openMap = false
    },
    reset() {
      this.form = {}
    },
    // 取消
    onClose() {
      this.reset()
      this.formLoadingText = '数据上传中'
      this.$refs.form.clearValidate()
    },
    close() {
      this.$emit('update:visible', false)
      this.onClose()
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .sf .el-form-item__label,
::v-deep .sf .el-form-item--medium .el-form-item__content {
  width: 220px !important;
}
</style>
