<template>
  <div class="bg-login">
    <div class="loginBox">
      <div class="login-title">金华市城市运行管理服务系统</div>
      <div class="icon-shine"></div>
      <div class="login-content">
        <div class="login-row color-0ff">
          <span class="icon-user lf"></span>
          <input id="username" class="ipt-un fs-40 lf input"name="username" placeholder="请输入您的账号"
                 type="text" v-model="loginUser.username"
                 readonly onfocus="this.removeAttribute('readonly');" onblur="this.setAttribute('readonly',true);"/>
        </div>
        <div class="login-row color-0ff">
          <span class="icon-pass lf"></span>
          <input id="password" class="ipt-pw fs-40 lf input" name="password" placeholder="请输入您的密码"
                 :type="[flag?'text':'password']" autocomplete="off" v-model="loginUser.password" data="password">
          <i :class="[flag?'el-icon-minus':'el-icon-view']" style="margin-top:8px;font-size:36px;color: #019cd8" @click="flag=!flag"></i>
          </input>
        </div>
        <div id="login" class="btn fs-50" @click="Tologin(3)">点击登录</div>
      </div>
      <div class="icon-shine icon-shine1"></div>
    </div>
  </div>
</template>

<script>
import { getUserArea, getUserInfo, getUrl, indexApi, login, loginV4 } from '@/api/indexApi'
import { setToken } from '@/utils/auth'
export default {
  name: 'index',
  data() {
    return {
      loginUser: {
        username: '',
        password: '',
      },
      flag: false,
    }
  },
  computed: {},
  created() {
    //url包含参数
    if (JSON.stringify(this.getUrlParams(window.location.href)) != '{}') {
      let params = this.getUrlParams(window.location.href)
      if (params.csdnToken) {
        getUserInfo('/token/authentication', { authorizeToken: params.csdnToken }).then((res) => {
          this.loginUser.username = res.data.data.userName
          this.loginUser.password = res.data.data.password
          this.Tologin(1)
        })
      } else if (params.appkey && params.appsecret) {
        //省厅单点登录系统大屏页
        return
        if (params.appkey == '43463654sfhyasda' && params.appsecret == 'jh374bf24mh2m5muvdtyd59u868998t754qn6547889') {
          loginV4().then((res) => {
            if (res.code == 200) {
              setToken(res.token)
              this.$message.success('登录成功!')
              this.$router.push('/home2')
            } else {
              this.$message.warning(res.msg)
            }
          })
        }
      } else {
        getUserInfo('/token/authentication', { appId: params.appid, authorizeToken: params.authorizeToken }).then(
          (res) => {
            if (res.data.code == 200) {
              this.loginUser.username = res.data.data.userName
              this.loginUser.password = window.atob(res.data.data.password)
              this.Tologin(2, params)
            } else {
              this.$message.warning(res.data.msg)
            }
          }
        )
      }
    }
  },
  mounted() {},
  methods: {
    //type:1 大脑跳转免登 type:2 县市区链接免登 type:3 正常登录流程
    Tologin(type, params) {
      const that = this
      let para = {
        username: this.loginUser.username,
        password: type == 1 ? this.loginUser.password : window.btoa(this.loginUser.password),
      }
      login(para)
        .then((res) => {
          console.log(res)
          if (res.code == 200) {
            setToken(res.token)
            localStorage.setItem('bigScreen', res.bigScreen)
            that.$message.success('登录成功!')
            type == 2 && params.page ? that.OpenMdJumpPage(params) : that.OpenDefaultPage()
          } else {
            that.$message.warning('用户名或密码错误!')
          }
        })
        .catch(() => {
          that.$message.error('系统错误,响应超时')
        })
    },
    OpenMdJumpPage(params) {
      //三色预警
      if (params.page == 'ssyj') {
        getUrl({ type: 'dashboard', module: 'ssyj' }).then((res) => {
          if (res.data.code == 200) {
            // window.open(res.data.data.url);
            window.location.href = res.data.data.url
          }
        })
      } else {
        indexApi('xzzfj_sy_cd_tz', { area: localStorage.getItem('adminCity'), name: params.page }).then((res) => {
          this.$router.push('/' + res.data[0].key)
        })
      }
    },
    OpenDefaultPage() {
      this.$router.push('/home2')
    },
    //获取URL中的参数
    getUrlParams(url) {
      // 创建空对象存储参数
      let obj = {}
      if (url.split('?')[1]) {
        // 通过 ? 分割获取后面的参数字符串
        let urlStr = url.split('?')[1]
        // 再通过 & 将每一个参数单独分割出来
        let paramsArr = urlStr.split('&')
        for (let i = 0, len = paramsArr.length; i < len; i++) {
          // 再通过 = 将每一个参数分割为 key:value 的形式
          let arr = paramsArr[i].split('=')
          obj[arr[0]] = arr[1]
        }
      }
      return obj
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
@font-face {
  font-family: 'PangMenZhengDao';
  src: url('@/assets/fonts/PangMenZhengDaoBiaoTiTi-1.ttf');
}

@font-face {
  font-family: 'SourceHanSansCN-Bold';
  src: url('@/assets/fonts/SourceHanSansCN-Bold.otf');
}
.bg-login {
  width: 100%;
  height: 100%;
  background: url('@/assets/login/login-bg.png') no-repeat 0 0;
  background-size: 100% 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.bg-circle {
  background: url('@/assets/login/login-circle.png') no-repeat;
  width: 4253px;
  height: 1036px;
}
.login-title {
  font-size: 110px;
  font-family: PangMenZhengDao;
  background: linear-gradient(to bottom, #ffffff, #6282ee);
  -webkit-background-clip: text;
  color: transparent;
  z-index: 1;
  letter-spacing: 0px;
  margin-top: 350px;
}
.loginBox {
  width: 2356px;
  height: 1356px;
  background: url('@/assets/login/login_box.png') no-repeat 0 0;
  background-size: 100% 100%;
  transform: scale(0.77);
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
}
.login-content {
  height: 511px;
  margin-top: 81px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
}
.login-row {
  width: 820px;
  height: 120px;
  line-height: 120px;
  background: #0a316b;
  border: 2px solid #019cd8;
  margin-bottom: 30px;
}
.ipt-un {
  background: rgba(0, 0, 0, 0);
  background-image: url('@/assets/login/username.png');
  background-repeat: no-repeat;
  background-position-y: 30px;
  border: none;
  border-radius: 8px;
  width: 92%;
  outline: none;
  height: inherit;
  box-sizing: border-box;
  padding: 0px 50px;
  font-size: 32px;
  color: #78abc2;
}
.ipt-pw {
  background: rgba(0, 0, 0, 0);
  background-image: url('@/assets/login/password.png');
  background-repeat: no-repeat;
  background-position-y: 30px;
  border: none;
  border-radius: 8px;
  width: 92%;
  outline: none;
  height: inherit;
  box-sizing: border-box;
  padding: 0px 50px;
  font-size: 32px;
  color: #78abc2;
}
.icon-user {
  background: url('@/assets/login/login-icon.png') no-repeat;
  width: 60px;
  height: 63px;
  margin: 20px 0 0 20px;
}
.icon-pass {
  background: url('@/assets/login/login-icon.png') no-repeat 0 -65px;
  width: 58px;
  height: 62px;
  margin: 20px 0 0 20px;
}
.ipt-error {
  color: #f8545b;
}
.jzmm {
  color: rgba(161, 181, 210, 0.5);
  margin-top: 8%;
}
.checkbox {
  display: inline-block;

  /* margin-right: 64px; */
  top: 4px;
  cursor: pointer;
}
.btn {
  width: 820px;
  height: 120px;
  background-color: #00c0ff;
  line-height: 120px;
  text-align: center;
  color: #000;
  font-size: 40px;
  font-family: SourceHanSansCN-Bold;
  font-weight: bold;
  border-radius: 8px;
  cursor: pointer;
  letter-spacing: 20px;
  z-index: 11;
  margin-top: 120px;
}
.login-text {
  font-size: 5rem;
  /*font-size: 2rem;*/

  opacity: 0.6;
  font-weight: bold;
}
.text1 {
  left: 307px;
  top: 559px;
  background: linear-gradient(0deg, #4481eb 0%, #04befe 100%);
  -webkit-background-clip: text;
  color: transparent;
  animation: show 3s ease infinite;
}
.text2 {
  left: 200px;
  top: 870px;
  /* left: 650px; */
  /* top: 108px; */
  background: linear-gradient(0deg, #4481eb 0%, #04befe 100%);
  -webkit-background-clip: text;
  color: transparent;
  animation: show 3s ease infinite 1s;
}
.text3 {
  left: 1288px;
  top: 559px;
  /* left: 480px; */
  /* top: 631px; */
  background: linear-gradient(0deg, #4481eb 0%, #04befe 100%);
  -webkit-background-clip: text;
  color: transparent;
  animation: show 3s ease infinite 2s;
}
.text4 {
  left: 1326px;
  top: 870px;
  /* left: 1366px; */
  /* top: 115px; */
  background: linear-gradient(0deg, #4481eb 0%, #04befe 100%);
  -webkit-background-clip: text;
  color: transparent;
  animation: show 3s ease infinite 1.5s;
}
.text5 {
  left: 2680px;
  top: 742px;
  /*left: 1337px;*/
  /*top: 262px;*/
  background: linear-gradient(rgba(255, 226, 159, 0.3), rgba(255, 169, 159, 0.3), rgba(255, 112, 154, 0.3));
  -webkit-background-clip: text;
  color: transparent;
  animation: show 3s ease infinite;
}
.text6 {
  left: 2860px;
  top: 885px;
  /*left: 1433px;*/
  /*top: 423px;*/
  background: linear-gradient(rgba(168, 129, 235, 0.3), rgba(4, 190, 254, 0.3));
  -webkit-background-clip: text;
  color: transparent;
  animation: show 3s ease infinite 2s;
}
@keyframes show {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    opacity: 1;
  }
}
.icon-shine {
  background: url('@/assets/login/login-shine.png') no-repeat;
  width: 1024px;
  height: 368px;
  position: absolute;
  top: -14px;
  left: 0;
  animation: moveR 10s infinite linear;
  animation-fill-mode: forwards;
}
.icon-shine1 {
  top: 991px;
  left: 1130px;
  animation: moveL 10s infinite linear;
  animation-fill-mode: forwards;
}
@keyframes moveR {
  0% {
    left: -60px;
  }
  25% {
    left: 1160px;
  }
  50% {
    left: -60px;
  }
  75% {
    left: 1160px;
  }
}
@keyframes moveL {
  0% {
    left: 1130px;
  }
  25% {
    left: -80px;
  }
  50% {
    left: 1130px;
  }
  75% {
    left: -80px;
  }
}
.input::-webkit-input-placeholder {
  font-size: 32px;
  color: #78abc2;
}
</style>