<template>
  <!-- 地图选择区域弹窗 -->
  <el-dialog :close-on-click-modal="false" v-bind="$attrs" append-to-body :before-close="mapBeforeClose" width="70%" @open="handleOpen" v-on="$listeners">
    <div class="map-box">
      <div id="map" ref="map" />
      <el-input v-model="mapSearch" placeholder="请输入关键字进行搜索" type="text" clearable class="map-search-box" @keyup.enter.native="handleMapSearch">
        <el-button slot="append" type="primary" @click="handleMapSearch">选择</el-button>
      </el-input>
      <div class="btns">
        <el-button size="mini" type="primary" @click="handleDraw">{{ status | btnName }}</el-button>
        <el-button size="mini" type="primary" @click="handleClear">清空全部</el-button>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="mapBeforeClose">取 消</el-button>
      <el-button type="primary" @click="handleMapSubmit">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'TMapDraw',
  filters: {
    btnName(status) {
      const names = { 0: '绘制区域', 1: '确认已绘制区域', 2: '开始编辑', 3: '确认编辑' }
      return names[status]
    }
  },
  inheritAttrs: false,
  props: {
    area: {
      type: String
    }
  },
  data() {
    return {
      $map: null,
      mapSearch: '',
      placeSearch: null,
      mapPolygon: null,
      polygonTool: null,
      status: 0
    }
  },
  methods: {
    createMap() {
      this.$map = new window.T.Map(this.$refs.map)
      this.$map.centerAndZoom(new window.T.LngLat(119.63126, 29.11181), 18)
      /* 添加搜索模块 */
      this.placeSearch = new window.T.LocalSearch(this.$map, { pageCapacity: 5, onSearchComplete: this.onSearchComplete })
      /* 绘制模块 */
      this.polygonTool = new window.T.PolygonTool(this.$map, {
        color: '#FF33FF',
        weight: 6,
        opacity: 0.2,
        fillOpacity: 0.4,
        fillColor: '#1791fc',
        showLabel: false
      })
      this.polygonTool.addEventListener('draw', e => {
        this.mapPolygon = e.currentPolygon
        this.status = 2
      })
    },
    onSearchComplete(result) {
      this.$map.clearOverLays()
      const pois = result.getPois()
      if (pois && pois.length) {
        const zoomArr = pois.map(item => {
          const lnglatArr = item.lonlat.split(' ')
          const lnglat = new window.T.LngLat(lnglatArr[0], lnglatArr[1])
          const winHtml = `名称:${item.name}<br/>地址:${item.address}`
          // 创建标注对象
          let marker = new window.T.Marker(lnglat)
          // 地图上添加标注点
          this.$map.addOverLay(marker)
          // 注册标注点的点击事件
          let markerInfoWin = new window.T.InfoWindow(winHtml, {autoPan: true})
          marker.addEventListener('click', function() {
            marker.openInfoWindow(markerInfoWin)
          })
          return lnglat
        })
        this.$map.setViewport(zoomArr)
      } else {
        this.$message.warning('未搜索到结果')
      }
    },
    handleOpen() {
      this.$nextTick(() => {
        if (!this.$map) this.createMap()
        /* 添加初始已绘制图形 */
        if (this.area) {
          this.status = 2
          const path = this.area.split(';').map(item => {
            const lnglat = item.split(',')
            return new window.T.LngLat(lnglat[0], lnglat[1])
          })
          this.mapPolygon = new window.T.Polygon(path, {
            color: '#FF33FF',
            weight: 6,
            opacity: 0.2,
            fillOpacity: 0.4,
            fillColor: '#1791fc'
          })
          this.$map.addOverLay(this.mapPolygon)
          this.$map.setViewport(path)
        } else {
          this.status = 0
          this.$map.centerAndZoom(new window.T.LngLat(119.63126, 29.11181), 18)
        }
      })
    },
    mapBeforeClose() {
      if (this.$map) this.$map.clearOverLays()
      this.mapSearch = null
      this.mapPolygon = null
      if (this.polygonTool) this.polygonTool.close()
      this.$emit('update:visible', false)
    },
    handleMapSearch() {
      this.placeSearch.search(this.mapSearch, 7)
    },
    handleDraw() {
      switch (this.status) {
        case 0:
          if (this.polygonTool) {
            this.polygonTool.open()
            this.status = 1
          } else {
            this.$message.error('绘制插件启用失败，请关闭弹窗重新绘制')
          }
          break
        case 2:
          if (this.mapPolygon) {
            this.mapPolygon.enableEdit()
            this.status = 3
          } else {
            this.$message.error('绘制出错，请关闭弹窗重新绘制')
          }
          break
        case 3:
          if (this.mapPolygon) {
            this.mapPolygon.disableEdit()
            this.status = 2
          } else {
            this.$message.error('绘制出错，请关闭弹窗重新绘制')
          }
          break
        default:
          return
      }
    },
    handleClear() {
      if (this.polygonTool) {
        // this.polygonTool.clear()
        this.$map.clearOverLays()
        this.mapPolygon = null
        this.status = 0
      } else {
        this.$message.error('绘制插件启用失败，请关闭弹窗重新绘制')
      }
    },
    handleMapSubmit() {
      if (this.mapPolygon) {
        const lngLats = this.mapPolygon.getLngLats()[0]
        if (lngLats && lngLats.length) {
          const lnglatAry = lngLats.map(item => {
            return `${item.lng},${item.lat}`
          })
          this.$emit('confirm', lnglatAry.join(';'))
        } else {
          this.$emit('confirm', '')
        }
      } else {
        this.$emit('confirm', '')
      }
      this.mapBeforeClose()
    }
  }
}
</script>

<style scoped lang="scss">
.map-box {
  position: relative;
  height: 60vh;
  overflow: hidden;
  #map {
    width: 100%;
    height: 620px;
  }
}
.map-search-box {
  width: 300px;
  position: absolute;
  right: 10px;
  top: 10px;
  z-index: 400;
  box-shadow: 0 0 5px #bbb;
}
.btns {
  padding: 10px;
  background: #fff;
  position: absolute;
  right: 10px;
  bottom: 10px;
  z-index: 400;
  box-shadow: 0 0 5px #bbb;
  border-radius: 5px;
}
</style>
