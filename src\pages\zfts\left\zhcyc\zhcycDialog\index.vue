<template>
  <ygfDialog :visible='visible' width='1615px'>
    <div id="jcdx" class="rwgz-tc">
      <div class="rw-title flex-between">
        <div class="fs-44 text-mid-yellow dialogTitle" id="rwTitle" style="margin-left: 20px;">{{name}}</div>
        <div class="close cursor" @click="close" style="margin-right: 20px;"></div>
      </div>
      <div class="content">
        <!--        <div style="width: 100%;display: flex;justify-content: center;align-items: center">-->
        <!--          <div class="tab-con s-flex" v-show="activeValue != '部门'">-->
        <!--            <span-->
        <!--              class="tab-item"-->
        <!--              v-for="(item,i) in tabList"-->
        <!--              :class="{tabConActive:btnActive==i}"-->
        <!--              @click="changeTab(i)"-->
        <!--            >{{item.name}}-->
        <!--            </span>-->
        <!--          </div>-->
        <!--        </div>-->
        <div class="table">
          <div class="table-line title-line">
            <div class="table-column table-title" style="flex: 1;margin-left: 30px">序号</div>
            <div class="table-column table-title" style="flex: 3" v-show="city == '金华市'">区域</div>
            <div class="table-column table-title" style="flex: 3" v-show="city != '金华市'">牵头部门</div>
            <div class="table-column table-title" style="flex: 2" v-show="name == '检查对象数'">检查对象数</div>
            <div class="table-column table-title" style="flex: 2" v-show="name == '检查次数'">检查次数(次)</div>
            <div class="table-column table-title" style="flex: 2" v-show="name == '转处罚率'">转处罚率</div>
            <div class="table-column table-title" style="flex: 2" v-show="name == '跨部门监管率'">跨部门监管率</div>
            <div class="table-column table-title" style="flex: 2" v-show="name == '问题发现率'">问题发现率</div>
          </div>
          <div class="table-container">
            <div class="table-line" v-for="(item,i) in tableData" :key="i" :class="{activeTableLine:i % 2 == 0}">
              <div class="table-column" style="flex: 1; margin-left: 30px">{{i + 1}}</div>
              <div class="table-column" style="flex: 3" v-show="city == '金华市'">{{item.qy}}</div>
              <div class="table-column" style="flex: 3" :title="item.depart" v-show="city != '金华市'">{{item.depart}}</div>
              <div class="table-column" style="flex: 2">{{name.indexOf("率") != -1?item.num + "%":item.num}}</div>
            </div>
          </div>
        </div>
        <!--        <div class="pagination">-->
        <!--          <el-pagination-->
        <!--            background-->
        <!--            :total="total"-->
        <!--            :page.sync="queryParams.pageNum"-->
        <!--            :limit.sync="queryParams.pageSize"-->
        <!--            @pagination="getDetail"-->
        <!--            layout="prev, pager, next, jumper">-->
        <!--          </el-pagination>-->
        <!--        </div>-->
      </div>
    </div>
  </ygfDialog>
</template>

<script>
import ygfDialog from '@/components/ygfDialog'
import { indexApi } from '@/api/indexApi'
export default {
  name: 'index',
  props: ['name','visible'],
  components: {
    ygfDialog
  },
  data() {
    return {
      btnActive: 0,
      btnList: [{name:"市直部门",type:"金华市"}, {name:"区域",type:"金华市"},{name:"部门",type:""}],
      tableData: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 100,
      city:localStorage.getItem("city")
    }
  },
  computed: {
    activeValue() {
      return this.tabList[this.btnActive].name
    },
    tabList() {
      return this.city == '金华市'?this.btnList.filter(item => item.type == '金华市'):this.btnList.filter(item => item.type != '金华市')
    }
  },
  mounted() {
    // 监听城市变化
    this.$bus.$on('cityChange', (city) => {
      this.city = city;
      this.getDetail(this.name, localStorage.getItem("year"));
    });
    // 监听年份变化
    this.$bus.$on('yearChange', (year) => {
      this.year = year;
      this.getDetail(this.name, year);
    });
    this.getDetail(this.name,localStorage.getItem("year"))
  },
  methods: {
    //获取数据
    getDetail(name,year) {
      switch (name) {
        case "问题发现率":
          this.getWtfxl(year);
          break;
        case "跨部门监管率":
          this.getKbmjg(year);
          break;
        case "检查对象数":
          this.getJcdxs(year);
          break;
        case "检查次数":
          this.getJccs(year);
          break;
        case "转处罚率":
          this.getZcfl(year);
          break;
      }
    },
    //检查次数
    getJccs(year) {
      const that = this;
      that.tableData = []
      indexApi("csdn_yjyp67",{qxwd:localStorage.getItem("city"),sjwd2:year}).then(res => {
        that.tableData = res.data.map(item => {return {
          qy:item.ywwd2,
          depart:item.ywwd1,
          num: item.tjz
        }})
      })
    },
    //跨部门监管
    getKbmjg(year) {
      const that = this;
      that.tableData = []
      indexApi("csdn_yjyp68",{qxwd:localStorage.getItem("city"),sjwd2:year}).then(res => {
        that.tableData = res.data.map(item => {return {
          qy:item.ywwd2,
          depart:item.ywwd1,
          num: item.tjz
        }})
      })
    },
    //检查对象数
    getJcdxs(year) {
      const that = this;
      that.tableData = []
      indexApi("csdn_yjyp69",{qxwd:localStorage.getItem("city"),sjwd2:year}).then(res => {
        that.tableData = res.data.map(item => {return {
          qy:item.ywwd2,
          depart:item.ywwd1,
          num: item.tjz
        }})
      })
    },
    //问题发现率
    getWtfxl(year) {
      const that = this;
      that.tableData = []
      indexApi("csdn_yjyp70",{qxwd:localStorage.getItem("city"),sjwd2:year}).then(res => {
        that.tableData = res.data.map(item => {return {
          qy:item.ywwd2,
          depart:item.ywwd1,
          num: item.tjz
        }})
      })
    },
    //转处罚率
    getZcfl(year) {
      const that = this;
      that.tableData = []
      indexApi("csdn_yjyp71",{qxwd:localStorage.getItem("city"),sjwd2:year}).then(res => {
        that.tableData = res.data.map(item => {return {
          qy:item.ywwd2,
          depart:item.ywwd1,
          num: item.tjz
        }})
      })
    },
    //关闭弹窗
    close() {
      this.$emit('close')
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.getDetail(this.name,localStorage.getItem("year"))
      }
    }
  }
}
</script>

<style scoped lang='less'>

::-webkit-scrollbar {
  display: none;
}

.dialogTitle {
  font-size: 44px;
  background-image: linear-gradient(180deg, #ffb637, #ffb637, #fff, #ffb637, #ffb637);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

ul,
ul li {
  list-style: none;
}

.rwgz-tc {
  width: 1730px;
  height: 999px;
  background: url("@/assets/zhdd/dialogBg.png") no-repeat;
  background-size: 100% 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-direction: column;
}

.rw-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 46px 3% 0;
  width: 95%;
  height: 60px;
  line-height: 60px;
}

.close {
  background: url("@/assets/zhdd/close.png") no-repeat;
  width: 34px;
  height: 34px;
}

.ql-indent-1 img {
  width: 100%;
}

img {
  width: 100%;
}

.content {
  height: 850px;
  margin-top: 10px;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  flex-direction: column;
}

::-webkit-scrollbar {
  width: 0;
  height: 0;
}

.scrollTab {
  width:928px;
  height:70px;
}

.table-container {
  height: 720px;
  overflow-y: scroll;
}

.activeContainer {
  height: 640px;
}

.tableContainer2 {
  height: 400px;
  overflow-y: scroll;
}

.table-line {
  width: 1625px;
  height: 80px;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  background: rgba(50,134,248,0.15);
}

.title-line {
  background: transparent !important;
}

.table-title {
  font-size: 32px;
  font-family: Source Han Sans CN;
  font-weight: bold;
  color: #FFFFFF;
  text-align: left;
}

.table-column {
  font-size: 32px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.activeTableLine {
  background: rgba(50,134,248,0.25);
}

/deep/ .el-input {
  /*position: absolute;*/
  /*top: 229px;*/
  /*left: 1630px;*/
  width: 537px;
  height: 72px;
}

/deep/ .el-input__icon {
  height: 100%;
  width: 25px;
  text-align: center;
  -webkit-transition: all .3s;
  transition: all .3s;
  line-height: 75px;
}

/deep/ .el-scrollbar {
  overflow: hidden;
  /*position: relative;*/
  height: 500px;
  background: #020b28;
  border-radius: 10px;
}

/deep/ .el-autocomplete-suggestion__wrap {
  max-height: unset !important;
  padding: 10px 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

/deep/ .el-input--suffix /deep/ .el-input__inner {
  border: 1px solid #359CF8;
  border-radius: 8px;
  padding-right: 30px;
  height: 70px;
  font-family: MicrosoftYaHei;
  font-size: 28px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 1px;
  color: #bbe5fd !important;
  background: #020b28;
}

/deep/ .el-input.is-active /deep/ .el-input__inner,
/deep/ .el-input__inner:focus {
  border: 1px solid #bbe5fd;
  outline: 0;
}

/deep/ .el-input__suffix-inner {
  pointer-events: all;
  font-size: 28px;
  margin: 15px 20px 0 0;
  color: #bbe5fd !important;
}

/deep/ .el-autocomplete-suggestion li {
  padding: 0 20px;
  line-height: 34px;
  cursor: pointer;
  color: #bbe5fd;
  font-size: 28px;
  list-style: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin: 10px 0 25px 0;
}

/deep/ .el-autocomplete-suggestion li:hover {
  background: unset !important;
}

.search {
  width: 100px;
  height: 65px;
  line-height: 65px;
  text-align: center;
  background: #0A619E;
  border: 1px solid #359CF8;
  border-radius: 8px;
  font-size: 28px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FEFEFE;
  margin-left: 10px;
  cursor: pointer;
}

.tab-con {
  width: 351px;
  height: 70px;
  line-height: 70px;
  color: #fff;
  font-size: 32px;
}

.s-flex {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-evenly;
}

.tab-item {
  font-size: 36px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  font-style: italic;
  color: rgba(171, 206, 239, 0.7);
  line-height: 59px;
}

.tabConActive {
  background: url('@/assets/zfts/tab-active.png') no-repeat;
  background-size: 110% 100%;
  font-family: Source Han Sans CN;
  font-weight: bold;
  font-size: 36px;
  color: #ffffff;
}

.pagination {
  width: 100%;
  margin-top: 50px;
  text-align: center;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

/deep/ .el-pagination.is-background .btn-next,
/deep/ .el-pagination.is-background .btn-prev,
/deep/ .el-pagination.is-background /deep/ .el-pager li {
  background: transparent;
  color: #c1e2fa;
  font-size: 34px;
  font-weight: normal;
  height: 42px;
  line-height: 40px;
  box-sizing: border-box;
}

/deep/ .el-pager li.active + li {
  border-left: 1px solid #0badc1 !important;
}

/deep/ .el-pagination.is-background /deep/ .el-pager li:not(.disabled).active {
  background: transparent;
  border-radius: 3px 3px 3px 3px;
  height: 42px;
  line-height: 42px;
  box-sizing: border-box;
}

/deep/ .el-pager li {
  background: transparent;
  padding: 0 20px;
  border: 1px solid #0badc1;
  box-sizing: border-box;
}

/deep/ .el-pagination .btn-next /deep/ .el-icon,
/deep/ .el-pagination .btn-prev /deep/ .el-icon {
  font-size: 30px;
}

/deep/ .el-pagination__jump {
  font-size: 32px !important;
  height: 42px !important;
  line-height: 42px !important;
  color: #c1e2fa;
  display: inline-flex !important;
  align-items: center;
}

/deep/ .el-pagination__editor/deep/ .el-input {
  height: 32px;
  width: 77px;
  margin: 0 12px 0 12px;
}

/deep/ .el-pagination__editor/deep/ .el-input /deep/ .el-input__inner {
  height: 32px;
  font-size: 28px;
  background: #132c4e;
  color: #C1E2FA;
  border: 1px solid;
  border-image: linear-gradient(-32deg, #359cf8, #afdcfb) 1 1;
}
</style>