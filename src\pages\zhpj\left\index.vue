<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-03-10 14:47:13
 * @LastEditors: wjb
 * @LastEditTime: 2025-03-11 08:42:20
-->
<template>
  <div class="left-Map">
    <hjwsgl />
    <gyssgl />
    <jtzxgl />
    <shangl />
  </div>
</template>

<script>
import hjwsgl from './hjwsgl'
import gyssgl from './gyssgl'
import jtzxgl from './jtzxgl'
import shangl from './shangl'
export default {
  name: 'index',
  components: { hjwsgl, gyssgl, jtzxgl,shangl },
  data() {
    return {}
  },
  computed: {},
  mounted() {},
  methods: {},
  watch: {},
}
</script>

<style scoped>
</style>