import {request} from '@/utils/request'

// 查询成长激励积分配置列表
export function listConfig(query) {
  return request({
    url: '/business/oa/score/config/list',
    method: 'get',
    params: query
  })
}

// 查询成长激励积分配置详细
export function getConfig(id) {
  return request({
    url: '/business/oa/score/config/' + id,
    method: 'get'
  })
}

// 新增成长激励积分配置
export function addConfig(data) {
  return request({
    url: '/business/oa/score/config/add',
    method: 'post',
    data: data
  })
}

// 修改成长激励积分配置
export function updateConfig(data) {
  return request({
    url: '/business/oa/score/config/edit',
    method: 'post',
    data: data
  })
}

// 删除成长激励积分配置
export function delConfig(id) {
  return request({
    url: '/business/oa/score/config/remove/' + id,
    method: 'post'
  })
}

// 导出成长激励积分配置
export function exportConfig(query) {
  return request({
    url: '/business/oa/score/config/export',
    method: 'get',
    params: query
  })
}
