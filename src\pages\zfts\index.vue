<template>
  <div class='pageContainer'>
    <animated-transition
      enter="fadeInLeft"
      leave="fadeOutLeft"
    >
      <left v-show="showPage"></left>
    </animated-transition>
    <animated-transition
      enter="fadeInRight"
      leave="fadeOutRight"
    >
      <right v-show="showPage"></right>
    </animated-transition>
    <animated-transition
      enter="fadeInUp"
      leave="fadeOutDown"
    >
      <bottom v-show="showPage" @dialogShow='showBottomDialog'></bottom>
    </animated-transition>

    <bottomDialog :details="params" :visible="dialogShow" @close="dialogShow = false"></bottomDialog>
  </div>
</template>

<script>
import left from './left'
import right from './right'
import bottom from './bottom'
import AnimatedTransition from '@/components/AnimatedTransition'
import bottomDialog from './bottom/bottomDialog'
export default {
  name: 'index',
  components: {
    left,
    right,
    bottom,
    AnimatedTransition,
    bottomDialog
  },
  data() {
    return {
      showPage: false,

      params: {},
      dialogShow: false
    }
  },
  computed: {},
  mounted() {
    this.$bus.$on("showPage", res => {
      this.showPage = res
    })
    setTimeout(() => {
      this.showPage = !this.showPage
    }, 100)
  },
  methods: {
    showBottomDialog(res) {
      this.params = res;
      this.dialogShow = true;
    }
  },
  watch: {}
}
</script>

<style scoped>

</style>