<template>
  <div class="container">
    <el-row style="margin-right: 10px;">
      <el-form ref="form" v-loading="loading" :model="form" label-width="110px">
        <el-col :span="24">
          <h3 class="title">
            <span>基本信息</span>
          </h3>
        </el-col>
        <el-col :span="12">
          <el-form-item label="发起人" prop="userName">
            <span>{{ form.userName }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="发生时间" prop="happenTime">
            <span>{{ form.happenTime }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="案件类型" prop="caseTypeName">
            <span>{{ form.caseTypeName }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="地址" prop="address">
            <span>{{ form.address }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="案件内容" prop="content">
            <span>{{ form.content }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="上传图片" prop="files">
            <el-image v-for="(src,idx) in formFiles" :key="idx" style="width: 200px; height: 200px;" fit="cover" :src="src" @load="() => handleLoadImg(idx)" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <h3 class="title">接警信息</h3>
        </el-col>
        <el-col :span="12">
          <el-form-item label="中队长名称" prop="squadronUserName">
            <span>{{ form.squadronUserName }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="执行人员" prop="userNames">
            <span>{{ form.userNames }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <h3 class="title">处警信息</h3>
        </el-col>
        <el-col :span="12">
          <el-form-item label="出警时间" prop="outTime">
            <span>{{ form.outTime }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="到达时间" prop="arriveTime">
            <span>{{ form.arriveTime }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="现场情况" prop="doDescn">
            <span>{{ form.doDescn }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="现场图片" prop="files">
            <el-image v-for="(src,idx) in descnFileList" :key="idx" style="width: 200px; height: 200px;" fit="cover" :src="src" @load="() => handleLoadImg(idx)" />
          </el-form-item>
        </el-col>
        <el-col v-if="form.status >= 6" :span="12">
          <el-form-item label="办结时间" prop="fbTime">
            <span>{{ form.fbTime }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="处警结果" prop="doResult">
            <span>{{ form.doResult }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24" style="border-bottom: 1px solid #ccc;">
          <el-form-item label="处警图片" prop="files">
            <el-image v-for="(src,idx) in resultFile" :key="idx" style="width: 200px; height: 200px;" fit="cover" :src="src" @load="() => handleLoadImg(idx)" />
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
  </div>
</template>

<script>
import { captureOne } from '@/api/case/synthetical/capture'
import { getFiles } from '@/api/supervise/swit'

export default {
  data() {
    return {
      loading: false,
      form: {},
      formFiles: [],
      descnFileList: [],
      resultFile: [],
      fileIdx: 0
    }
  },
  mounted() {
    const params = this.$route.query
    if (params.id) {
      this.loading = true
      Promise.all([
        captureOne(params.id),
        getFiles({businessId: params.id, tableName: 'case_capture'})
      ])
        .then(resAry => {
          const [formData, fileData] = resAry
          this.form = formData.data
          fileData.rows.map(file => {
            const url =  `/zqzfj${file.filePath}`
            if (file.status == 2) {
              this.formFiles.push(url)
            } else if (file.status == 6) {
              this.descnFileList.push(url)
            } else if (file.status == 9) {
              this.resultFile.push(url)
            }
          })
          this.loading = false
        }).catch(() => {
          this.loading = false
        })
    }
  },
  methods: {
    policeTypeDataName(type) {
      const res = this.policeTypeData.find(item => type == item.dictValue)
      if (res) {
        return res.dictLabel
      } else {
        return ''
      }
    },
    policeCategoryDataName(type) {
      const res = this.policeCategoryData.find(item => type == item.dictValue)
      if (res) {
        return res.dictLabel
      } else {
        return ''
      }
    },
    handleLoadImg() {
      this.fileIdx++
      if (this.fileIdx == this.formFiles.length + this.descnFileList.length + this.resultFile.length) {
        this.pagePrint()
      }
    },
    pagePrint() {
      this.$nextTick(() => {
        window.print()
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .container {
    .title {
      padding-left: 10px;
    }
    ::v-deep {
      .el-form-item {
        margin-bottom: 0;
      }
      .el-row {
        border-right: 1px solid #ccc;
      }
      [class*=el-col-] {
        border-top: 1px solid #ccc;
        border-left: 1px solid #ccc;
      }
      .el-form-item--medium .el-form-item__label {
        padding-left: 10px;
      }
      .el-form-item--medium .el-form-item__content {
        padding-left: 10px;
        border-left: 1px solid #ccc;
      }
    }
  }
</style>
